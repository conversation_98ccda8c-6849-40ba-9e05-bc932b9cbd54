/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import com.misyn.mcms.dbconfig.ConnectionPool;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import com.misyn.mcms.roleFacility.CompanyPrivilegeManager;
import com.misyn.mcms.roleFacility.RolePrivilege;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.LinkedList;
import java.util.List;
public class CompanyManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(CompanyManager.class);
    private static DbRecordCommonFunction dbRecordCommonFunction = new DbRecordCommonFunction();
    private static CompanyManager companyManager = null;
    String msg = "";
    private ConnectionPool cp = null;

    public CompanyManager() {

        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public static synchronized CompanyManager getInstance() {
        if (companyManager == null) {
            companyManager = new CompanyManager();
        }
        return companyManager;
    }

    //===================insertCompany Insert Method=======================================
    private synchronized int insertCompany(Company company) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;

        /* `n_comid`,
        `v_comCode`,
        `v_description`,
        `v_add1`,
        `v_add2`,
        `v_telno`,
        `v_contperson`,
        `v_contemail`,
        `v_comstatus`,
        `v_inpstat`,
        `v_inpuser`,
        `d_inptime`,
        `v_auth1stat`,
        `v_auth1user`,
        `d_auth1time`,
        `v_auth2stat`,
        `v_auth2user`,
        `d_auth2time`*/

        String strSQL = "INSERT INTO company_mst VALUES("
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?,?,?,?,?,?,?)";
        try {

            if (dbRecordCommonFunction.isRecExists("company_mst", "v_comCode='" + company.getV_comCode().trim() + "'")) {
                company.setErrorMessage("Company Code " + company.getV_comCode() + " is already exist");
                return result;
            }
            company.setN_comid(getNextID());

            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, company.getN_comid());
            ps.setString(2, company.getV_comCode());
            ps.setString(3, company.getV_description());
            ps.setString(4, company.getV_add1());
            ps.setString(5, company.getV_add2());
            ps.setString(6, company.getV_telno());
            ps.setString(7, company.getV_contperson());
            ps.setString(8, company.getV_contemail());
            ps.setString(9, company.getV_comstatus());
            ps.setString(10, "I");
            ps.setString(11, company.getV_inpuser());
            ps.setString(12, Utility.sysDate("yyyy-MM-dd hh:mm:ss"));
            ps.setString(13, company.getV_auth1stat());
            ps.setString(14, company.getV_auth1user());
            ps.setString(15, company.getD_auth1time());
            ps.setString(16, company.getV_auth2stat());
            ps.setString(17, company.getV_auth2user());
            ps.setString(18, company.getD_auth2time());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    //===================insertCompany Insert Method=======================================
    private synchronized int updateCompany(Company company) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;

        /* `n_comid`,
        `v_comCode`,
        `v_description`,
        `v_add1`,
        `v_add2`,
        `v_telno`,
        `v_contperson`,
        `v_contemail`,
        `v_comstatus`,
        `v_inpstat`,
        `v_inpuser`,
        `d_inptime`,
        `v_auth1stat`,
        `v_auth1user`,
        `d_auth1time`,
        `v_auth2stat`,
        `v_auth2user`,
        `d_auth2time`*/


        String strSQL = "UPDATE company_mst SET "
                + "n_comid = ?,"
                + "v_comCode = ?,"
                + "v_description = ?,"
                + "v_add1 = ?,"
                + "v_add2 =?,"
                + "v_telno = ?,"
                + "v_contperson = ?,"
                + "v_contemail = ?,"
                + "v_comstatus =?,"
                + "v_inpstat = ?,"
                + "v_inpuser =?,"
                + "d_inptime = ?,"
                + "v_auth1stat = ?,"
                + "v_auth1user =?,"
                + "d_auth1time = ?,"
                + "v_auth2stat =?,"
                + "v_auth2user = ?,"
                + "d_auth2time = ? "
                + "WHERE n_comid=?";


        try {

            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, company.getN_comid());
            ps.setString(2, company.getV_comCode());
            ps.setString(3, company.getV_description());
            ps.setString(4, company.getV_add1());
            ps.setString(5, company.getV_add2());
            ps.setString(6, company.getV_telno());
            ps.setString(7, company.getV_contperson());
            ps.setString(8, company.getV_contemail());
            ps.setString(9, company.getV_comstatus());
            ps.setString(10, "M");
            ps.setString(11, company.getV_inpuser());
            ps.setString(12, Utility.sysDate("yyyy-MM-dd hh:mm:ss"));
            ps.setString(13, company.getV_auth1stat());
            ps.setString(14, company.getV_auth1user());
            ps.setString(15, company.getD_auth1time());
            ps.setString(16, company.getV_auth2stat());
            ps.setString(17, company.getV_auth2user());
            ps.setString(18, company.getD_auth2time());

            ps.setInt(19, company.getN_comid());

            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public synchronized int saveCompany(Company company, List<RolePrivilege> rolePrivilegeList) {
        int result = 0;
        int n_comid = company.getN_comid();

        try {
            if (!DbRecordCommonFunction.getInstance().isRecExists("company_mst", "n_comid=" + n_comid)) {
                if (!dbRecordCommonFunction.isIsErrorExsist()) {
                    result = insertCompany(company);
                }
            } else {
                result = updateCompany(company);
            }
            if (result > 0) {
                int r = CompanyPrivilegeManager.getInstance().saveCompanyPrivilege(rolePrivilegeList, company);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return result;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public synchronized int deleteCompany(List<Company> companyList) {
        int result = 0;
        Company company = null;
        int n_comid = 0;


        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL = "SELECT n_usrcode FROM usr_mst WHERE n_comid=? AND v_usrstatus<>'C'";

        int updateCountResult = -1;
        try {
            conn = getJDBCConnection();
            conn.setAutoCommit(false);
            for (int i = 0; i < companyList.size(); i++) {
                setMsg("");
                company = companyList.get(i);
                n_comid = company.getN_comid();
                ps = conn.prepareStatement(strSQL);


                ps.setInt(1, n_comid);
                ResultSet rs1 = ps.executeQuery();
                if (!rs1.next()) {
                    ps = conn.prepareStatement("DELETE FROM company_mst WHERE n_comid=?");
                    ps.setInt(1, n_comid);
                    result = ps.executeUpdate();
                    if (result > 0) {
                        ps = conn.prepareStatement("DELETE FROM comprev_mst WHERE n_comid=?");
                        ps.setInt(1, n_comid);
                        ps.executeUpdate();
                    }
                    if (result > 0) {
                        updateCountResult++;
                    }
                } else {
                    setMsg("Can not delete " + company.getV_comCode() + ": The company is in use by the following table -> usr_mst");
                    try {
                        if (conn != null) {
                            conn.rollback();
                        }
                        conn.setAutoCommit(true);
                    } catch (Exception e1) {
                    }
                    return 0;
                }

            }

            conn.commit();
            conn.setAutoCommit(true);
            setMsg("Record Delete Successful");

        } catch (Exception e) {
            try {
                if (conn != null) {
                    conn.rollback();
                }
                conn.setAutoCommit(true);
            } catch (Exception e1) {
            }
            LOGGER.error(e.getMessage());
            setMsg("Can not be Delete");
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return updateCountResult;
    }

    private synchronized int getNextID() {
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT MAX(n_comid) as txnID from company_mst";
        int maxid = 0;
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                maxid = rs.getInt("txnID");
            }
            maxid++;
            rs.close();

        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return maxid;
    }

    private synchronized Company getCompany_For_ViewList(ResultSet rs) {
        Company company = new Company();
        try {
            company.setN_comid(rs.getInt("n_comid"));
            company.setV_comCode(rs.getString("v_comCode"));
            company.setV_description(rs.getString("v_description"));
            company.setV_add1(rs.getString("v_add1"));
            company.setV_add2(rs.getString("v_add2"));
            company.setV_telno(rs.getString("v_telno"));
            company.setV_contperson(rs.getString("v_contperson"));
            company.setV_contemail(rs.getString("v_contemail"));
            company.setV_comstatus(rs.getString("v_comstatus"));
            company.setV_inpstat(rs.getString("v_inpstat"));
            company.setV_inpuser(rs.getString("v_inpuser"));
            company.setD_inptime(rs.getString("d_inptime"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return company;
    }

    public synchronized List<Company> getCompanyList(User input_User, int n_comid) {
        List<Company> m_Company = new LinkedList<Company>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "";


        if (input_User.getN_accessusrtype() == 1) //super Admin accessess Level
        {
            strSql = "SELECT * "
                    + "FROM "
                    + "company_mst "
                    + "WHERE "
                    + "n_comid=" + n_comid;


        } else if (input_User.getN_accessusrtype() == 2) //Admin accessess Level
        {
            strSql = "SELECT * "
                    + "FROM "
                    + "company_mst "
                    + "WHERE "
                    + "n_comid=" + n_comid;
        } else if (input_User.getN_accessusrtype() == 3
                || input_User.getN_accessusrtype() == 4
                || input_User.getN_accessusrtype() == 5) //Agent admin,Internal Admin AND External Admin accessess Level
        {

            strSql = "SELECT * "
                    + "FROM "
                    + "company_mst "
                    + "WHERE "
                    + "n_comid=" + input_User.getN_comid();


        }
        // SystemMessage.getInstance().writeMessage("DEBUG :-->getRolePrivilegeViewList--> " + strSql);
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_Company.add(getCompany_For_ViewList(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_Company;
    }

    public synchronized List<Company> getCompanyViewList(User input_User, String searchKey, String limitSearchKey) {
        List<Company> m_Company = new LinkedList<Company>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "";

        searchKey = searchKey.trim();
        if (!searchKey.equalsIgnoreCase("")) {
            searchKey = " AND " + searchKey;
        }

        if (input_User.getN_accessusrtype() == 1) //super Admin accessess Level
        {
            strSql = "SELECT * "
                    + "FROM "
                    + "company_mst "
                    + "WHERE "
                    + "n_comid>0 "
                    + searchKey + " "
                    + "group by n_comid " + limitSearchKey;


        } else if (input_User.getN_accessusrtype() == 2) //Admin accessess Level
        {
            strSql = "SELECT * "
                    + "FROM "
                    + "company_mst "
                    + "WHERE "
                    + "n_comid>0 "
                    + searchKey + " "
                    + "group by n_comid " + limitSearchKey;
        } else if (input_User.getN_accessusrtype() == 3
                || input_User.getN_accessusrtype() == 4
                || input_User.getN_accessusrtype() == 5) //Agent admin,Internal Admin AND External Admin accessess Level
        {
            strSql = "SELECT * "
                    + "FROM "
                    + "company_mst "
                    + "WHERE "
                    + "n_comid=" + input_User.getN_comid() + " "
                    + searchKey + " "
                    + "group by n_comid " + limitSearchKey;
        }
        // SystemMessage.getInstance().writeMessage("DEBUG :-->getRolePrivilegeViewList--> " + strSql);
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_Company.add(getCompany_For_ViewList(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_Company;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}
