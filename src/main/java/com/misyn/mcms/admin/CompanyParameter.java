/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.misyn.mcms.admin;

/**
 * Created on : Dec 27, 2010, 10:03:54 AM
 *
 * <AUTHOR> Sepala
 * @version 2.0
 * @Product : Intranet - UA Intranet & Common Auth. System
 * @Copyright : Copyright, 2009-2010 (c)
 * @Company : M.I. Synergy (Pvt) Ltd
 */

public class CompanyParameter {
    private int id = 0;
    private int comid = 0;
    private String paraCode = "";
    private String description = "";

    private String comCode = "";
    private String comDesc = "";

    private String stats = "";
    private String inpstat = "I";
    private String inpuser = "";
    private String inptime = "1900-01-01 12:00:00";
    private String auth1stat = "P";
    private String auth1user = "";
    private String auth1time = "1900-01-01 12:00:00";
    private String auth2stat = "P";
    private String auth2user = "";
    private String auth2time = "1900-01-01 12:00:00";

    private boolean isNew = true;
    private String chkvalue = "";

    public boolean isIsNew() {
        return isNew;
    }

    public void setIsNew(boolean isNew) {
        this.isNew = isNew;
    }

    public String getComCode() {
        return comCode;
    }

    public void setComCode(String comCode) {
        this.comCode = comCode;
    }

    public String getComDesc() {
        return comDesc;
    }

    public void setComDesc(String comDesc) {
        this.comDesc = comDesc;
    }


    public String getChkvalue() {
        return chkvalue;
    }

    public void setChkvalue(String chkvalue) {
        this.chkvalue = chkvalue;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }


    public String getAuth1stat() {
        return auth1stat;
    }

    public void setAuth1stat(String auth1stat) {
        this.auth1stat = auth1stat;
    }

    public String getAuth1time() {
        return auth1time;
    }

    public void setAuth1time(String auth1time) {
        this.auth1time = auth1time;
    }

    public String getAuth1user() {
        return auth1user;
    }

    public void setAuth1user(String auth1user) {
        this.auth1user = auth1user;
    }

    public String getAuth2stat() {
        return auth2stat;
    }

    public void setAuth2stat(String auth2stat) {
        this.auth2stat = auth2stat;
    }

    public String getAuth2time() {
        return auth2time;
    }

    public void setAuth2time(String auth2time) {
        this.auth2time = auth2time;
    }

    public String getAuth2user() {
        return auth2user;
    }

    public void setAuth2user(String auth2user) {
        this.auth2user = auth2user;
    }

    public int getComid() {
        return comid;
    }

    public void setComid(int comid) {
        this.comid = comid;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getInpstat() {
        return inpstat;
    }

    public void setInpstat(String inpstat) {
        this.inpstat = inpstat;
    }

    public String getInptime() {
        return inptime;
    }

    public void setInptime(String inptime) {
        this.inptime = inptime;
    }

    public String getInpuser() {
        return inpuser;
    }

    public void setInpuser(String inpuser) {
        this.inpuser = inpuser;
    }

    public String getParaCode() {
        return paraCode;
    }

    public void setParaCode(String paraCode) {
        this.paraCode = paraCode;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }


}
