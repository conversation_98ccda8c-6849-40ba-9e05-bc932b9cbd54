/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import com.misyn.mcms.dbconfig.ConnectionPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.LinkedList;
import java.util.List;
public class CompanyParameterManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(CompanyParameterManager.class);

    String msg = "";
    private ConnectionPool cp = null;

    public CompanyParameterManager() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    //===================CompanyParamerers Insert Method=======================================
    private synchronized int insertCompanyParameter(CompanyParameter companyParameter) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;
        //n_comid,n_group_id,paracode
        String strSQL = "INSERT INTO "
                + "comparalist_mst "
                + "(comid, paraCode, status, inpstat, inpuser, inptime, auth1stat, auth1user, auth1time, auth2stat, auth2user, auth2time) "
                + "VALUES(?,?,'A','I',?,current_timestamp,'P','System1',current_timestamp,'P','System2',current_timestamp)";


        try {

            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, companyParameter.getComid());
            ps.setString(2, companyParameter.getParaCode());
            ps.setString(3, companyParameter.getInpuser());

            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public int saveCompanyParamerers(List<CompanyParameter> companyParameterList, int n_comid) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;
        String strSQL = "DELETE FROM comparalist_mst "
                + "WHERE comid=?";

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, n_comid);
            ps.executeUpdate();

            for (CompanyParameter m_CompanyParameter : companyParameterList) {
                if (m_CompanyParameter.getChkvalue().equalsIgnoreCase("checked")) {
                    insertCompanyParameter(m_CompanyParameter);
                    result++;
                } else {
                    strSQL = "DELETE FROM com_para_group_mst "
                            + "WHERE n_comid=? AND paracode=?";
                    ps = conn.prepareStatement(strSQL);
                    ps.setInt(1, n_comid);
                    ps.setString(2, m_CompanyParameter.getParaCode());
                    ps.executeUpdate();

                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public synchronized int deleteCompanyParameter(List<CompanyParameter> companyParameterList) {
        int result = 0;
        CompanyParameter companyParameter = null;
        String paracode = "";

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL = "SELECT * FROM com_para_group_mst WHERE n_comid=? AND paracode=?";

        int updateCountResult = -1;
        try {
            conn = getJDBCConnection();
            conn.setAutoCommit(false);
            for (int i = 0; i < companyParameterList.size(); i++) {
                setMsg("");
                companyParameter = companyParameterList.get(i);
                paracode = companyParameter.getParaCode();
                ps = conn.prepareStatement(strSQL);
                ps.setInt(1, companyParameter.getComid());
                ps.setString(2, paracode);
                rs = ps.executeQuery();
                if (rs.next()) {
                    setMsg("Can not delete " + paracode + ": The parameter code is in use by the following table -> com_para_group_mst");
                    try {
                        if (conn != null) {
                            conn.rollback();
                        }
                        conn.setAutoCommit(true);
                    } catch (Exception e1) {
                    }
                    return result;
                } else {
                    rs.close();
                    strSQL = "SELECT * FROM usrparalist_mst WHERE usrcode IN(SELECT n_usrcode FROM usr_mst where n_comid=?  AND v_usrstatus<>'C') AND paracode=?";
                    ps = conn.prepareStatement(strSQL);
                    ps.setInt(1, companyParameter.getComid());
                    ps.setString(2, paracode);
                    rs = ps.executeQuery();
                    if (rs.next()) {
                        setMsg("Can not delete " + paracode + ": The parameter code is in use by the following table -> usrparalist_mst,usr_mst");
                        try {
                            if (conn != null) {
                                conn.rollback();
                            }
                            conn.setAutoCommit(true);
                        } catch (Exception e1) {
                        }
                        return result;
                    }
                }
                rs.close();
                ps = conn.prepareStatement("DELETE FROM comparalist_mst WHERE comid=?");
                ps.setInt(1, companyParameter.getComid());
                result = ps.executeUpdate();
                if (result > 0) {
                    updateCountResult++;
                }
                rs.close();

            }

            conn.commit();
            conn.setAutoCommit(true);
            setMsg("Record Delete Successful");

        } catch (Exception e) {
            try {
                if (conn != null) {
                    conn.rollback();
                }
                conn.setAutoCommit(true);
            } catch (Exception e1) {
            }
            LOGGER.error(e.getMessage());
            setMsg("Can not be Delete");
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return updateCountResult;
    }

    private synchronized CompanyParameter getCompanyParameter_New(ResultSet rs) {
        CompanyParameter companyParameter = new CompanyParameter();
        try {
            companyParameter.setParaCode(rs.getString("t2.paracode"));
            companyParameter.setDescription(rs.getString("t2.description"));
            companyParameter.setIsNew(true);


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return companyParameter;
    }

    private synchronized CompanyParameter getCompanyParameter_Modify(ResultSet rs) {
        CompanyParameter companyParameter = new CompanyParameter();
        try {

            companyParameter.setComid(rs.getInt("t1.comid"));
            companyParameter.setParaCode(rs.getString("t2.paracode"));
            companyParameter.setDescription(rs.getString("t2.description"));
            companyParameter.setIsNew(false);


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return companyParameter;
    }

    public synchronized List<CompanyParameter> getCompanyParameter_New_List() {
        List<CompanyParameter> m_CompanyParameter = new LinkedList<CompanyParameter>();
        Connection conn = null;
        PreparedStatement ps = null;
//        String strSql = "SELECT "
//                + "t2.paraCode,"
//                + "t2.description "
//                + "from "
//                + "paralist_mst as t2 ";

        String strSql = "SELECT "
                + "t2.paraCode,"
                + "t2.description "
                + "from "
                + "paralist_mst as t2  WHERE "
                + "t2.paracode IN ("
                + "SELECT paracode "
                + "FROM "
                + "appparam_mst "
                + "WHERE "
                + "paracode =t2.paracode  "
                + "AND "
                + "tableName='User Parameter' "
                + "AND "
                + "fieldname='paravalue')";

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_CompanyParameter.add(getCompanyParameter_New(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_CompanyParameter;
    }

    public synchronized List<CompanyParameter> getCompanyParameter_List_Modify(int n_comid) {
        List<CompanyParameter> m_CompanyParameter = new LinkedList<CompanyParameter>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT "
                + "t2.paraCode,"
                + "t2.description "
                + "from "
                + "paralist_mst as t2  WHERE "
                + "t2.paracode IN ("
                + "SELECT paracode "
                + "FROM "
                + "appparam_mst "
                + "WHERE "
                + "paracode =t2.paracode  "
                + "AND "
                + "tableName='User Parameter' "
                + "AND "
                + "fieldname='paravalue')";


//        String strSql = "SELECT "
//                + "p.paracode,"
//                + "p.description "
//                + "FROM paralist_mst p,"
//                + "comparalist_mst c "
//                + "WHERE c.comid=? "//n_comid
//                + "AND "
//                + "c.paracode=p.paracode"
//                + " AND "
//                + "("
//                + "p.paracode NOT IN ("
//                + "SELECT "
//                + "paracode "
//                + "FROM "
//                + "appparam_mst "
//                + "WHERE "
//                + "paracode =p.paracode) "
//                + "OR "
//                + "p.paracode IN ("
//                + "SELECT paracode "
//                + "FROM "
//                + "appparam_mst "
//                + "WHERE "
//                + "paracode =p.paracode  "
//                + "AND "
//                + "tableName='User Parameter' "
//                + "AND "
//                + "fieldname='paravalue')"
//                + ")";

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                strSql = "SELECT t1.id,"
                        + "t1.comid,"
                        + "t2.paraCode,"
                        + "t2.description "
                        + "from "
                        + "comparalist_mst as t1,"
                        + "paralist_mst as t2 "
                        + "WHERE "
                        + "t1.paraCode=t2.paraCode "
                        + "AND "
                        + "t1.comid=? "
                        + "AND "
                        + "t1.paracode=?";

                ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
                ps.setInt(1, n_comid);
                ps.setString(2, rs.getString("t2.paraCode"));
                ResultSet rs1 = ps.executeQuery();
                String s = ps.toString();
                if (rs1.next()) {
                    m_CompanyParameter.add(getCompanyParameter_Modify(rs1));
                } else {
                    m_CompanyParameter.add(getCompanyParameter_New(rs));
                }

                rs1.close();

            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_CompanyParameter;
    }

    private synchronized CompanyParameter getCompanyParameter_For_ViewList(ResultSet rs) {
        CompanyParameter companyParameter = new CompanyParameter();
        try {
            companyParameter.setComid(rs.getInt("t1.comid"));
            companyParameter.setComCode(rs.getString("t2.v_comcode"));
            companyParameter.setComDesc(rs.getString("t2.v_description"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return companyParameter;
    }

    public synchronized List<CompanyParameter> getCompanyParameterViewList(User input_User, String searchKey, String limitSearchKey) {
        List<CompanyParameter> m_CompanyParameter = new LinkedList<CompanyParameter>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "";

        searchKey = searchKey.trim();
        if (!searchKey.equalsIgnoreCase("")) {
            searchKey = " AND " + searchKey;
        }

        if (input_User.getN_accessusrtype() == 1 || input_User.getN_accessusrtype() == 2) //super Admin accessess Level
        {
            strSql = "select "
                    + "t1.comid,"
                    + "t2.v_comcode,"
                    + "t2.v_description "
                    + "FROM "
                    + "comparalist_mst as t1,"
                    + "company_mst as t2 "
                    + "WHERE t1.comid=t2.n_comid "
                    + searchKey + " "
                    + "GROUP BY t1.comid " + limitSearchKey;
        } else if (input_User.getN_accessusrtype() == 3
                || input_User.getN_accessusrtype() == 4
                || input_User.getN_accessusrtype() == 5) //Agent admin,Internal Admin AND External Admin accessess Level
        {
            strSql = "select "
                    + "t1.comid,"
                    + "t2.v_comcode,"
                    + "t2.v_description "
                    + "FROM "
                    + "comparalist_mst as t1,"
                    + "company_mst as t2 "
                    + "WHERE t1.comid=t2.n_comid "
                    + "AND "
                    + "t1.comid=" + input_User.getN_comid() + " "
                    + searchKey + " "
                    + "GROUP BY t1.comid " + limitSearchKey;
        }
        // SystemMessage.getInstance().writeMessage("DEBUG :-->getRolePrivilegeViewList--> " + strSql);
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_CompanyParameter.add(getCompanyParameter_For_ViewList(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_CompanyParameter;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

}
