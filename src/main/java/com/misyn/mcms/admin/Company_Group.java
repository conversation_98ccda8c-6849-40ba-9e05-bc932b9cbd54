/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package com.misyn.mcms.admin;

/**
 * Created on : Dec 13, 2010, 1:47:26 PM
 *
 * <AUTHOR>
 * @version 2.0
 * @Product : Intranet - UA Intranet & Common Auth. System
 * @Copyright : Copyright, 2009-2010 (c)
 * @Company : M.I. Synergy (Pvt) Ltd
 */

public class Company_Group {
    private int n_comid = -1;
    private int n_group_id = -1;
    private String v_groupName = "";
    private String v_comCode = "";
    private String paracode = "";
    private String desception = "";
    private boolean isNew = true;
    private String chkvalue = "";

    public boolean isIsNew() {
        return isNew;
    }

    public void setIsNew(boolean isNew) {
        this.isNew = isNew;
    }

    public String getChkvalue() {
        return chkvalue;
    }

    public void setChkvalue(String chkvalue) {
        this.chkvalue = chkvalue;
    }


    public int getN_comid() {
        return n_comid;
    }

    public void setN_comid(int n_comid) {
        this.n_comid = n_comid;
    }

    public int getN_group_id() {
        return n_group_id;
    }

    public void setN_group_id(int n_group_id) {
        this.n_group_id = n_group_id;
    }

    public String getParacode() {
        return paracode;
    }

    public void setParacode(String paracode) {
        this.paracode = paracode;
    }

    public String getDesception() {
        return desception;
    }

    public void setDesception(String desception) {
        this.desception = desception;
    }

    public String getV_comCode() {
        return v_comCode;
    }

    public void setV_comCode(String v_comCode) {
        this.v_comCode = v_comCode;
    }

    public String getV_groupName() {
        return v_groupName;
    }

    public void setV_groupName(String v_groupName) {
        this.v_groupName = v_groupName;
    }

}
