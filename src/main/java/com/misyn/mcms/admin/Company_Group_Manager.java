/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import com.misyn.mcms.dbconfig.ConnectionPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.LinkedList;
import java.util.List;
public class Company_Group_Manager {
    private static final Logger LOGGER = LoggerFactory.getLogger(Company_Group_Manager.class);

    String msg = "";
    private ConnectionPool cp = null;

    public Company_Group_Manager() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    //===================CompanyParamerers Insert Method=======================================
    private synchronized int insertCompanyParamerers(Company_Group company_Group) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;
        //n_comid,n_group_id,paracode
        String strSQL = "INSERT INTO com_para_group_mst VALUES(?,?,?)";

        try {

            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, company_Group.getN_comid());
            ps.setInt(2, company_Group.getN_group_id());
            ps.setString(3, company_Group.getParacode());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    //===================CompanyParamerers Update Method=======================================
    private synchronized int updateCompanyParamerers(Company_Group company_Group) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;
        //n_comid,n_group_id,paracode
        String strSQL = "UPDATE prev_mst SET "
                + "n_comid=?,"
                + "n_group_id=?,"
                + "paracode=? "
                + "WHERE "
                + "n_comid=? "
                + "AND "
                + "n_group_id=?";
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, company_Group.getN_comid());
            ps.setInt(2, company_Group.getN_group_id());
            ps.setString(3, company_Group.getParacode());

            ps.setInt(4, company_Group.getN_comid());
            ps.setInt(5, company_Group.getN_group_id());

            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public int saveCompanyParamerers(List<Company_Group> company_GroupList, int n_comid, int n_group_id) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;
        String strSQL = "DELETE FROM com_para_group_mst "
                + "WHERE n_comid=? "
                + "AND "
                + "n_group_id=?";
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, n_comid);
            ps.setInt(2, n_group_id);
            ps.executeUpdate();

            for (Company_Group m_Company_Group : company_GroupList) {
                if (m_Company_Group.getChkvalue().equalsIgnoreCase("checked")) {
                    insertCompanyParamerers(m_Company_Group);
                    result++;
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public synchronized int deleteCompany_GroupParameter(List<Company_Group> companyGroupParameterList) {
        int result = 0;
        Company_Group companyGropParameter = null;


        Connection conn = null;
        PreparedStatement ps = null;

        //String strSQL = "SELECT * FROM com_para_group_mst WHERE n_comid=? AND paracode=?";

        int updateCountResult = -1;
        try {
            conn = getJDBCConnection();
            conn.setAutoCommit(false);
            for (int i = 0; i < companyGroupParameterList.size(); i++) {
                setMsg("");
                companyGropParameter = companyGroupParameterList.get(i);

                ps = conn.prepareStatement("DELETE FROM com_para_group_mst WHERE n_comid=? AND n_group_id=?");
                ps.setInt(1, companyGropParameter.getN_comid());
                ps.setInt(2, companyGropParameter.getN_group_id());
                result = ps.executeUpdate();
                if (result > 0) {
                    updateCountResult++;
                }


            }

            conn.commit();
            conn.setAutoCommit(true);
            setMsg("Record Delete Successful");

        } catch (Exception e) {
            try {
                if (conn != null) {
                    conn.rollback();
                }
                conn.setAutoCommit(true);
            } catch (Exception e1) {
            }
            LOGGER.error(e.getMessage());
            setMsg("Can not be Delete");
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return updateCountResult;
    }

    private synchronized Company_Group getCompany_Group_New(ResultSet rs) {
        Company_Group company_Group = new Company_Group();
        try {
            company_Group.setN_comid(rs.getInt("t1.comid"));
            company_Group.setParacode(rs.getString("t2.paracode"));
            company_Group.setDesception(rs.getString("t2.description"));
            company_Group.setIsNew(true);


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return company_Group;
    }

    private synchronized Company_Group getCompany_Group_Modify(ResultSet rs) {
        Company_Group company_Group = new Company_Group();
        try {

            company_Group.setN_comid(rs.getInt("t1.n_comid"));
            company_Group.setN_group_id(rs.getInt("t1.n_group_id"));
            company_Group.setParacode(rs.getString("t2.paracode"));
            company_Group.setDesception(rs.getString("t2.description"));
            company_Group.setIsNew(false);


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return company_Group;
    }

    public synchronized List<Company_Group> getCompany_Group_New_List(int n_comid) {
        List<Company_Group> m_Company_Group = new LinkedList<Company_Group>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT t1.id,t1.comid,"
                + "t2.paraCode,"
                + "t2.description "
                + "from "
                + "comparalist_mst as t1,"
                + "paralist_mst as t2 "
                + "WHERE "
                + "t1.paraCode=t2.paraCode "
                + "AND "
                + "t1.comid=? "//=====>
                + "AND "
                + "(t2.paracode IN("
                + "SELECT "
                + "paracode "
                + "FROM "
                + "appparam_mst "
                + "WHERE "
                + "appparam_mst.paracode =t2.paracode  "
                + "AND "
                + "appparam_mst.tableName='User Parameter' "
                + "AND "
                + "appparam_mst.fieldname='paravalue'))";
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ps.setInt(1, n_comid);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_Company_Group.add(getCompany_Group_New(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_Company_Group;
    }

    public synchronized List<Company_Group> getCompany_Group_Modify_List(int n_comid, int n_groupid) {
        List<Company_Group> m_Company_Group = new LinkedList<Company_Group>();
        Connection conn = null;
        PreparedStatement ps = null;

//         String strSql = "SELECT t1.id,t1.comid,"
//                + "t2.paraCode,"
//                + "t2.description "
//                + "from "
//                + "comparalist_mst as t1,"
//                + "paralist_mst as t2 "
//                + "WHERE "
//                + "t1.paraCode=t2.paraCode "
//                + "AND "
//                + "t1.comid=? "//=======
//                + "AND "
//                + "(t2.paracode NOT IN("
//                + "SELECT "
//                + "paracode "
//                + "FROM "
//                + "appparam_mst "
//                + "WHERE "
//                + "appparam_mst.paracode =t2.paracode) "
//                + "OR "
//                + "t2.paracode IN("
//                + "SELECT "
//                + "paracode "
//                + "FROM "
//                + "appparam_mst "
//                + "WHERE "
//                + "appparam_mst.paracode =t2.paracode  "
//                + "AND "
//                + "appparam_mst.tableName='User Parameter' "
//                + "AND "
//                + "appparam_mst.fieldname='paravalue'))";

        String strSql = "SELECT t1.id,t1.comid,"
                + "t2.paraCode,"
                + "t2.description "
                + "from "
                + "comparalist_mst as t1,"
                + "paralist_mst as t2 "
                + "WHERE "
                + "t1.paraCode=t2.paraCode "
                + "AND "
                + "t1.comid=? "//=======
                + "AND "
                + "(t2.paracode IN("
                + "SELECT "
                + "paracode "
                + "FROM "
                + "appparam_mst "
                + "WHERE "
                + "appparam_mst.paracode =t2.paracode  "
                + "AND "
                + "appparam_mst.tableName='User Parameter' "
                + "AND "
                + "appparam_mst.fieldname='paravalue'))";
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ps.setInt(1, n_comid);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                strSql = "SELECT "
                        + "t1.n_comid,"
                        + "t1.n_group_id,"
                        + "t2.paraCode,"
                        + "t2.description "
                        + "FROM com_para_group_mst as t1,"
                        + "paralist_mst as t2 "
                        + "WHERE "
                        + "t1.paraCode=t2.paraCode "
                        + "AND "
                        + "t1.n_comid=? "
                        + "AND "
                        + "t1.n_group_id=? "
                        + "AND "
                        + "t1.paracode=?";
                ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
                ps.setInt(1, rs.getInt("t1.comid"));
                ps.setInt(2, n_groupid);
                ps.setString(3, rs.getString("t2.paraCode"));
                ResultSet rs1 = ps.executeQuery();
                String s = ps.toString();
                if (rs1.next()) {
                    m_Company_Group.add(getCompany_Group_Modify(rs1));
                } else {
                    m_Company_Group.add(getCompany_Group_New(rs));
                }

                rs1.close();

            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_Company_Group;
    }

    private synchronized Company_Group getCompany_Group_For_ViewList(ResultSet rs) {
        Company_Group company_Group = new Company_Group();
        try {
            company_Group.setN_group_id(rs.getInt("t1.n_group_id"));
            company_Group.setN_comid(rs.getInt("t1.n_comid"));
            company_Group.setV_groupName(rs.getString("t2.v_group_name"));
            company_Group.setV_comCode(rs.getString("t3.v_comcode"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return company_Group;
    }

    public synchronized List<Company_Group> getCompany_GroupViewList(User input_User, String searchKey, String limitSearchKey) {
        List<Company_Group> m_Company_Group = new LinkedList<Company_Group>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "";

        searchKey = searchKey.trim();
        if (!searchKey.equalsIgnoreCase("")) {
            searchKey = " AND " + searchKey;
        }

        if (input_User.getN_accessusrtype() == 1 || input_User.getN_accessusrtype() == 2) //super Admin accessess Level
        {
            strSql = "SELECT "
                    + "t1.n_group_id,"
                    + "t1.n_comid,"
                    + "t2.v_group_name,"
                    + "t3.v_comcode "
                    + "FROM "
                    + "com_para_group_mst as t1,"
                    + "usr_group_mst as t2,"
                    + "company_mst as t3 "
                    + "WHERE "
                    + "t3.n_comid=t1.n_comid "
                    + "AND "
                    + "t1.n_group_id=t2.n_group_id "
                    + searchKey + " "
                    + "GROUP BY t1.n_comid,t1.n_group_id " + limitSearchKey;
        } else if (input_User.getN_accessusrtype() == 3
                || input_User.getN_accessusrtype() == 4
                || input_User.getN_accessusrtype() == 5) //Agent admin,Internal Admin AND External Admin accessess Level
        {
            strSql = "SELECT "
                    + "t1.n_group_id,"
                    + "t1.n_comid,"
                    + "t2.v_group_name,"
                    + "t3.v_comcode "
                    + "FROM "
                    + "com_para_group_mst as t1,"
                    + "usr_group_mst as t2,"
                    + "company_mst as t3 "
                    + "WHERE "
                    + "t3.n_comid=" + input_User.getN_comid() + " "
                    + "AND "
                    + "t1.n_group_id=t2.n_group_id "
                    + searchKey + " "
                    + "GROUP BY t1.n_group_id " + limitSearchKey;
        }
        // SystemMessage.getInstance().writeMessage("DEBUG :-->getRolePrivilegeViewList--> " + strSql);
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_Company_Group.add(getCompany_Group_For_ViewList(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_Company_Group;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

}
