/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.dbconfig.ConnectionPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.Hashtable;
public class LoginManager implements Serializable {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginManager.class);

    private static LoginManager admin;
    private ConnectionPool cp = null;
    private String message = "";
    private Hashtable users = null;
    private UserPasswordPara m_userPasswordPara = null;

    public LoginManager() {
        try {
            cp = ConnectionPool.getInstance();
            users = new Hashtable();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    /**
     * This method creates a new LoginManager object <br/> Creates only one
     * instance of the object
     *
     * @return LoginManager instance as <tt>LoginManager</tt> object
     * @throws java.lang.Exception
     */
    public static synchronized LoginManager getInstance() throws Exception {
        if (admin == null) {
            admin = new LoginManager();
        }
        return admin;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }


    public synchronized boolean hasSessionExpired(UserDto user) {
        String comCode = "HNB";
        String usrId = user.getUserId().trim().toUpperCase();
        String sessionID = user.getSessionId();


        String usrIDValue = comCode + "" + usrId;
        String tempSessionID = (String) users.get(usrIDValue);
        if (sessionID.equalsIgnoreCase(tempSessionID)) {
            return false;
        } else {
            return true;
        }
    }


}
