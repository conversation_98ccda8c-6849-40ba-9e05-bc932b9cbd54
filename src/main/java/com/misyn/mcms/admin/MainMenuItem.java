/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import java.io.Serializable;
public class MainMenuItem implements Serializable {

    private int n_prgid = 1;
    private int n_mnuid = 0;
    private String v_mnuname = "";
    private String v_apptype = "0";
    private int n_itm_seq_no = 0;
    private String v_inpstat = "";
    private String v_inpuser = "";
    private String d_inptime = "1900-01-01 12:00:00";
    private String v_auth1stat = "";
    private String v_auth1user = "";
    private String d_auth1time = "1900-01-01 12:00:00";
    private String v_auth2stat = "";
    private String v_auth2user = "";
    private String d_auth2time = "1900-01-01 12:00:00";

    public String getD_auth1time() {
        return d_auth1time;
    }

    public void setD_auth1time(String d_auth1time) {
        this.d_auth1time = d_auth1time;
    }

    public String getD_auth2time() {
        return d_auth2time;
    }

    public void setD_auth2time(String d_auth2time) {
        this.d_auth2time = d_auth2time;
    }

    public String getD_inptime() {
        return d_inptime;
    }

    public void setD_inptime(String d_inptime) {
        this.d_inptime = d_inptime;
    }

    public int getN_itm_seq_no() {
        return n_itm_seq_no;
    }

    public void setN_itm_seq_no(int n_itm_seq_no) {
        this.n_itm_seq_no = n_itm_seq_no;
    }

    public int getN_mnuid() {
        return n_mnuid;
    }

    public void setN_mnuid(int n_mnuid) {
        this.n_mnuid = n_mnuid;
    }

    public int getN_prgid() {
        return n_prgid;
    }

    public void setN_prgid(int n_prgid) {
        this.n_prgid = n_prgid;
    }

    public String getV_apptype() {
        return v_apptype;
    }

    public void setV_apptype(String v_apptype) {
        this.v_apptype = v_apptype;
    }

    public String getV_auth1stat() {
        return v_auth1stat;
    }

    public void setV_auth1stat(String v_auth1stat) {
        this.v_auth1stat = v_auth1stat;
    }

    public String getV_auth1user() {
        return v_auth1user;
    }

    public void setV_auth1user(String v_auth1user) {
        this.v_auth1user = v_auth1user;
    }

    public String getV_auth2stat() {
        return v_auth2stat;
    }

    public void setV_auth2stat(String v_auth2stat) {
        this.v_auth2stat = v_auth2stat;
    }

    public String getV_auth2user() {
        return v_auth2user;
    }

    public void setV_auth2user(String v_auth2user) {
        this.v_auth2user = v_auth2user;
    }

    public String getV_inpstat() {
        return v_inpstat;
    }

    public void setV_inpstat(String v_inpstat) {
        this.v_inpstat = v_inpstat;
    }

    public String getV_inpuser() {
        return v_inpuser;
    }

    public void setV_inpuser(String v_inpuser) {
        this.v_inpuser = v_inpuser;
    }

    public String getV_mnuname() {
        return v_mnuname;
    }

    public void setV_mnuname(String v_mnuname) {
        this.v_mnuname = v_mnuname;
    }


}
