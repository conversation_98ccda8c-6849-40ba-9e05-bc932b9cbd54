/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import com.misyn.mcms.dbconfig.ConnectionPool;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.LinkedList;
import java.util.List;
public class MainMenuItemManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(MainMenuItemManager.class);

    private static DbRecordCommonFunction dbRecordCommonFunction = new DbRecordCommonFunction();
    private static MainMenuItemManager mainMenuItemManager = null;
    String msg = "";
    private ConnectionPool cp = null;

    /**
     * Default constructor
     */
    public MainMenuItemManager() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public static synchronized MainMenuItemManager getInstance() {
        if (mainMenuItemManager == null) {
            mainMenuItemManager = new MainMenuItemManager();
        }
        return mainMenuItemManager;
    }

    //===================MainMenuItem Insert Method=======================================
    private synchronized int insertMainMenuItem(MainMenuItem mainMenuItem) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;


        String strSQL = "INSERT INTO mnu_mst VALUES("
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?,?,?)";
        try {
            mainMenuItem.setN_mnuid(getNextID(mainMenuItem.getN_prgid()));
            mainMenuItem.setV_inpstat("I");
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, mainMenuItem.getN_prgid());
            ps.setInt(2, mainMenuItem.getN_mnuid());
            ps.setString(3, mainMenuItem.getV_mnuname());
            ps.setString(4, mainMenuItem.getV_apptype());
            ps.setInt(5, mainMenuItem.getN_itm_seq_no());

            ps.setString(6, mainMenuItem.getV_inpstat());
            ps.setString(7, mainMenuItem.getV_inpuser());
            ps.setString(8, Utility.sysDate("yyyy-MM-dd hh:mm:ss"));
            ps.setString(9, mainMenuItem.getV_auth1stat());
            ps.setString(10, mainMenuItem.getV_auth1user());
            ps.setString(11, mainMenuItem.getD_auth1time());
            ps.setString(12, mainMenuItem.getV_auth2stat());
            ps.setString(13, mainMenuItem.getV_auth2user());
            ps.setString(14, mainMenuItem.getD_auth2time());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    //===================MainMenuItem Update Method=======================================
    private synchronized int updateMainMenuItem(MainMenuItem mainMenuItem) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;

        //  `itm_mst`.`n_prgid`, `itm_mst`.`n_mnuid`, `itm_mst`.`n_itmid`, `itm_mst`.`v_itmname`,
        //`itm_mst`.`v_url`, `itm_mst`.`v_authtype`, `itm_mst`.`v_target`, `itm_mst`.`v_apptype`, `itm_mst`.`n_itm_seq_no`,
        //`itm_mst`.`v_inpstat`, `itm_mst`.`v_inpuser`, `itm_mst`.`d_inptime`, `itm_mst`.`v_auth1stat`, `itm_mst`.`v_auth1user`,
        //`itm_mst`.`d_auth1time`, `itm_mst`.`v_auth2stat`, `itm_mst`.`v_auth2user`, `itm_mst`.`d_auth2time`

        String strSQL = "UPDATE mnu_mst SET "
                + "n_prgid=?,"
                + "n_mnuid=?,"
                + "v_mnuname=?,"
                + "v_apptype=?,"
                + "n_itm_seq_no=?,"
                + "v_inpstat=?,"
                + "v_inpuser=?,"
                + "d_inptime=?,"
                + "v_auth1stat=?,"
                + "v_auth1user=?,"
                + "d_auth1time=?,"
                + "v_auth2stat=?,"
                + "v_auth2user=?,"
                + "d_auth2time=? "
                + "WHERE "
                + "n_prgid=? "
                + "AND "
                + "n_mnuid=? ";

        try {
            mainMenuItem.setV_inpstat("M");
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, mainMenuItem.getN_prgid());
            ps.setInt(2, mainMenuItem.getN_mnuid());
            ps.setString(3, mainMenuItem.getV_mnuname());
            ps.setString(4, mainMenuItem.getV_apptype());
            ps.setInt(5, mainMenuItem.getN_itm_seq_no());

            ps.setString(6, mainMenuItem.getV_inpstat());
            ps.setString(7, mainMenuItem.getV_inpuser());
            ps.setString(8, Utility.sysDate("yyyy-MM-dd hh:mm:ss"));
            ps.setString(9, mainMenuItem.getV_auth1stat());
            ps.setString(10, mainMenuItem.getV_auth1user());
            ps.setString(11, mainMenuItem.getD_auth1time());
            ps.setString(12, mainMenuItem.getV_auth2stat());
            ps.setString(13, mainMenuItem.getV_auth2user());
            ps.setString(14, mainMenuItem.getD_auth2time());

            ps.setInt(15, mainMenuItem.getN_prgid());
            ps.setInt(16, mainMenuItem.getN_mnuid());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public synchronized int saveMainMenuItem(MainMenuItem mainMenuItem) {
        int result = 0;
        try {
            if (!dbRecordCommonFunction.isRecExists("mnu_mst",
                    "n_prgid=" + mainMenuItem.getN_prgid() + " "
                            + "AND "
                            + "n_mnuid=" + mainMenuItem.getN_mnuid())) {
                if (!dbRecordCommonFunction.isIsErrorExsist()) {
                    result = insertMainMenuItem(mainMenuItem);
                }
            } else {
                result = updateMainMenuItem(mainMenuItem);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return result;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public synchronized int deleteMainMenuItem(List<MainMenuItem> mainMenuItemList) {
        int result = 0;
        MainMenuItem mainMenuItem = null;
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL = "DELETE FROM mnu_mst WHERE n_prgid=? AND n_mnuid=?";

        int updateCountResult = -1;
        try {
            conn = getJDBCConnection();
            conn.setAutoCommit(false);
            for (int i = 0; i < mainMenuItemList.size(); i++) {
                setMsg("");
                mainMenuItem = mainMenuItemList.get(i);
                ps = conn.prepareStatement(strSQL);
                ps.setInt(1, mainMenuItem.getN_prgid());
                ps.setInt(2, mainMenuItem.getN_mnuid());

                result = ps.executeUpdate();
                if (result > 0) {
                    ps = conn.prepareStatement("DELETE FROM itm_mst WHERE n_prgid=? AND n_mnuid=?");
                    ps.setInt(1, mainMenuItem.getN_prgid());
                    ps.setInt(2, mainMenuItem.getN_mnuid());
                    result = ps.executeUpdate();

                    ps = conn.prepareStatement("DELETE FROM userPrev_mst WHERE n_prgid=? AND n_mnuid=?");
                    ps.setInt(1, mainMenuItem.getN_prgid());
                    ps.setInt(2, mainMenuItem.getN_mnuid());
                    result = ps.executeUpdate();

                    ps = conn.prepareStatement("DELETE FROM prev_mst WHERE n_prgid=? AND n_mnuid=?");
                    ps.setInt(1, mainMenuItem.getN_prgid());
                    ps.setInt(2, mainMenuItem.getN_mnuid());
                    result = ps.executeUpdate();

                    ps = conn.prepareStatement("DELETE FROM comuserprev_mst WHERE n_prgid=? AND n_mnuid=?");
                    ps.setInt(1, mainMenuItem.getN_prgid());
                    ps.setInt(2, mainMenuItem.getN_mnuid());
                    result = ps.executeUpdate();

                    ps = conn.prepareStatement("DELETE FROM appparam_mst WHERE prgid=? AND mnuid=?");
                    ps.setInt(1, mainMenuItem.getN_prgid());
                    ps.setInt(2, mainMenuItem.getN_mnuid());
                    result = ps.executeUpdate();

                    if (result > 0) {
                        updateCountResult++;
                    }
                }

            }

            conn.commit();
            conn.setAutoCommit(true);
            setMsg("Record Delete Successful");

        } catch (Exception e) {
            try {
                if (conn != null) {
                    conn.rollback();
                }
                conn.setAutoCommit(true);
            } catch (Exception e1) {
            }
            LOGGER.error(e.getMessage());
            setMsg("Can not be Delete");
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return updateCountResult;
    }

    private synchronized MainMenuItem getMainMenuItem(ResultSet rs) {
        MainMenuItem mainMenuItem = new MainMenuItem();
        try {
            mainMenuItem.setN_prgid(rs.getInt("n_prgid"));
            mainMenuItem.setN_mnuid(rs.getInt("n_mnuid"));
            mainMenuItem.setV_mnuname(rs.getString("v_mnuname"));
            mainMenuItem.setV_apptype(rs.getString("v_apptype"));
            mainMenuItem.setN_itm_seq_no(rs.getInt("n_itm_seq_no"));

            mainMenuItem.setV_inpstat(rs.getString("v_inpstat"));
            mainMenuItem.setV_inpuser(rs.getString("v_inpuser"));
            mainMenuItem.setD_inptime(rs.getString("d_inptime"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return mainMenuItem;
    }

    public synchronized List<MainMenuItem> getMainMenuItemList(String searchKey) {
        List<MainMenuItem> m_MainMenuItem = new LinkedList<MainMenuItem>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT * FROM mnu_mst  "
                + searchKey;

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_MainMenuItem.add(getMainMenuItem(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_MainMenuItem;
    }

    private synchronized int getNextID(int n_prgid) {
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT MAX(n_mnuid) as txnID from mnu_mst "
                + "WHERE n_prgid=? ";

        int maxid = 0;
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql);
            ps.setInt(1, n_prgid);

            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                maxid = rs.getInt("txnID");
            }
            maxid++;
            rs.close();

        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return maxid;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}
