/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import com.misyn.mcms.dbconfig.ConnectionPool;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.LinkedList;
import java.util.List;
public class ParameterManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(ParameterManager.class);
    private static DbRecordCommonFunction dbRecordCommonFunction = new DbRecordCommonFunction();
    private static ParameterManager parameterManager = null;
    String msg = "";
    private ConnectionPool cp = null;

    public ParameterManager() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public static synchronized ParameterManager getInstance() {
        if (parameterManager == null) {
            parameterManager = new ParameterManager();
        }
        return parameterManager;
    }

    //===================Parameter Insert Method=======================================
    private synchronized int insertParameter(Parameter parameter, User user) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;

        String strSQL = "INSERT INTO paralist_mst VALUES("
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?)";
        try {

            if (DbRecordCommonFunction.getInstance().isRecExists("paralist_mst", "paraCode='" + parameter.getParaCode().trim() + "'")) {
                parameter.setErrorMessage("Parameter Code " + parameter.getParaCode() + " is already exist");
                return result;
            }


            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);

            ps.setString(1, parameter.getParaCode());
            ps.setString(2, parameter.getDescription());

            ps.setString(3, "I");
            ps.setString(4, parameter.getInpstat());
            ps.setString(5, user.getV_inpuser());
            ps.setString(6, Utility.sysDate("yyyy-MM-dd hh:mm:ss"));
            ps.setString(7, parameter.getAuth1stat());
            ps.setString(8, parameter.getAuth1user());
            ps.setString(9, parameter.getAuth1time());
            ps.setString(10, parameter.getAuth2stat());
            ps.setString(11, parameter.getAuth2user());
            ps.setString(12, parameter.getAuth2time());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    //===================Parameter Update Method=======================================
    private synchronized int updateParameter(Parameter parameter, User user) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;
//usrcode, paraCode, paravalue, status, inpstat, inpuser, inptime, auth1stat, auth1user, auth1time, auth2stat, auth2user, auth2time
        String strSQL = "UPDATE paralist_mst SET "
                + "paraCode=?,"
                + "description=?,"
                + "status=?,"
                + "inpstat=?,"
                + "inpuser=?,"
                + "inptime=?,"
                + "auth1stat=?,"
                + "auth1user=?,"
                + "auth1time=?,"
                + "auth2stat=?,"
                + "auth2user=?,"
                + "auth2time=? "
                + "WHERE "
                + "paraCode=?";

        try {

            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setString(1, parameter.getParaCode());
            ps.setString(2, parameter.getDescription());

            ps.setString(3, "I");
            ps.setString(4, parameter.getInpstat());
            ps.setString(5, user.getV_inpuser());
            ps.setString(6, Utility.sysDate("yyyy-MM-dd hh:mm:ss"));
            ps.setString(7, parameter.getAuth1stat());
            ps.setString(8, parameter.getAuth1user());
            ps.setString(9, parameter.getAuth1time());
            ps.setString(10, parameter.getAuth2stat());
            ps.setString(11, parameter.getAuth2user());
            ps.setString(12, parameter.getAuth2time());

            ps.setString(13, parameter.getParaCode());

            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public synchronized int saveParameter(Parameter parameter, User user) {
        int result = 0;
        try {
            if (!dbRecordCommonFunction.isRecExists("paralist_mst",
                    "paraCode='" + parameter.getParaCode().trim() + "'")) {
                if (!dbRecordCommonFunction.isIsErrorExsist()) {
                    result = insertParameter(parameter, user);
                }
            } else {
                result = updateParameter(parameter, user);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return result;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public synchronized int deleteParameter(List<Parameter> parameterList) {
        int result = 0;
        Parameter parameter = null;
        String paracode = "";

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL = "SELECT * FROM comparalist_mst WHERE paracode=?";

        int updateCountResult = -1;
        try {
            conn = getJDBCConnection();
            conn.setAutoCommit(false);
            for (int i = 0; i < parameterList.size(); i++) {
                setMsg("");
                parameter = parameterList.get(i);
                paracode = parameter.getParaCode();
                ps = conn.prepareStatement(strSQL);
                ps.setString(1, paracode);
                rs = ps.executeQuery();
                if (rs.next()) {
                    setMsg("Can not delete " + paracode + ": The parameter code is in use by the following table -> comparalist_mst");
                    try {
                        if (conn != null) {
                            conn.rollback();
                        }
                        conn.setAutoCommit(true);
                    } catch (Exception e1) {
                    }
                    return result;
                }
                ps = conn.prepareStatement("DELETE FROM paralist_mst WHERE paracode=?");
                ps.setString(1, paracode);
                result = ps.executeUpdate();
                if (result > 0) {
                    updateCountResult++;
                }
                rs.close();

            }

            conn.commit();
            conn.setAutoCommit(true);
            setMsg("Record Delete Successful");

        } catch (Exception e) {
            try {
                if (conn != null) {
                    conn.rollback();
                }
                conn.setAutoCommit(true);
            } catch (Exception e1) {
            }
            LOGGER.error(e.getMessage());
            setMsg("Can not be Delete");
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return updateCountResult;
    }

    private synchronized Parameter getParameter(ResultSet rs) {
        Parameter parameter = new Parameter();
        try {
            parameter.setParaCode(rs.getString("paraCode"));
            parameter.setDescription(rs.getString("description"));
            parameter.setInpuser(rs.getString("inpuser"));
            parameter.setInptime(rs.getString("inptime"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return parameter;
    }

    public synchronized List<Parameter> getParameterList(String searchKey) {
        List<Parameter> m_Parameter = new LinkedList<Parameter>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT * FROM paralist_mst "
                + searchKey;

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_Parameter.add(getParameter(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_Parameter;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}
