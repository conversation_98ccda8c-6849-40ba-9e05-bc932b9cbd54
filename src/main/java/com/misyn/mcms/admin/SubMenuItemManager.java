/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import com.misyn.mcms.dbconfig.ConnectionPool;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.LinkedList;
import java.util.List;
public class SubMenuItemManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(SubMenuItemManager.class);


    private static DbRecordCommonFunction dbRecordCommonFunction = new DbRecordCommonFunction();
    private static SubMenuItemManager subMenuItemManager = null;
    String msg = "";
    private ConnectionPool cp = null;

    public SubMenuItemManager() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public static synchronized SubMenuItemManager getInstance() {
        if (subMenuItemManager == null) {
            subMenuItemManager = new SubMenuItemManager();
        }
        return subMenuItemManager;
    }

    //===================SubMenuItem Insert Method=======================================
    private synchronized int insertSubMenuItem(SubMenuItem subMenuItem) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;

        //  `itm_mst`.`n_prgid`, `itm_mst`.`n_mnuid`, `itm_mst`.`n_itmid`, `itm_mst`.`v_itmname`,
        //`itm_mst`.`v_url`, `itm_mst`.`v_authtype`, `itm_mst`.`v_target`, `itm_mst`.`v_apptype`, `itm_mst`.`n_itm_seq_no`,
        //`itm_mst`.`v_inpstat`, `itm_mst`.`v_inpuser`, `itm_mst`.`d_inptime`, `itm_mst`.`v_auth1stat`, `itm_mst`.`v_auth1user`, `itm_mst`.`d_auth1time`, `itm_mst`.`v_auth2stat`, `itm_mst`.`v_auth2user`, `itm_mst`.`d_auth2time`

        String strSQL = "INSERT INTO itm_mst VALUES("
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?,?,?,?,?,?,?)";
        try {
            subMenuItem.setN_itmid(getNextID(subMenuItem.getN_prgid(), subMenuItem.getN_mnuid()));
            subMenuItem.setV_inpstat("I");
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, subMenuItem.getN_prgid());
            ps.setInt(2, subMenuItem.getN_mnuid());
            ps.setInt(3, subMenuItem.getN_itmid());
            ps.setString(4, subMenuItem.getV_itmname());
            ps.setString(5, subMenuItem.getV_url());
            ps.setString(6, subMenuItem.getV_authtype());
            ps.setString(7, subMenuItem.getV_target());
            ps.setString(8, subMenuItem.getV_apptype());
            ps.setInt(9, subMenuItem.getN_itm_seq_no());
            ps.setString(10, subMenuItem.getV_inpstat());
            ps.setString(11, subMenuItem.getV_inpuser());
            ps.setString(12, Utility.sysDate("yyyy-MM-dd hh:mm:ss"));
            ps.setString(13, subMenuItem.getV_auth1stat());
            ps.setString(14, subMenuItem.getV_auth1user());
            ps.setString(15, subMenuItem.getD_auth1time());
            ps.setString(16, subMenuItem.getV_auth2stat());
            ps.setString(17, subMenuItem.getV_auth2user());
            ps.setString(18, subMenuItem.getD_auth2time());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    //===================SubMenuItem Update Method=======================================
    private synchronized int updateSubMenuItem(SubMenuItem subMenuItem) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;

        //  `itm_mst`.`n_prgid`, `itm_mst`.`n_mnuid`, `itm_mst`.`n_itmid`, `itm_mst`.`v_itmname`,
        //`itm_mst`.`v_url`, `itm_mst`.`v_authtype`, `itm_mst`.`v_target`, `itm_mst`.`v_apptype`, `itm_mst`.`n_itm_seq_no`,
        //`itm_mst`.`v_inpstat`, `itm_mst`.`v_inpuser`, `itm_mst`.`d_inptime`, `itm_mst`.`v_auth1stat`, `itm_mst`.`v_auth1user`,
        //`itm_mst`.`d_auth1time`, `itm_mst`.`v_auth2stat`, `itm_mst`.`v_auth2user`, `itm_mst`.`d_auth2time`

        String strSQL = "UPDATE itm_mst SET "
                + "n_prgid=?,"
                + "n_mnuid=?,"
                + "n_itmid=?,"
                + "v_itmname=?,"
                + "v_url=?,"
                + "v_authtype=?,"
                + "v_target=?,"
                + "v_apptype=?,"
                + "n_itm_seq_no=?,"
                + "v_inpstat=?,"
                + "v_inpuser=?,"
                + "d_inptime=?,"
                + "v_auth1stat=?,"
                + "v_auth1user=?,"
                + "d_auth1time=?,"
                + "v_auth2stat=?,"
                + "v_auth2user=?,"
                + "d_auth2time=? "
                + "WHERE "
                + "n_prgid=? "
                + "AND "
                + "n_mnuid=? "
                + "AND "
                + "n_itmid=?";
        try {
            subMenuItem.setV_inpstat("M");
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, subMenuItem.getN_prgid());
            ps.setInt(2, subMenuItem.getN_mnuid());
            ps.setInt(3, subMenuItem.getN_itmid());
            ps.setString(4, subMenuItem.getV_itmname());
            ps.setString(5, subMenuItem.getV_url());
            ps.setString(6, subMenuItem.getV_authtype());
            ps.setString(7, subMenuItem.getV_target());
            ps.setString(8, subMenuItem.getV_apptype());
            ps.setInt(9, subMenuItem.getN_itm_seq_no());
            ps.setString(10, subMenuItem.getV_inpstat());
            ps.setString(11, subMenuItem.getV_inpuser());
            ps.setString(12, Utility.sysDate("yyyy-MM-dd hh:mm:ss"));
            ps.setString(13, subMenuItem.getV_auth1stat());
            ps.setString(14, subMenuItem.getV_auth1user());
            ps.setString(15, subMenuItem.getD_auth1time());
            ps.setString(16, subMenuItem.getV_auth2stat());
            ps.setString(17, subMenuItem.getV_auth2user());
            ps.setString(18, subMenuItem.getD_auth2time());

            ps.setInt(19, subMenuItem.getN_prgid());
            ps.setInt(20, subMenuItem.getN_mnuid());
            ps.setInt(21, subMenuItem.getN_itmid());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public synchronized int saveSubMenuItem(SubMenuItem subMenuItem) {
        int result = 0;
        try {
            if (!dbRecordCommonFunction.isRecExists("itm_mst",
                    "n_prgid=" + subMenuItem.getN_prgid() + " "
                            + "AND "
                            + "n_mnuid=" + subMenuItem.getN_mnuid() + " "
                            + "AND "
                            + "n_itmid=" + subMenuItem.getN_itmid())) {
                if (!dbRecordCommonFunction.isIsErrorExsist()) {
                    result = insertSubMenuItem(subMenuItem);
                }
            } else {
                result = updateSubMenuItem(subMenuItem);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return result;
    }

    //===================SubMenuItem Insert Method=======================================
    private synchronized int insertApplicationParameter(ApplicationParameter applicationParameter, SubMenuItem subMenuItem) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;


        String strSQL = "INSERT INTO appparam_mst VALUES("
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?,?,?,?,?,?,?)";
        try {
            applicationParameter.setPrgid(subMenuItem.getN_prgid());
            applicationParameter.setMnuid(subMenuItem.getN_mnuid());
            applicationParameter.setItmid(subMenuItem.getN_itmid());

            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, applicationParameter.getPrgid());
            ps.setInt(2, applicationParameter.getMnuid());
            ps.setInt(3, applicationParameter.getItmid());
            ps.setString(4, applicationParameter.getParaCode());
            ps.setString(5, applicationParameter.getSelType());
            ps.setString(6, applicationParameter.getTableName());
            ps.setString(7, applicationParameter.getFieldname());
            ps.setString(8, applicationParameter.getValue());
            ps.setString(9, applicationParameter.getStatus());
            ps.setString(10, applicationParameter.getInpstat());
            ps.setString(11, applicationParameter.getInpuser());
            ps.setString(12, applicationParameter.getInptime());
            ps.setString(13, applicationParameter.getAuth1stat());
            ps.setString(14, applicationParameter.getAuth1user());
            ps.setString(15, applicationParameter.getAuth1time());
            ps.setString(16, applicationParameter.getAuth2stat());
            ps.setString(17, applicationParameter.getAuth2user());
            ps.setString(18, applicationParameter.getAuth2time());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public synchronized int saveApplicationParameter(List<ApplicationParameter> applicationParametersList, SubMenuItem subMenuItem) {
        int result = 0;
        int resultUpdate = 0;
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "DELETE FROM appparam_mst WHERE prgid=? AND mnuid=? AND itmid=?";
        try {
            result = saveSubMenuItem(subMenuItem);
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ps.setInt(1, subMenuItem.getN_prgid());
            ps.setInt(2, subMenuItem.getN_mnuid());
            ps.setInt(3, subMenuItem.getN_itmid());

            if (result > 0) {
                ps.executeUpdate();
                for (ApplicationParameter applicationParameter : applicationParametersList) {
                    if (!applicationParameter.getSelType().equalsIgnoreCase("Please select one")) {
                        resultUpdate = insertApplicationParameter(applicationParameter, subMenuItem);
                    }
                }

            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public synchronized int deleteAubMenuItem(List<SubMenuItem> subMenuItemList) {
        int result = 0;
        SubMenuItem subMenuItem = null;
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL = "DELETE FROM itm_mst WHERE n_prgid=? AND n_mnuid=? AND n_itmid=?";

        int updateCountResult = -1;
        try {
            conn = getJDBCConnection();
            conn.setAutoCommit(false);
            for (int i = 0; i < subMenuItemList.size(); i++) {
                setMsg("");
                subMenuItem = subMenuItemList.get(i);
                ps = conn.prepareStatement(strSQL);
                ps.setInt(1, subMenuItem.getN_prgid());
                ps.setInt(2, subMenuItem.getN_mnuid());
                ps.setInt(3, subMenuItem.getN_itmid());

                result = ps.executeUpdate();
                if (result > 0) {
                    ps = conn.prepareStatement("DELETE FROM userPrev_mst WHERE n_prgid=? AND n_mnuid=? AND n_itmid=?");
                    ps.setInt(1, subMenuItem.getN_prgid());
                    ps.setInt(2, subMenuItem.getN_mnuid());
                    ps.setInt(3, subMenuItem.getN_itmid());
                    result = ps.executeUpdate();

                    ps = conn.prepareStatement("DELETE FROM prev_mst WHERE n_prgid=? AND n_mnuid=? AND n_itmid=?");
                    ps.setInt(1, subMenuItem.getN_prgid());
                    ps.setInt(2, subMenuItem.getN_mnuid());
                    ps.setInt(3, subMenuItem.getN_itmid());
                    result = ps.executeUpdate();

                    ps = conn.prepareStatement("DELETE FROM comuserprev_mst WHERE n_prgid=? AND n_mnuid=? AND n_itmid=?");
                    ps.setInt(1, subMenuItem.getN_prgid());
                    ps.setInt(2, subMenuItem.getN_mnuid());
                    ps.setInt(3, subMenuItem.getN_itmid());
                    result = ps.executeUpdate();

                    ps = conn.prepareStatement("DELETE FROM appparam_mst WHERE prgid=? AND mnuid=? AND itmid=?");
                    ps.setInt(1, subMenuItem.getN_prgid());
                    ps.setInt(2, subMenuItem.getN_mnuid());
                    ps.setInt(3, subMenuItem.getN_itmid());
                    result = ps.executeUpdate();


                    if (result > 0) {
                        updateCountResult++;
                    }
                }

            }

            conn.commit();
            conn.setAutoCommit(true);
            setMsg("Record Delete Successful");

        } catch (Exception e) {
            try {
                if (conn != null) {
                    conn.rollback();
                }
                conn.setAutoCommit(true);
            } catch (Exception e1) {
            }
            LOGGER.error(e.getMessage());
            setMsg("Can not be Delete");
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return updateCountResult;
    }

    private synchronized SubMenuItem getSubMenuItem(ResultSet rs) {
        SubMenuItem subMenuItem = new SubMenuItem();
        try {
            subMenuItem.setN_prgid(rs.getInt("n_prgid"));
            subMenuItem.setN_mnuid(rs.getInt("n_mnuid"));
            subMenuItem.setN_itmid(rs.getInt("n_itmid"));
            subMenuItem.setV_itmname(rs.getString("v_itmname"));
            subMenuItem.setV_url(rs.getString("v_url"));
            subMenuItem.setV_authtype(rs.getString("v_authtype"));

            subMenuItem.setV_target(rs.getString("v_target"));
            subMenuItem.setV_apptype(rs.getString("v_apptype"));
            subMenuItem.setN_itm_seq_no(rs.getInt("n_itm_seq_no"));

            subMenuItem.setV_inpstat(rs.getString("v_inpstat"));
            subMenuItem.setV_inpuser(rs.getString("v_inpuser"));
            subMenuItem.setD_inptime(rs.getString("d_inptime"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return subMenuItem;
    }

    public synchronized List<SubMenuItem> getSubMenuItemList(String searchKey) {
        List<SubMenuItem> m_SubMenuItem = new LinkedList<SubMenuItem>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT * FROM itm_mst WHERE "
                + searchKey;


        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_SubMenuItem.add(getSubMenuItem(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_SubMenuItem;
    }

    private synchronized ApplicationParameter getApplicationParameter_New(ResultSet rs) {
        ApplicationParameter applicationParameter = new ApplicationParameter();
        try {
            applicationParameter.setParaCode(rs.getString("paraCode"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return applicationParameter;
    }

    private synchronized ApplicationParameter getApplicationParameter_Modify(ResultSet rs1, ResultSet rs) {
        ApplicationParameter applicationParameter = new ApplicationParameter();
        try {
            applicationParameter.setPrgid(rs1.getInt("prgid"));
            applicationParameter.setMnuid(rs1.getInt("mnuid"));
            applicationParameter.setItmid(rs1.getInt("itmid"));
            applicationParameter.setParaCode(rs1.getString("paracode"));
            applicationParameter.setDescription(rs.getString("description"));
            applicationParameter.setSelType(rs1.getString("selType"));
            applicationParameter.setTableName(rs1.getString("tableName"));
            applicationParameter.setFieldname(rs1.getString("fieldname"));
            applicationParameter.setValue(rs1.getString("value"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return applicationParameter;
    }

    public synchronized List<ApplicationParameter> getApplicationParameterList_New(String searchKey) {
        List<ApplicationParameter> m_ApplicationParameter = new LinkedList<ApplicationParameter>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT * FROM paralist_mst ORDER BY paracode";

        if (!searchKey.equalsIgnoreCase("")) {
            strSql = "SELECT * FROM paralist_mst ORDER BY paracode WHERE "
                    + searchKey;
        }


        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_ApplicationParameter.add(getApplicationParameter_New(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_ApplicationParameter;
    }

    public synchronized List<ApplicationParameter> getApplicationParameterList_Modify(int prgid, int mnuid, int itmid, String searchKey) {
        List<ApplicationParameter> m_ApplicationParameter = new LinkedList<ApplicationParameter>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT * FROM paralist_mst ORDER BY paracode";

        if (!searchKey.equalsIgnoreCase("")) {
            strSql = "SELECT * FROM paralist_mst ORDER BY paracode WHERE "
                    + searchKey;
        }


        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();
            ResultSet rs1 = null;
            while (rs.next()) {
                strSql = "SELECT * FROM appparam_mst "
                        + "WHERE "
                        + "prgid=? "
                        + "AND "
                        + "mnuid=? "
                        + "AND "
                        + "itmid=? "
                        + "AND "
                        + "paraCode=?";
                ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
                ps.setInt(1, prgid);
                ps.setInt(2, mnuid);
                ps.setInt(3, itmid);
                ps.setString(4, rs.getString("paraCode"));
                rs1 = ps.executeQuery();
                if (rs1.next()) {
                    m_ApplicationParameter.add(getApplicationParameter_Modify(rs1, rs));
                } else {
                    m_ApplicationParameter.add(getApplicationParameter_New(rs));
                }
                rs1.close();
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_ApplicationParameter;
    }

    private synchronized SubMenuItem getSubMenuItem_For_View(ResultSet rs) {
        SubMenuItem subMenuItem = new SubMenuItem();
        try {
            subMenuItem.setN_prgid(rs.getInt("t2.n_prgid"));
            subMenuItem.setN_mnuid(rs.getInt("t2.n_mnuid"));
            subMenuItem.setV_mnuname(rs.getString("t1.v_mnuname"));
            subMenuItem.setN_itmid(rs.getInt("t2.n_itmid"));
            subMenuItem.setV_itmname(rs.getString("t2.v_itmname"));
            subMenuItem.setV_url(rs.getString("t2.v_url"));
            subMenuItem.setV_authtype(rs.getString("t2.v_authtype"));

            subMenuItem.setV_target(rs.getString("t2.v_target"));
            subMenuItem.setV_apptype(rs.getString("t2.v_apptype"));
            subMenuItem.setN_itm_seq_no(rs.getInt("t2.n_itm_seq_no"));

            subMenuItem.setV_inpstat(rs.getString("t2.v_inpstat"));
            subMenuItem.setV_inpuser(rs.getString("t2.v_inpuser"));
            subMenuItem.setD_inptime(rs.getString("t2.d_inptime"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return subMenuItem;
    }

    public synchronized List<SubMenuItem> getSubMenuItemViewList(String searchKey) {
        List<SubMenuItem> m_SubMenuItem = new LinkedList<SubMenuItem>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT "
                + "t1.v_mnuname,"
                + "t2.* "
                + "FROM "
                + "mnu_mst as t1,"
                + "itm_mst as t2 "
                + "WHERE "
                + "t1.n_mnuid=t2.n_mnuid "
                + "AND "
                + "t1.n_prgid=t2.n_prgid "
                + searchKey;


        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_SubMenuItem.add(getSubMenuItem_For_View(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_SubMenuItem;
    }

    private synchronized int getNextID(int n_prgid, int n_mnuid) {
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT MAX(n_itmid) as txnID from itm_mst "
                + "WHERE n_prgid=? "
                + "AND "
                + "n_mnuid=?";
        int maxid = 0;
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql);
            ps.setInt(1, n_prgid);
            ps.setInt(2, n_mnuid);

            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                maxid = rs.getInt("txnID");
            }
            maxid++;
            rs.close();

        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return maxid;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}
