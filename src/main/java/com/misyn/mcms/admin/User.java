/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;

/**
 * Product: Intranet - UA Intranet & Common Auth. System
 * Copyright: Copyright, 2009-2010 (c)
 * Company: M.I. Synergy (Pvt) Ltd
 *
 * <AUTHOR>
 * @version 2.0
 */
@Deprecated
public class User implements Serializable {

    private int n_usrcode = 0;
    private int n_comid = 0;
    private String v_comCode = "";
    private String v_usrid = "";
    private String v_password = "";
    private String v_real_password = "";
    private String v_oldpassword = "";
    private String v_usrtypes = "";
    private String v_usrtype_desc = "";
    private int n_accessusrtype = 0;
    private String v_title = "";
    private String v_firstname = "";
    private String v_lastname = "";
    private String v_address1 = "";
    private String v_address2 = "";
    private String v_email = "";
    private String v_land_phone = "";
    private String v_mobile = "";
    private String v_fax = "";
    private String v_nic = "";
    private String v_emp_no = "";
    private String v_ass_name = "";
    private int n_brid = 0;
    private String d_activedate = "1900-01-01";
    private String d_expirydate = "1900-01-01";
    private String v_usrstatus = "P";
    private String v_oldusrstatus = "";
    private String d_lastlogindate = "1900-01-01";
    private String d_lastlogintime = "12:00:00";
    private int n_atmptno = 0;
    private String d_pwchgdate = "1900-01-01";
    private String d_pwprtdate = "1900-01-01";
    private String v_firstlogin = "";
    private String d_uidlockdate = "1900-01-01";
    private String d_uidlocktime = "12:00:00";
    private String v_anymodify = "N";
    private String v_group_ids = "";
    private String v_group_ids_desc = "";
    private String passwordHash = "";
    private int n_team_id = 0;
    private double n_liablity_limit = 0.0;
    private double n_payment_limit = 0.0;
    private double n_reserve_limit = 0.0;
    private double n_payment_auth_limit = 0.0;
    private String v_inpstat = "I";
    private String v_inpuser = "";
    private String d_inptime = "1900-01-01 12:00:00";
    private String v_auth1stat = "P";
    private String v_auth1user = "";
    private String d_auth1time = "1900-01-01 12:00:00";
    private String v_auth2stat = "P";
    private String v_auth2user = "";
    private String d_auth2time = "1900-01-01 12:00:00";
    private int n_prgid = 0;
    private String sessionID = "";
    private String ipAddress = "";
    private String errorMessage = "";
    private String v_district_code = "0";
    private String reportingTo = AppConstant.STRING_EMPTY;
    private String reportingToName = AppConstant.STRING_EMPTY;
    private String assessorType = AppConstant.STRING_EMPTY;
    private String branchCode = AppConstant.STRING_EMPTY;
    private String needToSendEmail = AppConstant.STRING_EMPTY;
    private Integer rteReserveLimitLevel = 0;

    private String rteLevel2 = AppConstant.STRING_EMPTY;
    private String rteLevel3 = AppConstant.STRING_EMPTY;
    private String rteLevel4 = AppConstant.STRING_EMPTY;

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public int getN_prgid() {
        return n_prgid;
    }

    public void setN_prgid(int n_prgid) {
        this.n_prgid = n_prgid;
    }

    public String getV_group_ids() {
        return v_group_ids;
    }

    public void setV_group_ids(String v_group_ids) {
        this.v_group_ids = v_group_ids;
    }

    public String getV_group_ids_desc() {
        return v_group_ids_desc;
    }

    public void setV_group_ids_desc(String v_group_ids_desc) {
        this.v_group_ids_desc = v_group_ids_desc;
    }

    public String getV_comCode() {
        return v_comCode;
    }

    public void setV_comCode(String v_comCode) {
        this.v_comCode = v_comCode;
    }

    public String getD_activedate() {
        return d_activedate;
    }

    public void setD_activedate(String d_activedate) {
        this.d_activedate = d_activedate;
    }

    public String getD_auth1time() {
        return d_auth1time;
    }

    public void setD_auth1time(String d_auth1time) {
        this.d_auth1time = d_auth1time;
    }

    public String getD_auth2time() {
        return d_auth2time;
    }

    public void setD_auth2time(String d_auth2time) {
        this.d_auth2time = d_auth2time;
    }

    public String getD_expirydate() {
        return d_expirydate;
    }

    public void setD_expirydate(String d_expirydate) {
        this.d_expirydate = d_expirydate;
    }

    public String getD_inptime() {
        return d_inptime;
    }

    public void setD_inptime(String d_inptime) {
        this.d_inptime = d_inptime;
    }

    public String getD_lastlogindate() {
        return d_lastlogindate;
    }

    public void setD_lastlogindate(String d_lastlogindate) {
        this.d_lastlogindate = d_lastlogindate;
    }

    public String getD_lastlogintime() {
        return d_lastlogintime;
    }

    public void setD_lastlogintime(String d_lastlogintime) {
        this.d_lastlogintime = d_lastlogintime;
    }

    public String getD_pwchgdate() {
        return d_pwchgdate;
    }

    public void setD_pwchgdate(String d_pwchgdate) {
        this.d_pwchgdate = d_pwchgdate;
    }

    public String getD_pwprtdate() {
        return d_pwprtdate;
    }

    public void setD_pwprtdate(String d_pwprtdate) {
        this.d_pwprtdate = d_pwprtdate;
    }

    public String getD_uidlockdate() {
        return d_uidlockdate;
    }

    public void setD_uidlockdate(String d_uidlockdate) {
        this.d_uidlockdate = d_uidlockdate;
    }

    public String getD_uidlocktime() {
        return d_uidlocktime;
    }

    public void setD_uidlocktime(String d_uidlocktime) {
        this.d_uidlocktime = d_uidlocktime;
    }

    public int getN_atmptno() {
        return n_atmptno;
    }

    public void setN_atmptno(int n_atmptno) {
        this.n_atmptno = n_atmptno;
    }

    public int getN_brid() {
        return n_brid;
    }

    public void setN_brid(int n_brid) {
        this.n_brid = n_brid;
    }

    public int getN_comid() {
        return n_comid;
    }

    public void setN_comid(int n_comid) {
        this.n_comid = n_comid;
    }

    public int getN_usrcode() {
        return n_usrcode;
    }

    public void setN_usrcode(int n_usrcode) {
        this.n_usrcode = n_usrcode;
    }

    public int getN_accessusrtype() {
        return n_accessusrtype;
    }

    public void setN_accessusrtype(int n_accessusrtype) {
        this.n_accessusrtype = n_accessusrtype;
    }

    public String getV_address1() {
        return v_address1;
    }

    public void setV_address1(String v_address1) {
        this.v_address1 = v_address1;
    }

    public String getV_address2() {
        return v_address2;
    }

    public void setV_address2(String v_address2) {
        this.v_address2 = v_address2;
    }

    public String getV_anymodify() {
        return v_anymodify;
    }

    public void setV_anymodify(String v_anymodify) {
        this.v_anymodify = v_anymodify;
    }

    public String getV_auth1stat() {
        return v_auth1stat;
    }

    public void setV_auth1stat(String v_auth1stat) {
        this.v_auth1stat = v_auth1stat;
    }

    public String getV_auth1user() {
        return v_auth1user;
    }

    public void setV_auth1user(String v_auth1user) {
        this.v_auth1user = v_auth1user;
    }

    public String getV_auth2stat() {
        return v_auth2stat;
    }

    public void setV_auth2stat(String v_auth2stat) {
        this.v_auth2stat = v_auth2stat;
    }

    public String getV_auth2user() {
        return v_auth2user;
    }

    public void setV_auth2user(String v_auth2user) {
        this.v_auth2user = v_auth2user;
    }

    public String getV_email() {
        return v_email;
    }

    public void setV_email(String v_email) {
        this.v_email = v_email;
    }

    public String getV_emp_no() {
        return v_emp_no;
    }

    public void setV_emp_no(String v_emp_no) {
        this.v_emp_no = v_emp_no;
    }

    public String getV_fax() {
        return v_fax;
    }

    public void setV_fax(String v_fax) {
        this.v_fax = v_fax;
    }

    public String getV_firstlogin() {
        return v_firstlogin;
    }

    public void setV_firstlogin(String v_firstlogin) {
        this.v_firstlogin = v_firstlogin;
    }

    public String getV_firstname() {
        return v_firstname;
    }

    public void setV_firstname(String v_firstname) {
        this.v_firstname = v_firstname;
    }

    public String getV_lastname() {
        return v_lastname;
    }

    public void setV_lastname(String v_lastname) {
        this.v_lastname = v_lastname;
    }

    public String getV_inpstat() {
        return v_inpstat;
    }

    public void setV_inpstat(String v_inpstat) {
        this.v_inpstat = v_inpstat;
    }

    public String getV_inpuser() {
        return v_inpuser;
    }

    public void setV_inpuser(String v_inpuser) {
        this.v_inpuser = v_inpuser;
    }

    public String getV_land_phone() {
        return v_land_phone;
    }

    public void setV_land_phone(String v_land_phone) {
        this.v_land_phone = v_land_phone;
    }

    public String getV_mobile() {
        return v_mobile;
    }

    public void setV_mobile(String v_mobile) {
        this.v_mobile = v_mobile;
    }

    public String getV_nic() {
        return v_nic;
    }

    public void setV_nic(String v_nic) {
        this.v_nic = v_nic;
    }

    public String getV_oldpassword() {
        return v_oldpassword;
    }

    public void setV_oldpassword(String v_oldpassword) {
        this.v_oldpassword = v_oldpassword;
    }

    public String getV_oldusrstatus() {
        return v_oldusrstatus;
    }

    public void setV_oldusrstatus(String v_oldusrstatus) {
        this.v_oldusrstatus = v_oldusrstatus;
    }

    public String getV_password() {
        return v_password;
    }

    public void setV_password(String v_password) {
        this.v_password = v_password;
    }

    public String getV_title() {
        return v_title;
    }

    public void setV_title(String v_title) {
        this.v_title = v_title;
    }

    public String getV_usrid() {
        return v_usrid;
    }

    public void setV_usrid(String v_usrid) {
        this.v_usrid = v_usrid;
    }

    public String getV_usrstatus() {
        return v_usrstatus;
    }

    public void setV_usrstatus(String v_usrstatus) {
        this.v_usrstatus = v_usrstatus;
    }

    public String getV_usrtypes() {
        return v_usrtypes;
    }

    public void setV_usrtypes(String v_usrtypes) {
        this.v_usrtypes = v_usrtypes;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getSessionID() {
        return sessionID;
    }

    public void setSessionID(String sessionID) {
        this.sessionID = sessionID;
    }

    public String getV_real_password() {
        return v_real_password;
    }

    public void setV_real_password(String v_real_password) {
        this.v_real_password = v_real_password;
    }

    public String getV_usrtype_desc() {
        return v_usrtype_desc;
    }

    public void setV_usrtype_desc(String v_usrtype_desc) {
        this.v_usrtype_desc = v_usrtype_desc;
    }


    public String getV_ass_name() {
        return v_ass_name;
    }

    public void setV_ass_name(String v_ass_name) {
        this.v_ass_name = v_ass_name;
    }

    public int getN_team_id() {
        return n_team_id;
    }

    public void setN_team_id(int n_team_id) {
        this.n_team_id = n_team_id;
    }


    public double getN_liablity_limit() {
        return n_liablity_limit;
    }

    public void setN_liablity_limit(double n_liablity_limit) {
        this.n_liablity_limit = n_liablity_limit;
    }

    public double getN_payment_limit() {
        return n_payment_limit;
    }

    public void setN_payment_limit(double n_payment_limit) {
        this.n_payment_limit = n_payment_limit;
    }

    public double getN_payment_auth_limit() {
        return n_payment_auth_limit;
    }

    public void setN_payment_auth_limit(double n_payment_auth_limit) {
        this.n_payment_auth_limit = n_payment_auth_limit;
    }

    public double getN_reserve_limit() {
        return n_reserve_limit;
    }

    public void setN_reserve_limit(double n_reserve_limit) {
        this.n_reserve_limit = n_reserve_limit;
    }

    public String getV_district_code() {
        return v_district_code;
    }

    public void setV_district_code(String v_district_code) {
        this.v_district_code = v_district_code;
    }

    public String getReportingTo() {
        return reportingTo;
    }

    public void setReportingTo(String reportingTo) {
        this.reportingTo = reportingTo;
    }

    public String getReportingToName() {
        return reportingToName;
    }

    public void setReportingToName(String reportingToName) {
        this.reportingToName = reportingToName;
    }

    public String getAssessorType() {
        return assessorType;
    }

    public void setAssessorType(String assessorType) {
        this.assessorType = assessorType;
    }

    public String getPasswordHash() {
        return passwordHash;
    }

    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getNeedToSendEmail() {
        return needToSendEmail;
    }

    public void setNeedToSendEmail(String needToSendEmail) {
        this.needToSendEmail = needToSendEmail;
    }

    public Integer getRteReserveLimitLevel() {
        return rteReserveLimitLevel;
    }

    public void setRteReserveLimitLevel(Integer rteReserveLimit) {
        this.rteReserveLimitLevel = rteReserveLimit;
    }

    public String getRteLevel2() {
        return rteLevel2;
    }

    public void setRteLevel2(String rteLevel2) {
        this.rteLevel2 = rteLevel2;
    }

    public String getRteLevel3() {
        return rteLevel3;
    }

    public void setRteLevel3(String rteLevel3) {
        this.rteLevel3 = rteLevel3;
    }

    public String getRteLevel4() {
        return rteLevel4;
    }

    public void setRteLevel4(String rteLevel4) {
        this.rteLevel4 = rteLevel4;
    }
}
