/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import com.misyn.mcms.dbconfig.ConnectionPool;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import com.misyn.mcms.log.AdminLog;
import com.misyn.mcms.log.UserLog;
import com.misyn.mcms.userProfile.UserProfileManager;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Cryptography;
import com.misyn.mcms.utility.Cryptography.EncryptionException;
import com.misyn.mcms.utility.ShaHashGenerator;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.LinkedList;
import java.util.List;

/**
 * Product: Intranet - UA Intranet & Common Auth. System Copyright: Copyright,
 * 2009-2010 (c) Company: M.I. Synergy (Pvt) Ltd
 *
 * <AUTHOR> Sepala
 * @version 2.0
 */
@Deprecated
public class UserManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserManager.class);

    private static final DbRecordCommonFunction dbRecordCommonFunction = new DbRecordCommonFunction();
    private static UserManager userManager = null;
    String msg = "";
    private ConnectionPool cp = null;

    /**
     * Default constructor
     */
    public UserManager() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    public static synchronized UserManager getInstance() {
        if (userManager == null) {
            userManager = new UserManager();
        }
        return userManager;
    }

    private synchronized User getUser(ResultSet rs) {
        User user = new User();
        try {
            user.setN_usrcode(rs.getInt("u.n_usrcode"));
            user.setN_comid(rs.getInt("u.n_comid"));
            user.setV_comCode(rs.getString("c.v_comcode"));
            user.setV_usrid(rs.getString("u.v_usrid").toLowerCase());
            user.setV_password(rs.getString("u.v_password"));
            user.setV_real_password(new Cryptography("DES").decrypt(rs.getString("u.v_password").trim()));
            user.setV_oldpassword(rs.getString("u.v_oldpassword"));
            user.setV_usrtypes(rs.getString("u.v_usrtypes"));
            user.setV_usrtype_desc(rs.getString("u.v_usrtype_desc"));
            user.setN_accessusrtype(rs.getInt("u.n_accessusrtype"));
            user.setV_title(rs.getString("u.v_title"));
            user.setV_firstname(rs.getString("u.v_firstname"));
            user.setV_lastname(rs.getString("u.v_lastname"));
            user.setV_address1(rs.getString("u.v_address1"));
            user.setV_address2(rs.getString("u.v_address2"));
            user.setV_email(rs.getString("u.v_email"));
            user.setV_land_phone(rs.getString("u.v_land_phone"));
            user.setV_mobile(rs.getString("u.v_mobile"));
            user.setV_fax(rs.getString("u.v_fax"));
            user.setV_nic(rs.getString("u.v_nic"));
            user.setV_emp_no(rs.getString("u.v_emp_no"));
            user.setN_brid(rs.getInt("u.n_brid"));
            user.setD_activedate(rs.getString("u.d_activedate"));
            user.setD_expirydate(rs.getString("u.d_expirydate"));
            user.setV_usrstatus(rs.getString("u.v_usrstatus"));
            user.setV_oldusrstatus(rs.getString("u.v_oldusrstatus"));
            user.setD_lastlogindate(rs.getString("u.d_lastlogindate"));
            user.setD_lastlogintime(rs.getString("u.d_lastlogintime"));
            user.setN_atmptno(rs.getInt("u.n_atmptno"));
            user.setD_pwchgdate(rs.getString("u.d_pwchgdate"));
            user.setD_pwprtdate(rs.getString("u.d_pwprtdate"));
            user.setV_firstlogin(rs.getString("u.v_firstlogin"));
            user.setD_uidlockdate(rs.getString("u.d_uidlockdate"));
            user.setD_uidlocktime(rs.getString("u.d_uidlocktime"));
            user.setV_anymodify(rs.getString("u.v_anymodify"));
            user.setV_group_ids(rs.getString("u.v_group_ids"));
            user.setV_group_ids_desc(rs.getString("u.v_group_ids_desc"));
            user.setPasswordHash(rs.getString("v_password_hash"));
            user.setReportingTo(rs.getString("u.V_REPORT_TO"));
            user.setN_team_id(rs.getInt("u.N_TEAM_ID"));
            user.setN_liablity_limit(rs.getDouble("u.N_LIABLITY_LIMIT"));
            user.setN_payment_limit(rs.getDouble("u.N_PAYMENT_LIMIT"));
            user.setN_reserve_limit(rs.getDouble("u.N_RESERVE_LIMIT"));
            user.setN_payment_auth_limit(rs.getDouble("u.N_PAYMENT_AUTH_LIMIT"));
            user.setV_inpstat(rs.getString("u.v_inpstat"));
            user.setV_inpstat(rs.getString("u.v_inpuser"));
            user.setD_inptime(rs.getString("u.d_inptime"));
            user.setV_auth1stat(rs.getString("u.v_auth1stat"));
            user.setV_auth1user(rs.getString("u.v_auth1user"));
            user.setD_auth1time(rs.getString("u.d_auth1time"));
            user.setV_auth2stat(rs.getString("u.v_auth2stat"));
            user.setV_auth2user(rs.getString("u.v_auth2user"));
            user.setD_auth2time(rs.getString("u.d_auth2time"));
            user.setAssessorType(rs.getString("assessor_type"));
            user.setBranchCode(null == rs.getString("branch_code") ? AppConstant.STRING_EMPTY : rs.getString("branch_code"));
            user.setNeedToSendEmail(null == rs.getString("need_to_send_email") ? AppConstant.NO : rs.getString("need_to_send_email"));
            user.setRteReserveLimitLevel(rs.getInt("n_auth_level"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return user;
    }

    private synchronized User getUser_For_View(ResultSet rs) {
        User user = new User();
        try {
            user.setN_usrcode(rs.getInt("u.n_usrcode"));
            user.setN_comid(rs.getInt("u.n_comid"));
            user.setV_comCode(rs.getString("c.v_comcode"));
            user.setV_usrid(rs.getString("u.v_usrid"));
            user.setV_usrtypes(rs.getString("u.v_usrtypes"));
            user.setV_usrtype_desc(rs.getString("u.v_usrtype_desc"));
            user.setV_group_ids_desc(rs.getString("u.v_group_ids_desc"));
            user.setV_firstname(rs.getString("u.v_firstname"));
            user.setV_lastname(rs.getString("u.v_lastname"));
            user.setV_email(rs.getString("u.v_email"));
            user.setV_usrstatus(rs.getString("u.v_usrstatus"));
            user.setPasswordHash(rs.getString("v_password_hash"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return user;
    }

    private int saveAllctionTable(User user) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;
        int allocationFunType = 1;
        String status = user.getV_usrstatus();

        String strSQL;
        try {
            if ("X".equals(user.getV_usrstatus())
                    || "L".equals(user.getV_usrstatus())) {
                status = "A";
            }
            conn = getJDBCConnection();
            if (dbRecordCommonFunction.isRecExists("claim_user_allocation", "V_ASSIGN_USER_ID='" + user.getV_usrid() + "'")) {
                strSQL = "UPDATE claim_user_allocation SET N_ACCESSUSRTYPE=?, V_STATUS=? WHERE V_ASSIGN_USER_ID=?";
                ps = conn.prepareStatement(strSQL);
                ps.setInt(1, user.getN_accessusrtype());
                ps.setString(2, status);
                ps.setString(3, user.getV_usrid());

                result = ps.executeUpdate();
            } else {
                if (user.getN_accessusrtype() == 40
                        || user.getN_accessusrtype() == 41
                        || user.getN_accessusrtype() == 43
                        || user.getN_accessusrtype() == 44
                        || user.getN_accessusrtype() == 45
                        || user.getN_accessusrtype() == 46
                        || user.getN_accessusrtype() == 47
                        || user.getN_accessusrtype() == 48
                        || user.getN_accessusrtype() == 27
                        || user.getN_accessusrtype() == 28
                        || user.getN_accessusrtype() == 60
                        || user.getN_accessusrtype() == 61
                        || user.getN_accessusrtype() == 63
                        || user.getN_accessusrtype() == 62
                        || user.getN_accessusrtype() == 42
                        || user.getN_accessusrtype() == 22
                        || user.getN_accessusrtype() == 23
                        || user.getN_accessusrtype() == 24
                        || user.getN_accessusrtype() == 103
                ) {

                    if (user.getN_accessusrtype() == 45) {
                        allocationFunType = 2;
                    }

                    strSQL = "INSERT INTO claim_user_allocation ("
                            + "V_ASSIGN_USER_ID,"
                            + "N_TOT_ASSIGN_REPORT,"
                            + "N_ASSIGN_INDEX,"
                            + "N_TOT_ASSIGN_REPORT_TODAY,"
                            + "D_LAST_ASSIGN_DATE,"
                            + "N_ACCESSUSRTYPE,"
                            + "N_ALLOCATION_FUN_TYPE,"
                            + "V_STATUS) "
                            + "VALUES"
                            + "(?,?,?,?,?,?,?,?)";

                    ps = conn.prepareStatement(strSQL);
                    ps.setString(1, user.getV_usrid());
                    ps.setInt(2, 0);
                    ps.setInt(3, 0);
                    ps.setInt(4, 0);
                    ps.setString(5, Utility.sysDate("yyyy-MM-dd"));
                    ps.setInt(6, user.getN_accessusrtype());
                    ps.setInt(7, allocationFunType);
                    ps.setString(8, status);
                    result = ps.executeUpdate();
                }
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    //===================User Insert Method=======================================
    private synchronized int insertUser(User user) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;

        String strSQL = "INSERT INTO usr_mst VALUES("
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?,?,?,?)";

        try {

            if (dbRecordCommonFunction.isRecExists("usr_mst", "v_usrid='" + user.getV_usrid().trim() + "' AND v_usrstatus<>'C' AND n_comid=" + user.getN_comid())) {
                user.setErrorMessage("This User ID " + user.getV_usrid() + " was already existed");
                return result;
            } else if (dbRecordCommonFunction.isRecExists("usr_mst", "V_emp_no='" + user.getV_emp_no().trim() + "'")) {
                if (user.getN_accessusrtype() == 20) {
                    user.setErrorMessage("This Assessor Code " + user.getV_emp_no() + " was already existed");
                    return result;
                }
            }
            String passwordHash = ShaHashGenerator.getSha256HashValue(user.getV_password());
            user.setPasswordHash(passwordHash);
            user.setN_usrcode(getNextID());

            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, user.getN_usrcode());
            ps.setInt(2, user.getN_comid());
            ps.setString(3, user.getV_usrid());
            ps.setString(4, new Cryptography("DES").encrypt(user.getV_password()));
            ps.setString(5, user.getV_oldpassword());
            ps.setString(6, user.getV_usrtypes());
            ps.setInt(7, user.getN_accessusrtype());
            ps.setString(8, user.getV_title());
            ps.setString(9, user.getV_firstname());
            ps.setString(10, user.getV_lastname());
            ps.setString(11, user.getV_address1());
            ps.setString(12, user.getV_address2());
            ps.setString(13, user.getV_email());
            ps.setString(14, user.getV_land_phone());
            ps.setString(15, user.getV_mobile());
            ps.setString(16, user.getV_fax());
            ps.setString(17, user.getV_nic());
            ps.setString(18, user.getV_emp_no());
            ps.setInt(19, user.getN_brid());
            ps.setString(20, user.getD_activedate());
            ps.setString(21, user.getD_expirydate());
            ps.setString(22, user.getV_usrstatus());
            ps.setString(23, user.getV_oldusrstatus());
            ps.setString(24, user.getD_lastlogindate());
            ps.setString(25, user.getD_lastlogintime());
            ps.setInt(26, user.getN_atmptno());
            ps.setString(27, user.getD_pwchgdate());
            ps.setString(28, user.getD_pwprtdate());
            ps.setString(29, user.getV_firstlogin());
            ps.setString(30, user.getD_uidlockdate());
            ps.setString(31, user.getD_uidlocktime());
            ps.setString(32, user.getV_anymodify());
            ps.setString(33, user.getV_group_ids());
            ps.setString(34, user.getV_group_ids_desc());
            // ps.setString(34, getUserTypesDesc(user.getV_usrtypes(), conn));
            ps.setString(35, user.getV_usrtype_desc());

            ps.setString(36, user.getPasswordHash());
            ps.setString(37, user.getReportingTo());
            ps.setInt(38, user.getN_team_id());
            ps.setDouble(39, user.getN_liablity_limit());
            ps.setDouble(40, user.getN_payment_limit());

            ps.setDouble(41, user.getN_reserve_limit());
            ps.setDouble(42, user.getN_payment_auth_limit());

            ps.setString(43, "I");
            ps.setString(44, user.getV_inpuser());
            ps.setString(45, Utility.sysDate("yyyy-MM-dd hh:mm:ss"));
            ps.setString(46, user.getV_auth1stat());
            ps.setString(47, user.getV_auth1user());
            ps.setString(48, user.getD_auth1time());
            ps.setString(49, user.getV_auth2stat());
            ps.setString(50, user.getV_auth2user());
            ps.setString(51, user.getD_auth2time());
            ps.setString(52, user.getAssessorType());
            ps.setString(53, user.getBranchCode());
            ps.setString(54, user.getNeedToSendEmail());
            ps.setInt(55, user.getRteReserveLimitLevel());

            result = ps.executeUpdate();
            if (result > 0) {
                int r = -1;
                if (user.getN_accessusrtype() == 0) {
                    r = updateExternalUserPrivilege(user.getN_comid(), user.getN_usrcode());
                } else if (user.getN_accessusrtype() == 22 || user.getN_accessusrtype() == 23 || user.getN_accessusrtype() == 24) {
                    saveAssignRte(user);
                    updateUserPrivilege(user.getN_comid(), user.getN_usrcode(), user.getV_usrtypes());
                    UserProfileManager.getInstance().insertUserProfile(user.getN_usrcode(), user.getV_firstname() + " " + user.getV_lastname(), "", "1900-01-01", "", "",
                            "", null, "", "", "", user.getV_inpuser());
                } else {
                    r = updateUserPrivilege(user.getN_comid(), user.getN_usrcode(), user.getV_usrtypes());
                    UserProfileManager.getInstance().insertUserProfile(user.getN_usrcode(), user.getV_firstname() + " " + user.getV_lastname(), "", "1900-01-01", "", "",
                            "", null, "", "", "", user.getV_inpuser());
                }

            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    //===================User Update Method=======================================
    private synchronized int updateUser(User user,User sessionUser) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;

        String strSQL = "UPDATE usr_mst SET "
                + "n_usrcode=?,"
                + "n_comid=?,"
                + "v_usrid=?,"
                + "v_password=?,"
                + "v_oldpassword=?,"
                + "v_usrtypes=?,"
                + "n_accessusrtype=?,"
                + "v_title=?,"
                + "v_firstname=?,"
                + "v_lastname=?,"
                + "v_address1=?,"
                + "v_address2=?,"
                + "v_email=?,"
                + "v_land_phone=?,"
                + "v_mobile=?,"
                + "v_fax=?,"
                + "v_nic=?,"
                + "v_emp_no=?,"
                + "n_brid=?,"
                + "d_activedate=?,"
                + "d_expirydate=?,"
                + "v_usrstatus=?,"
                + "v_oldusrstatus=?,"
                + " d_lastlogindate=?,"
                + " d_lastlogintime=?,"
                + "n_atmptno=?,"
                + "d_pwchgdate=?,"
                + "d_pwprtdate=?,"
                + "v_firstlogin=?,"
                + "d_uidlockdate=?,"
                + "d_uidlocktime=?,"
                + "v_anymodify=?,"
                + "v_group_ids=?,"
                + "v_group_ids_desc=?,"
                + "v_usrtype_desc=?,"
                + "v_password_hash=?,"
                + "V_REPORT_TO=?,"
                + "N_TEAM_ID=?,"
                + "N_LIABLITY_LIMIT=?,"
                + "N_PAYMENT_LIMIT=?,"
                + "N_RESERVE_LIMIT=?,"
                + "N_PAYMENT_AUTH_LIMIT=?,"
                + "v_inpstat=?,"
                + "v_inpuser=?,"
                + "d_inptime=?,"
                + "v_auth1stat=?,"
                + "v_auth1user=?,"
                + "d_auth1time=?,"
                + "v_auth2stat=?,"
                + "v_auth2user=?,"
                + "d_auth2time=?,"
                + "assessor_type=?,"
                + "branch_code=?,"
                + "need_to_send_email=?,"
                + "n_auth_level=? WHERE n_usrcode=?";

        try {
            String passwordHash = ShaHashGenerator.getSha256HashValue(user.getV_password());
            user.setPasswordHash(passwordHash);
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, user.getN_usrcode());
            ps.setInt(2, user.getN_comid());
            ps.setString(3, user.getV_usrid());
            ps.setString(4, new Cryptography("DES").encrypt(user.getV_password()));
            ps.setString(5, user.getV_oldpassword());
            ps.setString(6, user.getV_usrtypes());
            ps.setInt(7, user.getN_accessusrtype());
            ps.setString(8, user.getV_title());
            ps.setString(9, user.getV_firstname());
            ps.setString(10, user.getV_lastname());
            ps.setString(11, user.getV_address1());
            ps.setString(12, user.getV_address2());
            ps.setString(13, user.getV_email());
            ps.setString(14, user.getV_land_phone());
            ps.setString(15, user.getV_mobile());
            ps.setString(16, user.getV_fax());
            ps.setString(17, user.getV_nic());
            ps.setString(18, user.getV_emp_no());
            ps.setInt(19, user.getN_brid());
            ps.setString(20, user.getD_activedate());
            ps.setString(21, user.getD_expirydate());
            ps.setString(22, user.getV_usrstatus());
            ps.setString(23, user.getV_oldusrstatus());
            ps.setString(24, user.getD_lastlogindate());
            ps.setString(25, user.getD_lastlogintime());
            ps.setInt(26, user.getN_atmptno());
            ps.setString(27, user.getD_pwchgdate());
            ps.setString(28, user.getD_pwprtdate());
            ps.setString(29, user.getV_firstlogin());
            ps.setString(30, user.getD_uidlockdate());
            ps.setString(31, user.getD_uidlocktime());
            ps.setString(32, user.getV_anymodify());
            ps.setString(33, user.getV_group_ids());
            ps.setString(34, user.getV_group_ids_desc());
            //ps.setString(34, getUserTypesDesc(user.getV_usrtypes(), conn));
            ps.setString(35, user.getV_usrtype_desc());

            ps.setString(36, user.getPasswordHash());
            ps.setString(37, user.getReportingTo());
            ps.setInt(38, user.getN_team_id());
            ps.setDouble(39, user.getN_liablity_limit());
            ps.setDouble(40, user.getN_payment_limit());

            ps.setDouble(41, user.getN_reserve_limit());
            ps.setDouble(42, user.getN_payment_auth_limit());

            ps.setString(43, "M");
            ps.setString(44, user.getV_inpuser());
            ps.setString(45, Utility.sysDate("yyyy-MM-dd hh:mm:ss"));
            ps.setString(46, user.getV_auth1stat());
            ps.setString(47, user.getV_auth1user());
            ps.setString(48, user.getD_auth1time());
            ps.setString(49, user.getV_auth2stat());
            ps.setString(50, user.getV_auth2user());
            ps.setString(51, user.getD_auth2time());
            ps.setString(52, user.getAssessorType());
            ps.setString(53, user.getBranchCode());
            ps.setString(54, user.getNeedToSendEmail());
            ps.setInt(55, user.getRteReserveLimitLevel());

            ps.setInt(56, user.getN_usrcode());


            result = ps.executeUpdate();

            if (result > 0) {
                if (user.getN_accessusrtype() == 0) {
                    updateExternalUserPrivilege(user.getN_comid(), user.getN_usrcode());
                } else {
                    updateUserPrivilege(user.getN_comid(), user.getN_usrcode(), user.getV_usrtypes());
                    ps = conn.prepareStatement("SELECT usrcode FROM profile_mst WHERE usrcode=?");
                    ps.setInt(1, user.getN_usrcode());
                    if (!ps.executeQuery().next()) {
                        UserProfileManager.getInstance().insertUserProfile(user.getN_usrcode(), user.getV_firstname() + " " + user.getV_lastname(), "", "1900-01-01", "", "",
                                "", null, "", "", "", user.getV_inpuser());
                    }
                }

                if (user.getN_accessusrtype() == 22 || user.getN_accessusrtype() == 23 || user.getN_accessusrtype() == 24) {
                    updateAssignRte(user);
                } else {
                    ps = conn.prepareStatement("DELETE FROM auth_assign_rte WHERE v_usrid = ?");
                    ps.setString(1, user.getV_usrid());
                    ps.executeUpdate();
                }

            }
            String logString = "Access application -: " + "All User Management -->Create User -->" +"Update User, "+"User="+sessionUser.getV_usrid();
            AdminLog.getInstance().logRecord(sessionUser.getV_usrid(), sessionUser.getIpAddress(), logString);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }
    public static boolean isStringNullOrWhiteSpace(String empNo) {
        if (empNo == null) { return true; }

        if(empNo.equals(AppConstant.STRING_EMPTY) ){ return true; }

        if (!Character.isWhitespace(empNo.charAt(0))) { return false; }

        return true;
    }

    public synchronized int saveUser(User user, List<UserParameters> userParametersList,User sessionUser) {
        int result = 0;
        int userCode = user.getN_usrcode();
        String v_emp_no = user.getV_emp_no();

        if(isStringNullOrWhiteSpace(v_emp_no)){
            user.setV_emp_no(AppConstant.ZERO);
        }

        try {
            boolean isUpdated=false;
            if (!dbRecordCommonFunction.isRecExists("usr_mst", "n_usrcode=" + userCode)) {
                if (!dbRecordCommonFunction.isIsErrorExsist()) {
                    result = insertUser(user);
                }
            } else {
                result = updateUser(user,sessionUser);
                isUpdated=true;
            }
            //Save User Allocation
            if (result > 0) {
                saveAllctionTable(user);
            }

            if (result > 0 && (user.getN_accessusrtype() == 20)) {
                dbRecordCommonFunction.executeUpdate("UPDATE claim_assessor SET V_DISTRICT_CODE='" + user.getV_district_code() + "' WHERE V_CODE='" + user.getV_emp_no() + "' AND V_PARA_TYPE='ASSESSOR'");
            }

            if (result > 0) {
                int r = UserParametersManager.getInstance().saveUserParameters(userParametersList, user);
            }

            if(!isUpdated){
                String logString = "Access application -: " + "All User Management -->Create User -->" +"Add User, "+"User="+sessionUser.getV_usrid();
                AdminLog.getInstance().logRecord(sessionUser.getV_usrid(), sessionUser.getIpAddress(), logString);
            }





        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return result;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public synchronized int deleteUser(List<User> userList,User sessionUser) {
        int result = 0;
        User user = null;
        int n_usrcode = -1;

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL = "UPDATE usr_mst SET v_usrstatus='C' WHERE n_usrcode=?";

        int updateCountResult = -1;
        try {
            conn = getJDBCConnection();
            conn.setAutoCommit(false);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < userList.size(); i++) {
                setMsg("");
                user = userList.get(i);
                n_usrcode = user.getN_usrcode();
                ps = conn.prepareStatement(strSQL);
                ps.setInt(1, n_usrcode);
                result = ps.executeUpdate();
                if (result > 0) {
                    ps = conn.prepareStatement("DELETE FROM userprev_mst WHERE n_usrcode=?");
                    ps.setInt(1, n_usrcode);
                    result = ps.executeUpdate();

                    ps = conn.prepareStatement("UPDATE claim_user_allocation SET V_STATUS='C'  WHERE V_ASSIGN_USER_ID=?");
                    ps.setString(1, user.getV_usrid());
                    ps.executeUpdate();

                    ps = conn.prepareStatement("DELETE FROM usrparalist_mst WHERE usrcode=?");
                    ps.setInt(1, n_usrcode);
                    ps.executeUpdate();

                    ps = conn.prepareStatement("DELETE FROM profile_mst WHERE usrcode=?");
                    ps.setInt(1, n_usrcode);
                    ps.executeUpdate();

                    if (result > 0) {
                        updateCountResult++;
                    }
                }

                sb.append(user.getV_usrid()+", ");

            }

            conn.commit();
            conn.setAutoCommit(true);
            String logString = "Access application -: " + "All User Management -->Create User -->" +"Delete User, "+"User="+sessionUser.getV_usrid();
            AdminLog.getInstance().logRecord(sessionUser.getV_usrid(), sessionUser.getIpAddress(), logString);
            setMsg("Record Delete Successful");

        } catch (Exception e) {
            try {
                if (conn != null) {
                    conn.rollback();
                }
                conn.setAutoCommit(true);
            } catch (Exception e1) {
            }
            LOGGER.error(e.getMessage());
            setMsg("Can not be Delete");
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return updateCountResult;
    }

    public synchronized List<User> getUserList(String searchKey) {
        List<User> m_UserList = new LinkedList<User>();
        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        String strSql = "SELECT u.*,c.v_comcode "
                + "FROM "
                + "usr_mst u,"
                + "company_mst c "
                + "where u.n_comid=c.n_comid AND u.v_usrstatus<>'C'"
                + searchKey;
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();
            User user = null;
            while (rs.next()) {
                user = getUser(rs);
                if (user.getN_accessusrtype() == 22 || user.getN_accessusrtype() == 23 || user.getN_accessusrtype() == 24) {
                    ps1 = conn.prepareStatement("SELECT * FROM auth_assign_rte WHERE v_usrid = ?");
                    ps1.setString(1, user.getV_usrid());
                    ResultSet rs1 = ps1.executeQuery();
                    while (rs1.next()) {
                        user.setRteLevel2(rs1.getString("rte_lvl_2"));
                        user.setRteLevel3(rs1.getString("rte_lvl_3"));
                        user.setRteLevel4(rs1.getString("rte_lvl_4"));
                    }
                    rs1.close();
                }
                user.setV_ass_name(dbRecordCommonFunction.findRecord(conn, "claim_assessor", "V_NAME", "V_CODE='" + user.getV_emp_no() + "' AND V_PARA_TYPE='ASSESSOR' "));
                if (user.getN_accessusrtype() == 20) {
                    user.setV_district_code(dbRecordCommonFunction.findRecord(conn, "claim_assessor", "V_DISTRICT_CODE", "V_CODE='" + user.getV_emp_no() + "' AND V_PARA_TYPE='ASSESSOR' "));
                    if (user.getV_ass_name().equalsIgnoreCase("Invalid")) {
                        user.setV_emp_no("");
                    }
                }
                m_UserList.add(user);
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_UserList;
    }

    public User getUser(String companyCode, String userId) {
        User user = null;
        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        String strSql = "SELECT u.*,c.v_comcode "
                + "FROM "
                + "usr_mst u,"
                + "company_mst c "
                + "where u.n_comid=c.n_comid AND u.v_usrstatus<>'C' "
                + "  AND c.v_comcode=?"
                + "  AND u.v_usrid=?";
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ps.setString(1, companyCode);
            ps.setString(2, userId);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                user = getUser(rs);
                if (user.getN_accessusrtype() == 22 || user.getN_accessusrtype() == 23 || user.getN_accessusrtype() == 24) {
                    ps1 = conn.prepareStatement("SELECT * FROM auth_assign_rte WHERE v_usrid = ?");
                    ps1.setString(1, user.getV_usrid());
                    ResultSet rs1 = ps1.executeQuery();
                    while (rs1.next()) {
                        user.setRteLevel2(rs1.getString("rte_lvl_2"));
                        user.setRteLevel3(rs1.getString("rte_lvl_3"));
                        user.setRteLevel4(rs1.getString("rte_lvl_4"));
                    }
                    rs1.close();
                }
                user.setV_ass_name(dbRecordCommonFunction.findRecord(conn, "claim_assessor", "V_NAME", "V_CODE='" + user.getV_emp_no() + "' AND V_PARA_TYPE='ASSESSOR' "));
                if (user.getN_accessusrtype() == 20) {
                    user.setV_district_code(dbRecordCommonFunction.findRecord(conn, "claim_assessor", "V_DISTRICT_CODE", "V_CODE='" + user.getV_emp_no() + "' AND V_PARA_TYPE='ASSESSOR' "));
                    if (user.getV_ass_name().equalsIgnoreCase("Invalid")) {
                        user.setV_emp_no("");
                    }
                }
            }
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return user;
    }

    public synchronized List<User> getUserViewList(String searchKey) {
        List<User> m_UserList = new LinkedList<User>();
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT u.*,c.v_comcode "
                + "FROM "
                + "usr_mst u,"
                + "company_mst c "
                + "where u.n_comid=c.n_comid AND u.v_usrstatus<>'C' "
                + searchKey;

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                m_UserList.add(getUser_For_View(rs));
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_UserList;
    }

    private synchronized int getNextID() {
        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT MAX(n_usrcode) as txnID from usr_mst";
        int maxid = 0;
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                maxid = rs.getInt("txnID");
            }
            maxid++;
            rs.close();

        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return maxid;
    }

    public String getTextValue(String val) {
        String s = val;
        s = val.trim().equalsIgnoreCase("Invalid") ? "" : s;
        return s;
    }

    private int updateUserPrivilege(int n_comid, int n_usrcode, String v_usrtypes) {
        int result = 0;
        int tmp_result = 0;
        String[] v_split_usrtypes_Array = null;
        int n_usrtype = -1;
        Connection conn = null;
        PreparedStatement ps = null;
        String strSQL = "DELETE FROM userprev_mst WHERE n_usrcode=?";
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, n_usrcode);
            ps.execute();
            v_split_usrtypes_Array = v_usrtypes.trim().split(",");
            for (String val : v_split_usrtypes_Array) {
                try {
                    n_usrtype = Integer.parseInt(val);
                } catch (Exception e) {
                }
                strSQL = "INSERT INTO userprev_mst ("
                        + "SELECT "
                        + "?, "//n_usrcode
                        + "t1.n_usrtype, "
                        + "t1.n_prgid, "
                        + "t1.n_comid, "
                        + "t1.n_mnuid, "
                        + "t1.n_itmid, "
                        + "t1.v_view, "
                        + "t1.v_input, "
                        + "t1.v_modify, "
                        + "t1.v_delete, "
                        + "t1.v_auth1, "
                        + "t1.v_auth2, "
                        + "t1.v_grant, "
                        + "t1.recieve1, "
                        + "t1.v_inpstat, "
                        + "t1.v_inpuser, "
                        + "t1.d_inptime, "
                        + "t1.v_auth1stat, "
                        + "t1.v_auth1user, "
                        + "t1.d_auth1time, "
                        + "t1.v_auth2stat, "
                        + "v_auth2user, "
                        + "t1.d_auth2time "
                        + "FROM prev_mst as t1 "
                        + "WHERE n_comid=? AND n_usrtype=? AND "
                        + "(t1.v_view='checked' "
                        + "OR "
                        + "t1.v_input='checked' "
                        + "OR "
                        + "t1.v_modify='checked' "
                        + "OR "
                        + "t1.v_delete='checked' "
                        + "OR "
                        + "t1.v_auth1='checked' "
                        + "OR "
                        + "t1.v_auth2='checked' "
                        + "OR "
                        + "t1.v_grant='checked'))";

                ps = conn.prepareStatement(strSQL);
                ps.setInt(1, n_usrcode);
                ps.setInt(2, n_comid);
                ps.setInt(3, n_usrtype);
                tmp_result = ps.executeUpdate();
                if (tmp_result > 0) {
                    result++;
                }

            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    private boolean saveAssignRte(User user) throws SQLException {
        Connection connection = null;
        PreparedStatement ps = null;
        try {
            connection = getJDBCConnection();
            ps = connection.prepareStatement("INSERT INTO auth_assign_rte VALUES(0,?,?,?,?)");
            ps.setString(1, user.getV_usrid());
            ps.setString(2, user.getRteLevel2());
            ps.setString(3, user.getRteLevel3());
            ps.setString(4, user.getRteLevel4());
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            ps.close();
            releaseJDBCConnection(connection);
        }
        return false;
    }

    private boolean updateAssignRte(User user) throws SQLException {
        Connection connection = null;
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        ResultSet rs = null;
        try {
            connection = getJDBCConnection();
            ps = connection.prepareStatement("SELECT * FROM auth_assign_rte WHERE v_usrid = ?");
            ps.setString(1, user.getV_usrid());
            rs = ps.executeQuery();
            if (rs.next()) {
                ps1 = connection.prepareStatement("UPDATE auth_assign_rte set rte_lvl_2 = ?, rte_lvl_3 = ?, rte_lvl_4 = ? WHERE n_id = ?");
                ps1.setString(1, user.getRteLevel2());
                ps1.setString(2, user.getRteLevel3());
                ps1.setString(3, user.getRteLevel4());
                ps1.setInt(4, rs.getInt("n_id"));
                return ps1.executeUpdate() > 0;
            } else {
                ps1 = connection.prepareStatement("INSERT INTO auth_assign_rte VALUES(0,?,?,?,?)");
                ps1.setString(1, user.getV_usrid());
                ps1.setString(2, user.getRteLevel2());
                ps1.setString(3, user.getRteLevel3());
                ps1.setString(4, user.getRteLevel4());
                return ps1.executeUpdate() > 0;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            ps.close();
            ps1.close();
            rs.close();
            releaseJDBCConnection(connection);
        }
        return false;
    }

    private int updateExternalUserPrivilege(int n_comid, int n_usrcode) {
        int result = 0;
        int tmp_result = 0;
        // String v_split_usrtypes_Array[] = null;
        int n_usrtype = -1;
        Connection conn = null;
        PreparedStatement ps = null;
        String strSQL = "DELETE FROM comuserprev_mst WHERE n_usrcode=?";
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, n_usrcode);
            ps.execute();
            try {
                n_usrtype = 0;
            } catch (Exception e) {
            }
            strSQL = "INSERT INTO comuserprev_mst ("
                    + "SELECT "
                    + "?, "//n_usrcode
                    + "?, "//v_usrtype
                    + "t1.n_prgid, "
                    + "t1.n_comid, "
                    + "t1.n_mnuid, "
                    + "t1.n_itmid, "
                    + "t1.v_view, "
                    + "t1.v_input, "
                    + "t1.v_modify, "
                    + "t1.v_delete, "
                    + "t1.v_auth1, "
                    + "t1.v_auth2, "
                    + "t1.v_grant, "
                    + "t1.recieve1, "
                    + "t1.v_inpstat, "
                    + "t1.v_inpuser, "
                    + "t1.d_inptime, "
                    + "t1.v_auth1stat, "
                    + "t1.v_auth1user, "
                    + "t1.d_auth1time, "
                    + "t1.v_auth2stat, "
                    + "v_auth2user, "
                    + "t1.d_auth2time "
                    + "FROM comprev_mst as t1 "
                    + "WHERE n_comid=? AND "
                    + "(t1.v_view='checked' "
                    + "OR "
                    + "t1.v_input='checked' "
                    + "OR "
                    + "t1.v_modify='checked' "
                    + "OR "
                    + "t1.v_delete='checked' "
                    + "OR "
                    + "t1.v_auth1='checked' "
                    + "OR "
                    + "t1.v_auth2='checked' "
                    + "OR "
                    + "t1.v_grant='checked'))";

            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, n_usrcode);
            ps.setInt(2, n_usrtype);
            ps.setInt(3, n_comid);

            tmp_result = ps.executeUpdate();
            if (tmp_result > 0) {
                result++;
            }

            //    }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public synchronized boolean validateUserHistoryPassword(int userCode, String password) {
        Connection conn = null;
        PreparedStatement ps = null;
        boolean status = false;

        try {
            conn = getJDBCConnection();

            String encryptedPassword = (new Cryptography("DES")).encrypt(password);
            String storedPassword = "";

            ps = conn.prepareStatement("select t1.password from user_password_hst t1, usr_mst t2 where t2.n_usrcode=? and t1.usrcode=t2.n_usrcode order by t1.txndate desc,t1.txntime desc");
            ps.setInt(1, userCode);

            int count = 0;
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                if (count == 5) {
                    break;
                }

                storedPassword = rs.getString("password");
                if (storedPassword.equals(encryptedPassword)) {
                    status = true;
                    break;
                }
                count++;
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            status = false;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return status;
    }

    public synchronized boolean changeInitUserPassword(int userCode, String password,
                                                       String authorizerID, String ipAddress) {
        Connection conn = null;
        PreparedStatement ps = null;
        boolean status = false;
        String passwordHash = ShaHashGenerator.getSha256HashValue(password);
        setMsg("");
        String encryptedPWD = "";
        try {
            encryptedPWD = (new Cryptography("DES")).encrypt(password);
        } catch (EncryptionException ex) {
            LOGGER.error(ex.getMessage());;
        }
        try {
            conn = getJDBCConnection();
            if (validateUserHistoryPassword(userCode, password)) {
                setMsg("Warning! last five passwords not allowed. Use a new password");
                return false;
            }

            ps = conn.prepareStatement("update usr_mst set v_oldpassword=v_password, v_password=?, d_pwchgdate=current_date, n_atmptno=0, v_usrstatus='A', v_firstlogin='N',v_password_hash=? where  n_usrcode=?");
            ps.setString(1, encryptedPWD);
            ps.setString(2, passwordHash);
            ps.setInt(3, userCode);
            int i = ps.executeUpdate();

            ps = conn.prepareStatement("insert into user_password_hst (usrcode,password,txndate,txntime) select n_usrcode,?,current_date,current_time from usr_mst where n_usrcode=?");
            ps.setString(1, encryptedPWD);
            ps.setInt(2, userCode);
            ps.executeUpdate();

            if (i > 0) {
                status = true;
                setMsg("Password changed successfully");
                UserLog.getInstance().logRecord(authorizerID, ipAddress, "Modify User Password ->  UserCode= " + userCode);
            }
        } catch (Exception e) {
            setMsg("Password change failed");
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }

        return status;
    }

    public synchronized boolean changeUserPassword(int userCode, String oldpassword, String password,
                                                   String authorizerID, String ipAddress) {
        Connection conn = null;
        PreparedStatement ps = null;
        boolean status = false;

        String encryptedOLDPWD = "";
        String encryptedPWD = "";
        setMsg("");
        String passwordHash = ShaHashGenerator.getSha256HashValue(password);
        try {
            encryptedOLDPWD = (new Cryptography("DES")).encrypt(oldpassword);
            encryptedPWD = (new Cryptography("DES")).encrypt(password);
        } catch (EncryptionException ex) {
            LOGGER.error(ex.getMessage());;
        }

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement("SELECT v_password FROM usr_mst WHERE n_usrcode=?");
            ps.setInt(1, userCode);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    if (!rs.getString("v_password").equals(encryptedOLDPWD)) {
                        setMsg("The password you typed is incorrect.Please retype your current password.");
                        rs.close();
                        return false;
                    }
                }
            }
            if (validateUserHistoryPassword(userCode, password)) {
                setMsg("Warning! last five passwords not allowed. Use a new password");
                return false;
            }

            ps = conn.prepareStatement("update usr_mst set v_oldpassword=v_password, v_password=?, d_pwchgdate=current_date, n_atmptno=0, v_usrstatus='A', v_firstlogin='N',v_password_hash=? where  n_usrcode=?");
            ps.setString(1, encryptedPWD);
            ps.setString(2, passwordHash);
            ps.setInt(3, userCode);
            int i = ps.executeUpdate();

            ps = conn.prepareStatement("insert into user_password_hst (usrcode,password,txndate,txntime) select n_usrcode,?,current_date,current_time from usr_mst where n_usrcode=?");
            ps.setString(1, encryptedPWD);
            ps.setInt(2, userCode);
            ps.executeUpdate();
            if (i > 0) {
                status = true;
                setMsg("Password changed successfully");
                UserLog.getInstance().logRecord(authorizerID, ipAddress, "Modify User Password ->  UserCode= " + userCode);
            }
        } catch (Exception e) {
            setMsg("Password change failed");
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
            releaseJDBCConnection(conn);
        }

        return status;
    }

    public synchronized boolean isValiedUser(User user) {
        boolean b = false;
        Connection conn = null;
        PreparedStatement ps = null;

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement("SELECT n_comid, n_usrcode,v_usrid,v_usrstatus FROM usr_mst WHERE v_usrid=? AND n_comid=? AND v_usrstatus<>'C'");
            ps.setString(1, user.getV_usrid());
            ps.setInt(2, user.getN_comid());

            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    b = true;
                    user.setN_comid(rs.getInt("n_comid"));
                    user.setN_usrcode(rs.getInt("n_usrcode"));
                    user.setV_usrstatus(rs.getString("v_usrstatus"));
                }
                rs.close();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }

        return b;
    }

    public synchronized boolean isUpadeteUserSetting(User user, int type)//type=1->password reset, type=2 user status reset
    {
        boolean b = false;
        Connection conn = null;
        PreparedStatement ps = null;
        String sql = "";
        if (type == 1) {

            sql = "UPDATE usr_mst SET "
                    + "v_oldpassword=v_password,"
                    + "v_password=?,"
                    + "v_oldusrstatus=v_usrstatus,"
                    + "v_usrstatus='X',"
                    + "d_pwchgdate=current_date,"
                    + "d_lastlogindate=current_date,"
                    + "n_atmptno=0 WHERE n_usrcode=? AND v_usrstatus<>'C'";
        } else if (type == 2) {
            sql = "UPDATE usr_mst SET "
                    + "v_oldusrstatus=v_usrstatus,"
                    + "v_usrstatus=? ,"
                    + "d_lastlogindate=current_date,"
                    + "n_atmptno=0 WHERE n_usrcode=? AND v_usrstatus<>'C'";
        } else {
            return b;
        }

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(sql);
            if (type == 1) {
                ps.setString(1, new Cryptography("DES").encrypt(user.getV_password()));
                ps.setInt(2, user.getN_usrcode());
            } else if (type == 2) {
                ps.setString(1, user.getV_usrstatus());
                ps.setInt(2, user.getN_usrcode());
            }
            int r = ps.executeUpdate();
            if (r > 0) {
                b = true;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }

        return b;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            if (null != conn)
                conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

}
