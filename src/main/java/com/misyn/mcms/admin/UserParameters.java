/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import java.io.Serializable;
public class UserParameters implements Serializable {

    private int n_usrCode = 0;
    private String v_usrId = "";
    private String v_paracode = "";
    private String v_lblDesc = "";
    private String v_txtValue = "";


    private String stats = "";
    private String inpstat = "I";
    private String inpuser = "";
    private String inptime = "1900-01-01 12:00:00";
    private String auth1stat = "P";
    private String auth1user = "";
    private String auth1time = "1900-01-01 12:00:00";
    private String auth2stat = "P";
    private String auth2user = "";
    private String auth2time = "1900-01-01 12:00:00";

    public String getAuth1stat() {
        return auth1stat;
    }

    public void setAuth1stat(String auth1stat) {
        this.auth1stat = auth1stat;
    }

    public String getAuth1time() {
        return auth1time;
    }

    public void setAuth1time(String auth1time) {
        this.auth1time = auth1time;
    }

    public String getAuth1user() {
        return auth1user;
    }

    public void setAuth1user(String auth1user) {
        this.auth1user = auth1user;
    }

    public String getAuth2stat() {
        return auth2stat;
    }

    public void setAuth2stat(String auth2stat) {
        this.auth2stat = auth2stat;
    }

    public String getAuth2time() {
        return auth2time;
    }

    public void setAuth2time(String auth2time) {
        this.auth2time = auth2time;
    }

    public String getAuth2user() {
        return auth2user;
    }

    public void setAuth2user(String auth2user) {
        this.auth2user = auth2user;
    }

    public String getInpstat() {
        return inpstat;
    }

    public void setInpstat(String inpstat) {
        this.inpstat = inpstat;
    }

    public String getInptime() {
        return inptime;
    }

    public void setInptime(String inptime) {
        this.inptime = inptime;
    }

    public String getInpuser() {
        return inpuser;
    }

    public void setInpuser(String inpuser) {
        this.inpuser = inpuser;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }


    public int getN_usrCode() {
        return n_usrCode;
    }

    public void setN_usrCode(int n_usrCode) {
        this.n_usrCode = n_usrCode;
    }

    public String getV_lblDesc() {
        return v_lblDesc;
    }

    public void setV_lblDesc(String v_lblDesc) {
        this.v_lblDesc = v_lblDesc;
    }

    public String getV_paracode() {
        return v_paracode;
    }

    public void setV_paracode(String v_paracode) {
        this.v_paracode = v_paracode;
    }

    public String getV_txtValue() {
        return v_txtValue;
    }

    public void setV_txtValue(String v_txtValue) {
        this.v_txtValue = v_txtValue;
    }

    public String getV_usrId() {
        return v_usrId;
    }

    public void setV_usrId(String v_usrId) {
        this.v_usrId = v_usrId;
    }

}
