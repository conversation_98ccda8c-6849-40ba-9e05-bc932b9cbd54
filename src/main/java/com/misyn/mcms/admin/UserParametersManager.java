/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import com.misyn.mcms.dbconfig.ConnectionPool;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.LinkedList;
import java.util.List;
public class UserParametersManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserParametersManager.class);
    private static DbRecordCommonFunction dbRecordCommonFunction = new DbRecordCommonFunction();
    private static UserParametersManager userParametersManager = null;
    private ConnectionPool cp = null;

    public UserParametersManager() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public static synchronized UserParametersManager getInstance() {
        if (userParametersManager == null) {
            userParametersManager = new UserParametersManager();
        }
        return userParametersManager;
    }

    private synchronized int insertUserParameters(UserParameters userParameters, User user) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;

        String strSQL = "INSERT INTO usrparalist_mst VALUES("
                + "?,?,?,?,?,?,?,?,?,?,"
                + "?,?,?)";
        try {

            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, user.getN_usrcode());
            ps.setString(2, userParameters.getV_paracode());
            ps.setString(3, userParameters.getV_txtValue());

            ps.setString(4, "I");
            ps.setString(5, userParameters.getInpstat());
            ps.setString(6, user.getV_inpuser());
            ps.setString(7, Utility.sysDate("yyyy-MM-dd hh:mm:ss"));
            ps.setString(8, userParameters.getAuth1stat());
            ps.setString(9, userParameters.getAuth1user());
            ps.setString(10, userParameters.getAuth1time());
            ps.setString(11, userParameters.getAuth2stat());
            ps.setString(12, userParameters.getAuth2user());
            ps.setString(13, userParameters.getAuth2time());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    private synchronized int updateUserParameters(UserParameters userParameters, User user) {
        int result = 0;
        Connection conn = null;
        PreparedStatement ps = null;
        String strSQL = "UPDATE usrparalist_mst SET "
                + "usrcode=?,"
                + "paraCode=?,"
                + "paravalue=?,"
                + "status=?,"
                + "inpstat=?,"
                + "inpuser=?,"
                + "inptime=?,"
                + "auth1stat=?,"
                + "auth1user=?,"
                + "auth1time=?,"
                + "auth2stat=?,"
                + "auth2user=?,"
                + "auth2time=? "
                + "WHERE "
                + "usrcode=? "
                + "AND "
                + "paraCode=?";
        try {

            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, user.getN_usrcode());
            ps.setString(2, userParameters.getV_paracode());
            ps.setString(3, userParameters.getV_txtValue());

            ps.setString(4, userParameters.getStats());
            ps.setString(5, "M");
            ps.setString(6, user.getV_inpuser());
            ps.setString(7, Utility.sysDate("yyyy-MM-dd hh:mm:ss"));
            ps.setString(8, userParameters.getAuth1stat());
            ps.setString(9, userParameters.getAuth1user());
            ps.setString(10, userParameters.getAuth1time());
            ps.setString(11, userParameters.getAuth2stat());
            ps.setString(12, userParameters.getAuth2user());
            ps.setString(13, userParameters.getAuth2time());

            ps.setInt(14, user.getN_usrcode());
            ps.setString(15, userParameters.getV_paracode());


            result = ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public synchronized int saveUserParameters(List<UserParameters> userParametersList, User user) {
        int result = 0;
        UserParameters userParameters = null;
        int updateCountResult = 0;
        Connection conn = null;
        PreparedStatement ps = null;
        String strSQL = "DELETE FROM usrparalist_mst WHERE usrcode=?";
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, user.getN_usrcode());
            ps.executeUpdate();


            for (int i = 0; i < userParametersList.size(); i++) {
                userParameters = userParametersList.get(i);

                if (!dbRecordCommonFunction.
                        isRecExists("usrparalist_mst", ""
                                + "usrcode=" + user.getN_usrcode() + " "
                                + "AND paraCode='" + userParameters.getV_paracode().trim() + "' ")) {
                    if (!dbRecordCommonFunction.isIsErrorExsist()) {
                        result = insertUserParameters(userParameters, user);
                    }
                } else {
                    result = updateUserParameters(userParameters, user);
                }

                if (result > 0) {
                    updateCountResult++;
                }
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }

        return updateCountResult;
    }

    private synchronized UserParameters getUserParameters_New(ResultSet rs) {
        UserParameters userParameters = new UserParameters();
        try {
            userParameters.setV_paracode(rs.getString("p.paracode"));
            userParameters.setV_lblDesc(rs.getString("p.description"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return userParameters;
    }

    private synchronized UserParameters getUserParameters_Modify(ResultSet rs, ResultSet rs1) {
        UserParameters userParameters = new UserParameters();
        try {
            userParameters.setV_paracode(rs.getString("p.paracode"));
            userParameters.setV_lblDesc(rs.getString("p.description"));
            userParameters.setV_txtValue(rs1.getString("paravalue"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return userParameters;
    }

    public synchronized List<UserParameters> getUserParameterList_New(int n_comid, String v_groupids) {
        List<UserParameters> m_UserParameters = new LinkedList<UserParameters>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL = "SELECT "
                + "p.paracode,"
                + "p.description "
                + "FROM paralist_mst p,"
                + "comparalist_mst c "
                + "WHERE c.comid=? "
                + "AND "
                + "p.paracode=c.paracode "
                + "AND "
                + "(p.paracode NOT IN("
                + "SELECT "
                + "paracode "
                + "FROM "
                + "appparam_mst "
                + "WHERE "
                + "paracode =p.paracode) "
                + "OR "
                + "p.paracode IN("
                + "SELECT "
                + "paracode "
                + "FROM "
                + "appparam_mst "
                + "WHERE "
                + "paracode =p.paracode  "
                + "AND "
                + "tableName='User Parameter' "
                + "AND "
                + "fieldname='paravalue'))";

        if (!v_groupids.equalsIgnoreCase("-1")) {// chane 0 to -1
            v_groupids = Utility.getSplitText(v_groupids);
            strSQL = "SELECT "
                    + "p.paracode,"
                    + "p.description "
                    + "FROM paralist_mst p,"
                    + "comparalist_mst c,"
                    + "com_para_group_mst g "
                    + "WHERE g.n_comid=? "//n_comid
                    + "AND "
                    + "g.n_group_id IN(" + v_groupids + ") " //n_groupid
                    + "AND "
                    + "g.paracode=p.paracode "
                    + "AND "
                    + "p.paracode=c.paracode "
                    + "AND "
                    + "(p.paracode NOT IN("
                    + "SELECT "
                    + "paracode "
                    + "FROM "
                    + "appparam_mst "
                    + "WHERE "
                    + "paracode =p.paracode) "
                    + "OR "
                    + "p.paracode IN("
                    + "SELECT "
                    + "paracode "
                    + "FROM "
                    + "appparam_mst "
                    + "WHERE "
                    + "paracode =p.paracode  "
                    + "AND "
                    + "tableName='User Parameter' "
                    + "AND "
                    + "fieldname='paravalue')) GROUP BY p.paracode";
        }

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, n_comid);

            rs = ps.executeQuery();

            while (rs.next()) {
                m_UserParameters.add(getUserParameters_New(rs));
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
                if (rs != null) {
                    rs.close();
                    rs = null;
                }


            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }

        return m_UserParameters;
    }

    public synchronized List<UserParameters> getUserParameterList_Modify(int n_usrcode, int n_comid, String v_groupids) {
        List<UserParameters> m_UserParameters = new LinkedList<UserParameters>();
        Connection conn = null;
        PreparedStatement ps = null;

        ResultSet rs = null;
        ResultSet rs1 = null;


        String strSQL = "SELECT "
                + "p.paracode,"
                + "p.description "
                + "FROM paralist_mst p,"
                + "comparalist_mst c "
                + "WHERE c.comid=? "//n_comid
                + "AND "
                + "c.paracode=p.paracode"
                + " AND "
                + "("
                + "p.paracode NOT IN ("
                + "SELECT "
                + "paracode "
                + "FROM "
                + "appparam_mst "
                + "WHERE "
                + "paracode =p.paracode) "
                + "OR "
                + "p.paracode IN ("
                + "SELECT paracode "
                + "FROM "
                + "appparam_mst "
                + "WHERE "
                + "paracode =p.paracode  "
                + "AND "
                + "tableName='User Parameter' "
                + "AND "
                + "fieldname='paravalue')"
                + ")";


        if (!v_groupids.equalsIgnoreCase("-1")) {//0 to -1
            v_groupids = Utility.getSplitText(v_groupids);
            strSQL = "SELECT "
                    + "p.paracode,"
                    + "p.description "
                    + "FROM paralist_mst p,"
                    + "comparalist_mst c,"
                    + "com_para_group_mst g "
                    + "WHERE g.n_comid=? "//n_comid
                    + "AND "
                    + "g.n_group_id IN(" + v_groupids + ") " //n_groupid
                    + "AND "
                    + "g.paracode=p.paracode "
                    + "AND "
                    + "p.paracode=c.paracode "
                    + "AND "
                    + "(p.paracode NOT IN("
                    + "SELECT "
                    + "paracode "
                    + "FROM "
                    + "appparam_mst "
                    + "WHERE "
                    + "paracode =p.paracode) "
                    + "OR "
                    + "p.paracode IN("
                    + "SELECT "
                    + "paracode "
                    + "FROM "
                    + "appparam_mst "
                    + "WHERE "
                    + "paracode =p.paracode  "
                    + "AND "
                    + "tableName='User Parameter' "
                    + "AND "
                    + "fieldname='paravalue')) GROUP BY p.paracode";
        }


        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);


            ps.setInt(1, n_comid);


            rs = ps.executeQuery();

            while (rs.next()) {
                strSQL = "SELECT  "
                        + "paravalue "
                        + "FROM "
                        + "usrparalist_mst  "
                        + "WHERE "
                        + "usrcode "
                        + "IN("
                        + "SELECT "
                        + "n_usrcode "
                        + "FROM "
                        + "usr_mst "
                        + "WHERE "
                        + "n_usrcode=? "
                        + "AND "
                        + "paracode=?)";//'" + rs.getString("paracode") + "'

                ps = conn.prepareStatement(strSQL);
                ps.setInt(1, n_usrcode);
                ps.setString(2, rs.getString("paracode"));

                rs1 = ps.executeQuery();

                if (rs1.next()) {
                    m_UserParameters.add(getUserParameters_Modify(rs, rs1));
                } else {
                    m_UserParameters.add(getUserParameters_New(rs));
                }


            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
                if (rs != null) {
                    rs.close();
                    rs = null;
                }
                if (rs1 != null) {
                    rs1.close();
                    rs1 = null;
                }


            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_UserParameters;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}
