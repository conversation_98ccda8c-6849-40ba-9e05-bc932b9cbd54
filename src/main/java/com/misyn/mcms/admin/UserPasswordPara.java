/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import java.io.Serializable;
public class UserPasswordPara implements Serializable {

    private int n_accessusrtype = -1;
    private String v_accessusrtype = "";
    private int n_attempt = 0;
    private int n_timeout = 0;
    private int n_pswexpiryNodays = 0;
    private int n_lstloginexpirydays = 0;
    private String v_home_page_url = "";
    private String v_inpstat = "I";
    private String v_inpuser = "";
    private String d_inptime = "1900-01-01 12:00:00";
    private String v_auth1stat = "P";
    private String v_auth1user = "";
    private String d_auth1time = "1900-01-01 12:00:00";
    private String v_auth2stat = "P";
    private String v_auth2user = "";
    private String d_auth2time = "1900-01-01 12:00:00";

    public String getV_accessusrtype() {
        return v_accessusrtype;
    }

    public void setV_accessusrtype(String v_accessusrtype) {
        this.v_accessusrtype = v_accessusrtype;
    }


    public int getN_accessusrtype() {
        return n_accessusrtype;
    }

    public void setN_accessusrtype(int n_accessusrtype) {
        this.n_accessusrtype = n_accessusrtype;
    }

    public String getV_home_page_url() {
        return v_home_page_url;
    }

    public void setV_home_page_url(String v_home_page_url) {
        this.v_home_page_url = v_home_page_url;
    }

    public String getD_auth1time() {
        return d_auth1time;
    }

    public void setD_auth1time(String d_auth1time) {
        this.d_auth1time = d_auth1time;
    }

    public String getD_auth2time() {
        return d_auth2time;
    }

    public void setD_auth2time(String d_auth2time) {
        this.d_auth2time = d_auth2time;
    }

    public String getD_inptime() {
        return d_inptime;
    }

    public void setD_inptime(String d_inptime) {
        this.d_inptime = d_inptime;
    }

    public int getN_attempt() {
        return n_attempt;
    }

    public void setN_attempt(int n_attempt) {
        this.n_attempt = n_attempt;
    }

    public int getN_lstloginexpirydays() {
        return n_lstloginexpirydays;
    }

    public void setN_lstloginexpirydays(int n_lstloginexpirydays) {
        this.n_lstloginexpirydays = n_lstloginexpirydays;
    }

    public int getN_pswexpiryNodays() {
        return n_pswexpiryNodays;
    }

    public void setN_pswexpiryNodays(int n_pswexpiryNodays) {
        this.n_pswexpiryNodays = n_pswexpiryNodays;
    }

    public int getN_timeout() {
        return n_timeout;
    }

    public void setN_timeout(int n_timeout) {
        this.n_timeout = n_timeout;
    }

    public String getV_auth1stat() {
        return v_auth1stat;
    }

    public void setV_auth1stat(String v_auth1stat) {
        this.v_auth1stat = v_auth1stat;
    }

    public String getV_auth1user() {
        return v_auth1user;
    }

    public void setV_auth1user(String v_auth1user) {
        this.v_auth1user = v_auth1user;
    }

    public String getV_auth2stat() {
        return v_auth2stat;
    }

    public void setV_auth2stat(String v_auth2stat) {
        this.v_auth2stat = v_auth2stat;
    }

    public String getV_auth2user() {
        return v_auth2user;
    }

    public void setV_auth2user(String v_auth2user) {
        this.v_auth2user = v_auth2user;
    }

    public String getV_inpstat() {
        return v_inpstat;
    }

    public void setV_inpstat(String v_inpstat) {
        this.v_inpstat = v_inpstat;
    }

    public String getV_inpuser() {
        return v_inpuser;
    }

    public void setV_inpuser(String v_inpuser) {
        this.v_inpuser = v_inpuser;
    }
}
