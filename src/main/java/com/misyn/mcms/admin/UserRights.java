/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;
public class UserRights implements Serializable {

    private int n_prgid = 0;
    private int n_usrcode = -1;
    private int n_mnuid = 0;
    private int n_itmid = 0;
    private String v_mnuname = "";
    private String v_itmname = "";
    private String v_view = "";
    private String v_input = "";
    private String v_modify = "";
    private String v_delete = "";
    private String v_auth1 = "";
    private String v_auth2 = "";
    private String v_grant = "";
    private String url = "";
    private String target = "";

    private List<List<UserParameters>> userParameterList = new LinkedList<List<UserParameters>>();

    public List<List<UserParameters>> getUserParameterList() {
        return userParameterList;
    }

    public void setUserParameterList(List<UserParameters> userParameterList) {
        this.userParameterList.add(userParameterList);
    }


    public int getN_prgid() {
        return n_prgid;
    }

    public void setN_prgid(int n_prgid) {
        this.n_prgid = n_prgid;
    }


    public int getN_itmid() {
        return n_itmid;
    }

    public void setN_itmid(int n_itmid) {
        this.n_itmid = n_itmid;
    }

    public int getN_mnuid() {
        return n_mnuid;
    }

    public void setN_mnuid(int n_mnuid) {
        this.n_mnuid = n_mnuid;
    }

    public int getN_usrcode() {
        return n_usrcode;
    }

    public void setN_usrcode(int n_usrcode) {
        this.n_usrcode = n_usrcode;
    }

    public String getV_auth1() {
        return v_auth1;
    }

    public void setV_auth1(String v_auth1) {
        this.v_auth1 = v_auth1;
    }

    public String getV_auth2() {
        return v_auth2;
    }

    public void setV_auth2(String v_auth2) {
        this.v_auth2 = v_auth2;
    }

    public String getV_delete() {
        return v_delete;
    }

    public void setV_delete(String v_delete) {
        this.v_delete = v_delete;
    }

    public String getV_grant() {
        return v_grant;
    }

    public void setV_grant(String v_grant) {
        this.v_grant = v_grant;
    }

    public String getV_input() {
        return v_input;
    }

    public void setV_input(String v_input) {
        this.v_input = v_input;
    }

    public String getV_modify() {
        return v_modify;
    }

    public void setV_modify(String v_modify) {
        this.v_modify = v_modify;
    }

    public String getV_view() {
        return v_view;
    }

    public void setV_view(String v_view) {
        this.v_view = v_view;
    }

    public String getV_itmname() {
        return v_itmname;
    }

    public void setV_itmname(String v_itmname) {
        this.v_itmname = v_itmname;
    }

    public String getV_mnuname() {
        return v_mnuname;
    }

    public void setV_mnuname(String v_mnuname) {
        this.v_mnuname = v_mnuname;
    }

    public String getV_target() {
        return target;
    }

    public void setV_target(String target) {
        this.target = target;
    }

    public String getV_url() {
        return url;
    }

    public void setV_url(String url) {
        this.url = url;
    }


}
