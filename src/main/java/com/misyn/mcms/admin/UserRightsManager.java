/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import com.misyn.mcms.dbconfig.ConnectionPool;
import com.misyn.mcms.dbconfig.DbRecordCommonFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
public class UserRightsManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserRightsManager.class);
    private static UserRightsManager userRightsManager = null;
    private ConnectionPool cp = null;
    private Map<String, UserRights> userRightsMap = null;

    public UserRightsManager() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private synchronized List<UserParameters> getParameterList(int usrCode, UserRights userRights) {
        DbRecordCommonFunction dbRecordCommonFunction = DbRecordCommonFunction.getInstance();
        UserParameters userParameters = null;
        List<UserParameters> paramList = new LinkedList<UserParameters>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL = "SELECT * FROM "
                + "appparam_mst "
                + "WHERE "
                + "prgid=? "
                + "AND "
                + "mnuid=? "
                + "AND itmId=?";

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSQL);
            ps.setInt(1, userRights.getN_prgid());
            ps.setInt(2, userRights.getN_mnuid());
            ps.setInt(3, userRights.getN_itmid());

            rs = ps.executeQuery();
            while (rs.next()) {
                userParameters = new UserParameters();
                if (rs.getString("selType").equalsIgnoreCase("Static Field")) {
                    userParameters.setV_paracode(rs.getString("paraCode"));
                    userParameters.setV_txtValue(rs.getString("value"));

                } else if (rs.getString("selType").equalsIgnoreCase("Dynamic Field")) {
                    if (rs.getString("tableName").equalsIgnoreCase("User Master")) {
                        userParameters.setV_paracode(rs.getString("paraCode"));
                        userParameters.setV_txtValue(dbRecordCommonFunction.findRecord(conn,
                                "usr_mst",
                                rs.getString("fieldname"),
                                "n_usrcode='" + usrCode + "'"));

                    } else if (rs.getString("tableName").equalsIgnoreCase("User Parameter")) {

                        userParameters.setV_paracode(rs.getString("paraCode"));
                        userParameters.setV_txtValue(dbRecordCommonFunction.findRecord(conn,
                                "usrparalist_mst",
                                rs.getString("fieldname"),
                                "usrcode='" + usrCode + "' "
                                        + "AND "
                                        + "paraCode='" + userParameters.getV_paracode() + "'"));
                    }

                }
                paramList.add(userParameters);

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
                if (rs != null) {
                    rs.close();
                    rs = null;
                }


            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }


        return paramList;

    }

    private synchronized UserRights getUserRights(ResultSet rs) {
        UserRights userRights = new UserRights();
        try {
            userRights.setN_prgid(rs.getInt("c.n_prgid"));
            userRights.setN_usrcode(rs.getInt("c.n_usrcode"));
            userRights.setN_mnuid(rs.getInt("c.n_mnuid"));
            userRights.setN_itmid(rs.getInt("c.n_itmid"));
            userRights.setV_mnuname(rs.getString("m.v_mnuname"));
            userRights.setV_itmname(rs.getString("i.v_itmname"));
            userRights.setV_url(rs.getString("i.v_url"));
            userRights.setV_target(rs.getString("i.v_target"));

            userRights.setV_view(rs.getString("v_view"));
            userRights.setV_input(rs.getString("v_input"));
            userRights.setV_modify(rs.getString("v_modify"));
            userRights.setV_delete(rs.getString("v_delete"));
            userRights.setV_auth1(rs.getString("v_auth1"));
            userRights.setV_auth2(rs.getString("v_auth2"));
            userRights.setV_grant(rs.getString("v_grant"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return userRights;
    }

    public synchronized List<UserRights> getUserRightsList(int n_usrcode, int n_accessusrtype) {
        String KEY = "";
        List<UserRights> m_UserRights = new LinkedList<UserRights>();

        Connection conn = null;
        PreparedStatement ps = null;
        String strSql = "SELECT "
                + "c.n_prgid,"
                + "c.n_usrcode,"
                + "c.n_mnuid,"
                + "c.n_itmid,"
                + "m.v_mnuname,"
                + "i.v_itmname, "
                + "i.v_url, "
                + "i.v_target, "
                + "ifnull((SELECT v_view From userprev_mst where v_view='checked' AND n_mnuid=c.n_mnuid AND n_itmid=c.n_itmid AND n_usrcode=c.n_usrcode group by c.n_mnuid,c.n_itmid,c.n_usrcode),'') AS v_view, "
                + "ifnull((SELECT v_input From userprev_mst where v_input='checked' AND n_mnuid=c.n_mnuid AND n_itmid=c.n_itmid AND n_usrcode=c.n_usrcode group by c.n_mnuid,c.n_itmid,c.n_usrcode),'') AS v_input, "
                + "ifnull((SELECT v_modify From userprev_mst where v_modify='checked' AND n_mnuid=c.n_mnuid AND n_itmid=c.n_itmid AND n_usrcode=c.n_usrcode group by c.n_mnuid,c.n_itmid,c.n_usrcode),'') AS v_modify, "
                + "ifnull((SELECT v_delete From userprev_mst where v_delete='checked' AND n_mnuid=c.n_mnuid AND n_itmid=c.n_itmid AND n_usrcode=c.n_usrcode group by c.n_mnuid,c.n_itmid,c.n_usrcode),'') AS v_delete, "
                + "ifnull((SELECT v_auth1 From userprev_mst where v_auth1='checked' AND n_mnuid=c.n_mnuid AND n_itmid=c.n_itmid AND n_usrcode=c.n_usrcode group by c.n_mnuid,c.n_itmid,c.n_usrcode),'') AS v_auth1, "
                + "ifnull((SELECT v_auth2 From userprev_mst where v_auth2='checked' AND n_mnuid=c.n_mnuid AND n_itmid=c.n_itmid AND n_usrcode=c.n_usrcode group by c.n_mnuid,c.n_itmid,c.n_usrcode),'') AS v_auth2, "
                + "ifnull((SELECT v_grant From userprev_mst where v_grant='checked' AND n_mnuid=c.n_mnuid AND n_itmid=c.n_itmid AND n_usrcode=c.n_usrcode group by c.n_mnuid,c.n_itmid,c.n_usrcode),'') AS v_grant "
                + "FROM "
                + "userprev_mst AS c,"
                + "mnu_mst AS m,"
                + "itm_mst AS i "
                + "WHERE "
                + "c.n_usrcode=? "//n_usrcode
                + "AND "
                + "c.n_mnuid=m.n_mnuid "
                + "AND "
                + "c.n_mnuid=i.n_mnuid "
                + "AND "
                + "c.n_itmid=i.n_itmid AND c.v_view='checked'"
                + "GROUP BY c.n_mnuid,c.n_itmid,c.n_usrcode "
                + "ORDER BY i.n_mnuid,i.n_itm_seq_no,i.n_itmid";


        if (n_accessusrtype == 0) {
            strSql = "SELECT "
                    + "c.n_prgid,"
                    + "c.n_usrcode,"
                    + "c.n_mnuid,"
                    + "c.n_itmid,"
                    + "m.v_mnuname,"
                    + "i.v_itmname, "
                    + "i.v_url, "
                    + "i.v_target, "
                    + "ifnull((SELECT v_view From comuserprev_mst where v_view='checked' AND n_mnuid=c.n_mnuid AND n_itmid=c.n_itmid AND n_usrcode=c.n_usrcode group by c.n_mnuid,c.n_itmid,c.n_usrcode),'') AS v_view, "
                    + "ifnull((SELECT v_input From comuserprev_mst where v_input='checked' AND n_mnuid=c.n_mnuid AND n_itmid=c.n_itmid AND n_usrcode=c.n_usrcode group by c.n_mnuid,c.n_itmid,c.n_usrcode),'') AS v_input, "
                    + "ifnull((SELECT v_modify From comuserprev_mst where v_modify='checked' AND n_mnuid=c.n_mnuid AND n_itmid=c.n_itmid AND n_usrcode=c.n_usrcode group by c.n_mnuid,c.n_itmid,c.n_usrcode),'') AS v_modify, "
                    + "ifnull((SELECT v_delete From comuserprev_mst where v_delete='checked' AND n_mnuid=c.n_mnuid AND n_itmid=c.n_itmid AND n_usrcode=c.n_usrcode group by c.n_mnuid,c.n_itmid,c.n_usrcode),'') AS v_delete, "
                    + "ifnull((SELECT v_auth1 From comuserprev_mst where v_auth1='checked' AND n_mnuid=c.n_mnuid AND n_itmid=c.n_itmid AND n_usrcode=c.n_usrcode group by c.n_mnuid,c.n_itmid,c.n_usrcode),'') AS v_auth1, "
                    + "ifnull((SELECT v_auth2 From comuserprev_mst where v_auth2='checked' AND n_mnuid=c.n_mnuid AND n_itmid=c.n_itmid AND n_usrcode=c.n_usrcode group by c.n_mnuid,c.n_itmid,c.n_usrcode),'') AS v_auth2, "
                    + "ifnull((SELECT v_grant From comuserprev_mst where v_grant='checked' AND n_mnuid=c.n_mnuid AND n_itmid=c.n_itmid AND n_usrcode=c.n_usrcode group by c.n_mnuid,c.n_itmid,c.n_usrcode),'') AS v_grant "
                    + "FROM "
                    + "comuserprev_mst AS c,"
                    + "mnu_mst AS m,"
                    + "itm_mst AS i "
                    + "WHERE "
                    + "c.n_usrcode=? "//n_usrcode
                    + "AND "
                    + "c.n_mnuid=m.n_mnuid "
                    + "AND "
                    + "c.n_mnuid=i.n_mnuid "
                    + "AND "
                    + "c.n_itmid=i.n_itmid AND c.v_view='checked'"
                    + "GROUP BY c.n_mnuid,c.n_itmid,c.n_usrcode "
                    + "ORDER BY i.n_mnuid,i.n_itm_seq_no,i.n_itmid";
        }
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ps.setInt(1, n_usrcode);
            ResultSet rs = ps.executeQuery();
            UserRights userRights = null;
            userRightsMap = new HashMap<String, UserRights>();

            while (rs.next()) {
                userRights = getUserRights(rs);
                m_UserRights.add(userRights);
                userRights.getUserParameterList().add(getParameterList(n_usrcode, userRights));

                KEY = userRights.getN_mnuid() + "" + userRights.getN_itmid();
                userRightsMap.put(KEY, userRights);
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return m_UserRights;
    }

    public Map<String, UserRights> getUserRightsMap() {
        return userRightsMap;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}
