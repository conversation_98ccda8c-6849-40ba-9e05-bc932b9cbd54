/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

/**
 * Created on : Jan 4, 2011, 10:02:14 AM
 *
 * <AUTHOR>
 * @version 2.0
 * @Product : Intranet - UA Intranet & Common Auth. System
 * @Copyright : Copyright, 2009-2010 (c)
 * @Company : M.I. Synergy (Pvt) Ltd
 */

public class UserType {

    /*n_usrtype
    n_comid
    v_name
    v_description
    n_accessusrtype
    recieve
    v_inpstat
    v_inpuser
    v_inptime
    v_auth1stat
    v_auth1user
    d_auth1time
    v_auth2stat
    v_auth2user
    d_auth2time*/
    private int n_usrtype = -2;
    private int n_comid = -2;
    private String v_comCode = "";
    private String v_name = "";
    private String v_description = "";
    private int n_accessusrtype = -1;
    private String v_accessusrtype = "";
    private String recieve = "";
    private String v_inpstat = "I";
    private String v_inpuser = "";
    private String d_inptime = "1900-01-01 12:00:00";
    private String v_auth1stat = "P";
    private String v_auth1user = "System";
    private String d_auth1time = "1900-01-01 12:00:00";
    private String v_auth2stat = "P";
    private String v_auth2user = "";
    private String d_auth2time = "1900-01-01 12:00:00";
    private String errorMessage = "";

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getV_comCode() {
        return v_comCode;
    }

    public void setV_comCode(String v_comCode) {
        this.v_comCode = v_comCode;
    }

    public String getV_accessusrtype() {
        return v_accessusrtype;
    }

    public void setV_accessusrtype(String v_accessusrtype) {
        this.v_accessusrtype = v_accessusrtype;
    }


    public String getD_auth1time() {
        return d_auth1time;
    }

    public void setD_auth1time(String d_auth1time) {
        this.d_auth1time = d_auth1time;
    }

    public String getD_auth2time() {
        return d_auth2time;
    }

    public void setD_auth2time(String d_auth2time) {
        this.d_auth2time = d_auth2time;
    }

    public int getN_accessusrtype() {
        return n_accessusrtype;
    }

    public void setN_accessusrtype(int n_accessusrtype) {
        this.n_accessusrtype = n_accessusrtype;
    }

    public int getN_comid() {
        return n_comid;
    }

    public void setN_comid(int n_comid) {
        this.n_comid = n_comid;
    }

    public int getN_usrtype() {
        return n_usrtype;
    }

    public void setN_usrtype(int n_usrtype) {
        this.n_usrtype = n_usrtype;
    }

    public String getRecieve() {
        return recieve;
    }

    public void setRecieve(String recieve) {
        this.recieve = recieve;
    }

    public String getV_auth1stat() {
        return v_auth1stat;
    }

    public void setV_auth1stat(String v_auth1stat) {
        this.v_auth1stat = v_auth1stat;
    }

    public String getV_auth1user() {
        return v_auth1user;
    }

    public void setV_auth1user(String v_auth1user) {
        this.v_auth1user = v_auth1user;
    }

    public String getV_auth2stat() {
        return v_auth2stat;
    }

    public void setV_auth2stat(String v_auth2stat) {
        this.v_auth2stat = v_auth2stat;
    }

    public String getV_auth2user() {
        return v_auth2user;
    }

    public void setV_auth2user(String v_auth2user) {
        this.v_auth2user = v_auth2user;
    }

    public String getV_description() {
        return v_description;
    }

    public void setV_description(String v_description) {
        this.v_description = v_description;
    }

    public String getV_inpstat() {
        return v_inpstat;
    }

    public void setV_inpstat(String v_inpstat) {
        this.v_inpstat = v_inpstat;
    }

    public String getD_inptime() {
        return d_inptime;
    }

    public void setD_inptime(String d_inptime) {
        this.d_inptime = d_inptime;
    }

    public String getV_inpuser() {
        return v_inpuser;
    }

    public void setV_inpuser(String v_inpuser) {
        this.v_inpuser = v_inpuser;
    }

    public String getV_name() {
        return v_name;
    }

    public void setV_name(String v_name) {
        this.v_name = v_name;
    }
}
