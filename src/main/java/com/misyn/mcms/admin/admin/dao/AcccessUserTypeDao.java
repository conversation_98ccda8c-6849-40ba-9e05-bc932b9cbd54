package com.misyn.mcms.admin.admin.dao;

import com.misyn.mcms.admin.admin.dto.AccessUserTypeDto;
import com.misyn.mcms.claim.dao.BaseDao;

import java.sql.Connection;
import java.util.List;

public interface AcccessUserTypeDao extends BaseDao<AccessUserTypeDto> {

    String SELECT_ALL = "select * from accessusrtype_mst;";

    List<AccessUserTypeDto> searchAll(Connection connection) throws Exception;
}
