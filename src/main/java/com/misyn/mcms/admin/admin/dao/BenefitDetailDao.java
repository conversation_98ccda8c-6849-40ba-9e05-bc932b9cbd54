package com.misyn.mcms.admin.admin.dao;

import com.misyn.mcms.admin.admin.dto.BenefitDetailDto;
import com.misyn.mcms.claim.dao.BaseDao;

import java.sql.Connection;
import java.util.List;

public interface BenefitDetailDao extends BaseDao<BenefitDetailDto> {

    String INSERT_INTO_BENEFIT_DETAIL_PRODUCT_WISE = "INSERT INTO benefit_detail VALUES (?,?,?);";

    String SELECT_ALL_BY_PRODUCT_ID = "SELECT * FROM benefit_detail;";

    List<BenefitDetailDto> searchAll(Connection connection) throws Exception;


    BenefitDetailDto searchMaster(Connection connection, Object id) throws Exception;


    List<BenefitDetailDto> searchByProductId(Connection connection) throws Exception;
}
