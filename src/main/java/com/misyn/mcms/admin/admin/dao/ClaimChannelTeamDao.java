package com.misyn.mcms.admin.admin.dao;

import com.misyn.mcms.admin.admin.dto.ChannelTeamDto;
import com.misyn.mcms.admin.admin.dto.ChannelTeamGridDto;
import com.misyn.mcms.admin.admin.dto.ClaimChannelDto;

import java.sql.Connection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ClaimChannelTeamDao {

    String SELECT_CHANNELS_FROM_TEAM_ID = "SELECT N_REF_NO, V_DESCRIPTION FROM claim_channel WHERE N_TEAM_ID = ?";

    String SELECT_TEAM_LIST_FOR_GRID = "SELECT cct.V_TEAM_NAME, cct.N_TEAM_ID,COUNT(DISTINCT cc.N_REF_NO) as CHANNELS, count(DISTINCT um.v_usrid) as MEMBERS, cct.D_CREATED_DATE_TIME, cct.D_LAST_UPDATED_DATE_TIME\n" +
            "FROM claim_channel_team as cct\n" +
            "INNER JOIN claim_channel as cc\n" +
            "INNER JOIN usr_mst as um\n" +
            "WHERE um.N_TEAM_ID=cc.N_TEAM_ID\n" +
            "AND cct.N_TEAM_ID != 0\n" +
            "AND cct.N_TEAM_ID=cc.N_TEAM_ID GROUP BY N_TEAM_ID\n";

    String SELECT_TEAM_LIST_WITHOUT_CHANNELS_AND_MEMBERS_FOR_GRID = "SELECT * FROM claim_channel_team\n" +
            "WHERE\n" +
            "(N_TEAM_ID NOT IN(SELECT N_TEAM_ID FROM claim_channel GROUP BY N_TEAM_ID) AND\n" +
            "N_TEAM_ID NOT IN(SELECT N_TEAM_ID FROM usr_mst GROUP BY N_TEAM_ID)) AND\n" +
            "N_TEAM_ID != 0\n" +
            "GROUP BY N_TEAM_ID";

    String SELECT_TEAM_LIST_WITH_CHANNELS_ONLY = "SELECT cct.*, COUNT(cc.V_CHANNEL_CODE) AS CHANNELS FROM claim_channel_team AS cct\n" +
            "INNER JOIN claim_channel AS cc\n" +
            "WHERE\n" +
            "cct.N_TEAM_ID=cc.N_TEAM_ID AND\n" +
            "cct.N_TEAM_ID NOT IN (SELECT N_TEAM_ID FROM usr_mst GROUP BY N_TEAM_ID) AND\n" +
            "cct.N_TEAM_ID != 0\n" +
            "GROUP BY cct.N_TEAM_ID";

    String SELECT_TEAM_LIST_WITH_MEMBERS_ONLY = "SELECT cct.*, COUNT(um.v_usrid) AS MEMBERS FROM claim_channel_team AS cct\n" +
            "INNER JOIN usr_mst AS um\n" +
            "WHERE\n" +
            "cct.N_TEAM_ID=um.N_TEAM_ID AND\n" +
            "cct.N_TEAM_ID NOT IN (SELECT N_TEAM_ID FROM claim_channel GROUP BY N_TEAM_ID) AND\n" +
            "cct.N_TEAM_ID != 0\n" +
            "GROUP BY cct.N_TEAM_ID";

    String SELECT_TEAM_CHANNEL_TEAM = "SELECT * FROM claim_channel_team where N_TEAM_ID = ?";

    String SELECT_CHANNELS_NOT_IN_TEAMS = "SELECT * FROM claim_channel WHERE (N_TEAM_ID = 0 OR N_TEAM_ID IS NULL) AND V_STATUS = 'A'";

    String UPDATE_CHANNELS = "UPDATE claim_channel SET N_TEAM_ID = ? WHERE N_REF_NO = ?";

    String SAVE_NEW_CHANNEL_TEAM = "INSERT INTO claim_channel_team values(?, ?, ?, ?, ?)";

    String GET_NEXT_TEAM_ID = "SELECT N_TEAM_ID from claim_channel_team ORDER BY N_TEAM_ID DESC LIMIT 1";

    String SELECT_ALL_FROM_CHANNEL_TEAM = "SELECT * FROM claim_channel_team";

    String CHECK_TEAM_NAME_AVAILABILITY = "SELECT V_TEAM_NAME FROM claim_channel_team WHERE V_TEAM_NAME = ?";

    String UPDATE_CHANNEL_TEAM = "UPDATE claim_channel_team SET V_TEAM_NAME = ?, V_CHANNEL_DESC = ?, D_LAST_UPDATED_DATE_TIME = ? WHERE N_TEAM_ID = ?";

    List<ClaimChannelDto> getChannelsByTeamId(Connection connection, Integer teamId) throws Exception;

    List<ChannelTeamGridDto> getTeamList(Connection connection) throws Exception;

    ChannelTeamDto getChannelTeam(Connection connection, Integer teamId) throws Exception;

    List<ClaimChannelDto> getChannelsNotInTeams(Connection connection) throws Exception;

    void updateChannels(Connection connection, Integer teamId, List<Integer> ids) throws Exception;

    Integer saveNewTeam(Connection connection, String teamName, String channelDesc) throws Exception;

    List<ChannelTeamDto> getAllTeamDetails(Connection connection) throws Exception;

    boolean checkTeamName(Connection connection, String teamName) throws Exception;

    boolean updateTeamDetails(Connection connection, ChannelTeamDto channelTeamDto) throws Exception;
}
