package com.misyn.mcms.admin.admin.dao;

import com.misyn.mcms.admin.admin.dto.ConditionAndExclusionDto;
import com.misyn.mcms.claim.dao.BaseDao;

import java.sql.Connection;
import java.util.List;

public interface ConditionAndExclusionDetailDao extends BaseDao<ConditionAndExclusionDto> {

    String SELECT_ALL_BY_PRODUCT_ID = "SELECT * FROM condition_and_exclusion_detail;";

    String INSERT_INTO_CONDITION_AND_EXCLUSION_PRODUCT_WISE = "INSERT INTO condition_and_exclusion_detail VALUES (?,?,?);";

    List<ConditionAndExclusionDto> searchAll(Connection connection) throws Exception;
    ConditionAndExclusionDto searchMaster(Connection connection, Object id) throws Exception;
    List<ConditionAndExclusionDto> searchByProductId(Connection connection) throws  Exception;

}
