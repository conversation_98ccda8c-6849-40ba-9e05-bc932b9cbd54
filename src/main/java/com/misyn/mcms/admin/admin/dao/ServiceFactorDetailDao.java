package com.misyn.mcms.admin.admin.dao;

import com.misyn.mcms.admin.admin.dto.ServiceFactorDetailDto;
import com.misyn.mcms.claim.dao.BaseDao;

import java.sql.Connection;
import java.util.List;

public interface ServiceFactorDetailDao extends BaseDao<ServiceFactorDetailDto> {
    String SELECT_ALL_BY_PRODUCT_ID = "SELECT * FROM service_factor_detail;";

    String INSERT_INTO_SERVICE_FACTOR_PRODUCT_WISE = "INSERT INTO service_factor_detail VALUES (?,?,?);";

    List<ServiceFactorDetailDto> searchAll(Connection connection) throws Exception;

    ServiceFactorDetailDto searchMaster(Connection connection, Object id) throws Exception;

    List<ServiceFactorDetailDto> searchByProductId(Connection connection) throws Exception;
}
