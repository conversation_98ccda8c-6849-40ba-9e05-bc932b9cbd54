package com.misyn.mcms.admin.admin.dao;

import com.misyn.mcms.admin.admin.dto.SpecialPackagesDto;
import com.misyn.mcms.claim.dao.BaseDao;

import java.sql.Connection;
import java.util.List;

public interface SpecialPackageDao extends BaseDao<SpecialPackagesDto> {
    String SELECT_ALL = "SELECT * FROM special_package_detail;";

    String INSERT_INTO_SPECIAL_PACKAGE_DETAIL = "INSERT INTO special_package_detail VALUES (?,?,?);";

    List<SpecialPackagesDto> searchAll(Connection connection) throws Exception;

}
