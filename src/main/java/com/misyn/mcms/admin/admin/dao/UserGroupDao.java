package com.misyn.mcms.admin.admin.dao;

import com.misyn.mcms.admin.admin.dto.UserGroupDto;
import com.misyn.mcms.claim.dao.BaseDao;

import java.sql.Connection;
import java.util.List;

public interface UserGroupDao extends BaseDao<UserGroupDto> {

    String SELECT_ALL = "SELECT *\n" +
            "FROM usr_grp_mst\n" +
            "WHERE group_status IN ('ACTIVE', 'DISABLED');";

    String INSERT_GROUP = "INSERT INTO usr_grp_mst (group_name, created_by) VALUES (?, ?)";

    List<UserGroupDto> searchAll(Connection connection) throws Exception;
}
