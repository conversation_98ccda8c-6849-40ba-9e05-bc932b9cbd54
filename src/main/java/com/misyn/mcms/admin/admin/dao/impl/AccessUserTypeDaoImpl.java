package com.misyn.mcms.admin.admin.dao.impl;

import com.misyn.mcms.admin.admin.dao.AcccessUserTypeDao;
import com.misyn.mcms.admin.admin.dto.AccessUserTypeDto;
import com.misyn.mcms.claim.dao.AbstractBaseDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class AccessUserTypeDaoImpl extends AbstractBaseDao<AccessUserTypeDaoImpl> implements AcccessUserTypeDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccessUserTypeDaoImpl.class);

    @Override
    public AccessUserTypeDto insertMaster(Connection connection, AccessUserTypeDto accessUserTypeDto) throws Exception {
        return null;
    }

    @Override
    public AccessUserTypeDto updateMaster(Connection connection, AccessUserTypeDto accessUserTypeDto) throws Exception {
        return null;
    }

    @Override
    public AccessUserTypeDto insertTemporary(Connection connection, AccessUserTypeDto accessUserTypeDto) throws Exception {
        return null;
    }

    @Override
    public AccessUserTypeDto updateTemporary(Connection connection, AccessUserTypeDto accessUserTypeDto) throws Exception {
        return null;
    }

    @Override
    public AccessUserTypeDto insertHistory(Connection connection, AccessUserTypeDto accessUserTypeDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public AccessUserTypeDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public AccessUserTypeDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<AccessUserTypeDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<AccessUserTypeDto> accessUserTypeDtoArrayList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL);
            rs = ps.executeQuery();
            while (rs.next()) {
                AccessUserTypeDto accessUserTypeDto = new AccessUserTypeDto();
                accessUserTypeDto.setId(rs.getInt("n_accessusrtype"));
                accessUserTypeDto.setName(rs.getString("v_accessusrtype"));
                accessUserTypeDto.setDescription(rs.getString("v_desc"));
                accessUserTypeDtoArrayList.add(accessUserTypeDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return accessUserTypeDtoArrayList;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return "";
    }
}
