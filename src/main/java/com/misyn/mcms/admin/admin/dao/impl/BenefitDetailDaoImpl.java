package com.misyn.mcms.admin.admin.dao.impl;

import com.misyn.mcms.admin.admin.dao.BenefitDetailDao;
import com.misyn.mcms.admin.admin.dto.BenefitDetailDto;
import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class BenefitDetailDaoImpl extends AbstractBaseDao<BenefitDetailDaoImpl> implements BenefitDetailDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(BenefitDetailDaoImpl.class);

    @Override
    public List<BenefitDetailDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public BenefitDetailDto insertMaster(Connection connection, BenefitDetailDto benefitDetailDto) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(INSERT_INTO_BENEFIT_DETAIL_PRODUCT_WISE)) {
            ps.setInt(1, AppConstant.ZERO_INT);
            ps.setString(2, benefitDetailDto.getCode());
            ps.setString(3, benefitDetailDto.getBenefitName());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return benefitDetailDto;
    }

    @Override
    public BenefitDetailDto updateMaster(Connection connection, BenefitDetailDto benefitDetailDto) throws Exception {
        return null;
    }

    @Override
    public BenefitDetailDto insertTemporary(Connection connection, BenefitDetailDto benefitDetailDto) throws Exception {
        return null;
    }

    @Override
    public BenefitDetailDto updateTemporary(Connection connection, BenefitDetailDto benefitDetailDto) throws Exception {
        return null;
    }

    @Override
    public BenefitDetailDto insertHistory(Connection connection, BenefitDetailDto benefitDetailDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public BenefitDetailDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<BenefitDetailDto> searchByProductId(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<BenefitDetailDto> benefitDetailMstDtosList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_BY_PRODUCT_ID);
            rs = ps.executeQuery();
            while (rs.next()) {
                benefitDetailMstDtosList.add(setBenefitDetail(rs));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return benefitDetailMstDtosList;
    }

    @Override
    public BenefitDetailDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    private BenefitDetailDto setBenefitDetail(ResultSet rs) throws SQLException {
        BenefitDetailDto detailDto = new BenefitDetailDto();
        detailDto.setId(rs.getInt("N_ID"));
        detailDto.setBenefitName(rs.getString("V_BENEFIT_NAME"));
        detailDto.setCode(rs.getString("V_CODE"));
        return detailDto;
    }
}
