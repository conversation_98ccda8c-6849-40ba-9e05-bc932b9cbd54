package com.misyn.mcms.admin.admin.dao.impl;

import com.misyn.mcms.admin.admin.dao.BranchMstDao;
import com.misyn.mcms.admin.admin.dto.BranchDetailDto;
import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class BranchMstDaoImpl extends AbstractBaseDao<BranchMstDaoImpl> implements BranchMstDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(BranchMstDaoImpl.class);

    @Override
    public BranchDetailDto insertMaster(Connection connection, BranchDetailDto branchDetailDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(INSERT_MASTER);
            ps.setObject(++index, branchDetailDto.getBranchCode());
            ps.setObject(++index, branchDetailDto.getBranchName());
            ps.setObject(++index, branchDetailDto.getBranchCity());
            ps.setObject(++index, branchDetailDto.getInputUser());
            ps.setObject(++index, branchDetailDto.getInputDatetime());
            ps.setObject(++index, branchDetailDto.getRecordStatus());

            if (ps.executeUpdate() > 0) {
                return branchDetailDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public BranchDetailDto updateMaster(Connection connection, BranchDetailDto branchDetailDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_MASTER);
            ps.setObject(++index, branchDetailDto.getBranchName());
            ps.setObject(++index, branchDetailDto.getBranchCity());
            ps.setObject(++index, branchDetailDto.getInputUser());
            ps.setObject(++index, branchDetailDto.getInputDatetime());
            ps.setObject(++index, branchDetailDto.getRecordStatus());
            ps.setObject(++index, branchDetailDto.getBranchCode());

            if (ps.executeUpdate() > 0) {
                return branchDetailDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public BranchDetailDto insertTemporary(Connection connection, BranchDetailDto branchDetailDto) throws Exception {
        return null;
    }

    @Override
    public BranchDetailDto updateTemporary(Connection connection, BranchDetailDto branchDetailDto) throws Exception {
        return null;
    }

    @Override
    public BranchDetailDto insertHistory(Connection connection, BranchDetailDto branchDetailDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public BranchDetailDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public BranchDetailDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<BranchDetailDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<BranchDetailDto> branchDetailDtoList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_BY_RECORD_STATUS_EQUALS_ACTIVE);
            rs = ps.executeQuery();
            while (rs.next()) {
                branchDetailDtoList.add(getBranchDetailDto(rs));
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return branchDetailDtoList;
    }

    private BranchDetailDto getBranchDetailDto(ResultSet rs) throws SQLException {
        BranchDetailDto branchDetailDto = new BranchDetailDto();
        branchDetailDto.setTxnId(rs.getInt("txn_id"));
        branchDetailDto.setBranchCode(rs.getString("branch_code"));
        branchDetailDto.setBranchName(rs.getString("branch_name"));
        branchDetailDto.setBranchCity(rs.getString("branch_city"));
        branchDetailDto.setInputUser(rs.getString("input_user"));
        branchDetailDto.setInputDatetime(rs.getString("input_datetime"));
        branchDetailDto.setRecordStatus(rs.getString("record_status"));
        return branchDetailDto;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public BranchDetailDto getBranchDetailByBranchCode(Connection connection, String branchCode) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        BranchDetailDto branchDetailDto = new BranchDetailDto();
        try {
            ps = connection.prepareStatement(SELECT_ALL_FROM_BRANCH_CODE);
            ps.setObject(1, branchCode);
            rs = ps.executeQuery();
            if (rs.next()) {
                return getBranchDetailDto(rs);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public DataGridDto getDataGridDto(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField) {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List claimList = new ArrayList(200);
        String DATE_BETWEEN = "";

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        String SQL_SEARCH1 = formatSQL1(parameterList).toString();
        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();

        final String SEL_SQL = SELECT_ALL_BY_RECORD_STATUS_EQUALS_ACTIVE.concat(SQL_SEARCH1).concat(DATE_BETWEEN).concat(SQL_ORDER);


        final String COUNT_SQL = COUNT_ALL_BY_RECORD_STATUS_EQUALS_ACTIVE.concat(SQL_SEARCH1);
        try {

            ps = connection.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }
            ps = connection.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    BranchDetailDto branchDetailDto = getBranchDetailDto(rs);
                    branchDetailDto.setIndex(++index);
                    claimList.add(branchDetailDto);
                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(claimList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public boolean isAlreadyHaveRecordByBranchCodeAndRecordStatus(Connection connection, String branchCode, String recordStatus) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_ONE_BY_BRANCH_CODE_AND_RECORD_STATUS);
            ps.setObject(1, branchCode);
            ps.setObject(2, recordStatus);
            rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return false;
    }
}
