package com.misyn.mcms.admin.admin.dao.impl;

import com.misyn.mcms.admin.admin.dao.ClaimChannelTeamDao;
import com.misyn.mcms.admin.admin.dto.ChannelTeamDto;
import com.misyn.mcms.admin.admin.dto.ChannelTeamGridDto;
import com.misyn.mcms.admin.admin.dto.ClaimChannelDto;
import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class ClaimChannelTeamDaoImpl extends AbstractBaseDao<ClaimChannelTeamDaoImpl> implements ClaimChannelTeamDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimChannelTeamDaoImpl.class);

    @Override
    public List<ClaimChannelDto> getChannelsByTeamId(Connection connection, Integer teamId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ClaimChannelDto> claimChannels = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_CHANNELS_FROM_TEAM_ID);
            ps.setInt(1, teamId);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimChannelDto claimChannelDto = new ClaimChannelDto();
                claimChannelDto.setRefNo(rs.getInt("N_REF_NO"));
                claimChannelDto.setChannelDesc(rs.getString("V_DESCRIPTION"));
                claimChannels.add(claimChannelDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            if (ps != null) {
                ps.close();
            }
            if (rs != null) {
                rs.close();
            }
            return claimChannels.size() > 0 ? claimChannels : null;
        }
    }

    @Override
    public List<ChannelTeamGridDto> getTeamList(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        Integer index = 0;
        List<ChannelTeamGridDto> teamList = new ArrayList();
        try {
            ps = connection.prepareStatement(SELECT_TEAM_LIST_FOR_GRID);
            rs = ps.executeQuery();
            while (rs.next()) {
                ChannelTeamGridDto gridDto = new ChannelTeamGridDto();
                gridDto.setTeamName(rs.getString("V_TEAM_NAME"));
                gridDto.setTeamId(rs.getInt("N_TEAM_ID"));
                gridDto.setChannelCount(rs.getInt("CHANNELS"));
                gridDto.setMemberCount(rs.getInt("MEMBERS"));
                gridDto.setCreatedDate(Utility.getDate(rs.getString("D_CREATED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                gridDto.setUpdatedDate(Utility.getDate(rs.getString("D_LAST_UPDATED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                gridDto.setIndex(++index);
                teamList.add(gridDto);
            }
            ps.close();
            rs.close();
            teamList = getTeamsWithChannelsOnly(connection, teamList);
            teamList = getTeamsWithMembersOnly(connection, teamList);
            teamList = getTeamsWithoutChannelsOrMembers(connection, teamList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return teamList.size() > 0 ? teamList : null;
    }

    List<ChannelTeamGridDto> getTeamsWithoutChannelsOrMembers(Connection connection, List<ChannelTeamGridDto> teamList) throws Exception {
        try {
            PreparedStatement ps = connection.prepareStatement(SELECT_TEAM_LIST_WITHOUT_CHANNELS_AND_MEMBERS_FOR_GRID);
            ResultSet rs = ps.executeQuery();
            Integer index = teamList.size() > 0 ? teamList.get(teamList.size() - 1).getIndex() : AppConstant.ZERO_INT;
            while (rs.next()) {
                ChannelTeamGridDto gridDto = new ChannelTeamGridDto();
                gridDto.setTeamName(rs.getString("V_TEAM_NAME"));
                gridDto.setTeamId(rs.getInt("N_TEAM_ID"));
                gridDto.setChannelCount(0);
                gridDto.setMemberCount(0);
                gridDto.setCreatedDate(Utility.getDate(rs.getString("D_CREATED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                gridDto.setUpdatedDate(Utility.getDate(rs.getString("D_LAST_UPDATED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                gridDto.setIndex(++index);
                teamList.add(gridDto);
            }
            ps.close();
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return teamList;
    }

    List<ChannelTeamGridDto> getTeamsWithChannelsOnly(Connection connection, List<ChannelTeamGridDto> teamList) throws Exception {
        try {
            PreparedStatement ps = connection.prepareStatement(SELECT_TEAM_LIST_WITH_CHANNELS_ONLY);
            ResultSet rs = ps.executeQuery();
            Integer index = teamList.size() > 0 ? teamList.get(teamList.size() - 1).getIndex() : AppConstant.ZERO_INT;
            while (rs.next()) {
                ChannelTeamGridDto gridDto = new ChannelTeamGridDto();
                gridDto.setTeamName(rs.getString("V_TEAM_NAME"));
                gridDto.setTeamId(rs.getInt("N_TEAM_ID"));
                gridDto.setChannelCount(rs.getInt("CHANNELS"));
                gridDto.setMemberCount(0);
                gridDto.setCreatedDate(Utility.getDate(rs.getString("D_CREATED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                gridDto.setUpdatedDate(Utility.getDate(rs.getString("D_LAST_UPDATED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                gridDto.setIndex(++index);
                teamList.add(gridDto);
            }
            ps.close();
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return teamList;
    }

    List<ChannelTeamGridDto> getTeamsWithMembersOnly(Connection connection, List<ChannelTeamGridDto> teamList) throws Exception {
        try {
            PreparedStatement ps = connection.prepareStatement(SELECT_TEAM_LIST_WITH_MEMBERS_ONLY);
            ResultSet rs = ps.executeQuery();
            Integer index = teamList.size() > 0 ? teamList.get(teamList.size() - 1).getIndex() : AppConstant.ZERO_INT;
            while (rs.next()) {
                ChannelTeamGridDto gridDto = new ChannelTeamGridDto();
                gridDto.setTeamName(rs.getString("V_TEAM_NAME"));
                gridDto.setTeamId(rs.getInt("N_TEAM_ID"));
                gridDto.setChannelCount(0);
                gridDto.setMemberCount(rs.getInt("MEMBERS"));
                gridDto.setCreatedDate(Utility.getDate(rs.getString("D_CREATED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                gridDto.setUpdatedDate(Utility.getDate(rs.getString("D_LAST_UPDATED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                gridDto.setIndex(++index);
                teamList.add(gridDto);
            }
            ps.close();
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return teamList;
    }

    @Override
    public ChannelTeamDto getChannelTeam(Connection connection, Integer teamId) throws Exception {
        ChannelTeamDto channelTeamDto = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_TEAM_CHANNEL_TEAM);
            ps.setInt(1, teamId);
            rs = ps.executeQuery();
            while (rs.next()) {
                channelTeamDto = new ChannelTeamDto();
                channelTeamDto.setTeamId(rs.getInt("N_TEAM_ID"));
                channelTeamDto.setTeamName(rs.getString("V_TEAM_NAME"));
                channelTeamDto.setChannelDesc(rs.getString("V_CHANNEL_DESC"));
                channelTeamDto.setCreatedDate(rs.getString("D_CREATED_DATE_TIME"));
                channelTeamDto.setUpdatedDate(rs.getString("D_LAST_UPDATED_DATE_TIME"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return channelTeamDto;
    }

    @Override
    public List<ClaimChannelDto> getChannelsNotInTeams(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ClaimChannelDto> channelList = new ArrayList();
        Integer index = 0;
        try {
            ps = connection.prepareStatement(SELECT_CHANNELS_NOT_IN_TEAMS);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimChannelDto claimChannelDto = new ClaimChannelDto();
                claimChannelDto.setRefNo(rs.getInt("N_REF_NO"));
                claimChannelDto.setChannelDesc(rs.getString("V_DESCRIPTION"));
                claimChannelDto.setIndex(++index);
                channelList.add(claimChannelDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return channelList.size() > 0 ? channelList : null;
    }

    @Override
    public void updateChannels(Connection connection, Integer teamId, List<Integer> ids) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(UPDATE_CHANNELS);
            for (Integer id : ids) {
                ps.setInt(1, teamId);
                ps.setInt(2, id);
                ps.executeUpdate();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer saveNewTeam(Connection connection, String teamName, String channelDesc) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(GET_NEXT_TEAM_ID);
            ResultSet resultSet = ps.executeQuery();
            Integer lastTeamId = 0;
            if (resultSet.next()) {
                lastTeamId = resultSet.getInt("N_TEAM_ID");
            }
            ps.close();

            ps = connection.prepareStatement(SAVE_NEW_CHANNEL_TEAM);
            ps.setInt(1, ++lastTeamId);
            ps.setString(2, teamName);
            ps.setString(3, channelDesc);
            ps.setString(4, Utility.sysDateTime());
            ps.setString(5, Utility.sysDateTime());
            if (ps.executeUpdate() > 0) {
                return lastTeamId;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return null;
    }

    @Override
    public List<ChannelTeamDto> getAllTeamDetails(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ChannelTeamDto> teams = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_FROM_CHANNEL_TEAM);
            rs = ps.executeQuery();
            while (rs.next()) {
                ChannelTeamDto channelTeamDto = new ChannelTeamDto();
                channelTeamDto.setTeamId(rs.getInt("N_TEAM_ID"));
                channelTeamDto.setTeamName(rs.getString("V_TEAM_NAME"));
                channelTeamDto.setChannelDesc(rs.getString("V_CHANNEL_DESC"));
                channelTeamDto.setCreatedDate(rs.getString("D_CREATED_DATE_TIME"));
                channelTeamDto.setUpdatedDate(rs.getString("D_LAST_UPDATED_DATE_TIME"));
                teams.add(channelTeamDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return teams;
    }

    @Override
    public boolean checkTeamName(Connection connection, String teamName) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(CHECK_TEAM_NAME_AVAILABILITY);
            ps.setString(1, teamName);
            rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
            ps.close();
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return false;
    }

    @Override
    public boolean updateTeamDetails(Connection connection, ChannelTeamDto channelTeamDto) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_CHANNEL_TEAM);
            ps.setString(1, channelTeamDto.getTeamName());
            ps.setString(2, channelTeamDto.getChannelDesc());
            ps.setString(3, Utility.sysDateTime());
            ps.setInt(4, channelTeamDto.getTeamId());
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }
}
