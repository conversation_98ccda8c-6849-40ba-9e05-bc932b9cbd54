package com.misyn.mcms.admin.admin.dao.impl;

import com.misyn.mcms.admin.admin.dao.ConditionAndExclusionDetailDao;
import com.misyn.mcms.admin.admin.dto.ConditionAndExclusionDto;
import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class ConditionAndExclusionDetailDaoImpl extends AbstractBaseDao<ConditionAndExclusionDetailDaoImpl> implements ConditionAndExclusionDetailDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConditionAndExclusionDetailDaoImpl.class);
    @Override
    public List<ConditionAndExclusionDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public ConditionAndExclusionDto insertMaster(Connection connection, ConditionAndExclusionDto conditionAndExclusionDto) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(INSERT_INTO_CONDITION_AND_EXCLUSION_PRODUCT_WISE)) {
            ps.setInt(1, AppConstant.ZERO_INT);
            ps.setString(2, conditionAndExclusionDto.getCode());
            ps.setString(3, conditionAndExclusionDto.getcAndEName());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return conditionAndExclusionDto;
    }

    @Override
    public ConditionAndExclusionDto updateMaster(Connection connection, ConditionAndExclusionDto conditionAndExclusionDto) throws Exception {
        return null;
    }

    @Override
    public ConditionAndExclusionDto insertTemporary(Connection connection, ConditionAndExclusionDto conditionAndExclusionDto) throws Exception {
        return null;
    }

    @Override
    public ConditionAndExclusionDto updateTemporary(Connection connection, ConditionAndExclusionDto conditionAndExclusionDto) throws Exception {
        return null;
    }

    @Override
    public ConditionAndExclusionDto insertHistory(Connection connection, ConditionAndExclusionDto conditionAndExclusionDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ConditionAndExclusionDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ConditionAndExclusionDto> searchByProductId(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ConditionAndExclusionDto> conditionAndExclusionDtoList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_BY_PRODUCT_ID);
            rs = ps.executeQuery();
            while (rs.next()) {
                conditionAndExclusionDtoList.add(setConditionAndExclusionDetail(rs));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return conditionAndExclusionDtoList;
    }

    @Override
    public ConditionAndExclusionDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }
    private ConditionAndExclusionDto setConditionAndExclusionDetail(ResultSet rs) throws SQLException {
        ConditionAndExclusionDto detailDto = new ConditionAndExclusionDto();
        detailDto.setId(rs.getInt("N_C_E_ID"));
        detailDto.setcAndEName(rs.getString("V_C_E_NAME"));
        detailDto.setCode(rs.getString("V_CODE"));
        return detailDto;
    }
}
