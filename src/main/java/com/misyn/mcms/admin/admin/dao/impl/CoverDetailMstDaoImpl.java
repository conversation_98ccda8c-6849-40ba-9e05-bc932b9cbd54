package com.misyn.mcms.admin.admin.dao.impl;

import com.misyn.mcms.admin.admin.dao.CoverDetailMstDao;
import com.misyn.mcms.admin.admin.dto.CoverDetailMstDto;
import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class CoverDetailMstDaoImpl extends AbstractBaseDao<CoverDetailMstDaoImpl> implements CoverDetailMstDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(CoverDetailMstDaoImpl.class);

    @Override
    public List<CoverDetailMstDto> searchAll(Connection connection) throws Exception {
       return null;
    }

    @Override
    public List<CoverDetailMstDto> searchAllBenefitCoverLoadingDetail(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs;
        List<CoverDetailMstDto> benefitCoverLoadingDetail = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_BENEFIT_COVER_DETAIL);
            rs = ps.executeQuery();
            while (rs.next()) {
                benefitCoverLoadingDetail.add(setCoverDetailMain(rs));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
        return benefitCoverLoadingDetail;
    }

    @Override
    public List<CoverDetailMstDto> searchAllCWEDetail(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs;
        List<CoverDetailMstDto> cWEDetailList= new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_CWE_DETAIL);
            rs = ps.executeQuery();
            while (rs.next()) {
                cWEDetailList.add(setCoverDetailMain(rs));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
        return cWEDetailList;
    }

    @Override
    public List<CoverDetailMstDto> searchAllSrccTcDetail(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs;
        List<CoverDetailMstDto> srccTcDetail= new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_SRCCTC_MST);
            rs = ps.executeQuery();
            while (rs.next()) {
                srccTcDetail.add(setCoverDetailMain(rs));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
        return srccTcDetail;
    }

    @Override
    public List<CoverDetailMstDto> searchAllChargesAndDiscountDetail(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs;
        List<CoverDetailMstDto> chargesAndDiscountDetail = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_CHARGES_DETAIL);
            rs = ps.executeQuery();
            while (rs.next()) {
                chargesAndDiscountDetail.add(setCoverDetailMain(rs));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
        return chargesAndDiscountDetail;
    }

    @Override
    public void EmptyAllCoverDetail(Connection connection) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(EMPTY_ALL_COVER_TABLE)) {
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public void EmptyAllBenefitDetail(Connection connection) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(EMPTY_ALL_BENEFIT_TABLE)) {
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public void EmptyAllServiceFactorDetail(Connection connection) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(EMPTY_ALL_SERVICE_FACTOR_TABLE)) {
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public void EmptyAllSpecialPackageDetail(Connection connection) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(EMPTY_ALL_SPECIAL_PACKAGE_TABLE)) {
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public void EmptyAllConditionDetail(Connection connection) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(EMPTY_ALL_CONDITION_TABLE)) {
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public List<CoverDetailMstDto> searchAllChargesAndDiscountDetail(Connection connection, String code, String name) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs;
        int index = AppConstant.ZERO_INT;
        List<CoverDetailMstDto> chargesAndDiscountDetail = new ArrayList<>();
        StringBuilder para = setCoverSearchPara(code,name);
        try {
            ps = connection.prepareStatement(SEARCH_ALL_CHARGES_DETAIL+para);
            setSalientPara(code, name, ps, index, chargesAndDiscountDetail);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
        return chargesAndDiscountDetail;
    }

    @Override
    public List<CoverDetailMstDto> searchAllBenefitCoverLoadingDetailMater(Connection connection, String code, String name) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs;
        int index = AppConstant.ZERO_INT;
        List<CoverDetailMstDto> benefitCoverLoadingDetail = new ArrayList<>();
        StringBuilder para = setCoverSearchPara(code,name);
        try {
            ps = connection.prepareStatement(SEARCH_ALL_BENEFIT_COVER_DETAIL.concat(para.toString()));
            setSalientPara(code, name, ps, index, benefitCoverLoadingDetail);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
        return benefitCoverLoadingDetail;
    }

    private void setSalientPara(String code, String name, PreparedStatement ps, int index, List<CoverDetailMstDto> benefitCoverLoadingDetail) throws SQLException {
        ResultSet rs;
        if(!AppConstant.EMPTY_STRING.equals(code)){
              ps.setString(++index,"%"+code+"%");
          }
        if(!AppConstant.EMPTY_STRING.equals(name)){
            ps.setString(++index,"%"+name+"%");
        }
        rs = ps.executeQuery();
        while (rs.next()) {
            benefitCoverLoadingDetail.add(setCoverDetailMain(rs));
        }
    }

    @Override
    public List<CoverDetailMstDto> searchAllCWEDetailMater(Connection connection, String code, String name) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs;
        int index = AppConstant.ZERO_INT;
        List<CoverDetailMstDto> cWEDetailList= new ArrayList<>();
        StringBuilder para = setCoverSearchPara(code,name);
        try {
            ps = connection.prepareStatement(SEARCH_ALL_CWE_DETAIL+para);
            setSalientPara(code, name, ps, index, cWEDetailList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
        return cWEDetailList;
    }

    @Override
    public void updateAllChargesAndDiscountDetailByListIn(Connection connection, List<String> codeList,String status) throws Exception {
        PreparedStatement ps = null;
        try {

            String placeholders = generatePlaceholders(codeList.size());
            ps = connection.prepareStatement(UPDATE_ALL_CHARGES_DETAIL + "(" + placeholders + ")");
            for (int i = 0; i < codeList.size(); i++) {
                ps.setString(i + 1, codeList.get(i));
            }
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
    }

    @Override
    public void updateAllBenefitCoverLoadingDetailMaterByListIn(Connection connection, List<String> codeList,String status) throws Exception {
        PreparedStatement ps = null;
        try {
            String placeholders = generatePlaceholders(codeList.size());
            ps = connection.prepareStatement(UPDATE_ALL_BENEFIT_COVER_DETAIL + "(" + placeholders + ")");
             for (int i = 0; i < codeList.size(); i++) {
                ps.setString(i + 1, codeList.get(i));
            }
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
    }

    @Override
    public void updateAllCWEDetailMaterByListIn(Connection connection, List<String> codeList,String status) throws Exception {
        PreparedStatement ps = null;
        try {
            String placeholders = generatePlaceholders(codeList.size());
            ps = connection.prepareStatement(UPDATE_ALL_CWE_DETAIL + "(" + placeholders + ")");
             for (int i = 0; i < codeList.size(); i++) {
                ps.setString(i + 1, codeList.get(i));
            }
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
    }

    @Override
    public void updateAllSRCCTcDetailMaterByListIn(Connection connection, List<String> codeList,String status) throws Exception {
        PreparedStatement ps = null;
        try {
            String placeholders = generatePlaceholders(codeList.size());
            ps = connection.prepareStatement(UPDATE_ALL_SRCC_TC_DETAIL + "(" + placeholders + ")");
             for (int i = 0; i < codeList.size(); i++) {
                ps.setString(i + 1, codeList.get(i));
            }
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
    }

    @Override
    public void updateStatusChargesAndDiscountDetail(Connection connection, String status) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_ALL_CHARGES_DETAIL_STATUS);
            ps.setString(1,status);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
    }

    @Override
    public void updateStatusBefitCoverLoadingDetailMater(Connection connection, String status) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_ALL_BENEFIT_COVER_DETAIL_STATUS);
            ps.setString(1,status);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
    }

    @Override
    public void updateStatusCWEDetailMater(Connection connection, String status) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_ALL_CWE_DETAIL_STATUS);
            ps.setString(1,status);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
    }

    @Override
    public void updateStatusSRCCTcDetailMater(Connection connection, String status) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_ALL_SRCC_TC_DETAIL_STATUS);
            ps.setString(1,status);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public CoverDetailMstDto insertMaster(Connection connection, CoverDetailMstDto coverDetailMstDto) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(INSERT_INTO_COVER_PRODUCT_WISE)) {
            ps.setInt(1, AppConstant.ZERO_INT);
            ps.setString(2, coverDetailMstDto.getCode());
            ps.setString(3, coverDetailMstDto.getCoverName());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return coverDetailMstDto;
    }

    @Override
    public CoverDetailMstDto updateMaster(Connection connection, CoverDetailMstDto coverDetailMstDto) throws Exception {
        return null;
    }

    @Override
    public CoverDetailMstDto insertTemporary(Connection connection, CoverDetailMstDto coverDetailMstDto) throws Exception {
        return null;
    }

    @Override
    public CoverDetailMstDto updateTemporary(Connection connection, CoverDetailMstDto coverDetailMstDto) throws Exception {
        return null;
    }

    @Override
    public CoverDetailMstDto insertHistory(Connection connection, CoverDetailMstDto coverDetailMstDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public CoverDetailMstDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<CoverDetailMstDto> searchAdminCoverDetail(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs;
        List<CoverDetailMstDto> coverDetailMstDtosList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_BY_PRODUCT_ID);
            rs = ps.executeQuery();
            while (rs.next()) {
                coverDetailMstDtosList.add(setCoverDetail(rs));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
        return coverDetailMstDtosList;
    }

    @Override
    public CoverDetailMstDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    private CoverDetailMstDto setCoverDetail(ResultSet rs) throws SQLException {
        CoverDetailMstDto detailDto = new CoverDetailMstDto();
        detailDto.setId(rs.getInt("N_COVER_ID"));
        detailDto.setCoverName(rs.getString("V_COVER_NAME"));
        detailDto.setCode(rs.getString("V_CODE"));
        return detailDto;
    }

    private CoverDetailMstDto setCoverDetailMain(ResultSet rs) throws SQLException {
        CoverDetailMstDto detailDto = new CoverDetailMstDto();
        detailDto.setId(rs.getInt("N_ID"));
        detailDto.setCode(rs.getString("V_CODE"));
        detailDto.setCoverName(rs.getString("V_NAME"));
        detailDto.setInsertDate(rs.getString("D_INSERT_DATE_TIME"));
        detailDto.setUpdateDate(rs.getString("D_UPDATE_DATE_TIME"));
        detailDto.setStatus(rs.getString("IS_UPDATED"));
        return detailDto;
    }

    private StringBuilder setCoverSearchPara(String code, String name) {
        int index = AppConstant.ZERO_INT;
        StringBuilder search = new StringBuilder((code.isEmpty() && name.isEmpty()) ?  "  " :" WHERE");
        String orderBy = " ORDER BY CASE WHEN `IS_UPDATED` = 'N' THEN 0 ELSE 1 END, `N_ID`";
        if(!AppConstant.EMPTY_STRING.equals(code)){
            ++index;
            search.append(" V_CODE LIKE ?");
        }
        if(!AppConstant.EMPTY_STRING.equals(name)){
            search.append(index > 0 ? " OR V_NAME LIKE ?" : " V_NAME LIKE ?");
        }
        return search.append(orderBy);
    }

    private static String generatePlaceholders(int size) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < size; i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append("?");
        }
        return sb.toString();
    }

}
