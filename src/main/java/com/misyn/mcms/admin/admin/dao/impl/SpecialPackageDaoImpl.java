package com.misyn.mcms.admin.admin.dao.impl;

import com.misyn.mcms.admin.admin.dao.SpecialPackageDao;
import com.misyn.mcms.admin.admin.dto.SpecialPackagesDto;
import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class SpecialPackageDaoImpl extends AbstractBaseDao<SpecialPackageDaoImpl> implements SpecialPackageDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(SpecialPackageDaoImpl.class);


    @Override
    public SpecialPackagesDto insertMaster(Connection connection, SpecialPackagesDto specialPackagesDto) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(INSERT_INTO_SPECIAL_PACKAGE_DETAIL)) {
            ps.setInt(1, AppConstant.ZERO_INT);
            ps.setString(2, specialPackagesDto.getCode());
            ps.setString(3, specialPackagesDto.getName());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return specialPackagesDto;
    }

    @Override
    public SpecialPackagesDto updateMaster(Connection connection, SpecialPackagesDto specialPackagesDto) throws Exception {
        return null;
    }

    @Override
    public SpecialPackagesDto insertTemporary(Connection connection, SpecialPackagesDto specialPackagesDto) throws Exception {
        return null;
    }

    @Override
    public SpecialPackagesDto updateTemporary(Connection connection, SpecialPackagesDto specialPackagesDto) throws Exception {
        return null;
    }

    @Override
    public SpecialPackagesDto insertHistory(Connection connection, SpecialPackagesDto specialPackagesDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public SpecialPackagesDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public SpecialPackagesDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<SpecialPackagesDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<SpecialPackagesDto> serviceFactorDetailDtoList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL);
            rs = ps.executeQuery();
            while (rs.next()) {
                serviceFactorDetailDtoList.add(setServiceFactorDetailDto(rs));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return serviceFactorDetailDtoList;
    }

    private SpecialPackagesDto setServiceFactorDetailDto(ResultSet rs) throws SQLException {
        SpecialPackagesDto detailDto = new SpecialPackagesDto();
        detailDto.setId(rs.getInt("N_ID"));
        detailDto.setName(rs.getString("V_NAME"));
        detailDto.setCode(rs.getString("V_CODE"));
        return detailDto;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }
}
