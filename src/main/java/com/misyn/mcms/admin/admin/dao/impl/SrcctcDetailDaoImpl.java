package com.misyn.mcms.admin.admin.dao.impl;

import com.misyn.mcms.admin.admin.dao.SrcctcDetailDao;
import com.misyn.mcms.admin.admin.dto.SrcctcDetailDto;
import com.misyn.mcms.claim.dao.AbstractBaseDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class SrcctcDetailDaoImpl extends AbstractBaseDao<SrcctcDetailDaoImpl> implements SrcctcDetailDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(SrcctcDetailDaoImpl.class);
    @Override
    public List<SrcctcDetailDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<SrcctcDetailDto> serviceFactorDetailDtoList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL);
            rs = ps.executeQuery();
            while (rs.next()) {
                serviceFactorDetailDtoList.add(setSrcctcDetail(rs));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;

        }
        return serviceFactorDetailDtoList;
    }
    private SrcctcDetailDto setSrcctcDetail(ResultSet rs) throws SQLException {
        SrcctcDetailDto detailDto = new SrcctcDetailDto();
        detailDto.setId(rs.getInt("N_ID"));
        detailDto.setName(rs.getString("V_NAME"));
        detailDto.setCode(rs.getString("V_CODE"));
        return detailDto;
    }
}
