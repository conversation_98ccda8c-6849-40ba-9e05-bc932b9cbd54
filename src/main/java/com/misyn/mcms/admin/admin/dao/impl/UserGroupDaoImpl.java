package com.misyn.mcms.admin.admin.dao.impl;

import com.misyn.mcms.admin.admin.dao.UserGroupDao;
import com.misyn.mcms.admin.admin.dto.UserGroupDto;
import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class UserGroupDaoImpl extends AbstractBaseDao<UserGroupDaoImpl> implements UserGroupDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserTypeDaoImpl.class);

    @Override
    public UserGroupDto updateMaster(Connection connection, UserGroupDto userRoleDto) throws Exception {
        return null;
    }

    @Override
    public UserGroupDto insertTemporary(Connection connection, UserGroupDto userRoleDto) throws Exception {
        return null;
    }

    @Override
    public UserGroupDto updateTemporary(Connection connection, UserGroupDto userRoleDto) throws Exception {
        return null;
    }

    @Override
    public UserGroupDto insertHistory(Connection connection, UserGroupDto userRoleDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public UserGroupDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public UserGroupDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<UserGroupDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<UserGroupDto> userGroupDtoArrayList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL);
            rs = ps.executeQuery();
            while (rs.next()) {
                UserGroupDto userRoleDto = new UserGroupDto();
                userRoleDto.setGroupId(rs.getInt("group_id"));
                userRoleDto.setGroupKeyCloakId(rs.getInt("group_key_cloak_id"));
                userRoleDto.setGroupName(rs.getString("group_name"));
                userRoleDto.setStatus(rs.getString("group_status"));
                userGroupDtoArrayList.add(userRoleDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return userGroupDtoArrayList;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return "";
    }

    @Override
    public UserGroupDto insertMaster(Connection connection, UserGroupDto userGroupDto) throws Exception {
        PreparedStatement ps;
        int result;
        int index = 0;
        try {
            ps = connection.prepareStatement(INSERT_GROUP);
            ps.setString(++index, userGroupDto.getGroupName());
            ps.setString(++index, userGroupDto.getCreatedBy());
            result = ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }
}