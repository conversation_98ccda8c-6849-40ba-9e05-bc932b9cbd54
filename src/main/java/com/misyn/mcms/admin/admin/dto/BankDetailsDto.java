package com.misyn.mcms.admin.admin.dto;

import com.misyn.mcms.utility.ListBoxItem;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
public class BankDetailsDto implements Serializable {
    private Integer id;
    private Integer claimNo;
    private Integer instrumentType;
    private Integer payeeType;
    private String payeeName;
    private String uploadStatus;
    private String updateStatus;
    private String verifyStatus;
    private String inputUser;
    private String verifyUser;
    private String rejectUser;
    private String lastUpdateUser;
    private LocalDateTime inputDateTime;
    private LocalDateTime verifyDateTime;
    private LocalDateTime rejectDateTime;
    private LocalDateTime lastUpdateDateTime;
    private Integer docRefNo;
    private LocalDateTime docUpdateDateTime;
    private String policyNumber;
    private Integer renCount;
    private Integer endCount;
    private String policyChannelType;
    private String customerName;
    private String customerNIC;
    private String loggedUser;
    private Integer loggedUserType;
    private List<ListBoxItem> payeeNameList;
    private String instrumentTypeDesc;
    private String payeeTypeDesc;
    private String payeeNameDesc;

    public BankDetailsDto() {
    }

    public BankDetailsDto(Integer id, Integer claimNo, Integer instrumentType, Integer payeeType, String payeeName, String uploadStatus, String updateStatus, String verifyStatus, String inputUser, String verifyUser, String rejectUser, String lastUpdateUser, LocalDateTime inputDateTime, LocalDateTime verifyDateTime, LocalDateTime rejectDateTime, LocalDateTime lastUpdateDateTime, Integer docRefNo, LocalDateTime docUpdateDateTime, String policyNumber, Integer renCount, Integer endCount, String policyChannelType, String customerName, String customerNIC, String loggedUser, Integer loggedUserType, List<ListBoxItem> payeeNameList, String instrumentTypeDesc, String payeeTypeDesc, String payeeNameDesc) {
        this.id = id;
        this.claimNo = claimNo;
        this.instrumentType = instrumentType;
        this.payeeType = payeeType;
        this.payeeName = payeeName;
        this.uploadStatus = uploadStatus;
        this.updateStatus = updateStatus;
        this.verifyStatus = verifyStatus;
        this.inputUser = inputUser;
        this.verifyUser = verifyUser;
        this.rejectUser = rejectUser;
        this.lastUpdateUser = lastUpdateUser;
        this.inputDateTime = inputDateTime;
        this.verifyDateTime = verifyDateTime;
        this.rejectDateTime = rejectDateTime;
        this.lastUpdateDateTime = lastUpdateDateTime;
        this.docRefNo = docRefNo;
        this.docUpdateDateTime = docUpdateDateTime;
        this.policyNumber = policyNumber;
        this.renCount = renCount;
        this.endCount = endCount;
        this.policyChannelType = policyChannelType;
        this.customerName = customerName;
        this.customerNIC = customerNIC;
        this.loggedUser = loggedUser;
        this.loggedUserType = loggedUserType;
        this.payeeNameList = payeeNameList;
        this.instrumentTypeDesc = instrumentTypeDesc;
        this.payeeTypeDesc = payeeTypeDesc;
        this.payeeNameDesc = payeeNameDesc;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getInstrumentType() {
        return instrumentType;
    }

    public void setInstrumentType(Integer instrumentType) {
        this.instrumentType = instrumentType;
    }

    public Integer getPayeeType() {
        return payeeType;
    }

    public void setPayeeType(Integer payeeType) {
        this.payeeType = payeeType;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(String uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public String getUpdateStatus() {
        return updateStatus;
    }

    public void setUpdateStatus(String updateStatus) {
        this.updateStatus = updateStatus;
    }

    public String getVerifyStatus() {
        return verifyStatus;
    }

    public void setVerifyStatus(String verifyStatus) {
        this.verifyStatus = verifyStatus;
    }

    public String getInputUser() {
        return inputUser;
    }

    public void setInputUser(String inputUser) {
        this.inputUser = inputUser;
    }

    public String getVerifyUser() {
        return verifyUser;
    }

    public void setVerifyUser(String verifyUser) {
        this.verifyUser = verifyUser;
    }

    public String getRejectUser() {
        return rejectUser;
    }

    public void setRejectUser(String rejectUser) {
        this.rejectUser = rejectUser;
    }

    public String getLastUpdateUser() {
        return lastUpdateUser;
    }

    public void setLastUpdateUser(String lastUpdateUser) {
        this.lastUpdateUser = lastUpdateUser;
    }

    public LocalDateTime getInputDateTime() {
        return inputDateTime;
    }

    public void setInputDateTime(LocalDateTime inputDateTime) {
        this.inputDateTime = inputDateTime;
    }

    public LocalDateTime getVerifyDateTime() {
        return verifyDateTime;
    }

    public void setVerifyDateTime(LocalDateTime verifyDateTime) {
        this.verifyDateTime = verifyDateTime;
    }

    public LocalDateTime getRejectDateTime() {
        return rejectDateTime;
    }

    public void setRejectDateTime(LocalDateTime rejectDateTime) {
        this.rejectDateTime = rejectDateTime;
    }

    public LocalDateTime getLastUpdateDateTime() {
        return lastUpdateDateTime;
    }

    public void setLastUpdateDateTime(LocalDateTime lastUpdateDateTime) {
        this.lastUpdateDateTime = lastUpdateDateTime;
    }

    public Integer getDocRefNo() {
        return docRefNo;
    }

    public void setDocRefNo(Integer docRefNo) {
        this.docRefNo = docRefNo;
    }

    public LocalDateTime getDocUpdateDateTime() {
        return docUpdateDateTime;
    }

    public void setDocUpdateDateTime(LocalDateTime docUpdateDateTime) {
        this.docUpdateDateTime = docUpdateDateTime;
    }

    public String getPolicyNumber() {
        return policyNumber;
    }

    public void setPolicyNumber(String policyNumber) {
        this.policyNumber = policyNumber;
    }

    public Integer getRenCount() {
        return renCount;
    }

    public void setRenCount(Integer renCount) {
        this.renCount = renCount;
    }

    public Integer getEndCount() {
        return endCount;
    }

    public void setEndCount(Integer endCount) {
        this.endCount = endCount;
    }

    public String getPolicyChannelType() {
        return policyChannelType;
    }

    public void setPolicyChannelType(String policyChannelType) {
        this.policyChannelType = policyChannelType;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerNIC() {
        return customerNIC;
    }

    public void setCustomerNIC(String customerNIC) {
        this.customerNIC = customerNIC;
    }

    public String getLoggedUser() {
        return loggedUser;
    }

    public void setLoggedUser(String loggedUser) {
        this.loggedUser = loggedUser;
    }

    public Integer getLoggedUserType() {
        return loggedUserType;
    }

    public void setLoggedUserType(Integer loggedUserType) {
        this.loggedUserType = loggedUserType;
    }

    public List<ListBoxItem> getPayeeNameList() {
        return payeeNameList;
    }

    public void setPayeeNameList(List<ListBoxItem> payeeNameList) {
        this.payeeNameList = payeeNameList;
    }

    public String getInstrumentTypeDesc() {
        return instrumentTypeDesc;
    }

    public void setInstrumentTypeDesc(String instrumentTypeDesc) {
        this.instrumentTypeDesc = instrumentTypeDesc;
    }

    public String getPayeeTypeDesc() {
        return payeeTypeDesc;
    }

    public void setPayeeTypeDesc(String payeeTypeDesc) {
        this.payeeTypeDesc = payeeTypeDesc;
    }

    public String getPayeeNameDesc() {
        return payeeNameDesc;
    }

    public void setPayeeNameDesc(String payeeNameDesc) {
        this.payeeNameDesc = payeeNameDesc;
    }
}
