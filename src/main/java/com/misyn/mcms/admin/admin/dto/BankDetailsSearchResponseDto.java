package com.misyn.mcms.admin.admin.dto;

import com.misyn.mcms.claim.dto.ClaimCalculationSheetPayeeNameDto;
import com.misyn.mcms.utility.ListBoxItem;

import java.io.Serializable;
import java.util.List;
public class BankDetailsSearchResponseDto implements Serializable {
    private List<BankDetailsDto> bankDetailsDtoList;
    private List<ClaimCalculationSheetPayeeNameDto> payeeTypeList;
    private List<ListBoxItem> payeeNameList;
    private Integer payeeTypeId;
    private String payeeNameId;
    private Integer lastID;

    public BankDetailsSearchResponseDto() {
    }

    public BankDetailsSearchResponseDto(List<BankDetailsDto> bankDetailsDtoList, List<ClaimCalculationSheetPayeeNameDto> payeeTypeList, List<ListBoxItem> payeeNameList, Integer payeeTypeId, String payeeNameId, Integer lastID) {
        this.bankDetailsDtoList = bankDetailsDtoList;
        this.payeeTypeList = payeeTypeList;
        this.payeeNameList = payeeNameList;
        this.payeeTypeId = payeeTypeId;
        this.payeeNameId = payeeNameId;
        this.lastID = lastID;
    }

    public List<BankDetailsDto> getBankDetailsDtoList() {
        return bankDetailsDtoList;
    }

    public void setBankDetailsDtoList(List<BankDetailsDto> bankDetailsDtoList) {
        this.bankDetailsDtoList = bankDetailsDtoList;
    }

    public List<ClaimCalculationSheetPayeeNameDto> getPayeeTypeList() {
        return payeeTypeList;
    }

    public void setPayeeTypeList(List<ClaimCalculationSheetPayeeNameDto> payeeTypeList) {
        this.payeeTypeList = payeeTypeList;
    }

    public List<ListBoxItem> getPayeeNameList() {
        return payeeNameList;
    }

    public void setPayeeNameList(List<ListBoxItem> payeeNameList) {
        this.payeeNameList = payeeNameList;
    }

    public Integer getPayeeTypeId() {
        return payeeTypeId;
    }

    public void setPayeeTypeId(Integer payeeTypeId) {
        this.payeeTypeId = payeeTypeId;
    }

    public String getPayeeNameId() {
        return payeeNameId;
    }

    public void setPayeeNameId(String payeeNameId) {
        this.payeeNameId = payeeNameId;
    }

    public Integer getLastID() {
        return lastID;
    }

    public void setLastID(Integer lastID) {
        this.lastID = lastID;
    }
}
