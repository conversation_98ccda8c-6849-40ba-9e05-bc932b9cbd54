package com.misyn.mcms.admin.admin.dto;

import java.io.Serializable;
public class BranchDetailDto implements Serializable {

    private Integer txnId;
    private String branchCode;
    private String branchName;
    private String branchCity;
    private String inputUser;
    private String inputDatetime;
    private String recordStatus;
    private Integer index;

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getBranchCity() {
        return branchCity;
    }

    public void setBranchCity(String branchCity) {
        this.branchCity = branchCity;
    }

    public String getInputUser() {
        return inputUser;
    }

    public void setInputUser(String inputUser) {
        this.inputUser = inputUser;
    }

    public String getInputDatetime() {
        return inputDatetime;
    }

    public void setInputDatetime(String inputDatetime) {
        this.inputDatetime = inputDatetime;
    }

    public String getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }
}
