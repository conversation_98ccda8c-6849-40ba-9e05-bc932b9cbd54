package com.misyn.mcms.admin.admin.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class ChannelTeamGridDto implements Serializable {

    private Integer index = AppConstant.ZERO_INT;
    private Integer teamId = AppConstant.ZERO_INT;
    private String teamName = AppConstant.STRING_EMPTY;
    private Integer channelCount = AppConstant.ZERO_INT;
    private Integer memberCount = AppConstant.ZERO_INT;
    private String createdDate = AppConstant.STRING_EMPTY;
    private String updatedDate = AppConstant.STRING_EMPTY;

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Integer getTeamId() {
        return teamId;
    }

    public void setTeamId(Integer teamId) {
        this.teamId = teamId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public Integer getChannelCount() {
        return channelCount;
    }

    public void setChannelCount(Integer channelCount) {
        this.channelCount = channelCount;
    }

    public Integer getMemberCount() {
        return memberCount;
    }

    public void setMemberCount(Integer memberCount) {
        this.memberCount = memberCount;
    }

    public String getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(String createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(String updatedDate) {
        this.updatedDate = updatedDate;
    }
}
