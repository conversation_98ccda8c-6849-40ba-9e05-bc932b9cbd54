package com.misyn.mcms.admin.admin.dto;

import java.util.List;
public class ProductDetailListDto {
    List<CoverDetailMstDto> coverData;
    List<ServiceFactorDetailDto> serviceFactorData;
    List<BenefitDetailDto> benefitData;
    List<ConditionAndExclusionDto> conditionData;
    List<SpecialPackagesDto> specialPackagesData ;
    List<SrcctcDetailDto> srcctcDetailData ;

    public ProductDetailListDto() {
    }

    public ProductDetailListDto(List<CoverDetailMstDto> coverData, List<ServiceFactorDetailDto> serviceFactorData, List<BenefitDetailDto> benefitData, List<ConditionAndExclusionDto> conditionData, List<SpecialPackagesDto> specialPackagesData, List<SrcctcDetailDto> srcctcDetailData) {
        this.coverData = coverData;
        this.serviceFactorData = serviceFactorData;
        this.benefitData = benefitData;
        this.conditionData = conditionData;
        this.specialPackagesData = specialPackagesData;
        this.srcctcDetailData = srcctcDetailData;
    }

    public List<CoverDetailMstDto> getCoverData() {
        return coverData;
    }

    public void setCoverData(List<CoverDetailMstDto> coverData) {
        this.coverData = coverData;
    }

    public List<ServiceFactorDetailDto> getServiceFactorData() {
        return serviceFactorData;
    }

    public void setServiceFactorData(List<ServiceFactorDetailDto> serviceFactorData) {
        this.serviceFactorData = serviceFactorData;
    }

    public List<BenefitDetailDto> getBenefitData() {
        return benefitData;
    }

    public void setBenefitData(List<BenefitDetailDto> benefitData) {
        this.benefitData = benefitData;
    }

    public List<ConditionAndExclusionDto> getConditionData() {
        return conditionData;
    }

    public void setConditionData(List<ConditionAndExclusionDto> conditionData) {
        this.conditionData = conditionData;
    }

    public List<SpecialPackagesDto> getSpecialPackagesData() {
        return specialPackagesData;
    }

    public void setSpecialPackagesData(List<SpecialPackagesDto> specialPackagesData) {
        this.specialPackagesData = specialPackagesData;
    }

    public List<SrcctcDetailDto> getSrcctcDetailData() {
        return srcctcDetailData;
    }

    public void setSrcctcDetailData(List<SrcctcDetailDto> srcctcDetailData) {
        this.srcctcDetailData = srcctcDetailData;
    }
}
