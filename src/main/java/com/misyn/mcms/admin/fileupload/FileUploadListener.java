/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.misyn.mcms.admin.fileupload;

/**
 * Created on : Feb 4, 2011, 2:11:16 PM
 *
 * <AUTHOR>
 * @version 2.0
 * @Product : Intranet - UA Intranet & Common Auth. System
 * @Copyright : Copyright, 2009-2010 (c)
 * @Company : M.I. Synergy (Pvt) Ltd
 */


public class FileUploadListener implements OutputStreamListener {
    private FileUploadStats fileUploadStats = new FileUploadStats();

    public FileUploadListener(long totalSize) {
        fileUploadStats.setTotalSize(totalSize);
    }

    public void start() {
        fileUploadStats.setCurrentStatus("start");
    }

    public void bytesRead(int byteCount) {
        fileUploadStats.incrementBytesRead(byteCount);
        fileUploadStats.setCurrentStatus("reading");
    }

    public void error(String s) {
        fileUploadStats.setCurrentStatus("error");
    }

    public void done() {
        fileUploadStats.setBytesRead(fileUploadStats.getTotalSize());
        fileUploadStats.setCurrentStatus("done");
    }

    public FileUploadStats getFileUploadStats() {
        return fileUploadStats;
    }

    public static class FileUploadStats {
        String uploadPresentage = "0.00";
        StringBuilder sb = new StringBuilder();
        private long totalSize = 0;
        private long bytesRead = 0;
        private long startTime = System.currentTimeMillis();
        private String currentStatus = "none";

        public long getTotalSize() {
            return totalSize;
        }

        public void setTotalSize(long totalSize) {
            this.totalSize = totalSize;
        }

        public long getBytesRead() {
            return bytesRead;
        }

        public void setBytesRead(long bytesRead) {
            this.bytesRead = bytesRead;
        }

        public long getElapsedTimeInSeconds() {
            return (System.currentTimeMillis() - startTime) / 1000;
        }

        public String getCurrentStatus() {
            return currentStatus;
        }

        public void setCurrentStatus(String currentStatus) {
            this.currentStatus = currentStatus;
        }

        public void incrementBytesRead(int byteCount) {
            this.bytesRead += byteCount;
        }

        public String getUploadPresentage() {
            double d = 0.00;

            try {
                d = getBytesRead() * 100 / getTotalSize();
                if (d >= 99) {
                    d = 99.00;

                }
                this.uploadPresentage = Double.toString(d);
            } catch (Exception e) {
            }
            return this.uploadPresentage + " %";
        }

        public void setUploadPresentage(String uploadPresentage) {
            this.uploadPresentage = uploadPresentage;
        }

        public String getXML() {
            sb.delete(0, sb.toString().length());
//        sb.append("<?xml version='1.0' encoding='ISO-8859-1'?>");
//        sb.append("<reply>");
//        sb.append("<FileStat>").append(getCurrentStatus()).append("</FileStat>");
//        sb.append("<UploadProgs>").append(getUploadPresentage()).append("</UploadProgs>");
//        sb.append("<ElapsedTime>").append(getElapsedTimeInSeconds()).append("</ElapsedTime>");
//        sb.append("</reply>");
            String stat = "";
            sb.append("<?xml version='1.0' encoding='ISO-8859-1'?>");
            sb.append("<reply>");
            try {
                stat = getCurrentStatus();
                sb.append("<inputtext1>").append(stat).append("</inputtext1>");
                sb.append("<inputtext2>").append(getUploadPresentage()).append("</inputtext2>");
            } catch (Exception e) {
                sb.append("<inputtext>").append("ERROR : ").append(stat).append("</inputtext>");
            }
            sb.append("</reply>");


            return sb.toString();
        }

//    public static void main(String[] args) {
//        FileUploadStats f=new FileUploadStats();
//        f.getXML();
//        f.getXML();
//
//     }

    }


}
