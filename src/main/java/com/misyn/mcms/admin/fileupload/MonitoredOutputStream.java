/* Licence:
 *   Use this however/wherever you like, just don't blame me if it breaks anything.
 *
 * Credit:
 *   If you're nice, you'll leave this bit:
 *
 *   Class by <PERSON><PERSON><PERSON> -- http://www.telio.be/blog
 *   email : <EMAIL>
 */
package com.misyn.mcms.admin.fileupload;

import java.io.IOException;
import java.io.OutputStream;
public class MonitoredOutputStream extends OutputStream {
    private OutputStream target;
    private OutputStreamListener listener;

    public MonitoredOutputStream(OutputStream target, OutputStreamListener listener) {
        this.target = target;
        this.listener = listener;
        this.listener.start();
    }

    public void write(byte b[], int off, int len) throws IOException {
        target.write(b, off, len);
        listener.bytesRead(len - off);
    }

    public void write(byte b[]) throws IOException {
        target.write(b);
        listener.bytesRead(b.length);
    }

    public void write(int b) throws IOException {
        target.write(b);
        listener.bytesRead(1);
    }

    public void close() throws IOException {
        target.close();
        listener.done();
    }

    public void flush() throws IOException {
        target.flush();
    }
}
