package com.misyn.mcms.claim.controller.admin;

import com.itextpdf.text.log.Logger;
import com.itextpdf.text.log.LoggerFactory;
import com.misyn.mcms.admin.admin.dto.UserGroupDto;
import com.misyn.mcms.admin.admin.service.UserManagementService;
import com.misyn.mcms.admin.admin.service.impl.UserManagementServiceImpl;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.controller.ClaimUserLeaveController;
import com.misyn.mcms.claim.dto.JWTClaimDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.JwtUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;


@WebServlet(name = "rolesController", urlPatterns = "/rolesController/*")
public class RoleController extends BaseController{
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimUserLeaveController.class);
    private final UserManagementService userManagementService = new UserManagementServiceImpl();

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();

        try {
            switch (pathInfo) {
                case "/saveUserGroup":
                    saveUserGroup(request, response);
                    break;
                case "/loadAllGroups":
                    loadAllGroups(request, response);
                    break;
                case "/loadRoleById":
                    loadRoleById(request, response);
                    break;
                case "/updateRoles":
                    updateRoles(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void updateRoles(HttpServletRequest request, HttpServletResponse response) {

    }

    private void loadAllGroups(HttpServletRequest request, HttpServletResponse response) {
        try {
            List<UserGroupDto> groupList = userManagementService.getGroupList();
            request.setAttribute("groupList", groupList);
            requestDispatcher(request, response, "/admin/mainMenuItem/mainItemList.jsp");

        } catch (Exception e) {
            LOGGER.error("Error loading role screen: " + e.getMessage(), e);
        }
    }

    private void loadRoleById(HttpServletRequest request, HttpServletResponse response) {
        String groupId = null == request.getParameter("groupId") || request.getParameter("groupId").isEmpty() ? AppConstant.STRING_EMPTY : String.valueOf(request.getParameter("groupId"));
        try {
            request.setAttribute("groupId", groupId);
            requestDispatcher(request, response, "/admin/system_application/applicationItemList.jsp");
        } catch (Exception e) {
            LOGGER.error("Error loading role screen: " + e.getMessage(), e);
        }
    }

    private UserGroupDto setData(BufferedReader reader,HttpServletRequest request) throws IOException {
        StringBuilder jsonBuffer = new StringBuilder();
        String line;
        UserGroupDto userGroupDto = new UserGroupDto();
        String token = (String) request.getSession(true).getAttribute("token");
        JWTClaimDto jwtClaimDto = JwtUtil.decodeJwt(token);
        String userName = jwtClaimDto.getUsername().toLowerCase();
        while ((line = reader.readLine()) != null) {
            jsonBuffer.append(line);
        }

        // Convert JSON string to JSONObject
        String jsonString = jsonBuffer.toString();
        JSONObject jsonObject = new JSONObject(jsonString);

        userGroupDto.setCreatedBy(userName);
        userGroupDto.setGroupName(jsonObject.optString("groupName"));
        userGroupDto.setStatus("ACTIVE");
        return userGroupDto;

    }

    private void saveUserGroup(HttpServletRequest request, HttpServletResponse response) {
        try {
            BufferedReader reader = request.getReader();
            UserGroupDto userGroupDto = setData(reader, request);
            UserGroupDto savedUserGroup = userManagementService.saveUserGroup(userGroupDto);
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            PrintWriter out = response.getWriter();

            if (savedUserGroup != null) {
                out.write("{\"status\":\"success\", \"message\":\"User Group created successfully\", \"userCode\":" + savedUserGroup.getGroupName() + "}");
            } else {
                out.write("{\"status\":\"error\", \"message\":\"Failed to create user Group\"}");
            }
            out.flush();

        } catch (Exception e) {
            LOGGER.error("Error saving user Group: " + e.getMessage(), e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                PrintWriter out = response.getWriter();
                out.write("{\"status\":\"error\", \"message\":\"" + e.getMessage() + "\"}");
                out.flush();
            } catch (IOException ioException) {
                LOGGER.error("Error writing error response: " + ioException.getMessage());
            }
        }
    }
}
