package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ARIInspectionDetailsDto;

import java.sql.Connection;

/**
 * Created by akila on 5/21/18.
 */
public interface ARIInspectionDetailsDao extends BaseDao<ARIInspectionDetailsDto> {

    String SQL_INSERT_ARI_INSPECTION_DETAILS = "INSERT INTO ari_inspection_details\n"
            + "	(inspection_id, n_ref_no, is_ari, ari_order, is_salvage, salvage_order, "
            + "assessor_remark, inspection_remark, professional_fee, miles, telephone_charge, "
            + "other_charge, special_deduction, reason, total_charge) VALUES "
            + "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    String SQL_SELECT_ARI_DETAILS = "SELECT\n" +
            "t1.*,\n" +
            "CASE\n" +
            "WHEN t2.`status` = 'PR'\n" +
            "THEN 'Y'\n" +
            "ELSE 'N'\n" +
            "END AS STATUS\n" +
            "FROM ari_inspection_details t1\n" +
            "LEFT JOIN request_ari_details t2 on t1.n_ref_no = t2.ref_id\n" +
            "WHERE n_ref_no=?";

    String SQL_SELECT_ARI_INSPECTION_DETAILS = "SELECT\n" +
            "is_ari,\n" +
            "ari_order,\n" +
            "is_salvage,\n" +
            "salvage_order,\n" +
            "assessor_remark,\n" +
            "inspection_remark \n" +
            "FROM\n" +
            "	ari_inspection_details \n" +
            "WHERE\n" +
            "	n_ref_no =?";


    String SQL_SELECT_ARI_INSPECTION_ASSESSOR_FEE_DETAILS = "SELECT\n" +
            "	professional_fee,\n" +
            "	miles,\n" +
            "	telephone_charge,\n" +
            "	other_charge,\n" +
            "	special_deduction,\n" +
            "	reason,\n" +
            "	total_charge \n" +
            "FROM\n" +
            "	ari_inspection_details \n" +
            "WHERE\n" +
            "	n_ref_no =?";

    String SQL_UPDATE_ARI_INSPECTION = "UPDATE ari_inspection_details\n"
            + "SET is_ari= ?,\n"
            + " ari_order = ?,\n"
            + " is_salvage = ?,\n"
            + " salvage_order = ?,\n"
            + " assessor_remark = ?,\n"
            + " inspection_remark = ?,\n"
            + " professional_fee = ?,\n"
            + " miles = ?,\n"
            + " telephone_charge = ?,\n"
            + " other_charge = ?,\n"
            + " special_deduction = ?,\n"
            + " reason= ?,\n"
            + " total_charge= ?\n"
            + "WHERE\n"
            + " n_ref_no = ?\n";

    ARIInspectionDetailsDto getARIInspectionAssessorFeeDetails(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto, Object id);

    ARIInspectionDetailsDto getARIInspectionDetails(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto, Object id);
}
