package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.enums.FileTypeEnum;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;

/**
 * Created by a<PERSON><PERSON> on 4/10/18.
 */
public abstract class AbstractBaseDao<T> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstarctDbCommonFunction.class);
    private static final String ORDER_BY = "ORDER BY";
    private static final String SPACE = " ";
    private static final String COMA = ",";
    private static final String LIMIT = "LIMIT";
    private static final String WHERE = " WHERE ";
    private static final String EQUAL = " = ";
    private static final String EQUAL_AND_GREATER_THAN = " >= ";
    private static final String EQUAL_AND_LESS_THAN = " <= ";
    private static final String NOT_EQUAL = " <> ";
    private static final String LIKE = " LIKE ";
    private static final String IN = " IN ";
    private static final String NOT_IN = " NOT IN ";
    private static final String IS_NULL = " IS NULL ";
    private static final String AND = " AND ";
    private static final String PERCENTAGE_SIGN_START = " '%";
    private static final String PERCENTAGE_SIGN_END = "%' ";
    private static final String BRACKET_OPEN = "(";
    private static final String BRACKET_CLOSE = ")";
    private static final String SQL_SELECT_ENTITY_STATUS_BY_ALL = "SELECT * FROM entity_status ";
    private static final String SQL_SELECT_MESSAGE_BY_ALL = "SELECT * FROM message ";
    private final static String SQL_SELECT_PACKAGE_TYPE_BY_PACKAGE_TYPE_ID = "SELECT * FROM package_type WHERE package_type_id=?";
    private final static String SQL_SELECT_PACKAGE_TYPE_BY_ALL = "SELECT * FROM package_type";
    /**
     * Rejected Message select query
     */
    private static final String SQL_SELECT_REJECT_MESSAGE = "SELECT * FROM reject_message";
    private static final String SQL_SELECT_MAX_KEY_TABLE_BY_TABLE_TYPE_ID = "SELECT max_value FROM max_key_table WHERE table_type_id=?";
    private static final String SQL_UPDATE_MAX_KEY_TABLE_BY_TABLE_TYPE_ID = "UPDATE max_key_table SET max_value=max_value+1 WHERE table_type_id=?";
    /**
     * Rejected Message insert query
     */
    private static final String SQL_INSERT_REJECT_MESSAGE = "INSERT INTO reject_message VALUE (0,?,?,?,?,?)";
    /**
     * Select Rejected Messages by Super Rejected Message id query
     */
    private static final String SQL_SELECT_REJECT_MESSAGE_BY_SUPER_REJECT_MESSAGE_ID = "SELECT * FROM reject_message WHERE `type` = ? AND guest_table_id = ? ORDER BY datetime DESC";
    private boolean isErrorExsist = false;

    protected int executeUpdate(Connection conn, String strSQL) throws Exception {
        int result = -1;
        PreparedStatement ps = null;
        try {
            ps = conn.prepareStatement(strSQL);
            result = ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception(e);

        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }

            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());
                throw new Exception(ex);
            }
        }
        return result;
    }

    public String findRecord(Connection conn, String tblName, String outField, String searchKey1) {
        String strval = "Invalid";
        PreparedStatement ps = null;
        ResultSet rs = null;
        String strSQL = "SELECT distinct(" + outField.trim() + ") FROM " + tblName;
        if (!searchKey1.trim().equals("")) {
            strSQL = "SELECT distinct(" + outField.trim() + ") FROM " + tblName + " WHERE " + searchKey1;
        }
        try {
            ps = conn.prepareStatement(strSQL, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            rs = ps.executeQuery();
            if (rs.next()) {
                strval = rs.getString(outField);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }

            } catch (Exception ex) {
            }
        }
        return strval;
    }

    protected boolean isRecExists(Connection conn, String tblName, String searchKey1) {
        boolean b = false;
        PreparedStatement ps = null;
        ResultSet rs = null;

        String strSQL = "SELECT 1 FROM " + tblName;

        if (!searchKey1.equals("".trim())) {
            strSQL = "SELECT 1 FROM " + tblName + " WHERE " + searchKey1;
        }
        try {
            ps = conn.prepareStatement(strSQL, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            rs = ps.executeQuery();
            if (rs.next()) {
                b = true;
            }
        } catch (Exception e) {
            setIsErrorExsist(true);
            LOGGER.error(e.getMessage(), e);
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (rs != null) {
                    rs.close();
                }
            } catch (Exception ex) {
            }
        }
        return b;
    }

    public boolean isIsErrorExsist() {
        return isErrorExsist;
    }

    public void setIsErrorExsist(boolean isErrorExsist) {
        this.isErrorExsist = isErrorExsist;
    }

    protected StringBuilder formatSQL(List<FieldParameterDto> parameterList) {
        StringBuilder sbFormattedSql = new StringBuilder();
        if (!parameterList.isEmpty()) {
            sbFormattedSql.append(WHERE);
            for (FieldParameterDto fieldParameter : parameterList) {
                if (fieldParameter.isStringType()) {
                    String formattedValue = toSQL(fieldParameter.getFieldValue());
                    fieldParameter.setFieldValue(formattedValue);
                } else {
                    // if(!AppConstant.STRING_EMPTY.equals(fieldParameter.getFieldValue()) && !Utility.isNumber(fieldParameter.getFieldValue())){
                    //    fieldParameter.setFieldValue(AppConstant.ZERO);
                    //  }
                }
                sbFormattedSql.append(fieldParameter.getDbFieldName());
                if (fieldParameter.isStringType() || fieldParameter.getSearchType() == FieldParameterDto.SearchType.Like) {
                    if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.Like) {
                        sbFormattedSql.append(LIKE);
                        sbFormattedSql.append(fieldParameter.isStringType() ? PERCENTAGE_SIGN_START : "");
                        sbFormattedSql.append(fieldParameter.getFieldValue());
                        sbFormattedSql.append(fieldParameter.isStringType() ? PERCENTAGE_SIGN_END : "");
                    } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.NOT_Equal) {
                        sbFormattedSql.append(NOT_EQUAL);
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                        sbFormattedSql.append(fieldParameter.getFieldValue());
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                    } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.Equal_And_Greater_Than) {
                        sbFormattedSql.append(EQUAL_AND_GREATER_THAN);
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                        sbFormattedSql.append(fieldParameter.getFieldValue());
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                    } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.Equal_And_Less_Than) {
                        sbFormattedSql.append(EQUAL_AND_LESS_THAN);
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                        sbFormattedSql.append(fieldParameter.getFieldValue());
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                    } else {
                        sbFormattedSql.append(EQUAL);
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                        sbFormattedSql.append(fieldParameter.getFieldValue());
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                    }

                } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.IN) {
                    sbFormattedSql.append(IN);
                    sbFormattedSql.append(BRACKET_OPEN);
                    sbFormattedSql.append(fieldParameter.getFieldValue());
                    sbFormattedSql.append(BRACKET_CLOSE);
                } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.NOT_IN) {
                    sbFormattedSql.append(NOT_IN);
                    sbFormattedSql.append(BRACKET_OPEN);
                    sbFormattedSql.append(fieldParameter.getFieldValue());
                    sbFormattedSql.append(BRACKET_CLOSE);
                } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.NOT_Equal) {
                    sbFormattedSql.append(NOT_EQUAL).append(fieldParameter.getFieldValue());
                } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.IS_NULL) {
                    sbFormattedSql.append(IS_NULL);
                } else {
                    sbFormattedSql.append(EQUAL).append(fieldParameter.getFieldValue());
                }
                sbFormattedSql.append(AND);
            }
            sbFormattedSql.delete(sbFormattedSql.toString().length() - 4, sbFormattedSql.toString().length());
        }
//        LOGGER.debug(sbFormattedSql.toString());
        return sbFormattedSql;
    }

    protected String toSQL(String fieldValue) {
        return fieldValue.replaceAll("_", "\\\\_")
                .replaceAll("%", "\\\\%")
                .replaceAll("\\^", "\\\\^")
                .replaceAll("!", "\\\\!")
                .replaceAll("\'", "\\\\'");
    }

    protected StringBuilder formatOrderSQL(String orderType, String orderColumnName) {
        StringBuilder sbFormattedSql = new StringBuilder();
        //t` ORDER BY `usrid` LIMIT 0, 1000
        sbFormattedSql.append(ORDER_BY).append(SPACE).append(orderColumnName).append(SPACE).append(orderType).append(SPACE);
//        LOGGER.debug(sbFormattedSql.toString());
        return sbFormattedSql;
    }

    protected StringBuilder formatOrderSQL(int start, int length, String orderType, String orderColumnName) {
        StringBuilder sbFormattedSql = new StringBuilder();
        //t` ORDER BY `usrid` LIMIT 0, 1000
        sbFormattedSql.append(ORDER_BY).append(SPACE).append(orderColumnName).append(SPACE).append(orderType)
                .append(SPACE).append(LIMIT).append(SPACE).append(start).append(COMA).append(length);
//        LOGGER.debug(sbFormattedSql.toString());
        return sbFormattedSql;
    }

    protected String getToolTip(String documentName, String uploadUserName, String uploadDateTime) {
        StringBuilder sb = new StringBuilder();
        sb.append("Document Name : ").append(documentName).append("</br>");
        sb.append("Upload User Name :").append(uploadUserName).append("</br>");
        sb.append("Upload Date/Time :").append(uploadDateTime).append("</br>");
        return sb.toString();
    }

    protected FileTypeEnum getFileType(int documentTypeId){
        if (documentTypeId == AppConstant.REJECTION_AUDIO_TYPE ) {
            return FileTypeEnum.AUDIO;
        }
        return FileTypeEnum.PDF;
    }

    protected StringBuilder formatSQL1(List<FieldParameterDto> parameterList) {
        StringBuilder sbFormattedSql = new StringBuilder();
        if (!parameterList.isEmpty()) {
            sbFormattedSql.append(AND);
            for (FieldParameterDto fieldParameter : parameterList) {
                if (fieldParameter.isStringType()) {
                    String formattedValue = toSQL(fieldParameter.getFieldValue());
                    fieldParameter.setFieldValue(formattedValue);
                } else {
                    // if(!AppConstant.STRING_EMPTY.equals(fieldParameter.getFieldValue()) && !Utility.isNumber(fieldParameter.getFieldValue())){
                    //    fieldParameter.setFieldValue(AppConstant.ZERO);
                    //  }
                }
                sbFormattedSql.append(fieldParameter.getDbFieldName());
                if (fieldParameter.isStringType() || fieldParameter.getSearchType() == FieldParameterDto.SearchType.Like) {
                    if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.Like) {
                        sbFormattedSql.append(LIKE);
                        sbFormattedSql.append(fieldParameter.isStringType() ? PERCENTAGE_SIGN_START : "");
                        sbFormattedSql.append(fieldParameter.getFieldValue());
                        sbFormattedSql.append(fieldParameter.isStringType() ? PERCENTAGE_SIGN_END : "");
                    } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.NOT_Equal) {
                        sbFormattedSql.append(NOT_EQUAL);
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                        sbFormattedSql.append(fieldParameter.getFieldValue());
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                    } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.Equal_And_Greater_Than) {
                        sbFormattedSql.append(EQUAL_AND_GREATER_THAN);
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                        sbFormattedSql.append(fieldParameter.getFieldValue());
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                    } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.Equal_And_Less_Than) {
                        sbFormattedSql.append(EQUAL_AND_LESS_THAN);
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                        sbFormattedSql.append(fieldParameter.getFieldValue());
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                    } else {
                        sbFormattedSql.append(EQUAL);
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                        sbFormattedSql.append(fieldParameter.getFieldValue());
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                    }

                } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.IN) {
                    sbFormattedSql.append(IN);
                    sbFormattedSql.append(BRACKET_OPEN);
                    sbFormattedSql.append(fieldParameter.getFieldValue());
                    sbFormattedSql.append(BRACKET_CLOSE);
                } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.NOT_IN) {
                    sbFormattedSql.append(NOT_IN);
                    sbFormattedSql.append(BRACKET_OPEN);
                    sbFormattedSql.append(fieldParameter.getFieldValue());
                    sbFormattedSql.append(BRACKET_CLOSE);
                } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.NOT_Equal) {
                    sbFormattedSql.append(NOT_EQUAL).append(fieldParameter.getFieldValue());
                } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.IS_NULL) {
                    sbFormattedSql.append(IS_NULL);
                } else {
                    sbFormattedSql.append(EQUAL).append(fieldParameter.getFieldValue());
                }
                sbFormattedSql.append(AND);
            }
            sbFormattedSql.delete(sbFormattedSql.toString().length() - 4, sbFormattedSql.toString().length());
        }
//        LOGGER.debug(sbFormattedSql.toString());
        return sbFormattedSql;
    }

//    with priority order by
//    protected StringBuilder orderByCases(int start, int length, String orderType, String caseOrderColumn, String[] orderColumns, Object[] instances) {
//        StringBuilder stringBuilder = new StringBuilder();
//        int size = instances.length;
//        stringBuilder.append(ORDER_BY).append(SPACE).append("CASE").append(SPACE).append(caseOrderColumn).append(SPACE);
//        for (int i = 0; i < size - 1; i++) {
//            stringBuilder.append("WHEN").append(SPACE).append("'" + instances[i] + "'").append(SPACE).append("THEN").append(SPACE).append(i + 1).append(SPACE);
//        }
//        stringBuilder.append("ELSE").append(SPACE).append(size).append(SPACE).append("END").append(COMA);
//        String coma = "";
//        for (int i = 0; i < orderColumns.length; i++) {
//            stringBuilder.append(coma).append(orderColumns[orderColumns.length - 1]);
//            coma = ",";
//        }
//        stringBuilder.append(SPACE).append(orderType).append(SPACE).append(LIMIT).append(SPACE).append(start).append(COMA).append(length);
//        return stringBuilder;
//    }

}
