package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.AcknowledgementDetailsDto;
import com.misyn.mcms.claim.dto.AcknowledgementSummaryDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;

import java.sql.Connection;
import java.util.List;

public interface AcknowledgementSummaryDao {
    String SQL_INSERT_INTO_ACKNOWLEDGMENT_SUMMARY = "INSERT INTO acknowledgement_summary VALUES (0,?,?,?,?,?,?,?,?)";

    String SQL_SELECT_ACKNOWLEDGMENT_SUMMARY = "SELECT * FROM acknowledgement_summary WHERE acknowledgement_id =?";

    String SQL_SELECT_ACKNOWLEDGMENT_SUMMARY_DOCUMENT_ID_LIST = "SELECT document_id FROM acknowledgement_summary as t1 INNER JOIN " +
            "acknowledgement_details AS t2 ON t2.acknowledgement_id = t1.acknowledgement_id where t1.acknowledgement_id =?";

    String SQL_INSERT_INTO_ACKNOWLEDGMENT_DETAILS = "INSERT INTO acknowledgement_details VALUES (0,?,?)";

    String SQL_SELECT_ALL_TO_GRID = "SELECT\n" +
            "t1.*,t2.*,t3.*\n" +
            "FROM\n" +
            "claim_claim_info_main AS t1\n" +
            "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" +
            "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id ";

    String SQL_COUNT_CLAIM_HANDLER = "SELECT\n" +
            "count(*) as cnt\n" +
            "FROM\n" +
            "claim_claim_info_main AS t1\n" +
            "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO \n" +
            "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id ";

    String SQL_SELECT_ACKNOWLEDGMENT_SUMMARY_BY_CLAIM_NO = "SELECT * FROM acknowledgement_summary WHERE claim_no =?";

    AcknowledgementSummaryDto insertMaster(Connection connection, AcknowledgementSummaryDto acknowledgementSummaryDto) throws Exception;

    AcknowledgementSummaryDto getAcknowledgementSummeryList(Connection connection, Integer acknowledgementId) throws Exception;

    AcknowledgementDetailsDto insertDetailsMaster(Connection connection, AcknowledgementDetailsDto acknowledgementDetailsDto) throws Exception;

    DataGridDto getClaimHandlerDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) throws Exception;

    List<AcknowledgementSummaryDto> getAcknowledgementSummeryListByClaimNo(Connection connection, Integer claimNo) throws Exception;


}
