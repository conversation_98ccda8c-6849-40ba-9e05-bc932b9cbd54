package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.AgentGarageDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;

import java.sql.Connection;
import java.util.List;

public interface AgentGarageDao {
    String CLAIM_AGENT_GARAGE_INSERT = "INSERT INTO claim_agent_garage VALUES(0,?,?,?,?,?,?,?,?,?)";
    String CLAIM_AGENT_GARAGE_UPDATE = "UPDATE claim_agent_garage SET " +
            "V_GARG_CODE =?,\n" +
            "V_GARG_NAME =?,\n" +
            "V_ADDRESS1 =?,\n" +
            "V_ADDRESS2 =?,\n" +
            "V_ADDRESS3 =?,\n" +
            "V_CON_PERSON =?,\n" +
            "V_CON_NUMBER =?,\n" +
            "V_GEN_TEL_NO =?,\n" +
            "V_STATUS =? WHERE N_ID=?";

    String CLAIM_AGENT_GARAGE_SEARCH = "SELECT * FROM claim_agent_garage WHERE N_ID=?";
    String VALIDATE_CLAIM_AGENT_GARAGE_NAME = "SELECT V_GARG_NAME FROM claim_agent_garage WHERE V_GARG_NAME like ?";

    AgentGarageDto addnewGarage(Connection connection, AgentGarageDto agentGarageDto) throws Exception;

    AgentGarageDto updateGarage(Connection connection, AgentGarageDto agentGarageDto) throws Exception;

    AgentGarageDto searchGarage(Connection connection, Integer id) throws Exception;

    DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

    String validategarageNmae(Connection connection, String garageName) throws Exception;
}


