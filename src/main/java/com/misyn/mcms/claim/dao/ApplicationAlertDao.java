package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ApplicationAlertDto;

import java.sql.Connection;

public interface ApplicationAlertDao {

    String SQL_INSERT_TO_ALERT_TYPE = "INSERT INTO application_alerts VALUES(0,?,?,?,?)";
    String SQL_SEARCH_ALERT_TYPE = "SELECT * FROM application_alerts WHERE key_value=? AND type=?  ";

    ApplicationAlertDto insertMaster(ApplicationAlertDto applicationAlertDto, Connection connection) throws Exception;

    ApplicationAlertDto searchByKeyValueAndType(Connection connection, Integer keyValue, Integer type) throws Exception;
}
