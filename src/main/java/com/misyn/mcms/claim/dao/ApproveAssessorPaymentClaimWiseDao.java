package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ApproveAssessorPaymentClaimWiseDto;

import java.sql.Connection;

public interface ApproveAssessorPaymentClaimWiseDao extends BaseDao<ApproveAssessorPaymentClaimWiseDto> {

    String SQL_SELECT_ALL_BY_CLAIM_NO_AND_ASSESSOR_CODE = "SELECT * FROM approve_assessor_payment_claim_wise_mst WHERE claim_no = ? AND assessor_code = ?";

    String SQL_UPDATE_BY_CLAIM_NO_AND_ASSESSOR_CODE = "UPDATE `approve_assessor_payment_claim_wise_mst` \n" +
            "SET `total_other_amount` = ?,\n" +
            "`total_schedule_amount` = ? \n" +
            "WHERE\n" +
            "	`claim_no` = ? \n" +
            "	AND `assessor_code` = ?;";

    String SQL_INSERT_MASTER = "INSERT INTO `approve_assessor_payment_claim_wise_mst` ( `claim_no`, `assessor_code`, `ref_no`, `total_other_amount`, `total_schedule_amount` )\n" +
            "VALUES\n" +
            "	( ?, ?, ?, ?, ? );";

    String SQL_INSERT_HISTORY = "INSERT INTO `approve_assessor_payment_claim_wise_hst` ( `claim_no`, `assessor_code`, `ref_no`, `total_other_amount`, `total_schedule_amount`, `action`, `input_datetime` )\n" +
            "VALUES\n" +
            "	( ?, ?, ?, ?, ?, ?, ?);";

    ApproveAssessorPaymentClaimWiseDto getDetailByAssessorCodeAndClaimNo(Connection connection, int claimNo, String assessorCode);

    void updateByclaimNoAndAssessorCode(Connection connection, ApproveAssessorPaymentClaimWiseDto approveAssessorPaymentClaimWiseDto) throws Exception;

}
