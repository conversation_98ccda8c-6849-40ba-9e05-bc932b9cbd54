package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.AriNotificationDto;

import java.sql.Connection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface AriNotificationDao {
    String SAVE_ARI_NOTIFICATION = "INSERT INTO ari_notification (txn_id,claim_no,input_date_time,input_user,assign_user) VALUES (0,?,?,?,?)";

    String IS_UNSENT_NOTIFICATION_AVAILABLE = "SELECT 1 FROM ari_notification WHERE claim_no = ?";

    String REMOVE_AVAILABLE_RECORDS = "DELETE FROM ari_notification WHERE claim_no = ?";

    String GET_ALL_ARI_NOTIFICATION = "SELECT * FROM ari_notification ORDER BY claim_no DESC, input_date_time DESC";

    void saveAriNotification(Connection connection, Integer claimNo, String inputUser, String assignUser) throws Exception;

    boolean isAvailableUnsentNotification(Connection connection, Integer claimNo) throws Exception;

    void removeAvailableRecordsByClaimNo(Connection connection, Integer claimNo) throws Exception;

    List<AriNotificationDto> getPendingNotifications(Connection connection) throws Exception;
}
