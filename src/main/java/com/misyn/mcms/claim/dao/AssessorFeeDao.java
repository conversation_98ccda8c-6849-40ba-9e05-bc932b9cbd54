package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.AssessorFeeDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.ListItemDto;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;

public interface AssessorFeeDao {
    String ASSESSOR_FEE_INSERT = "INSERT INTO assessor_fee_detail VALUES(0,?,?,?,?,?,?,?,?,?,?,?)";
    String ASSESSOR_FEE_UPDATE = "UPDATE assessor_fee_detail SET \n" +
            "from_time =?,\n" +
            "to_time =?,\n" +
            "inspection_type_id =?,\n" +
            "day_type_id =?,\n" +
            "permanent_staff_fee =?,\n" +
            "hybrid_staff_fee =?,\n" +
            "record_status =?,\n" +
            "last_modified_date_time =?,\n" +
            "last_modified_user =?\n" +
            "WHERE assessor_fee_detail_id =?\n";
    String SELECT_ASSESSOR_FEE = "SELECT * FROM assessor_fee_detail where assessor_fee_detail_id = ?";

    String SELECT_INSPECTION_TYPE_LIST = "SELECT * FROM claim_inspection_type";

    String SELECT_INSPECTION_TYPE_DESC = "SELECT inspection_type_desc FROM claim_inspection_type where inspection_type_id = ?";

    String SELECT_DAY_TYPE_LIST = "SELECT * FROM assessor_fee_day_type";

    String SELECT_DAY_TYPE_DESC = "SELECT day_type_description FROM assessor_fee_day_type where day_type_id = ?";

    String SELECT_PERMANENT_ASSESSOR_FEE_BY_ASSESSOR_FEE_DETAIL = "SELECT permanent_staff_fee FROM assessor_fee_detail where assessor_fee_detail_id = ?";

    String SELECT_HYBRID_ASSESSOR_FEE_BY_ASSESSOR_FEE_DETAIL = "SELECT hybrid_staff_fee FROM assessor_fee_detail where assessor_fee_detail_id = ?";

    String SELECT_ALL_ASSESSOR_FEE_BY_INSPECTION_TYPE = "SELECT * FROM assessor_fee_detail where inspection_type_id = ? and day_type_id = ? and record_status = 'A'";

    String EXIST_TIME_SLOT_CHECK = "SELECT\n" +
            "	COUNT( assessor_fee_detail_id ) AS cnt \n" +
            "FROM\n" +
            "	assessor_fee_detail \n" +
            "WHERE\n" +
            "	inspection_type_id = ? \n" +
            "	AND day_type_id = ? \n" +
            "	AND (\n" +
            "	from_time < ? AND to_time > ?) \n" +
            "	AND assessor_fee_detail_id != ?";

    AssessorFeeDto insertAssessorFee(Connection connection, AssessorFeeDto assessorFeeDto) throws Exception;

    AssessorFeeDto updateAssessorFee(Connection connection, AssessorFeeDto assessorFeeDto) throws Exception;

    DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

    AssessorFeeDto getAssessorFee(Connection connection, Integer assessorFeeDetailId) throws Exception;

    List<ListItemDto> getInspectionTypeList(Connection connection) throws Exception;

    List<ListItemDto> getDayTypeList(Connection connection) throws Exception;

    List<ListItemDto> getTimeSlotsByInspectionTypeId(Connection connection, Integer inspectionTypeId, Integer jobType) throws Exception;

    void deleteAssessorFee(Connection connection, Integer assessorFeeDetailId, String userName) throws Exception;

    Boolean isExistTimeSlot(Connection connection, AssessorFeeDto assessorFeeDto) throws Exception;

    BigDecimal getMileageFeeByAssessorType(Connection connection, Integer assessorFeeDetailId, String assessorType);
}
