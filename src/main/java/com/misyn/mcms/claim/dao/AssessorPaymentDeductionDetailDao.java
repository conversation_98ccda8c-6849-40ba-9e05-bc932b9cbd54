package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.AssessorPaymentDeductionDetailDto;

import java.sql.Connection;

public interface AssessorPaymentDeductionDetailDao extends BaseDao<AssessorPaymentDeductionDetailDto> {

    String SQL_INSERT_ASSESSOR_PAYMENT_DEDUCTION_DETAIL_MST = "INSERT INTO assessor_payment_deduction_detail_mst VALUES (0, ?, ?, ?, ?, ?, ?, ?, ?);";

    String SQL_INSERT_ASSESSOR_PAYMENT_DEDUCTION_DETAIL_HST = "INSERT INTO assessor_payment_deduction_detail_hst VALUES (0, ?, ?, ?, ?, ?, ?, ?, ?);";

    String SELECT_FROM_ASSESSOR_PAYMENT_DEDUCTION_DETAIL_MST_BY_REF_NO = "SELECT * FROM assessor_payment_deduction_detail_mst WHERE ref_no=?";

    String SELECT_ONE_FROM_ASSESSOR_PAYMENT_DEDUCTION_DETAIL_MST_BY_REF_NO = "SELECT 1 FROM assessor_payment_deduction_detail_mst WHERE ref_no=?";

    String SQL_UPDATE_ASSESSOR_PAYMENT_DEDUCTION_DETAIL_MST = "UPDATE assessor_payment_deduction_detail_mst \n" +
            "SET claim_no = ?,\n" +
            "before_deduction_other_amount = ?,\n" +
            "before_deduction_schedule_amount = ?,\n" +
            "deduction = ?,\n" +
            "other_amount = ?,\n" +
            "schedule_amount = ?,\n" +
            "input_datetime = ? \n" +
            "WHERE\n" +
            "	ref_no =?";

    boolean isAlreadySaved(Connection connection, int refNo);
}
