package com.misyn.mcms.claim.dao;


import com.misyn.mcms.claim.dto.AssessorPaymentDetailForEmailDto;
import com.misyn.mcms.claim.dto.AssessorPaymentDetailsDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.UserDto;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

/**
 * Created by akila on 7/24/18.
 */
public interface AssessorPaymentDetailsDao extends BaseDao<AssessorPaymentDetailsDto> {

    String SQL_INSERT_INTO_ASSESSOR_PAYMENT_MST = "INSERT INTO assessor_payment_approve_mst VALUES(0,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

    String SQL_SELECT_FROM_ASSESSOR_PAYMENT_MST = "SELECT \n" +
            "t1.*,\n" +
            "t2.V_VEHICLE_NO,\n" +
            "t2.V_POL_NUMBER,\n" +
            "t2.V_ISF_CLAIM_NO,\n" +
            "t2.D_ACCID_DATE,\n" +
            "t2.T_ACCID_TIME,\n" +
            "t2.V_POLICY_CHANNEL_TYPE,\n" +
            "t3.job_id,\n" +
            "t5.before_deduction_schedule_amount,\n" +
            "t5.schedule_amount,\n" +
            "t6.v_place_of_inspection\n" +
            "FROM\n" +
            "assessor_payment_approve_mst AS t1\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no \n" +
            "INNER JOIN claim_assign_assesor AS t3 ON t3.ref_no = t1.key_id\n" +
            "INNER JOIN usr_mst AS t4 ON t4.v_emp_no = t3.assessor_code\n" +
            "LEFT JOIN assessor_payment_deduction_detail_mst AS t5 ON  t5.ref_no=t3.ref_no\n" +
            "LEFT JOIN claim_inspection_info_main_me AS t6 ON t6.n_ref_no = t3.ref_no";

    String SQL_SELECT_FROM_ASSESSOR_PAYMENT_MST_PAGINATION = "SELECT\n" +
            "    t1.*,\n" +
            "    t2.V_VEHICLE_NO,\n" +
            "    t2.V_POL_NUMBER,\n" +
            "    t2.V_ISF_CLAIM_NO,\n" +
            "    t2.D_ACCID_DATE,\n" +
            "    t2.T_ACCID_TIME,\n" +
            "    t2.V_POLICY_CHANNEL_TYPE,\n" +
            "    t3.job_id,\n" +
            "    t5.before_deduction_schedule_amount,\n" +
            "    t5.schedule_amount,\n" +
            "    t6.v_place_of_inspection\n" +
            "FROM\n" +
            "    assessor_payment_approve_mst AS t1\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_assign_assesor AS t3 ON t3.ref_no = t1.key_id\n" +
            "INNER JOIN usr_mst AS t4 ON t4.v_emp_no = t3.assessor_code\n" +
            "LEFT JOIN assessor_payment_deduction_detail_mst AS t5 ON t5.ref_no = t3.ref_no\n" +
            "LEFT JOIN claim_inspection_info_main_me AS t6 ON t6.n_ref_no = t3.ref_no\n";

    String SQL_COUNT_FROM_ASSESSOR_PAYMENT_MST_PAGINATION = "SELECT\n" +
            "    COUNT(*) AS total_records\n" +
            "FROM\n" +
            "    assessor_payment_approve_mst AS t1\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_assign_assesor AS t3 ON t3.ref_no = t1.key_id\n" +
            "INNER JOIN usr_mst AS t4 ON t4.v_emp_no = t3.assessor_code\n" +
            "LEFT JOIN assessor_payment_deduction_detail_mst AS t5 ON t5.ref_no = t3.ref_no\n" +
            "LEFT JOIN claim_inspection_info_main_me AS t6 ON t6.n_ref_no = t3.ref_no\n";


    String SQL_UPDATE_PAYMENT_BY_ID = "UPDATE assessor_payment_approve_mst SET " +
            "payment_status=?, " +
            "milleage=?, " +
            "cost_of_call=?, " +
            "other_fee=?, " +
            "deduction_fee=?, " +
            "professional_fee=?, " +
            "travel_fee=?, " +
            "total_fee=?, " +
            "inp_userid=?, " +
            "inp_datetime=? " +
            "WHERE txn_id=?";

    String SQL_UPDATE_PAYMENT_STATUS_BY_ID = "UPDATE assessor_payment_approve_mst SET payment_status=?,apprv_user=?,apprv_datetime=?,email_send_status=?,generate_occurrency=?,voucher_status=?,from_date=?,to_date=? WHERE txn_id=?";

    String SQL_UPDATE_PAYMENT_STATUS_REMARK_BY_ID = "UPDATE assessor_payment_approve_mst SET payment_status=?,remark=?,apprv_user=?,apprv_datetime=? WHERE txn_id=?";

    String SQL_SELECT_PAYMENT_MST_BY_KEY_AND_TYPE = "SELECT * FROM assessor_payment_approve_mst WHERE key_id=? AND type=?";

    String SQL_SELECT_PAYMENT_MST_BY_ID = "SELECT * FROM assessor_payment_approve_mst WHERE txn_id=?";

    String SQL_SELECT_TOTAL_AMOUNT = "SELECT\n" +
            "sum(t1.total_fee) as totalFee\n" +
            "FROM\n" +
            "assessor_payment_approve_mst AS t1\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no \n" +
            "INNER JOIN claim_assign_assesor AS t3 ON t3.ref_no = t1.key_id\n" +
            "INNER JOIN usr_mst AS t4 ON t4.v_emp_no = t3.assessor_code\n" +
            "LEFT JOIN assessor_payment_deduction_detail_mst AS t5 ON  t5.ref_no=t3.ref_no\n" +
            "LEFT JOIN claim_inspection_info_main_me AS t6 ON t6.n_ref_no = t3.ref_no";

    String SQL_SELECT_PAYMENT_STATUS_BY_KEY_ID = "SELECT payment_status FROM assessor_payment_approve_mst WHERE key_id=?";

    String SQL_UPDATE_PAYMENT_STATUS_BY_KEY_ID = "UPDATE assessor_payment_approve_mst SET payment_status='H' WHERE key_id=?";

    String SQL_SELECT_FROM_PENDING_EMAIL_STATUS = "SELECT\n" +
            "	t1.voucher_no,\n" +
            "	t1.key_id,\n" +
            "	t1.claim_no,\n" +
            "   t4.V_ISF_CLAIM_NO,\n" +
            "	t1.cost_of_call,\n" +
            "	t1.other_fee,\n" +
            "	t1.deduction_fee,\n" +
            "	t1.professional_fee,\n" +
            "	t1.travel_fee,\n" +
            "	t1.total_fee,\n" +
            "	t1.name,\n" +
            "	t1.apprv_datetime,\n" +
            "   t1.apprv_user,\n" +
            "   t1.inp_userid,\n" +
            "   t1.from_date,\n" +
            "   t1.to_date,\n" +
            "	t2.n_ref_no,\n" +
            "	t2.n_job_type,\n" +
            "	t3.inspection_type_desc,\n" +
            "	t4.V_VEHICLE_NO,\n" +
            "   t4.V_POLICY_CHANNEL_TYPE,\n" +
            "	t5.*\n" +
            "FROM\n" +
            "	assessor_payment_approve_mst AS t1\n" +
            "INNER JOIN claim_inspection_info_main_me AS t2 ON t1.key_id = t2.n_ref_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t2.n_inspection_type = t3.inspection_type_id\n" +
            "INNER JOIN claim_claim_info_main AS t4 ON t1.claim_no = t4.N_CLIM_NO\n" +
            "INNER JOIN assessor_payment_deduction_detail_mst AS t5 ON t1.key_id = t5.ref_no\n" +
            "WHERE\n" +
            "	t1.voucher_no IS NOT NULL\n" +
            "AND t1.voucher_no <> ''\n" +
            "AND t1.type = 'A'\n" +
            "AND t1.email_send_status = 'N'\n" +
            "AND t1.generate_occurrency=?";

    String SQL_SELECT_FROM_PENDING_EMAIL_AND_VOUCHER_GENERATED = "SELECT\n" +
            "	txn_id,\n" +
            "	generate_occurrency\n" +
            "FROM\n" +
            "	assessor_payment_approve_mst\n" +
            "WHERE\n" +
            "	email_send_status = 'N'\n" +
            "AND voucher_no IS NOT NULL\n" +
            "GROUP BY\n" +
            "	generate_occurrency";

    String SQL_SELECT_COUNT_FROM_PENDING_EMAIL_LIST_BY_GENERATE_OCCURRENCY = "SELECT\n" +
            "	COUNT(generate_occurrency) AS count\n" +
            "FROM\n" +
            "	assessor_payment_approve_mst\n" +
            "WHERE\n" +
            "	generate_occurrency =?\n" +
            "AND email_send_status = 'N'";

    String SQL_SELECT_COUNT_FROM_PENDING_EMAIL_LIST_BY_GENERATE_OCCURRENCY_AND_VOUCHER_NO_IS_NOT_NULL = "SELECT\n" +
            "	COUNT(voucher_no) AS count\n" +
            "FROM\n" +
            "	assessor_payment_approve_mst\n" +
            "WHERE\n" +
            "	(\n" +
            "		voucher_no IS NOT NULL\n" +
            "		AND voucher_no <> ''\n" +
            "	)\n" +
            "AND generate_occurrency = ?\n" +
            "AND email_send_status = 'N'";

    String SQL_UPDATE_EMAIL_SEND_STATUS_BY_GENERATE_OCCURRENCY = "UPDATE assessor_payment_approve_mst\n" +
            "SET email_Send_status ='Y'\n" +
            "WHERE generate_occurrency=?";

    String SELECT_ALL_ASSESSORS_BY_REPORTING_TO = "SELECT v_usrid FROM usr_mst WHERE V_REPORT_TO = ?";

    void updateStausByIdAndStatus(Connection connection, Integer txnId, AssessorPaymentDetailsDto assessorPaymentDetailsDto) throws Exception;

    void updateStausByIdAndStatus(Connection connection, Integer txnId, String status, String apprvUser, String apprvDatetime, Integer key, String from_date, String to_date) throws Exception;

    void updateStausByIdAndStatus(Connection connection, Integer txnId, String status, String remark, String apprvUser, String apprvDatetime) throws Exception;

    AssessorPaymentDetailsDto searchByKeyAndType(Connection connection, Integer key, String type) throws Exception;

    BigDecimal getTotalAmount(Connection connection, String paymentStaus, String status, String name, String fromDate, String toDate, String vehicleNumber, String claimNumber, String jobNumber, String inspectionType, String rteCode, String claimType, String policyChannelType);

    List<AssessorPaymentDetailsDto> searchAll(Connection connection, String paymentStaus, String status, String name, String fromDate, String toDate, String vehicleNumber, String claimNumber, String jobNumber, String inspectionType, String rteCode, boolean isGridSearch, String claimType, String policyChannelType) throws Exception;

    DataGridDto getAssessorPaymentDetailsDataGridDto(Connection conn, Map<String,Object> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) throws Exception;

    String getPaymentStatus(Connection connection, Integer keyId) throws Exception;

    void updateHoldPayment(Connection connection, Integer keyId) throws Exception;

    List<AssessorPaymentDetailForEmailDto> getPendingEmailList(Connection connection, Integer generateOccurrency) throws Exception;

    List<AssessorPaymentDetailsDto> getPendingEmailAndSuccessVoucherGeneration(Connection connection) throws Exception;

    Integer getCountForAssessorPaymentListByGenerateOccurrency(Connection connection, Integer generateOccurrency) throws Exception;

    Integer getCountForAssessorPaymentListByVoucherNo(Connection connection, Integer generateOccurrency) throws Exception;

    boolean updateEmailStatus(Connection connection, Integer generateOccurrency) throws Exception;

    List<UserDto> getAssessorListByReportingRte(Connection connection, String rteName);
}
