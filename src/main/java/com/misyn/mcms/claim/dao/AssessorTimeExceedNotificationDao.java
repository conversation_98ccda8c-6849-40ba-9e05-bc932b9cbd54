package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.AssessorAllocationDto;
import com.misyn.mcms.claim.dto.AssessorTimeExceedNotificationDto;

import java.sql.Connection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface AssessorTimeExceedNotification<PERSON>ao  extends BaseDao<AssessorTimeExceedNotificationDto>{

    String SQL_INSERT_TO_ASSESSOR_NOTIFICATION = "insert into assessor_time_exceed_notification values(0,?,?,?,?,?,?,?,?,?,?,?)";
    String SQL_UPDATE_TO_SENT_NOTIFICATION = " UPDATE assessor_time_exceed_notification SET `status` = 'Y' WHERE `id`= ?;";;
    String SELECT_ALL_FROM_ASSESSOR_NOTIFICATION = "select * from assessor_time_exceed_notification as t1 where t1.status = 'P' ";

    List<AssessorTimeExceedNotificationDto> fetchAll(Connection connection) throws Exception ;

    void updateStatus(Connection connection, int claimNo);
}
