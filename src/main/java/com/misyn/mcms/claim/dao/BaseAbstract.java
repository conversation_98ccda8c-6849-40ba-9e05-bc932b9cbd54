/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dao;


import com.misyn.mcms.claim.dto.FieldParameterDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * Created by Kelum Sepala on 1/1/2016.
 */
public abstract class BaseAbstract<T> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseAbstract.class);
    private static final String ORDER_BY = "ORDER BY";
    private static final String GROUP_BY = "GROUP BY";
    private static final String SPACE = " ";
    private static final String COMA = ",";
    private static final String LIMIT = "LIMIT";
    private static final String WHERE = " WHERE ";
    private static final String EQUAL = " = ";
    private static final String EQUAL_AND_GREATER_THAN = " >= ";
    private static final String EQUAL_AND_LESS_THAN = " <= ";
    private static final String NOT_EQUAL = " <> ";
    private static final String LIKE = " LIKE ";
    private static final String IN = " IN ";
    private static final String NOT_IN = " NOT IN ";
    private static final String IS_NULL = " IS NULL ";
    private static final String AND = " AND ";
    private static final String PERCENTAGE_SIGN_START = " '%";
    private static final String PERCENTAGE_SIGN_END = "%' ";
    private static final String BRACKET_OPEN = "(";
    private static final String BRACKET_CLOSE = ")";
    private static final String SQL_SELECT_ENTITY_STATUS_BY_ALL = "SELECT * FROM entity_status ";
    private static final String SQL_SELECT_MESSAGE_BY_ALL = "SELECT * FROM message ";
    private final static String SQL_SELECT_PACKAGE_TYPE_BY_PACKAGE_TYPE_ID = "SELECT * FROM package_type WHERE package_type_id=?";
    private final static String SQL_SELECT_PACKAGE_TYPE_BY_ALL = "SELECT * FROM package_type";

    /**
     * Rejected Message select query
     */
    private static final String SQL_SELECT_REJECT_MESSAGE = "SELECT * FROM reject_message";
    private static final String SQL_SELECT_MAX_KEY_TABLE_BY_TABLE_TYPE_ID = "SELECT max_value FROM max_key_table WHERE table_type_id=?";
    private static final String SQL_UPDATE_MAX_KEY_TABLE_BY_TABLE_TYPE_ID = "UPDATE max_key_table SET max_value=max_value+1 WHERE table_type_id=?";

    /**
     * Rejected Message insert query
     */
    private static final String SQL_INSERT_REJECT_MESSAGE = "INSERT INTO reject_message VALUE (0,?,?,?,?,?)";

    /**
     * Select Rejected Messages by Super Rejected Message id query
     */
    private static final String SQL_SELECT_REJECT_MESSAGE_BY_SUPER_REJECT_MESSAGE_ID = "SELECT * FROM reject_message WHERE `type` = ? AND guest_table_id = ? ORDER BY datetime DESC";


    protected StringBuilder formatSQL(List<FieldParameterDto> parameterList) {
        StringBuilder sbFormattedSql = new StringBuilder();
        if (!parameterList.isEmpty()) {
            sbFormattedSql.append(WHERE);
            for (FieldParameterDto fieldParameter : parameterList) {
                if (fieldParameter.isStringType()) {
                    String formattedValue = toSQL(fieldParameter.getFieldValue());
                    fieldParameter.setFieldValue(formattedValue);
                } else {
                    // if(!AppConstant.STRING_EMPTY.equals(fieldParameter.getFieldValue()) && !Utility.isNumber(fieldParameter.getFieldValue())){
                    //    fieldParameter.setFieldValue(AppConstant.ZERO);
                    //  }
                }
                sbFormattedSql.append(fieldParameter.getDbFieldName());
                if (fieldParameter.isStringType() || fieldParameter.getSearchType() == FieldParameterDto.SearchType.Like) {
                    if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.Like) {
                        sbFormattedSql.append(LIKE);
                        sbFormattedSql.append(fieldParameter.isStringType() ? PERCENTAGE_SIGN_START : "");
                        sbFormattedSql.append(fieldParameter.getFieldValue());
                        sbFormattedSql.append(fieldParameter.isStringType() ? PERCENTAGE_SIGN_END : "");
                    } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.NOT_Equal) {
                        sbFormattedSql.append(NOT_EQUAL);
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                        sbFormattedSql.append(fieldParameter.getFieldValue());
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                    } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.Equal_And_Greater_Than) {
                        sbFormattedSql.append(EQUAL_AND_GREATER_THAN);
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                        sbFormattedSql.append(fieldParameter.getFieldValue());
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                    } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.Equal_And_Less_Than) {
                        sbFormattedSql.append(EQUAL_AND_LESS_THAN);
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                        sbFormattedSql.append(fieldParameter.getFieldValue());
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                    } else {
                        sbFormattedSql.append(EQUAL);
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                        sbFormattedSql.append(fieldParameter.getFieldValue());
                        sbFormattedSql.append(fieldParameter.isStringType() ? "'" : "");
                    }

                } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.IN) {
                    sbFormattedSql.append(IN);
                    sbFormattedSql.append(BRACKET_OPEN);
                    sbFormattedSql.append(fieldParameter.getFieldValue());
                    sbFormattedSql.append(BRACKET_CLOSE);
                } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.NOT_IN) {
                    sbFormattedSql.append(NOT_IN);
                    sbFormattedSql.append(BRACKET_OPEN);
                    sbFormattedSql.append(fieldParameter.getFieldValue());
                    sbFormattedSql.append(BRACKET_CLOSE);
                } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.NOT_Equal) {
                    sbFormattedSql.append(NOT_EQUAL).append(fieldParameter.getFieldValue());
                } else if (fieldParameter.getSearchType() == FieldParameterDto.SearchType.IS_NULL) {
                    sbFormattedSql.append(IS_NULL);
                } else {
                    sbFormattedSql.append(EQUAL).append(fieldParameter.getFieldValue());
                }
                sbFormattedSql.append(AND);
            }
            sbFormattedSql.delete(sbFormattedSql.toString().length() - 4, sbFormattedSql.toString().length());
        }
        // LOGGER.debug(sbFormattedSql.toString());
        return sbFormattedSql;
    }

    protected String toSQL(String fieldValue) {
        return fieldValue
                .replaceAll("%", "\\\\%")
                .replaceAll("\\^", "\\\\^")
                .replaceAll("!", "\\\\!")
                .replaceAll("\'", "\\\\'");
    }

    protected StringBuilder formatOrderSQL(String orderType, String orderColumnName) {
        StringBuilder sbFormattedSql = new StringBuilder();
        //t` ORDER BY `usrid` LIMIT 0, 1000
        sbFormattedSql.append(ORDER_BY).append(SPACE).append(orderColumnName).append(SPACE).append(orderType).append(SPACE);
        //   LOGGER.debug(sbFormattedSql.toString());
        return sbFormattedSql;
    }

    protected StringBuilder formatOrderSQL(int start, int length, String orderType, String orderColumnName) {
        StringBuilder sbFormattedSql = new StringBuilder();
        //t` ORDER BY `usrid` LIMIT 0, 1000
        sbFormattedSql.append(ORDER_BY).append(SPACE).append(orderColumnName).append(SPACE).append(orderType)
                .append(SPACE).append(LIMIT).append(SPACE).append(start).append(COMA).append(length);
        //   LOGGER.debug(sbFormattedSql.toString());
        return sbFormattedSql;
    }

    protected StringBuilder formatGroupSQL(String groupColumnName) {
        StringBuilder sbFormattedSql = new StringBuilder();
        sbFormattedSql.append(GROUP_BY).append(SPACE).append(groupColumnName).append(SPACE);
        LOGGER.debug(sbFormattedSql.toString());
        return sbFormattedSql;
    }

    protected StringBuilder formatOrderSQL(int start, String orderType, String orderColumnName) {
        StringBuilder sbFormattedSql = new StringBuilder();
        //t` ORDER BY `usrid`
        sbFormattedSql.append(ORDER_BY).append(SPACE).append(orderColumnName).append(SPACE).append(orderType);
        //  LOGGER.debug(sbFormattedSql.toString());
        return sbFormattedSql;
    }

    protected StringBuilder orderByCases(int start, int length, String orderType, String caseOrderColumn, String[] orderColumns, Object[] instances) {
        StringBuilder stringBuilder = new StringBuilder();
        int size = instances.length;
        stringBuilder.append(ORDER_BY).append(SPACE).append("CASE").append(SPACE).append(caseOrderColumn).append(SPACE);
        for (int i = 0; i < size - 1; i++) {
            stringBuilder.append("WHEN").append(SPACE).append("'" + instances[i] + "'").append(SPACE).append("THEN").append(SPACE).append(i + 1).append(SPACE);
        }
        stringBuilder.append("ELSE").append(SPACE).append(size).append(SPACE).append("END").append(COMA);
        String coma = "";
        for (int i = 0; i < orderColumns.length; i++) {
            stringBuilder.append(coma).append(orderColumns[orderColumns.length - 1]);
            coma = ",";
        }
        stringBuilder.append(SPACE).append(orderType).append(SPACE).append(LIMIT).append(SPACE).append(start).append(COMA).append(length);
        return stringBuilder;
    }
}
