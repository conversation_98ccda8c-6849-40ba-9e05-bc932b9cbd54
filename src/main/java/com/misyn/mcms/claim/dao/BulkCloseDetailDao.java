package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.BulkCloseDetailDto;

import java.sql.Connection;

/**
 * <AUTHOR>
 */
public interface BulkCloseDetailDao {
    String SQL_INSERT = "INSERT INTO claim_bulk_close_details VALUES(0,?,?,?,?)";

    String SQL_DELETE_BY_CLAIM_NO = "DELETE FROM claim_bulk_close_details WHERE N_CLAIM_NO = ?";

    String SQL_GET_BY_CLAIM_NO = "SELECT * FROM claim_bulk_close_details WHERE N_CLAIM_NO = ? ORDER BY N_ID DESC LIMIT 1";

    BulkCloseDetailDto insert(Connection connection, BulkCloseDetailDto bulkCloseDetailDto) throws Exception;

    boolean deleteByClaimNo(Connection connection, Integer claimNo) throws Exception;

    BulkCloseDetailDto getBulkCloseDetails(Connection connection, Integer claimNoToSearch) throws Exception;
}
