package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.CalSheetSpecialRemarkDto;

import java.sql.Connection;
import java.util.List;

/**
 * Created by a<PERSON><PERSON> on 9/6/18.
 */
public interface CalSheetSpecialRemarkDao {

    String SQL_INSERT_INTO_VALUES = "INSERT INTO cal_sheet_special_remark VALUES(0,?,?,?,?)";

    String SQL_SELECT_SPECIAL_REMARK_LIST = "SELECT * FROM cal_sheet_special_remark WHERE cal_sheet_id=?";

    CalSheetSpecialRemarkDto save(Connection connection, CalSheetSpecialRemarkDto calSheetSpecialRemarkDto) throws Exception;

    List<CalSheetSpecialRemarkDto> calSheetRemarkList(Connection connection, Integer calSheetId) throws Exception;
}
