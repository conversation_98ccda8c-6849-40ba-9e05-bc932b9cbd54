package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.CalculationProcessFlowDto;

import java.sql.Connection;
import java.util.List;

public interface CalculationProcessFlowDao {
    String SQL_INSERT = "INSERT INTO claim_calculation_process_flow VALUES(0,?,?,?,?,?,?,?)";
    String SQL_SELECT_ALL = "SELECT\n" +
            "t1.txn_id,\n" +
            "t1.cal_sheet_id,\n" +
            "t1.claim_no,\n" +
            "t1.cal_sheet_status,\n" +
            "t1.inp_user_id,\n" +
            "t1.inp_date_time,\n" +
            "t1.assign_user_id,\n" +
            "t1.task,\n" +
            "t2.v_status_desc\n" +
            "FROM\n" +
            "claim_calculation_process_flow AS t1\n" +
            "INNER JOIN claim_status_para AS t2 ON t2.n_ref_id = t1.cal_sheet_status " +
            "WHERE t1.cal_sheet_id = ?";

    CalculationProcessFlowDto insert(Connection connection, CalculationProcessFlowDto calculationProcessFlowDto) throws Exception;

    List<CalculationProcessFlowDto> searchAllByCalSheetId(Connection connection, Integer calSheetId);
}
