package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.CalculationSheetAssignMofaLevelDetailDto;
import com.misyn.mcms.claim.dto.CalculationSheetMofaLevelHistoryDto;

import java.sql.Connection;
import java.util.List;

public interface CalculationSheetAssignMofaLevelDetailDao extends BaseDao<CalculationSheetAssignMofaLevelDetailDto> {

    String INSERT_INTO_CALCULATION_SHEET_ASSIGN_MOFA_LEVEL_MST = "INSERT INTO calculation_sheet_assign_mofa_level_mst VALUES (0, ?, ?, ?, ?, ?, ?, ?)";

    String INSERT_INTO_CALCULATION_SHEET_ASSIGN_MOFA_LEVEL_HST = "INSERT INTO calculation_sheet_assign_mofa_level_hst VALUES (0, ?, ?, ?, ?, ?, ?, ?)";

    String DELETE_BY_CALSHEET_ID = "DELETE FROM calculation_sheet_assign_mofa_level_mst WHERE calsheet_id = ?";

    String SELECT_ASSIGN_MOFA_USER_BY_ASSIGN_MOFA_LEVEL_AND_CALSHEET_ID = "SELECT\n" +
            "	assign_user_id \n" +
            "FROM\n" +
            "	calculation_sheet_assign_mofa_level_mst AS t1\n" +
            "	INNER JOIN usr_mst\n" +
            "	AS t2 ON t1.assign_user_id = t2.v_usrid\n" +
            "WHERE\n" +
            "	t1.assign_user_mofa_level = ?\n" +
            "	AND t1.calsheet_id = ? AND t2.n_accessusrtype = ?\n" +
            "   ORDER BY txn_id DESC LIMIT 1";

    String SELECT_APPROVED_MOFA_LEVEL = "SELECT\n" +
            "   t1.txn_id,\n" +
            "   t1.input_user_id,\n" +
            "   t1.input_user_mofa_level,\n" +
            "   t1.assign_datetime,\n" +
            "   t2.from_limit,\n" +
            "   t2.to_limit\n" +
            "   FROM\n" +
            "   calculation_sheet_assign_mofa_level_mst AS t1\n" +
            "   INNER JOIN user_authority_limit AS t2 ON t1.input_user_mofa_level = t2.level_code\n" +
            "   WHERE\n" +
            "   t2.department_id = 7\n" +
            "   AND t1.calsheet_id = ?\n" +
            "   AND t1.txn_id>=(SELECT max(txn_id)AS max FROM calculation_sheet_assign_mofa_level_mst AS t3 WHERE input_user_mofa_level = 1 AND t3.calsheet_id = t1.calsheet_id)\n" +
            "   ORDER BY txn_id";

    String getAssignMofaUserByAssignMofaLevelAndCalsheetId(Connection connection, Integer calSheetId, int levelCode, int accessUserType) throws Exception;

    List<CalculationSheetMofaLevelHistoryDto> getApprovedMofaListByCalsheetId(Connection connection, Integer calsheetId);
}
