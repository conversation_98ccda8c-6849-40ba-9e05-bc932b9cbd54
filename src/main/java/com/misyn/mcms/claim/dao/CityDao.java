package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.CityDto;

import java.sql.Connection;
import java.util.List;

/**
 * Created by aki<PERSON> on 4/16/18.
 */
public interface CityDao extends BaseDao<CityDto> {

    String SELECT_ALL_FROM_CITY_MASTER = "select * from claim_gramas";
    String SELECT_ALL_FROM_CITY_MASTER_BY_DIVISION_CODE = "select * from claim_gramas where V_DISTRICT_CODE=?";
    String SELECT_ALL_FROM_CITY_MASTER_BY_CITY_CODE = "select * from claim_gramas WHERE N_GRAMA_CODE=?";

    public List<CityDto> getCityListByDistrictCode(Connection connection, String divisionCode) throws Exception;
}
