package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimCalculationSheetDetailTypeDto;

public interface ClaimCalculationSheetDetailTypeDao extends BaseDao<ClaimCalculationSheetDetailTypeDto> {
    String CLAIM_CALCULATION_SHEET_DETAIL_TYPE_INSERT = " INSERT INTO claim_calculation_sheet_detail_type VALUES (0,?)";
    String CLAIM_CALCULATION_SHEET_DETAIL_TYPE_UPDATE = "UPDATE claim_calculation_sheet_detail_type SET V_CAL_SHEET_DETAIL_TYPE_DESC =? WHERE N_CAL_SHEET_DETAIL_TYPE_ID =?";
    String CLAIM_CALCULATION_SHEET_DETAIL_TYPE_SEARCH = "SELECT * FROM claim_calculation_sheet_detail_type WHERE N_CAL_SHEET_DETAIL_TYPE_ID =?";
    String CLAIM_CALCULATION_SHEET_DETAIL_TYPE_SEARCH_ALL = "SELECT * FROM claim_calculation_sheet_detail_type";
    String CLAIM_CALCULATION_SHEET_DETAIL_TYPE_DELETE = "DELETE FROM claim_calculation_sheet_detail_type WHERE N_CAL_SHEET_DETAIL_TYPE_ID =?";
}
