package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimCalculationSheetPayeeNameDto;

import java.sql.Connection;
import java.util.Map;

public interface ClaimCalculationSheetPayeeNameDao extends BaseDao<ClaimCalculationSheetPayeeNameDto> {
    String CLAIM_CALCULATION_SHEET_PAYEE_NAME_INSERT = "INSERT INTO claim_calculation_sheet_payee_name VALUES (0,?,?)";
    String CLAIM_CALCULATION_SHEET_PAYEE_NAME_UPDATE = "UPDATE claim_calculation_sheet_payee_name SET \n" +
            "V_PAYEE_NAME =?,\n" +
            "V_STATUS =? WHERE N_CAL_SHEET_PAYEE_NAME_ID =?\n";
    String CLAIM_CALCULATION_SHEET_PAYEE_NAME_DELETE = "DELETE FROM claim_calculation_sheet_payee_name WHERE N_CAL_SHEET_PAYEE_NAME_ID =?";
    String CLAIM_CALCULATION_SHEET_PAYEE_NAME_SEARCH = "SELECT * FROM claim_calculation_sheet_payee_name WHERE N_CAL_SHEET_PAYEE_NAME_ID =?";
    String CLAIM_CALCULATION_SHEET_PAYEE_NAME_SEARCH_ALL = "SELECT * FROM claim_calculation_sheet_payee_name";

    Map<Integer,String> getInstrumentTypeDetails(Connection connection);
}
