package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimCalculationSheetSupplierOrderDto;

import java.sql.Connection;

public interface ClaimCalculationSheetSupplierOrderDao extends BaseDao<ClaimCalculationSheetSupplierOrderDto> {

    String CLAIM_CALCULATION_SHEET_SUPPLIER_ORDER_INSERT = "INSERT INTO claim_calculation_sheet_supplier_order VALUES (0,?,?)";
    String CLAIM_CALCULATION_SHEET_SUPPLIER_ORDER_UPDATE = "UPDATE claim_calculation_sheet_supplier_order SET \n"
            + "N_CAL_SHEET_ID =?,\n"
            + "N_SUPPLIER_ORDER_ID =? WHERE N_CAL_SHEET_SO_ID =?\n";
    String CLAIM_CALCULATION_SHEET_SUPPLIER_ORDER_SEARCH = "SELECT * FROM claim_calculation_sheet_supplier_order WHERE N_CAL_SHEET_SO_ID =?";
    String CLAIM_CALCULATION_SHEET_SUPPLIER_ORDER_SEARCH_BY_CAL_SHEET_ID = "SELECT * FROM claim_calculation_sheet_supplier_order WHERE N_CAL_SHEET_ID =?";
    String CLAIM_CALCULATION_SHEET_SUPPLIER_ORDER_SEARCH_ALL = "SELECT * FROM claim_calculation_sheet_supplier_order";
    String CLAIM_CALCULATION_SHEET_SUPPLIER_ORDER_DELETE = "DELETE FROM claim_calculation_sheet_supplier_order WHERE N_CAL_SHEET_SO_ID =?";

    ClaimCalculationSheetSupplierOrderDto searchMasterByCalSheetId(Connection connection, Integer calSheetId) throws Exception;
}
