package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimDocumentDto;
import com.misyn.mcms.claim.dto.ClaimDocumentLossTypeOldDto;
import com.misyn.mcms.claim.dto.ClaimImageDto;
import com.misyn.mcms.claim.dto.ClaimUploadViewDto;
import com.misyn.mcms.claim.enums.DocumentStatusEnum;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.sql.Connection;
import java.util.List;

public interface ClaimDocumentDao extends BaseDao<ClaimDocumentDto> {
    String INSERT_CLAIM_UPLOAD_DOCUMENTS = "INSERT INTO claim_upload_documents VALUES(0,?,?,?,?" +
            ",?,?,?,?,?" +
            ",?,?,?,?,?" +
            ",?,?,?,?,?" +
            ",?,?,?,?,?" +
            ",?,?,?,?)";

    String SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_REF_NO = "SELECT\n" +
            "t1.*,\n" +
            "t2.V_DOC_TYPE_NAME\n" +
            "FROM\n" +
            "claim_upload_documents AS t1\n" +
            "INNER JOIN claim_document_type AS t2 ON t1.N_DOC_TYPE_ID = t2.N_DOC_TYPE_ID\n" +
            "WHERE\n" +
            "t1.N_REF_NO = ?";

    String SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_BILL_CHECK_REF_NO = "SELECT\n" +
            "t1.*,\n" +
            "t2.V_DOC_TYPE_NAME\n" +
            "FROM\n" +
            "claim_upload_documents AS t1\n" +
            "INNER JOIN claim_document_type AS t2 ON t1.N_DOC_TYPE_ID = t2.N_DOC_TYPE_ID\n" +
            "WHERE\n" +
            "t1.N_BILL_CHECK_REF_NO = ?";

    String SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_N_JOB_REF_NO = "SELECT\n" +
            "t2.N_DOC_TYPE_ID,\n" +
            "t2.V_DOC_TYPE_NAME,\n" +
            "t1.N_REF_NO,\n" +
            "t1.N_JOB_REF_NO,\n" +
            "t1.N_CLIM_NO,\n" +
            "t1.N_DOC_TYPE_ID,\n" +
            "t1.N_DEPARTMENT_ID,\n" +
            "t1.V_IS_MANDATORY,\n" +
            "t1.V_DOC_STATUS,\n" +
            "t1.V_DOC_PATH,\n" +
            "t1.V_DOC_NAME,\n" +
            "t1.V_IS_DOC_UPLOAD,\n" +
            "t1.V_IS_CHECK,\n" +
            "t1.V_CHECK_USER,\n" +
            "t1.D_CHECK_DATE_TIME,\n" +
            "t1.V_HOLD_USER,\n" +
            "t1.D_HOLD_DATE_TIME,\n" +
            "t1.V_REJECT_USER,\n" +
            "t1.D_REJECT_DATE_TIME,\n" +
            "t1.V_BILL_SUMMARY_REMARK,\n" +
            "t1.V_BILL_SUMMARY_CHECK_USER,\n" +
            "t1.D_BILL_SUMMARY_CHECK_DATE_TIME,\n" +
            "t1.V_REMARK,\n" +
            "t1.V_CANCEL_USER,\n" +
            "t1.D_CANCEL_DATE_TIME,\n" +
            "t1.V_CANCEL_REMARK,\n" +
            "t1.V_INP_STAT,\n" +
            "t1.V_INP_USER,\n" +
            "t1.D_INP_DATE_TIME\n" +
            "FROM\n" +
            "claim_document_type AS t2\n" +
            "INNER JOIN claim_upload_documents AS t1 ON t1.N_DOC_TYPE_ID = t2.N_DOC_TYPE_ID AND t1.N_JOB_REF_NO = ? " +
            "WHERE\n" +
            "t2.N_DEPARTMENT_ID = ?\n" +
            "ORDER BY t2.N_DOC_TYPE_ID, N_REF_NO DESC";


    String SELECT_CLAIM_DOCUMENT_TYPE_BY_N_DEPARTMENT_ID_AND_INSPECTION_TYPE = "SELECT\n" +
            "t2.*\n" +
            "FROM\n" +
            "claim_document_type AS t2\n" +
            "INNER JOIN claim_document_type_inspection_type AS t1 ON t1.N_DOC_TYPE_ID = t2.N_DOC_TYPE_ID\n" +
            "WHERE\n" +
            "t2.N_DEPARTMENT_ID = ? AND\n" +
            "t1.inspection_type_id = ?";

    String DELETE_CLAIM_UPLOAD_DOCUMENTS_BY_REF_ID = "DELETE FROM claim_upload_documents where N_REF_NO=? and N_DOC_TYPE_ID IN (?)";

    // String SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_N_JOB_REF_NO_AND_CLAIM_NO_AND_DOC_TYPE_ID = "SELECT t1.* FROM claim_upload_documents AS t1 WHERE t1.N_JOB_REF_NO=? AND t1.N_CLIM_NO=? AND t1.N_DOC_TYPE_ID=?";
    String SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_N_JOB_REF_NO_AND_CLAIM_NO_AND_DOC_TYPE_ID = "SELECT t1.*,t2.V_DOC_TYPE_NAME FROM \n" +
            "claim_upload_documents AS t1 \n" +
            "INNER JOIN claim_document_type AS t2 ON t1.N_DOC_TYPE_ID = t2.N_DOC_TYPE_ID\n" +
            "WHERE t1.N_JOB_REF_NO=? AND t1.N_CLIM_NO=? AND t1.N_DOC_TYPE_ID=?";

    String SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_CLAIM_NO_AND_JOB_REF_NO = "SELECT\n" +
            "t1.N_REF_NO,\n" +
            "t1.N_JOB_REF_NO,\n" +
            "t1.N_CLIM_NO,\n" +
            "t1.N_DOC_TYPE_ID,\n" +
            "t1.N_DEPARTMENT_ID,\n" +
            "t1.V_IS_MANDATORY,\n" +
            "t1.V_DOC_STATUS,\n" +
            "t1.V_DOC_PATH,\n" +
            "t1.V_DOC_NAME,\n" +
            "t1.V_IS_DOC_UPLOAD,\n" +
            "t1.V_IS_CHECK,\n" +
            "t1.V_CHECK_USER,\n" +
            "t1.D_CHECK_DATE_TIME,\n" +
            "t1.V_HOLD_USER,\n" +
            "t1.D_HOLD_DATE_TIME,\n" +
            "t1.V_REJECT_USER,\n" +
            "t1.D_REJECT_DATE_TIME,\n" +
            "t1.V_BILL_SUMMARY_REMARK,\n" +
            "t1.V_BILL_SUMMARY_CHECK_USER,\n" +
            "t1.D_BILL_SUMMARY_CHECK_DATE_TIME,\n" +
            "t1.V_REMARK,\n" +
            "t1.V_CANCEL_USER,\n" +
            "t1.D_CANCEL_DATE_TIME,\n" +
            "t1.V_CANCEL_REMARK,\n" +
            "t1.V_INP_STAT,\n" +
            "t1.V_INP_USER,\n" +
            "t1.D_INP_DATE_TIME,\n" +
            "t2.N_DOC_TYPE_ID,\n" +
            "t2.V_DOC_TYPE_NAME\n" +
            "FROM\n" +
            "claim_upload_documents AS t1\n" +
            "INNER JOIN claim_document_type AS t2 ON t1.N_DOC_TYPE_ID = t2.N_DOC_TYPE_ID\n" +
            "WHERE\n" +
            "t1.N_CLIM_NO = ? AND\n" +
            "t1.N_JOB_REF_NO IN ((SELECT ref_no FROM claim_assign_assesor WHERE claim_no=? AND job_id=?))";

    String SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_CLAIM_NO = "SELECT\n" +
            "t1.N_REF_NO,\n" +
            "t1.N_JOB_REF_NO,\n" +
            "t1.N_CLIM_NO,\n" +
            "t1.N_DOC_TYPE_ID,\n" +
            "t1.N_DEPARTMENT_ID,\n" +
            "t1.V_IS_MANDATORY,\n" +
            "t1.V_DOC_STATUS,\n" +
            "t1.V_DOC_PATH,\n" +
            "t1.V_DOC_NAME,\n" +
            "t1.V_IS_DOC_UPLOAD,\n" +
            "t1.V_IS_CHECK,\n" +
            "t1.V_CHECK_USER,\n" +
            "t1.D_CHECK_DATE_TIME,\n" +
            "t1.V_HOLD_USER,\n" +
            "t1.D_HOLD_DATE_TIME,\n" +
            "t1.V_REJECT_USER,\n" +
            "t1.D_REJECT_DATE_TIME,\n" +
            "t1.V_BILL_SUMMARY_REMARK,\n" +
            "t1.V_BILL_SUMMARY_CHECK_USER,\n" +
            "t1.D_BILL_SUMMARY_CHECK_DATE_TIME,\n" +
            "t1.V_REMARK,\n" +
            "t1.V_CANCEL_USER,\n" +
            "t1.D_CANCEL_DATE_TIME,\n" +
            "t1.V_CANCEL_REMARK,\n" +
            "t1.V_INP_STAT,\n" +
            "t1.V_INP_USER,\n" +
            "t1.D_INP_DATE_TIME,\n" +
            "t2.N_DOC_TYPE_ID,\n" +
            "t2.V_DOC_TYPE_NAME\n" +
            "FROM\n" +
            "claim_upload_documents AS t1\n" +
            "INNER JOIN claim_document_type AS t2 ON t1.N_DOC_TYPE_ID = t2.N_DOC_TYPE_ID\n" +
            "WHERE\n" +
            "t1.N_CLIM_NO = ? ";

    String SELECT_CLAIM_UPLOAD_DOCUMENTS_CLAIM_NO_AND_DOC_TYPE_AND_DOC_STATUS = "SELECT\n" +
            "t1.*,\n" +
            "t2.V_DOC_TYPE_NAME\n" +
            "FROM\n" +
            "claim_upload_documents AS t1\n" +
            "INNER JOIN claim_document_type AS t2 ON t1.N_DOC_TYPE_ID = t2.N_DOC_TYPE_ID\n" +
            "WHERE\n" +
            "t1.N_CLIM_NO = ? AND\n" +
            "t2.N_DOC_TYPE_ID = ? AND\n" +
            "t1.V_DOC_STATUS = ?";

    String SELECT_CLAIM_UPLOAD_DOCUMENTS_CLAIM_NO_AND_DOC_TYPE_AND_IN_DOC_STATUS = "SELECT\n" +
            "	t1.*, t2.V_DOC_TYPE_NAME\n" +
            "FROM\n" +
            "	claim_upload_documents AS t1\n" +
            "INNER JOIN claim_document_type AS t2 ON t1.N_DOC_TYPE_ID = t2.N_DOC_TYPE_ID\n" +
            "WHERE\n" +
            "	t1.N_CLIM_NO =?\n" +
            "AND t1.N_DOC_TYPE_ID =?\n" +
            "AND t1.V_DOC_STATUS IN ('P', 'A')";


    String SELECT_IS_UPLOAD_ALL_MANDATORY_DOCUMNET = "SELECT DISTINCT\n" +
            "	(t1.N_DOC_TYPE_ID)\n" +
            "FROM\n" +
            "	claim_upload_documents AS t1\n" +
            "WHERE\n" +
            "	N_CLIM_NO = ?\n" +
            "AND N_DOC_TYPE_ID = ?\n" +
            "AND V_DOC_STATUS IN ('P', 'A')";

    String SELECT_IS_CHECK_ALL_MANDATORY_DOCUMNET = "SELECT DISTINCT\n" +
            "	(t1.N_DOC_TYPE_ID)\n" +
            "FROM\n" +
            "	claim_upload_documents AS t1\n" +
            "WHERE\n" +
            "	t1.N_CLIM_NO = ?\n" +
            "AND t1.N_DOC_TYPE_ID = ?\n" +
            "AND t1.V_DOC_STATUS = 'P' ";

    String SELECT_IS_FOUND_CHECK_ALL_MANDATORY_DOCUMNET = "SELECT\n" +
            "	(t1.N_DOC_TYPE_ID),V_DOC_STATUS\n" +
            "FROM\n" +
            "	claim_upload_documents AS t1\n" +
            "WHERE\n" +
            "	t1.N_CLIM_NO = ?\n" +
            "AND t1.N_DOC_TYPE_ID = ?\n" +
            "AND t1.V_DOC_STATUS IN('P','A')";

    String SELECT_ALL_MANDATORY_DOC_BY_CLAIM_NO = "SELECT\n" +
            "t1.N_DOC_TYPE_ID,\n" +
            "t1.N_CLAIM_NO,\n" +
            "t1.V_IS_MANDATORY,\n" +
            "IFNULL(t2.V_DOC_STATUS,'P') AS V_DOC_STATUS\n" +
            "FROM\n" +
            "claim_documents_claim_wise AS t1\n" +
            "LEFT JOIN claim_upload_documents AS t2 ON t1.N_CLAIM_NO = t2.N_CLIM_NO AND t1.N_DOC_TYPE_ID = t2.N_DOC_TYPE_ID \n" +
            "AND t2.V_DOC_STATUS NOT IN('R','H')\n" +
            "WHERE\n" +
            "t1.N_CLAIM_NO = ? AND\n" +
            "t1.V_IS_MANDATORY = 'Y'";

    String UPDATE_CLAIM_UPLOAD_DOCUMENTS_BILL_CHECK_BY_REF_ID = "UPDATE claim_upload_documents SET " +
            "V_DOC_STATUS=?," +
            "V_IS_CHECK=?," +
            "V_CHECK_USER=?," +
            "D_CHECK_DATE_TIME=?," +
            "V_BILL_SUMMARY_REMARK=?," +
            "V_BILL_SUMMARY_CHECK_USER=?," +
            "D_BILL_SUMMARY_CHECK_DATE_TIME=? " +
            "WHERE N_REF_NO=?";
    String UPDATE_CLAIM_UPLOAD_DOCUMENTS_BY_REF_ID = "UPDATE claim_upload_documents SET V_DOC_STATUS=?,V_IS_CHECK=?,V_CHECK_USER=?,D_CHECK_DATE_TIME=? WHERE N_REF_NO=?";
    String UPDATE_CLAIM_UPLOAD_DOCUMENTS_REJECT_BY_REF_ID = "UPDATE claim_upload_documents SET V_DOC_STATUS=?,V_REJECT_USER=?,D_REJECT_DATE_TIME=?,V_REMARK=? WHERE N_REF_NO=?";
    String UPDATE_CLAIM_UPLOAD_DOCUMENTS_HOLD_BY_REF_ID = "UPDATE claim_upload_documents SET V_DOC_STATUS=?,V_HOLD_USER=?,D_HOLD_DATE_TIME=? WHERE N_REF_NO=?";

    String INSERT_CLAIM_DOCUMENT_LOSS_TYPE_OLD = "INSERT INTO claim_document_loss_type_old VALUES(0,?,?,?,?,?,?)";

    String UPDATE_CLAIM_DOCUMENT_LOSS_TYPE_OLD = "UPDATE claim_document_loss_type_old SET \n" +
            "V_IS_MANDATORY = ?,\n" +
            "V_OLD_STATUS = ?,\n" +
            "V_INP_USER_ID = ?,\n" +
            "D_INP_DATE_TIME = ? WHERE N_DOC_TYP_ID = ? AND N_CLAIM_NO = ?\n";

    String UPDATE_JOB_REF_NO = "UPDATE claim_upload_documents SET N_JOB_REF_NO =? WHERE N_CLIM_NO = ? AND N_JOB_REF_NO =0 AND N_DOC_TYPE_ID=3";

    String SELECT_CLAIM_DOCUMENT_LOSS_TYPE_OLD = "SELECT * FROM claim_document_loss_type_old WHERE N_CLAIM_NO =?";

    String SELECT_ALL_UPLOADED_DOCUMENTS_BY_CLAIM_NO = "SELECT * FROM claim_upload_documents WHERE N_CLIM_NO = ?";

    String UPDATE_CANCEL_DOCUMENT_DETAILS = "UPDATE claim_upload_documents SET V_DOC_STATUS = ?, V_CANCEL_USER = ?, D_CANCEL_DATE_TIME = ?, V_CANCEL_REMARK = ? WHERE N_REF_NO = ?";

    String GET_LAST_BRANCH_DOCUMENT_UPLOAD_DATE_TIME = "SELECT t1.D_INP_DATE_TIME FROM claim_upload_documents t1\n" +
            "INNER JOIN usr_mst AS t2 ON t1.V_INP_USER = t2.v_usrid\n" +
            "WHERE N_CLIM_NO = ?\n" +
            "AND t2.n_accessusrtype = 100\n" +
            "ORDER BY D_INP_DATE_TIME DESC LIMIT 1";

    List<ClaimDocumentDto> getClaimDocumentDtoList(Connection connection, Integer jobRefNo, Integer departmentId);

    List<ClaimUploadViewDto> getClaimUploadViewDtoList(Connection connection, Integer claimNo, Integer jobRefNo, Integer departmentId, Integer inspectionTypeId);

    boolean deleteMaster(Connection connection, Integer refId, Integer documentTypeId) throws Exception;

    List<ClaimDocumentDto> findAllByClaimNoAndInspectionJobNoAndDocumentTypeId(Connection connection, Integer claimNo, String inspectionJobNo, Integer documentTypeId);

    List<ClaimDocumentDto> findAllByClaimNoAndDocumentTypeId(Connection connection, Integer claimNo, Integer documentTypeId);

    List<ClaimDocumentDto> findAllByClaimNoAndDocumentTypeIdAndDocumetStatus(Connection connection, Integer claimNo, Integer documentTypeId, DocumentStatusEnum documentStatus);

    List<ClaimDocumentDto> findAllByClaimNoAndDocumentTypeIdAndInDocumentStatus(Connection connection, Integer claimNo, Integer documentTypeId);

    List<ClaimDocumentDto> findAllByClaimNoAndInDocumentTypeIdAndInDocumentStatus(Connection connection, Integer claimNo, String documentTypeIds);

    boolean isUploadAllMandatoryDocument(Connection connection, Integer claimNo, Integer documentTypeId);

    boolean isCheckedAllMandatoryDocument(Connection connection, Integer claimNo, Integer documentTypeId);

    ClaimDocumentDto updateDocumentCheckStatus(Connection connection, ClaimDocumentDto claimDocumentDto) throws MisynJDBCException;

    ClaimDocumentDto updateDocumentHoldStatus(Connection connection, ClaimDocumentDto claimDocumentDto) throws MisynJDBCException;

    ClaimDocumentDto updateDocumentRejectStatus(Connection connection, ClaimDocumentDto claimDocumentDto) throws MisynJDBCException;

    ClaimDocumentDto updateDocumentBillCheckDetails(Connection connection, ClaimDocumentDto claimDocumentDto) throws MisynJDBCException;

    ClaimDocumentDto searchByBillCheckRefNo(Connection connection, Integer billCheckRefNo) throws MisynJDBCException;

    Integer getBillApprovedDocTypeId(Connection connection, Integer documentTypeId);

    List<ClaimDocumentDto> findAllMandatoryDocumentByClaimNo(Connection connection, Integer claimNo);

    ClaimDocumentLossTypeOldDto insertClaimDocumentLossTypeOld(Connection connection, ClaimDocumentLossTypeOldDto claimDocumentLossTypeOldDto) throws Exception;

    ClaimDocumentLossTypeOldDto updateClaimDocumentLossTypeOld(Connection connection, ClaimDocumentLossTypeOldDto claimDocumentLossTypeOldDto) throws Exception;

    List<ClaimDocumentLossTypeOldDto> allClaimDocumentLossTypeOldByClaimNo(Connection connection, Integer claimNo) throws Exception;

    void updateJobRefNoByClaimNo(Connection connection, Integer claimNo, Integer jobRefNo) throws Exception;

    List<Integer> getDocumentIdList(Connection connection, Integer claimNo);

    void updateDocumentBillCancelDetails(Connection connection, ClaimDocumentDto claimDocumentDto) throws Exception;

    String getDocumentUploadTime(Connection connection, Integer claimNo) throws Exception;

    ClaimDocumentDto insertBankDetails(Connection connection, ClaimDocumentDto claimDocumentDto) throws Exception;

    ClaimDocumentDto getClaimDocumentByRefNo(Connection connection, Integer refNo);
}
