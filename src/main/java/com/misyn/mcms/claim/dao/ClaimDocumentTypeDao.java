package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.*;

import java.sql.Connection;
import java.util.List;

public interface ClaimDocumentTypeDao {
    String SELECT_CLAIM_DOCUMENT_TYPE_BY_N_DEPARTMENT_ID = "SELECT * FROM claim_document_type WHERE N_DEPARTMENT_ID=?";
    String SELECT_CLAIM_DOCUMENT_TYPE = "SELECT * FROM claim_document_type ORDER BY V_DOC_TYPE_NAME ";
    String SELECT_CLAIM_DOCUMENT_TYPE_BY_NOT_DOCUMENT_TYPE_ID = "SELECT * FROM claim_document_type WHERE N_DOC_TYPE_ID<>? ORDER BY V_DOC_TYPE_NAME ";
    String SELECT_CLAIM_DOCUMENT_TYPE_BY_DOCUMENT_TYPE_ID = "SELECT * FROM claim_document_type WHERE N_DOC_TYPE_ID=?";
    String SELECT_CLAIM_DOCUMENT_TYPE_BY_N_DEPARTMENT_ID_AND_INSPECTION_TYPE = "SELECT\n" +
            "t1.*,\n" +
            "t2.inspection_type_id\n" +
            "FROM\n" +
            "claim_document_type AS t1\n" +
            "INNER JOIN claim_document_type_inspection_type AS t2 ON t2.N_DOC_TYPE_ID = t1.N_DOC_TYPE_ID\n" +
            "WHERE\n" +
            "t1.N_DEPARTMENT_ID = ? AND\n" +
            "t2.inspection_type_id = ?";
    String CLAIM_DOCUMENT_TYPE_INSERT = "INSERT INTO claim_document_type VALUES (0,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    String CLAIM_DOCUMENT_TYPE_UPDATE = "UPDATE claim_document_type SET \n" +
            "V_DOC_TYPE_NAME =?,\n" +
            "N_DEPARTMENT_ID  =?,\n" +
            "V_IS_MANDATORY  =?,\n" +
            "V_IS_PARTIAL_LOSS  =?,\n" +
            "V_IS_TOT_LOSS  =?,\n" +
            "V_IS_LUMP_SUM  =?,\n" +
            "V_IS_THIRD_PARTY_PROP_VEHICLE  =?,\n" +
            "V_IS_THIRD_PARTY_DEATH  =?,\n" +
            "V_IS_THIRD_PARTY_INJURIES  =?,\n" +
            "N_DOC_REQ_FROM  =?,\n" +
            "V_REMIN_DOC_DISPLAY_NAME  =?,\n" +
            "V_REC_STATUS  =?,\n" +
            "V_INP_USER_ID  =?,\n" +
            "D_INP_DATE_TIME  =? WHERE N_DOC_TYPE_ID =?";

    String CLAIM_DOCUMENT_NAME = "SELECT * FROM claim_department";
    String CLAIM_DOC_REQ_FORM = "SELECT * FROM claim_doc_req_from";
    String VALIDATE_DOC_TYPE_NAME = "SELECT V_DOC_TYPE_NAME FROM claim_document_type WHERE V_DOC_TYPE_NAME like ?";

    ClaimDocumentTypeDto searchByDocumentTypeId(Connection connection, Integer documentTypeId);

    List<ClaimDocumentTypeDto> searchAll(Connection connection);

    List<ClaimDepartmentDto> searchClaimDocument(Connection connection);

    List<DocReqFormDto> claimDocReqForm(Connection connection);

    List<ClaimDocumentTypeDto> searchExcludeDocumentType(Connection connection, Integer documentTypeId);

    List<ClaimDocumentTypeDto> getClaimDocumentTypeDtoList(Connection connection, Integer departmentId);

    List<ClaimDocumentTypeDto> getClaimDocumentTypeDtoList(Connection connection, Integer departmentId, Integer inspectionTypeId);

    ClaimDocumentTypeDto insert(Connection connection, ClaimDocumentTypeDto claimDocumentTypeDto) throws Exception;

    ClaimDocumentTypeDto update(Connection connection, ClaimDocumentTypeDto claimDocumentTypeDto) throws Exception;

    DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

    String validateDocTypeNmae(Connection connection, String docTypeName) throws Exception;
}
