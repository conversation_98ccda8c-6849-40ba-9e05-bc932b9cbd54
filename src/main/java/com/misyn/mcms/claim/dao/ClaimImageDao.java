package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimImageDto;

import java.sql.Connection;
import java.util.List;

public interface ClaimImageDao extends BaseDao<ClaimImageDto> {
    String INSERT_CLAIM_UPLOAD_IMAGES = "INSERT INTO claim_upload_images VALUES(0,?,?,?,?,?,?,?,?,?)";

    String SELECT_CLAIM_UPLOAD_IMAGES_BY_JOB_REF_NO_AND_CLAIM_NO = "SELECT t1.* FROM claim_upload_images AS t1 WHERE t1.N_JOB_REF_NO=? AND t1.N_CLIM_NO=?";

    String SELECT_CLAIM_UPLOAD_IMAGES_BY_CLAIM_NO = "SELECT t1.* FROM claim_upload_images AS t1 WHERE t1.N_CLIM_NO=?";
    String SELECT_CLAIM_UPLOAD_IMAGES_BY_CLAIM_NO_AND_JOB_NO_IN = "SELECT t1.* FROM claim_upload_images AS t1 " +
            "WHERE t1.N_CLIM_NO=?\n" +
            "AND t1.N_JOB_REF_NO IN " +
            "((SELECT ref_no FROM claim_assign_assesor WHERE claim_no=? AND job_id=?))";

    String SELECT_CLAIM_UPLOAD_IMAGES_BY_REF_NO = "SELECT t1.* FROM claim_upload_images AS t1 WHERE t1.N_REF_NO=?";

    String DELETE_FROM_CLAIM_UPLOAD_IMAGES = "DELETE FROM claim_upload_images where N_REF_NO=?";


    List<ClaimImageDto> getClaimImageDtoList(Connection connection, Integer claimNo, Integer jobRefNo);

    List<ClaimImageDto> findAllClaimImageDtoByClaimNo(Connection connection, Integer claimNo);

    List<ClaimImageDto> findAllClaimImageDtoByClaimNoAndInspectionType(Connection connection, Integer claimNo, String inspectionJobNo);

    ClaimImageDto getClaimImageByRefNo(Connection connection, Integer refNo);

}
