package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.InvestigationSelectImageDto;

import java.sql.Connection;
import java.util.List;

public interface ClaimInvestigationSelectImageDao {

    String INSERT_MASTER = "INSERT INTO investigation_select_images\n" +
            "VALUES\n" +
            "	( 0, ?, ?, ?, ? )";

    String SQL_SELECT_ALL_BY_CLAIM_NO = "SELECT * FROM investigation_select_images WHERE claim_no = ?";

    String SQL_SELECT_ONE_BY_CLAIM_NO = "SELECT 1 FROM investigation_select_images WHERE claim_no = ?";

    String SQL_COUNT_BY_CLAIM_NO = "SELECT COUNT(*) AS count FROM investigation_select_images WHERE claim_no = ?";

    boolean insertMaster(Connection connection, InvestigationSelectImageDto investigationSelectImageDto) throws Exception;

    List<InvestigationSelectImageDto> getSelectImages(Connection connection, Integer claimNo);

    Boolean isSeletedInvestigationImages(Connection connection, Integer claimNo);

    int getSelectedImagesCount(Connection connection, Integer claimNo);
}
