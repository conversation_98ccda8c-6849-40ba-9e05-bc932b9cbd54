package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimLogsDto;

import java.sql.Connection;
import java.util.List;

public interface ClaimLogsDao extends BaseDao<ClaimLogsDto> {

    String INSERT_CLAIM_LOGS = "INSERT INTO claim_logs VALUES (?,?,?,?,?,?,?,?,?)";

    String SEARCH_CLAIM_LOGS = "SELECT * FROM claim_logs WHERE department_id =? and claim_no =? order by txnid desc";

    public List<ClaimLogsDto> searchByKeyValueAndType(Connection connection, int department_id, int claim_no) throws Exception;
}
