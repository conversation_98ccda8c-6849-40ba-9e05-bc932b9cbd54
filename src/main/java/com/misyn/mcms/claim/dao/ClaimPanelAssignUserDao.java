/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimPanelAssignUserDto;
import com.misyn.mcms.claim.dto.PanelDecisionDto;
import com.misyn.mcms.claim.dto.PanelMemberListDto;
import com.misyn.mcms.claim.dto.UserDto;

import java.sql.Connection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ClaimPanelAssignUserDao extends BaseDao<ClaimPanelAssignUserDto> {

    String IS_ALL_PANEL_MEMBERS_APPROVED = "SELECT V_USER_ID FROM claim_panel_assign_user WHERE V_STATUS = 'P' AND V_USER_ID <> ? AND N_CLAIM_NO = ?";

    String GET_ASSIGNED_PANEL_MEMBERS = "SELECT\n" +
            "\tcpau.V_USER_ID,\n" +
            "\tcpau.V_INPUT_USER,\n" +
            "\tcpau.V_STATUS,\n" +
            "\tcpau.D_INPUT_DATETIME,\n" +
            "\tcpau.D_UPDATED_DATE_TIME\n" +
            "FROM\n" +
            "\tclaim_panel_assign_user cpau\n" +
            "INNER JOIN claim_claim_panel_user ccpu ON cpau.V_USER_ID = ccpu.V_USER_ID\n" +
            "WHERE\n" +
            "\tccpu.N_PANEL_ID IN(1,2)\n" +
            "AND cpau.N_CLAIM_NO = ?\n" +
            "ORDER BY\n" +
            "\tcpau.V_STATUS ASC";

    String APPROVE_OR_REJECT_BY_MAIN_PANEL = "UPDATE claim_panel_assign_user SET V_STATUS = ?, D_UPDATED_DATE_TIME = ?, V_REMARK = ? WHERE V_USER_ID = ? AND N_CLAIM_NO = ?";

    String GET_REJECTED_USER_BY_CLAIM_NO = "SELECT V_USER_ID FROM claim_panel_assign_user WHERE N_CLAIM_NO = ? AND V_STATUS = 'R'";

    String IS_APPROVAL_PENDING_BY_PANEL_MEMBER = "SELECT 1 FROM claim_panel_assign_user WHERE V_STATUS = 'P' AND V_USER_ID = ? AND N_CLAIM_NO = ?";

    String RETURN_BY_MAIN_PANEL = "UPDATE claim_panel_assign_user SET V_STATUS = ?, D_UPDATED_DATE_TIME = ?, V_REMARK = ? WHERE N_CLAIM_NO = ? AND V_USER_ID = ?";

    String IS_FORWARDED_TO_MAIN_PANEL = "SELECT 1 FROM claim_panel_assign_user cpau\n" +
            "LEFT JOIN main_panel_assign_hst mpah ON cpau.N_CLAIM_NO = mpah.N_CLAIM_NO\n" +
            "INNER JOIN claim_assign_claim_handler cach ON cach.N_CLAIM_NO = cpau.N_CLAIM_NO\n" +
            "WHERE (cpau.V_STATUS IS NOT NULL AND cach.N_CLAIM_STATUS NOT IN(52,56) AND cpau.N_PANEL_ID = 2 AND cpau.N_CLAIM_NO = ?)\n" +
            "OR mpah.N_CLAIM_NO = ?";

    String SQL_GET_PANEL_MEMBERS_BY_CLAIM_NO = "SELECT\n" +
            "t1.V_USER_ID,\n" +
            "t2.N_CLAIM_NO,\n" +
            "t2.D_INPUT_DATETIME,\n" +
            "t2.V_STATUS,\n" +
            "t2.D_UPDATED_DATE_TIME,\n" +
            "t1.V_USER_STATUS\n" +
            "FROM\n" +
            "claim_claim_panel_user t1\n" +
            "LEFT JOIN claim_panel_assign_user t2 ON t1.V_USER_ID = t2.V_USER_ID\n" +
            "AND t2.N_CLAIM_NO = ?\n" +
            "WHERE\n" +
            "t1.N_PANEL_ID = 2";

    String CHECK_USER_ALREADY_AVAILABLE = "SELECT V_USER_ID from claim_panel_assign_user WHERE N_CLAIM_NO = ? AND V_USER_ID = ?";

    String INSERT_PANEL_ASSIGN_USER = "INSERT INTO claim_panel_assign_user\n" +
            "(V_USER_ID, N_CLAIM_NO, V_INPUT_USER, D_INPUT_DATETIME, V_STATUS, V_DM_REMARK, N_PANEL_ID) VALUES (?, ?, ?, ?, ?, ?, ?)";

    String DELETE_PANEL_ASSIGN_USER = "DELETE FROM claim_panel_assign_user WHERE N_CLAIM_NO = ? AND V_USER_ID = ?";

    String GET_CURRENT_PANEL_DECISION = "SELECT * FROM claim_panel_assign_user WHERE N_CLAIM_NO = ?";

    String GET_PANEL_DATE = "SELECT\n" +
            "MIN(\n" +
            "CASE WHEN D_UPDATED_DATE_TIME <> '1980-01-01 00:00:00' THEN\n" +
            "D_UPDATED_DATE_TIME\n" +
            "ELSE\n" +
            "'N/A'\n" +
            "END\n" +
            ") AS PANEL_DATE\n" +
            "FROM\n" +
            "claim_panel_assign_user\n" +
            "WHERE\n" +
            "N_CLAIM_NO = ?";

    String IS_ALL_MAIN_PANEL_REJECTED = "SELECT\n" +
            "	COUNT( * ) AS pending_count \n" +
            "FROM\n" +
            "	claim_panel_assign_user\n" +
            "WHERE\n" +
            "	V_STATUS = 'P' \n" +
            "	AND N_CLAIM_NO = ? ";

    ClaimPanelAssignUserDto searchByClaimNo(Connection connection, Integer claimNo) throws Exception;

    boolean deleteByClaimNo(Connection connection, Integer claimNo) throws Exception;

    boolean isPanelApprovedOrRejected(Connection connection, Integer claimNo, String userName) throws Exception;

    List<ClaimPanelAssignUserDto> getAssignedUSersByClaimNo(Connection connection, Integer claimNo) throws Exception;

    void approveClaimByPanelUser(Connection connection, Integer claimNo, UserDto user, String remark) throws Exception;

    void rejectClaimByPanel(Connection connection, Integer claimNo, UserDto user, String remark) throws Exception;

    String getRejectedUser(Connection connection, Integer claimNo) throws Exception;

    boolean isPendingApproval(Connection connection, Integer claimNoToSearch, String v_usrid) throws Exception;

    boolean isMainPanelAssigned(Connection connection, Integer claimNoToSearch) throws Exception;

    void returnToDmaker(Connection connection, Integer claimNo, String userName, String remark) throws Exception;

    List<PanelMemberListDto> getPanelMembersForClaim(Connection connection, Integer claimNo) throws Exception;

    void updatePanelMembers(Connection connection, Integer claimNo, String name, String userName, boolean isInsert) throws Exception;

    List<PanelDecisionDto> getCurrentPanelDecision(Connection connection, Integer claimNo) throws Exception;

    boolean checkAllMainPanelUserRejected(Connection connection, Integer claimNo) throws Exception;
}
