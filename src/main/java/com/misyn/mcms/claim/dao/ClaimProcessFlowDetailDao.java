package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimProcessFlowDto;
import com.misyn.mcms.claim.dto.UserDto;

import java.sql.Connection;
import java.util.List;

public interface ClaimProcessFlowDetailDao {
    String SQL_CLAIM_PROCESS_FLOW = "INSERT INTO claim_process_flow_detail_mst VALUES(0,?,?,?,?)";
    String SQL_CLAIM_PROCESS_FLOW_GET_ALL = "SELECT * FROM  claim_process_flow_detail_mst";
    boolean save(Connection connection, String task, UserDto user) throws Exception;
    List<ClaimProcessFlowDto> getAll(Connection connection) throws Exception;
}
