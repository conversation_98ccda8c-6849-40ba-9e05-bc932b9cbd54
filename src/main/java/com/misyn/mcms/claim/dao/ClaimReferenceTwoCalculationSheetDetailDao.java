package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimCalculationSheetDetailDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;

public interface ClaimReferenceTwoCalculationSheetDetailDao extends BaseDao<ClaimCalculationSheetDetailDto> {

    String CLAIM_CALCULATION_SHEET_DETAIL_INSERT = "INSERT INTO claim_ref_two_calculation_sheet_detail VALUES(0,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    String CLAIM_CALCULATION_SHEET_DETAIL_UPDATE = "UPDATE claim_ref_two_calculation_sheet_main \n" +
            "SET N_CAL_SHEET_ID =?,\n" +
            "N_ITEM_NO =?,\n" +
            "N_ESTIMATED_AMOUNT =?,\n" +
            "N_BILL_AMOUNT =?,\n" +
            "N_APPROVED_AMOUNT =?,\n" +
            "V_REMARKS =?,\n" +
            "N_NBT_RATE =?,\n" +
            "V_NBT_TYPE =?,\n" +
            "N_NBT_AMOUNT =?,\n" +
            "N_VAT_RATE =?,\n" +
            "V_VAT_TYPE =?,\n" +
            "N_VAT_AMOUNT =?,\n" +
            "N_OA =?,\n" +
            "N_OA_AMOUNT =?,\n" +
            "N_TOTAL_AMOUNT =?,\n" +
            "V_BILL_CHECKED =?,\n" +
            "V_RECORD_TYPE =?,\n" +
            "V_REMOVE_VAT =?,\n" +
            "N_REMOVE_VATE_RATE = ?,\n" +
            "V_ADD_NBT =?,\n" +
            "V_ADD_VAT_TYPE =? \n" +
            "WHERE\n" +
            "	N_CAL_SHEET_DETAIL_ID =?";
    String CLAIM_CALCULATION_SHEET_DETAIL_DELETE = "DELETE FROM claim_ref_two_calculation_sheet_main WHERE N_CAL_SHEET_DETAIL_ID =?";
    String CLAIM_CALCULATION_SHEET_DETAIL_DELETE_BY_CAL_SHEET_NO = "DELETE FROM claim_ref_two_calculation_sheet_detail WHERE N_CAL_SHEET_ID =?";
    String CLAIM_CALCULATION_SHEET_DETAIL_SEARCH = "SELECT * FROM claim_ref_two_calculation_sheet_main WHERE N_CAL_SHEET_DETAIL_ID =?";
    String CLAIM_CALCULATION_SHEET_DETAIL_SEARCH_ALL = "SELECT * FROM claim_ref_two_calculation_sheet_main";
    String CLAIM_CALCULATION_SHEET_DETAIL_SEARCH_ALL_BY_CAL_SHEET_NO = "SELECT * FROM claim_ref_two_calculation_sheet_detail WHERE N_CAL_SHEET_ID = ?";

    String CALIM_CALCULATION_SHEET_DETAILS_GRID = "SELECT\n" +
            " t1.N_CLIM_NO,\n" +
            " t1.V_VEHICLE_NO,\n" +
            " t1.V_POL_NUMBER,\n" +
            " t1.D_ACCID_DATE,\n" +
            " t1.V_PRIORITY,\n" +
            " t2.V_IS_ALL_DOC_UPLOAD,\n" +
            " t2.V_IS_CHECK_ALL_MND_DOCS,\n" +
            " t2.V_LIABILITY_APRV_STATUS,\n" +
            " t2.N_CLAIM_STATUS,\n" +
            " t2.N_TXN_NO,\n" +
            " t2.N_APRV_TOT_ACR_AMOUNT,\n" +
            " t2.N_RESERVE_AMOUNT,\n" +
            " t2.V_IS_FILE_STORE,\n" +
            " t2.N_LOSS_TYPE,\n" +
            " t2.V_LIABILITY_APRV_ASSIGN_USER,\n" +
            " t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME,\n" +
            " t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME,\n" +
            " t2.V_INIT_LIABILITY_ASSIGN_USER_ID,\n" +
            " t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME,\n" +
            " t2.V_INVESTIGATION_STATUS,\n" +
            " t2.V_CLOSE_STATUS,\n" +
            " t2.V_CLOSE_STATUS,\n" +
            " t3.V_IS_NO_OBJECTION_UPLOAD,\n" +
            " t3.N_NO_OBJECTION_DOC_REF_NO,\n" +
            " t3.V_IS_PREMIUM_OUTSTANDING_UPLOAD,\n" +
            " t3.N_PREMIUM_OUTSTANDING_DOC_REF_NO,\n" +
            " t3.V_INPUT_USER,\n" +
            " t3.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID,\n" +
            " t3.D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME,\n" +
            " t3.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID,\n" +
            " t3.V_APR_ASSIGN_USER,\n" +
            " t3.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID,\n" +
            " t3.V_SPECIAL_TEAM_ASSIGN_USER_ID,\n" +
            " t3.N_CAL_SHEET_ID,\n" +
            " t3.V_NO_OBJECTION_STATUS,\n" +
            " t3.V_PREMIUM_OUTSTANDING_STATUS,\n" +
            " t3.V_RTE_ASSIGN_USER_ID,\n" +
            " t3.D_RTE_ASSIGN_DATE_TIME,\n" +
            " t4.v_status_desc,\n" +
            "(SELECT  v_status_desc FROM claim_status_para WHERE n_ref_id=t3.V_STATUS) AS CALSHEETSTATUS\n" +
            "FROM\n" +
            "claim_assign_claim_handler AS t2\n" +
            "INNER JOIN claim_calculation_sheet_main AS t3 ON t2.N_CLAIM_NO = t3.N_CLAIM_NO \n" +
            "INNER JOIN claim_claim_info_main AS t1 ON t2.N_CLAIM_NO = t1.N_CLIM_NO \n" +
            "INNER JOIN claim_status_para AS t4 ON t2.N_CLAIM_STATUS = t4.n_ref_id\n";

    String CALIM_CALCULATION_SHEET_DETAILS_GRID_COUNT = "SELECT\n" +
            "COUNT(*) as cnt\n" +
            "FROM\n" +
            "claim_assign_claim_handler AS t2\n" +
            "INNER JOIN claim_calculation_sheet_main AS t3 ON t2.N_CLAIM_NO = t3.N_CLAIM_NO \n" +
            "INNER JOIN claim_claim_info_main AS t1 ON t2.N_CLAIM_NO = t1.N_CLIM_NO \n" +
            "INNER JOIN claim_status_para AS t4 ON t2.N_CLAIM_STATUS = t4.n_ref_id\n";

    String SELECT_TOT_PAYABLE_AMOUNT = "SELECT\n" +
            "	sum(N_PAYABLE_AMOUNT) AS tot\n" +
            "FROM\n" +
            "	claim_ref_two_calculation_sheet_main\n" +
            "WHERE\n" +
            "	N_CLAIM_NO = ? AND V_STATUS NOT IN(66, 73)";

    String SELECT_PAYABLE_AMOUNT_APPROVED_CALSHEET = "SELECT\n" +
            "	sum(N_PAYABLE_AMOUNT) AS tot\n" +
            "FROM\n" +
            "	claim_calculation_sheet_main\n" +
            "WHERE\n" +
            "	N_CLAIM_NO = ?\n" +
            "AND V_STATUS IN(67,70,65)";

    String UPDATE_BILLCHECKED_BY_ID = "UPDATE claim_ref_two_calculation_sheet_main \n" +
            "SET V_BILL_CHECKED = ? \n" +
            "WHERE\n" +
            "	N_CAL_SHEET_DETAIL_ID = ?";

    List<ClaimCalculationSheetDetailDto> searchByCalSheetId(Connection connection, Integer calSheetId) throws Exception;

    void deleteByCalSheetNo(Connection connection, Integer calSheetId) throws Exception;

    DataGridDto getClaimHandlerDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, int type) throws Exception;

    DataGridDto getClaimHandlerRtePendingDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) throws Exception;

    BigDecimal getTotalPayableAmount(Connection connection, Integer claimNo) throws Exception;

    BigDecimal getTotalPayableAmountApprovedCalsheet(Connection connection, Integer claimNo) throws Exception;

    void updateBillCheckById(Connection connection, Integer id, String isChecked) throws Exception;
}
