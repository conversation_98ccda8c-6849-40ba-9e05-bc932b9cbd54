package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimCalculationSheetMainTempDto;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;

public interface ClaimReferenceTwoCalculationSheetMainTempDao extends BaseDao<ClaimCalculationSheetMainTempDto> {

    String CLAIM_CALCULATION_SHEET_MAIN_TEMP_INSERT = "INSERT INTO claim_ref_two_calculation_sheet_main_temp\n" +
            "VALUES\n" +
            "	( 0,?,?,?,?,?,?,?,?,?,?,?,? )";

    String SELECT_ALL_FROM_CALSHEET_MAIN_TEMP_BY_CALSHEET_ID = "SELECT * FROM claim_ref_two_calculation_sheet_main_temp WHERE calsheet_id = ?";

    String UPDATE_RESERVED_AMOUNT_BY_CALSHEET = "UPDATE claim_ref_two_calculation_sheet_main_temp \n" +
            "SET calsheet_type = ?,\n" +
            "reserve_amount = ?,\n" +
            "reserve_amount_after_approved = ?,\n" +
            "payable_amount = ? \n" +
            "WHERE\n" +
            "	calsheet_id =?";

    String DELETE_FROM_CALSHEET_TEMP_BY_CALSHEET_ID = "DELETE \n" +
            "FROM\n" +
            "	claim_ref_two_calculation_sheet_main_temp \n" +
            "WHERE\n" +
            "	calsheet_id = ?";

    String UPDATE_RESERVE_AMOUNT_AFTER_APPROVED_BY_CALSHEET_ID = "UPDATE claim_ref_two_calculation_sheet_main_temp \n" +
            "SET reserve_amount_after_approved =? \n" +
            "WHERE\n" +
            "	calsheet_id =?";
    String UPDATE_PREV_RESERVE_AMOUNT_AFTER_APPROVED_BY_CALSHEET_ID = "UPDATE claim_ref_two_calculation_sheet_main_temp \n" +
            "SET prev_reserve_amount_after_approved =? \n" +
            "WHERE\n" +
            "	calsheet_id =?";
    String UPDATE_ADVANCE_AMOUNT_BY_CALSHEET_ID = "UPDATE claim_ref_two_calculation_sheet_main_temp \n" +
            "SET advance_amount =? \n" +
            "WHERE\n" +
            "	calsheet_id =?";

    String UPDATE_PREV_ADVANCE_AMOUNT_BY_CALSHEET_ID = "UPDATE claim_ref_two_calculation_sheet_main_temp \n" +
            "SET prev_advance_amount =? \n" +
            "WHERE\n" +
            "	calsheet_id =?";

    String SELECT_CALSHEET_TEMP_DETAILS_BY_CALSHEET_ID = "SELECT 1 FROM claim_ref_two_calculation_sheet_main_temp WHERE calsheet_id = ?";

    String SELECT_ALL_FROM_CALSHEET_MAIN_TEMP_BY_CLAIM_NO = "SELECT * FROM claim_ref_two_calculation_sheet_main_temp WHERE claim_no = ?";

    String UPDATE_DETAILS_BY_CALSHEET_ID = "UPDATE claim_ref_two_calculation_sheet_main_temp \n" +
            "SET total_approved_acr = ?,\n" +
            "reserve_amount = ?,\n" +
            "prev_reserve_amount = ?,\n" +
            "reserve_amount_after_approved = ?,\n" +
            "prev_reserve_amount_after_approved = ?,\n" +
            "advance_amount = ?,\n" +
            "prev_advance_amount = ?\n" +
            "WHERE\n" +
            "	calsheet_id = ?";

    ClaimCalculationSheetMainTempDto searchByCalsheetId(Connection connection, Integer calSheetId) throws Exception;

    void updateReserveAmountAfterApproved(Connection connection, BigDecimal payableAmount, Integer calSheetId) throws Exception;

    void updateAdvanceAmount(Connection connection, BigDecimal advanceAmount, Integer calSheetId) throws Exception;

    void updatePrevReserveAmountAfterApproved(Connection connection, BigDecimal prevReserveAmountAfterApproved, Integer calSheetId) throws Exception;

    void updatePrevAdvanceAmount(Connection connection, BigDecimal prevAdvanceAmount, Integer calSheetId) throws Exception;

    boolean isAlreadySaved(Connection connection, Integer calsheetId) throws Exception;

    List<ClaimCalculationSheetMainTempDto> searchAllByClaimNo(Connection connection, Integer claimNo) throws Exception;

    void updateTempDetailsWhenAcrIsUpdated(Connection connection, ClaimCalculationSheetMainTempDto dto) throws Exception;
}
