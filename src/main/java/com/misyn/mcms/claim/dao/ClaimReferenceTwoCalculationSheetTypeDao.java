package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimCalculationSheetTypeDto;

import java.sql.Connection;

public interface ClaimReferenceTwoCalculationSheetTypeDao extends BaseDao<ClaimCalculationSheetTypeDto> {
    String CLAIM_CALCULATION_SHEET_TYPE_INSERT = "INSERT INTO claim_ref_two_calculation_sheet_type VALUES (0,?)";
    String CLAIM_CALCULATION_SHEET_TYPE_UPDATE = "UPDATE claim_ref_two_calculation_sheet_type SET \n" +
            "V_CAL_SHEET_TYPE_DESC =? WHERE N_CAL_SHEET_TYPE_ID =?\n";
    String CLAIM_CALCULATION_SHEET_TYPE_SEARCH = "SELECT * FROM claim_ref_two_calculation_sheet_type WHERE N_CAL_SHEET_TYPE_ID =?";
    String CLAIM_CALCULATION_SHEET_TYPE_SEARCH_ALL = "SELECT * FROM claim_ref_two_calculation_sheet_type WHERE V_REC_STATUS = 'A'";
    String CLAIM_CALCULATION_SHEET_TYPE_DELETE = "DELETE FROM claim_ref_two_calculation_sheet_type WHERE N_CAL_SHEET_TYPE_ID =?";

    String CLAIM_CALCULATION_SHEET_TYPE_BY_CALSHEET_ID = "SELECT\n" +
            "	t1.* \n" +
            "FROM\n" +
            "	claim_ref_two_calculation_sheet_type AS t1\n" +
            "	INNER JOIN claim_calculation_sheet_main AS t2 ON t1.N_CAL_SHEET_TYPE_ID = t2.N_CAL_SHEET_TYPE \n" +
            "WHERE\n" +
            "	t2.N_CAL_SHEET_ID = ?";

    ClaimCalculationSheetTypeDto getCalsheetTypeDetailByCalSheetId(Connection connection, Integer calSheetId);
}
