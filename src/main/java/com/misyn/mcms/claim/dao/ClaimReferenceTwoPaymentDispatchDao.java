package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimPaymentDispatchDto;

import java.sql.Connection;

/**
 * <AUTHOR>
 */

public interface ClaimReferenceTwoPaymentDispatchDao {

    String SELECT_PAYMENT_DISPATCH_DETAILS_BY_VOUCHER_NO = "SELECT * FROM claim_ref_two_payment_dispatch WHERE V_VOUCHER_NO = ?";

    String SELECT_PAYMENT_DISPATCH_DETAILS_BY_PAYEE_ID = "SELECT * FROM claim_ref_two_payment_dispatch WHERE N_PAYEE_ID = ?";

    String SQL_INSERT_INTO_CLAIM_PAYMENT_DISPATCH = "INSERT into claim_ref_two_payment_dispatch VALUES (0,?,?,?,?,?,?,?,?,?,?)";

    String DELETE_FROM_CLAIM_PAYMENT_DISPATCH = "DELETE FROM claim_ref_two_payment_dispatch WHERE N_CAL_SHEET_ID = ?";

    String UPDATE_PAYMENT_DISPATCH = "UPDATE claim_ref_two_payment_dispatch" +
            " SET V_VOUCHER_NO = ?," +
            " V_CHEQUE_NO = ?," +
            " V_DISPATCH_LOCATION = ?," +
            " V_DISPATCHED_LOCATION = ?," +
            " V_DISPATCH_USER = ?," +
            " D_DISPATCH_DATE_TIME = ?," +
            " V_CHEQUE_DISPATCH_STATUS = ?" +
            " WHERE N_PAYEE_ID = ?";

    ClaimPaymentDispatchDto getVoucherDetails(Connection connection, String voucherNo) throws Exception;

    ClaimPaymentDispatchDto getVoucherDetails(Connection connection, Integer payeeId) throws Exception;

    void savePaymentDispatch(Connection connection, ClaimPaymentDispatchDto claimPaymentDispatchDto) throws Exception;

    void deleteByCalSheetId(Connection connection, Integer calSheetId) throws Exception;

    void updatePaymentDispatch(Connection connection, ClaimPaymentDispatchDto claimPaymentDispatchDto) throws Exception;
}
