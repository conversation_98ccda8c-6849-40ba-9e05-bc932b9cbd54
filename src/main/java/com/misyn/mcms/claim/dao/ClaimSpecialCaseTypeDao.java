package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimSpecialCaseTypeDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;

import java.sql.Connection;
import java.util.List;

public interface ClaimSpecialCaseTypeDao {
    String CLAIM_SPECIAL_TYPE_INSERT = "INSERT INTO claim_special_case_type VALUES(0,?,?,?,?,?,?,?,?)";
    String CLAIM_SPECIAL_TYPE_UPDATE = "UPDATE claim_special_case_type SET \n" +
            "claim_type =?,\n" +
            "remark =?,\n" +
            "claim_no =?,\n" +
            "record_status =?,\n" +
            "last_modified_date_time =?,\n" +
            "last_modified_user =?\n" +
            "WHERE id =?\n";
    String CLAIM_SPECIAL_TYPE = "SELECT * FROM claim_special_case_type where id = ?";
    String CLAIM_SPECIAL_TYPE_LIST = "SELECT * FROM claim_special_case_type";


    String EXIST_CLAIM_CHECK = "SELECT\n" +
            "	count( N_CLIM_NO ) AS cnt \n" +
            "FROM\n" +
            "	claim_claim_info_main \n" +
            "WHERE\n" +
            "	N_CLIM_NO = ?";

    ClaimSpecialCaseTypeDto insertClaimSpecialCaseType(Connection connection, ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto) throws Exception;

    ClaimSpecialCaseTypeDto updateClaimSpecialCaseType(Connection connection, ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto) throws Exception;

    DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

    ClaimSpecialCaseTypeDto getClaimSpecialCaseType(Connection connection, Integer id) throws Exception;

    List<ClaimSpecialCaseTypeDto> getClaimSpecialCaseTypeList(Connection connection) throws Exception;

    void deleteClaimSpecialCaseType(Connection connection, Integer id, String userName) throws Exception;

    Boolean isExistClaimNo(Connection connection, String claimNo) throws Exception;
}
