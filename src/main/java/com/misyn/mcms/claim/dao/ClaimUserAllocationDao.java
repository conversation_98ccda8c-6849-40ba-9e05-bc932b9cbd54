/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimUserAllocationDto;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ClaimUserAllocationDao {

    ClaimUserAllocationDto updateMaster(Connection connection, ClaimUserAllocationDto t) throws Exception;

    List<ClaimUserAllocationDto> getListNotInLeave(Connection connection, Integer accessLevel, Integer allocationFunctionType, String sysDateTime, String status) throws Exception;

    List<ClaimUserAllocationDto> getListInLeave(Connection connection, Integer accessLevel, Integer allocationFunctionType, String sysDateTime, String status) throws Exception;

    void updateLeaveUserAssignReport(Connection connection, Integer assignReport, Integer assignIndex, String assignUserId) throws Exception;

    List<ClaimUserAllocationDto> getMofaListNotInLeave(Connection connection, Integer accessLevel, Integer allocationFunctionType, String sysDatetime, String status, BigDecimal amount, BigDecimal limit, boolean isMandatory) throws Exception;

    boolean checkIfLeave(Connection connection, Integer accessLevel, Integer allocationFunctionType, String sysDatetime, String assignUserId) throws Exception;

    List<ClaimUserAllocationDto> getListNotInLeaveClaimHandlerDepartment(Connection connection, Integer accessLevel, Integer allocationFunctionType, String sysDatetime, String status, Integer claimNo) throws Exception;

    List<ClaimUserAllocationDto> getListInLeaveClaimHandlerDepartment(Connection connection, Integer accessLevel, Integer allocationFunctionType, String sysDatetime, String status, Integer claimNo) throws Exception;

    List<ClaimUserAllocationDto> getRteListNotInLeave(Connection connection, Integer allocationFunctionType, String sysDatetime, String status, int limitLevel) throws Exception;

    boolean isRteLeave(Connection connection, Integer allocationFunctionType, String sysDateTime, String status, String assignUserId) throws Exception;

    int checkAssignReportForToday(Connection connection, Integer accessLevel) throws Exception;
}
