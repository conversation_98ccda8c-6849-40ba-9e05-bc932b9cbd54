package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimUserLeaveDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;

import java.sql.Connection;
import java.util.List;

public interface ClaimUserLeaveDao extends BaseDao<ClaimUserLeaveDto> {
    String CLAIM_USER_LEAVE_INSERT = "INSERT INTO claim_user_leave VALUES (?,?,?,?,?,?)";
    String CLAIM_USER_LEAVE_UPDATE = "UPDATE claim_user_leave SET \n" +
            "D_FROM_DATE_TIME =?,\n" +
            "D_TO_DATE_TIME =?,\n" +
            "V_LEAVE_TYPE =?,\n" +
            "V_INPUT_USER =?,\n" +
            "D_INPUT_DATE_TIME =? WHERE V_USER_ID =?\n";
    String CLAIM_USER_LEAVE_SEARCH = "SELECT\n" +
            "t2.D_FROM_DATE_TIME,\n" +
            "t2.D_TO_DATE_TIME,\n" +
            "t2.V_LEAVE_TYPE,\n" +
            "t2.V_INPUT_USER,\n" +
            "t2.D_INPUT_DATE_TIME,\n" +
            "t1.v_usrid,\n" +
            "t1.v_firstname,\n" +
            "t1.v_lastname\n" +
            "FROM\n" +
            "usr_mst AS t1\n" +
            "LEFT JOIN claim_user_leave AS t2 ON t1.v_usrid = t2.V_USER_ID\n" +
            "WHERE t1.v_usrid =? ";
    String CLAIM_USER_LEAVE_SEARCH_ALL = "SELECT * FROM claim_user_leave";

    String SQL_TODAY_LEAVE = "SELECT 1 FROM claim_user_leave WHERE V_USER_ID = ? AND V_LEAVE_TYPE= 'LEAVE' AND D_FROM_DATE_TIME <= ? AND D_TO_DATE_TIME >= ?";

    DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type);

    boolean isLeaveUser(Connection connection, String assignUserId) throws Exception;
}
