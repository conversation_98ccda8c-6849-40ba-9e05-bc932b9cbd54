package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimHandlerDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.UserDto;

import java.sql.Connection;
import java.util.List;

public interface ClaimUserUpdateDao {
    String SEARCH_CLAIM_USER_BY_TXNID = "SELECT N_TXN_NO,V_ASSIGN_USER_ID,V_LIABILITY_APRV_ASSIGN_USER,N_CLAIM_NO FROM claim_assign_claim_handler WHERE N_CLAIM_NO=?";

    String UPDATE_CLAIM_USER_BY_TXNID = "UPDATE claim_assign_claim_handler SET V_ASSIGN_USER_ID=? ,V_LIABILITY_APRV_ASSIGN_USER=? WHERE N_TXN_NO =?";

    String SQL_SELECT_ALL_TO_GRID_DECISION_MAKING = "SELECT\n" +
            "\tt1.*,\n" +
            "\tt2.*,\n" +
            "\tt3.* \n" +
            "FROM\n" +
            "\tclaim_claim_info_main AS t1\n" +
            "\tINNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" +
            "\tINNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" +
            "\tINNER JOIN usr_mst AS t4 ON t2.V_DECISION_MAKING_ASSIGN_USER_ID = t4.v_usrid ";

    String SQL_COUNT_DECISION_MAKING = "SELECT\n" +
            "count(*) as cnt\n" +
            "FROM\n" +
            "\tclaim_claim_info_main AS t1\n" +
            "\tINNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" +
            "\tINNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" +
            "\tINNER JOIN usr_mst AS t4 ON t2.V_DECISION_MAKING_ASSIGN_USER_ID = t4.v_usrid ";

    String SQL_SELECT_ALL_TO_GRID = "SELECT\n" +
            "\tt1.*,\n" +
            "\tt2.*,\n" +
            "\tt3.* \n" +
            "FROM\n" +
            "\tclaim_claim_info_main AS t1\n" +
            "\tINNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" +
            "\tINNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" +
            "\tINNER JOIN usr_mst AS t4 ON t2.V_ASSIGN_USER_ID = t4.v_usrid ";

    String SQL_COUNT_CLAIM_HANDLER = "SELECT\n" +
            "count(*) as cnt\n" +
            "FROM\n" +
            "\tclaim_claim_info_main AS t1\n" +
            "\tINNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" +
            "\tINNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" +
            "\tINNER JOIN usr_mst AS t4 ON t2.V_ASSIGN_USER_ID = t4.v_usrid ";

    String SQL_SELECT_ALL_INIT_CLAIM_HANDLER_TO_GRID = "SELECT\n" +
            "\tt1.*,\n" +
            "\tt2.*,\n" +
            "\tt3.* \n" +
            "FROM\n" +
            "\tclaim_claim_info_main AS t1\n" +
            "\tINNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" +
            "\tINNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" +
            "\tINNER JOIN usr_mst AS t4 ON t2.V_INIT_LIABILITY_ASSIGN_USER_ID = t4.v_usrid ";

    String SQL_COUNT_INIT_CLAIM_HANDLER = "SELECT\n" +
            "count(*) as cnt\n" +
            "FROM\n" +
            "\tclaim_claim_info_main AS t1\n" +
            "\tINNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" +
            "\tINNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" +
            "\tINNER JOIN usr_mst AS t4 ON t2.V_INIT_LIABILITY_ASSIGN_USER_ID = t4.v_usrid ";

    String SELECT_SQL_TWO_MEMBER = "SELECT\n" +
            "\tt1.N_CLAIM_NO,\n" +
            "\tt1.V_CLOSE_STATUS,\n" +
            "\tt3.V_VEHICLE_NO,\n" +
            "\tt3.V_POL_NUMBER,\n" +
            "\tt2.V_USER_ID,\n" +
            "\tt2.D_INPUT_DATETIME,\n" +
            "\tt1.N_CLAIM_STATUS,\n" +
            "\tt4.V_STATUS_DESC,\n" +
            "\tt2.N_ID \n" +
            "FROM\n" +
            "\tclaim_assign_claim_handler AS t1\n" +
            "\tINNER JOIN claim_panel_assign_user AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "\tINNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "\tINNER JOIN claim_status_para AS t4 ON t1.N_CLAIM_STATUS = t4.n_ref_id\n" +
            "\tINNER JOIN claim_claim_panel_user AS t5 ON t5.V_USER_ID = t2.V_USER_ID\n" +
            "\tINNER JOIN claim_claim_panel AS t6 ON t5.N_PANEL_ID = t6.N_ID ";


    String SELECT_SQL_FOUR_MEMBER = "SELECT\n" +
            "\tt1.N_CLAIM_NO,\n" +
            "\tt1.V_CLOSE_STATUS,\n" +
            "\tt3.V_VEHICLE_NO,\n" +
            "\tt3.V_POL_NUMBER,\n" +
            "\tt2.V_USER_ID,\n" +
            "\tt2.D_INPUT_DATETIME,\n" +
            "\tt1.N_CLAIM_STATUS,\n" +
            "\tt4.V_STATUS_DESC,\n" +
            "\tt2.N_ID \n" +
            "FROM\n" +
            "\tclaim_assign_claim_handler AS t1\n" +
            "\tINNER JOIN claim_panel_assign_user AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "\tINNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "\tINNER JOIN claim_status_para AS t4 ON t1.N_CLAIM_STATUS = t4.n_ref_id\n" +
            "\tINNER JOIN claim_claim_panel_user AS t5 ON t5.V_USER_ID = t2.V_USER_ID\n" +
            "\tINNER JOIN claim_claim_panel AS t6 ON t5.N_PANEL_ID = t6.N_ID ";


    String SELECT_SQL_SPECIAL_TEAM = "SELECT\n" +
            "	t1.N_CLAIM_NO,\n" +
            "	t1.V_CLOSE_STATUS,\n" +
            "	t2.V_SPECIAL_TEAM_ASSIGN_USER_ID,\n" +
            "	t2.D_SPECIAL_TEAM_ASSIGN_DATE_TIME,\n" +
            "	t3.V_VEHICLE_NO,\n" +
            "	t3.V_POL_NUMBER,\n" +
            "	t3.N_CLAIM_STATUS,\n" +
            "	t4.V_STATUS_DESC,\n" +
            "	t2.N_CAL_SHEET_ID \n" +
            "FROM\n" +
            "	claim_assign_claim_handler AS t1\n" +
            "	INNER JOIN claim_calculation_sheet_main AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_status_para AS t4 ON t2.V_STATUS = t4.n_ref_id \n" +
            "	INNER JOIN usr_mst AS t5 ON t5.v_usrid = t2.V_SPECIAL_TEAM_ASSIGN_USER_ID";

    String SELECT_SQL_MOFA_TEAM = "	SELECT\n" +
            "t1.N_CLAIM_NO,\n" +
            "t1.V_CLOSE_STATUS,\n" +
            "t2.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID AS ASSIGN_USER_ID,\n" +
            "t2.D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME AS ASSIGN_DATE_TIME,\n" +
            "\tt2.V_SPECIAL_TEAM_ASSIGN_USER_ID,\n" +
            "	t3.V_VEHICLE_NO,\n" +
            "	t3.V_POL_NUMBER,\n" +
            "	t3.N_CLAIM_STATUS,\n" +
            "	t4.V_STATUS_DESC,\n" +
            "	t2.N_CAL_SHEET_ID \n" +
            "FROM\n" +
            "claim_assign_claim_handler AS t1\n" +
            "INNER JOIN claim_calculation_sheet_main AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_status_para AS t4 ON t2.V_STATUS = t4.n_ref_id \n" +
            "	INNER JOIN usr_mst AS t5 ON t5.v_usrid = t2.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID";

    String SELECT_SQL_SPECIAL2_TEAM = "	SELECT\n" +
            "t1.N_CLAIM_NO,\n" +
            "t1.V_CLOSE_STATUS,\n" +
            "t2.V_SPECIAL_TEAM_ASSIGN_USER_ID AS ASSIGN_USER_ID,\n" +
            "t2.D_SPECIAL_TEAM_ASSIGN_DATE_TIME AS ASSIGN_DATE_TIME,\n" +
            "\tt2.V_SPECIAL_TEAM_ASSIGN_USER_ID,\n" +
            "	t3.V_VEHICLE_NO,\n" +
            "	t3.V_POL_NUMBER,\n" +
            "	t3.N_CLAIM_STATUS,\n" +
            "	t4.V_STATUS_DESC,\n" +
            "	t2.N_CAL_SHEET_ID \n" +
            "FROM\n" +
            "claim_assign_claim_handler AS t1\n" +
            "INNER JOIN claim_calculation_sheet_main AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_status_para AS t4 ON t2.V_STATUS = t4.n_ref_id \n" +
            "	INNER JOIN usr_mst AS t5 ON t5.v_usrid = t2.V_SPECIAL_TEAM_ASSIGN_USER_ID";

    String SELECT_SQL_SPECIAL1_TEAM = "	SELECT\n" +
            "t1.N_CLAIM_NO,\n" +
            "t1.V_CLOSE_STATUS,\n" +
            "t2.V_SPECIAL_TEAM_ASSIGN_USER_ID AS ASSIGN_USER_ID,\n" +
            "t2.D_SPECIAL_TEAM_ASSIGN_DATE_TIME AS ASSIGN_DATE_TIME,\n" +
            "\tt2.V_SPECIAL_TEAM_ASSIGN_USER_ID,\n" +
            "	t3.V_VEHICLE_NO,\n" +
            "	t3.V_POL_NUMBER,\n" +
            "	t3.N_CLAIM_STATUS,\n" +
            "	t4.V_STATUS_DESC,\n" +
            "	t2.N_CAL_SHEET_ID \n" +
            "FROM\n" +
            "claim_assign_claim_handler AS t1\n" +
            "INNER JOIN claim_calculation_sheet_main AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_status_para AS t4 ON t2.V_STATUS = t4.n_ref_id \n" +
            "	INNER JOIN usr_mst AS t5 ON t5.v_usrid = t2.V_SPECIAL_TEAM_ASSIGN_USER_ID";


    String COUNT_SQL_MOFA_TEAM = "	SELECT\n" +
            "COUNT(*) as cnt\n" +
            "FROM\n" +
            "claim_assign_claim_handler AS t1\n" +
            "INNER JOIN claim_calculation_sheet_main AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_status_para AS t4 ON t2.V_STATUS = t4.n_ref_id \n" +
            "	INNER JOIN usr_mst AS t5 ON t5.v_usrid = t2.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID";

    String COUNT_SQL_SPECIAL2_TEAM = "	SELECT\n" +
            "COUNT(*) as cnt\n" +
            "FROM\n" +
            "claim_assign_claim_handler AS t1\n" +
            "INNER JOIN claim_calculation_sheet_main AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_status_para AS t4 ON t2.V_STATUS = t4.n_ref_id \n" +
            "	INNER JOIN usr_mst AS t5 ON t5.v_usrid = t2.V_SPECIAL_TEAM_ASSIGN_USER_ID";

    String COUNT_SQL_SPECIAL1_TEAM = "	SELECT\n" +
            "COUNT(*) as cnt\n" +
            "FROM\n" +
            "claim_assign_claim_handler AS t1\n" +
            "INNER JOIN claim_calculation_sheet_main AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_status_para AS t4 ON t2.V_STATUS = t4.n_ref_id \n" +
            "	INNER JOIN usr_mst AS t5 ON t5.v_usrid = t2.V_SPECIAL_TEAM_ASSIGN_USER_ID";

    String COUNT_SQL_SPECIAL_TEAM = "	SELECT\n" +
            "	COUNT( * ) AS cnt \n" +
            "FROM\n" +
            "	claim_assign_claim_handler AS t1\n" +
            "	INNER JOIN claim_calculation_sheet_main AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_status_para AS t4 ON t2.V_STATUS = t4.n_ref_id \n" +
            "	INNER JOIN usr_mst AS t5 ON t5.v_usrid = t2.V_SPECIAL_TEAM_ASSIGN_USER_ID";


    String COUNT_SQL_TWO_MEMBER = "SELECT\n" +
            "COUNT(*) as cnt\n" +
            "FROM\n" +
            "claim_assign_claim_handler AS t1\n" +
            "INNER JOIN claim_panel_assign_user AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "INNER JOIN claim_status_para AS t4 ON t1.N_CLAIM_STATUS = t4.n_ref_id\n" +
            "INNER JOIN claim_claim_panel_user AS t5 ON t5.V_USER_ID = t2.V_USER_ID\n" +
            "INNER JOIN claim_claim_panel AS t6 ON t5.N_PANEL_ID = t6.N_ID ";


    String COUNT_SQL_FOUR_MEMBER = "SELECT\n" +
            "COUNT(*) as cnt\n" +
            "FROM\n" +
            "claim_assign_claim_handler AS t1\n" +
            "INNER JOIN claim_panel_assign_user AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "INNER JOIN claim_status_para AS t4 ON t1.N_CLAIM_STATUS = t4.n_ref_id\n" +
            "INNER JOIN claim_claim_panel_user AS t5 ON t5.V_USER_ID = t2.V_USER_ID\n" +
            "INNER JOIN claim_claim_panel AS t6 ON t5.N_PANEL_ID = t6.N_ID ";

    String UPDATE_MOFA_ASSIGN_USER = "UPDATE claim_calculation_sheet_main SET V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID=?,D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME=? WHERE N_CAL_SHEET_ID=?";
    String UPDATE_SPECIAL_TEAM_ASSIGN_USER = "UPDATE claim_calculation_sheet_main SET V_SPECIAL_TEAM_ASSIGN_USER_ID=?,D_SPECIAL_TEAM_ASSIGN_DATE_TIME=? WHERE N_CAL_SHEET_ID=?";
    String UPDATE_TWO_MEMBER_PANEL_ASSIGN_USER = "UPDATE claim_panel_assign_user SET V_USER_ID=?,D_INPUT_DATETIME=? WHERE N_ID=?";
    String UPDATE_FOUR_MEMBER_PANEL_ASSIGN_USER = "UPDATE claim_panel_assign_user SET V_USER_ID=?,D_INPUT_DATETIME=? WHERE N_ID=?";
    String UPDATE_CLAIM_HANDLER_ASSIGN_USER = "UPDATE claim_assign_claim_handler SET V_ASSIGN_USER_ID=?,D_ASSIGN_DATE_TIME=? WHERE N_CLAIM_NO=?";

    String UPDATE_CLAIM_HANDLER_ASSIGN_USER_ANDINIT_LIABILITI_ASSIGN_USER = "UPDATE claim_assign_claim_handler SET V_ASSIGN_USER_ID=?, V_INIT_LIABILITY_ASSIGN_USER_ID=?, D_ASSIGN_DATE_TIME=? WHERE N_CLAIM_NO=?";
    String UPDATE_INIT_CLAIM_HANDLER_ASSIGN_USER = "UPDATE claim_assign_claim_handler SET V_INIT_LIABILITY_ASSIGN_USER_ID=?,V_INIT_LIABILITY_ASSIGN_DATE_TIME=? WHERE N_CLAIM_NO=?";
    String UPDATE_DECISION_MAKER_ASSIGN_USER = "UPDATE claim_assign_claim_handler SET V_DECISION_MAKING_ASSIGN_USER_ID=?,D_DECISION_MAKING_ASSIGN_DATE_TIME=? WHERE N_CLAIM_NO=?";

    String UPDATE_CAL_SHEET_ASSIGN_USER = "UPDATE claim_calculation_sheet_main SET V_ASSIGN_USER_ID=?,D_ASSIGN_DATE_TIME=? WHERE N_CLAIM_NO=?";
    String SELECT_CAL_SHEET_ASSIGN_USER = "SELECT * FROM claim_calculation_sheet_main WHERE N_CLAIM_NO=?";

    String SELECT_ONE_WHERE_LIABILITY_APPR_STATUS_PENDING = "SELECT 1\n" +
            "FROM claim_assign_claim_handler\n" +
            "WHERE N_CLAIM_NO=? AND V_INIT_LIABILITY_APRV_STATUS='P'";

    String SELECT_SQL_SPAREPARTS_COORDINATOR = "SELECT\n" +
            "	t1.N_CLAIM_NO,\n" +
            "   t1.V_CLOSE_STATUS,\n " +
            "	t2.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID,\n" +
            "	t2.D_SPARE_PART_CORDINATOR_ASSIGN_DATE_TIME,\n" +
            "	t3.V_VEHICLE_NO,\n" +
            "	t3.V_POL_NUMBER,\n" +
            "	t3.N_CLAIM_STATUS,\n" +
            "	t4.V_STATUS_DESC,\n" +
            "	t2.N_CAL_SHEET_ID \n" +
            "FROM\n" +
            "	claim_assign_claim_handler AS t1\n" +
            "	INNER JOIN claim_calculation_sheet_main AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_status_para AS t4 ON t2.V_STATUS = t4.n_ref_id \n" +
            "	INNER JOIN usr_mst AS t5 ON t5.v_usrid = t2.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID";

    String COUNT_SQL_SPAREPARTS_COORDINATOR = "SELECT\n" +
            "	COUNT(*) AS cnt\n" +
            "FROM\n" +
            "	claim_assign_claim_handler AS t1\n" +
            "	INNER JOIN claim_calculation_sheet_main AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_status_para AS t4 ON t2.V_STATUS = t4.n_ref_id \n" +
            "	INNER JOIN usr_mst AS t5 ON t5.v_usrid = t2.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID";

    String SELECT_SQL_SCURTINIZING_TEAM = "SELECT\n" +
            "	t1.N_CLAIM_NO,\n" +
            "	t2.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID,\n" +
            "	t2.D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME,\n" +
            "	t3.V_VEHICLE_NO,\n" +
            "	t3.V_POL_NUMBER,\n" +
            "   t1.V_CLOSE_STATUS,\n" +
            "	t3.N_CLAIM_STATUS,\n" +
            "	t4.V_STATUS_DESC,\n" +
            "	t2.N_CAL_SHEET_ID \n" +
            "FROM\n" +
            "	claim_assign_claim_handler AS t1\n" +
            "	INNER JOIN claim_calculation_sheet_main AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_status_para AS t4 ON t2.V_STATUS = t4.n_ref_id \n" +
            "	INNER JOIN usr_mst AS t5 ON t5.v_usrid = t2.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID";

    String COUNT_SQL_SCURTINIZING_TEAM = "SELECT\n" +
            "	COUNT(*) AS cnt\n" +
            "FROM\n" +
            "	claim_assign_claim_handler AS t1\n" +
            "	INNER JOIN claim_calculation_sheet_main AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "	INNER JOIN claim_status_para AS t4 ON t2.V_STATUS = t4.n_ref_id \n" +
            "	INNER JOIN usr_mst AS t5 ON t5.v_usrid = t2.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID";

    String SELECT_SQL_SPAREPARTS_COORDINATOR_SUPPLY_ORDER = "SELECT\n" +
            "	t1.*,\n" +
            "	t2.*,\n" +
            "	t3.*,\n" +
            "   t5.v_supply_order_status \n" +
            "FROM\n" +
            "	claim_claim_info_main AS t1\n" +
            "	INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" +
            "	INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" +
            "	INNER JOIN usr_mst AS t4 ON t2.V_SUPPLY_ORDER_ASSIGN_USER = t4.v_usrid\n" +
            "   LEFT JOIN claim_supply_order_summary as t5 ON t1.N_CLIM_NO = t5.n_claim_no";

    String COUNT_SQL_SPAREPARTS_COORDINATOR_SUPPLY_ORDER = "SELECT\n" +
            "	COUNT(*) AS cnt\n" +
            "FROM\n" +
            "	claim_claim_info_main AS t1\n" +
            "	INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" +
            "	INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" +
            "	INNER JOIN usr_mst AS t4 ON t2.V_SUPPLY_ORDER_ASSIGN_USER = t4.v_usrid\n" +
            "   LEFT JOIN claim_supply_order_summary as t5 ON t1.N_CLIM_NO = t5.n_claim_no";

    String UPDATE_SUPPLY_ORDER_ASSIGN_USER = "UPDATE claim_assign_claim_handler SET V_SUPPLY_ORDER_ASSIGN_USER=?,D_SUPPLY_ORDER_ASSIGN_DATE_TIME=? WHERE N_CLAIM_NO=?";

    String UPDATE_SPARE_PARTS_COORDINATOR = "UPDATE claim_calculation_sheet_main SET V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID=?,D_SPARE_PART_CORDINATOR_ASSIGN_DATE_TIME=? WHERE N_CAL_SHEET_ID=?";

    String UPDATE_SCURTINIZING_TEAM = "UPDATE claim_calculation_sheet_main SET V_SCRUTINIZE_TEAM_ASSIGN_USER_ID=?,D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME=? WHERE N_CAL_SHEET_ID=?";

    String UPDATE_CALSHEET_MOFA_ASSIGN_USER = "UPDATE claim_calculation_sheet_main SET V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID=?,D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME=? WHERE N_CAL_SHEET_ID=?";

    String UPDATE_SUPPLY_ORDER_SCRUTINIZING_USER = "UPDATE claim_supply_order_summary SET v_apprv_assign_scrutinizing_user_id=?,d_apprv_assign_scrutinizing_date_time=? WHERE n_supply_order_ref_no=?";

    String SELECT_SQL_SCRUTINIZING_SUPPLY_ORDER = "SELECT\n" +
            "t1.N_CLIM_NO,\n" +
            "t4.n_supply_order_ref_no,\n" +
            "t1.V_VEHICLE_NO,\n" +
            "t1.V_POL_NUMBER,\n" +
            "t4.d_apprv_assign_scrutinizing_date_time,\n" +
            "t2.N_CLAIM_STATUS,\n" +
            "t3.v_status_desc,\n" +
            "t4.v_apprv_assign_scrutinizing_user_id,\n" +
            "t2.V_CLOSE_STATUS\n" +
            "FROM\n" +
            "claim_claim_info_main AS t1\n" +
            "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" +
            "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" +
            "LEFT JOIN claim_supply_order_summary AS t4 ON t1.N_CLIM_NO = t4.n_claim_no\n" +
            "INNER JOIN usr_mst AS t5 ON t4.v_apprv_assign_scrutinizing_user_id= t5.v_usrid";

    String COUNT_SQL_SCRUTINIZING_SUPPLY_ORDER = "SELECT\n" +
            "COUNT(*) AS cnt\n" +
            "FROM\n" +
            "claim_claim_info_main AS t1\n" +
            "INNER JOIN claim_assign_claim_handler AS t2 ON t2.N_CLAIM_NO = t1.N_CLIM_NO\n" +
            "INNER JOIN claim_status_para AS t3 ON t2.N_CLAIM_STATUS = t3.n_ref_id\n" +
            "LEFT JOIN claim_supply_order_summary as t4 ON t1.N_CLIM_NO = t4.n_claim_no\n" +
            "INNER JOIN usr_mst AS t5 ON t4.v_apprv_assign_scrutinizing_user_id = t5.v_usrid";

    String SELECT_SQL_MOFA_SPECIAL_COMMENT = "SELECT\n" +
            "\tt1.N_CLAIM_NO,\n" +
            "\tt1.V_CLOSE_STATUS,\n" +
            "\tt1.V_SPECIAL_APPROVAL_INPUT_DATE_TIME AS ASSIGN_DATE_TIME,\n" +
            "\tt1.V_SPECIAL_APPROVAL_USER_ID AS ASSIGN_USER_ID,\n" +
            "\tt2.V_VEHICLE_NO,\n" +
            "\tt2.V_POL_NUMBER,\n" +
            "\tt4.V_STATUS_DESC,\n" +
            "\tt1.N_CLAIM_STATUS\n" +
            "FROM\n" +
            "\tclaim_assign_claim_handler AS t1\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.N_CLAIM_NO\n" +
            "INNER JOIN claim_status_para AS t4 ON t1.N_CLAIM_STATUS = t4.n_ref_id\n" +
            "INNER JOIN usr_mst AS t5 ON t5.v_usrid = t1.V_SPECIAL_APPROVAL_USER_ID";

    String SELECT_SQL_MOFA_SPECIAL_COMMENT1 = "SELECT\n" +
            "\tt1.N_CLAIM_NO,\n" +
            "\tt1.V_CLOSE_STATUS,\n" +
            "\tt2.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID AS ASSIGN_USER_ID,\n" +
            "\tt2.D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME AS ASSIGN_DATE_TIME,\n" +
            "\tt2.D_SPECIAL_TEAM_ASSIGN_DATE_TIME,\n"+
            "\tt2.V_SPECIAL_TEAM_ASSIGN_USER_ID,\n" +
            "\tt3.V_VEHICLE_NO,\n" +
            "\tt3.V_POL_NUMBER,\n" +
            "\tt3.N_CLAIM_STATUS,\n" +
            "\tt4.V_STATUS_DESC,\n" +
            "\tt2.N_CAL_SHEET_ID\n" +
            "FROM\n" +
            "\tclaim_assign_claim_handler AS t1\n" +
            "INNER JOIN claim_calculation_sheet_main AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "INNER JOIN claim_status_para AS t4 ON t2.V_STATUS = t4.n_ref_id \n" +
            " left JOIN usr_mst AS t5 ON (t5.v_usrid = t2.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID OR t5.v_usrid = t2.V_SPECIAL_TEAM_ASSIGN_USER_ID )";

    String COUNT_SQL_MOFA_SPECIAL_COMMENT = "SELECT\n" +
            "COUNT(*) AS cnt\n" +
            "FROM\n" +
            "\tclaim_assign_claim_handler AS t1\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.N_CLAIM_NO\n" +
            "INNER JOIN usr_mst AS t5 ON t5.v_usrid = t1.V_SPECIAL_APPROVAL_USER_ID";

    String COUNT_SQL_MOFA_SPECIAL_COMMENT1 = "SELECT\n" +
            "COUNT(*) AS cnt\n" +
            "FROM\n" +
            "\tclaim_assign_claim_handler AS t1\n" +
            "INNER JOIN claim_calculation_sheet_main AS t2 ON t1.N_CLAIM_NO = t2.N_CLAIM_NO\n" +
            "INNER JOIN claim_claim_info_main AS t3 ON t3.N_CLIM_NO = t2.N_CLAIM_NO\n" +
            "INNER JOIN claim_status_para AS t4 ON t2.V_STATUS = t4.n_ref_id \n" +
            " left JOIN usr_mst AS t5 ON (t5.v_usrid = t2.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID OR t5.v_usrid = t2.V_SPECIAL_TEAM_ASSIGN_USER_ID )";

    String UPDATE_SPECIAL_APPROVAL_USER = "UPDATE claim_assign_claim_handler SET V_SPECIAL_APPROVAL_USER_ID = ?, V_SPECIAL_APPROVAL_DATE_TIME = ?, V_SPECIAL_APPROVAL_INPUT_DATE_TIME = ? WHERE N_CLAIM_NO = ?";

    ClaimHandlerDto searchClaimUser(Connection connection, Integer txnId) throws Exception;

    Boolean updateClaimUserByTxnId(Connection connection, Integer txnId, String assignUserId, String liabilityAssignUser) throws Exception;

    DataGridDto getClaimHandlerDataGridDtoClaimHandler(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus, String assignUserType, UserDto sessionUser) throws Exception;

    DataGridDto getTotalLossDataGridDtoClaimHandler(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus) throws Exception;

    DataGridDto getInitialClaimHandlerDataGridDtoClaimHandler(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus, String assignUserType) throws Exception;

    DataGridDto getClaimHandlerDataGridDtoDecisionMaker(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, boolean isRejectedClaim, String calsheetStatus) throws Exception;

    DataGridDto getClaimHandlerDataGridDtoTwoMember(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus) throws Exception;

    DataGridDto getClaimHandlerDataGridDtoFourMember(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus) throws Exception;

    DataGridDto getClaimHandlerDataGridDtoSpecialTeam(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus, String assignUserType) throws Exception;

    DataGridDto getClaimHandlerDataGridDtoMofa(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus, String assignUserType, String status,String assingUserLevel) throws Exception;

    Boolean updateClaimUHandlerByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception;

    Boolean updateTotalLossTeamByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception;

    Boolean updateInitClaimHandlerByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception;

    Boolean updateDecisionMakerByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception;

    Boolean updateSpecialTeamByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception;

    Boolean updateMofaByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception;

    Boolean updateTwoMemberByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception;

    Boolean updateFourMemberByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception;

    Boolean updateCalsheetAssignUser(Connection connection, String assignUserId, Integer claimNo) throws Exception;

    Boolean selectCalsheetAssignUser(Connection connection, Integer claimNo) throws Exception;

    DataGridDto getClaimHandlerDataGridDtoSparePartsCoordinatorCheckCalsheet(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus, String assignUserType) throws Exception;

    DataGridDto getClaimHandlerDataGridDtoBillCheckingTeamCheckCalsheet(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus, String assignUserType) throws Exception;

    DataGridDto getClaimHandlerDataGridDtoSparePartsCoordinatorSupplyOrder(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus, String assignUserType) throws Exception;

    boolean updateSupplyOrderByClaimNo(Connection connection, Integer claimNo, String assignUser) throws Exception;

    boolean updateSparePartsCoordinatorByTxnId(Connection connection, Integer txnId, String assignUser) throws Exception;

    boolean updateScurtinizingTeamByTxnId(Connection connection, Integer txnId, String assignUser) throws Exception;

    void updateCalsheetAssignMofaUser(Connection connection, String assignUser, Integer txnId) throws Exception;

    DataGridDto getClaimHandlerDataGridDtoBillCheckingTeamSupplyOrder(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String assignUserType);

    boolean updateSupplyOrderScurtinizingTeamByTxnId(Connection connection, Integer txnId, String assignUser) throws Exception;

    boolean updateSpecialApprovalUser(Connection connection, Integer claimNo, String assignUser, String user) throws Exception;

}
