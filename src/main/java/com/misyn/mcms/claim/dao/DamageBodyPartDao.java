package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimsDto;
import com.misyn.mcms.claim.dto.DamageBodyPartDto;

import java.sql.Connection;
import java.util.List;

public interface DamageBodyPartDao {

     String INSERT_CLAIM_DAMAGE_PART_MAIN = "INSERT INTO claim_damage_part_main VALUES(?,?,?,?,?)";
     String DELETE_CLAIM_DAMAGE_PART_MAIN = "DELETE FROM claim_damage_part_main WHERE N_CLIM_NO=?";
     String SELECT_DAMAGE_PART_MAIN = "SELECT t1.*,IFNULL(t2.N_CLIM_NO,0) as N_CLIM_NO ,"
            + "IFNULL(t2.V_DAMEG_TYPE,'') as V_DAMEG_TYPE,"
            + "IFNULL(t2.V_OTHER_TEXT,'') as V_OTHER_TEXT "
            + "FROM claim_vehicle_part t1 "
            + "LEFT OUTER JOIN claim_damage_part_main t2  "
            + "ON  t1.N_VEH_CLS_ID=t2.N_VEH_CLS_ID  "
            + "AND  "
            + "t1.v_part_code=t2.v_part_code  "
            + "AND   "
            + "t2.N_CLIM_NO=?  "
            + "WHERE t1.N_VEH_CLS_ID=? "
            + "ORDER BY t1.n_txn_id";

    int insertDamageBodyPartsList(Connection connection, List<DamageBodyPartDto> damageBodyPartsList, ClaimsDto claimsDto) throws Exception;

    void deleteDamageBodyPartsList(Connection connection, Integer claimNo) throws Exception;

    List<DamageBodyPartDto> getDamageBodyPartDtoList(Connection conn, Integer claimNo, Integer vehClsId) throws Exception;

}
