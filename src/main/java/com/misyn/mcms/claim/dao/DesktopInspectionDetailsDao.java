package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.DesktopInspectionDetailsDto;

/**
 * Created by akila on 5/21/18.
 */
public interface DesktopInspectionDetailsDao extends BaseDao<DesktopInspectionDetailsDto> {

    String SQL_INSERT_INTO_DESKTIOP_INSPECTION = "INSERT INTO desktop_inspection_details\n" +
            "	(inspction_id, n_ref_no, provide_offer, offer_type, app_cost_report, "
            + "pre_accident_value, excess, acr, bold_tyre_penalty_amount, under_insurance_penalty_amount, "
            + "payable_amount, desktop_offer, ari_salvage, settlement_method, inspection_remark, "
            + "police_report_requested, special_remark, investigaed_claim, professional_fee, miles, "
            + "telephone_charge, other_charge, special_deduction, reason, total_charge,advanced_amount) VALUES "
            + "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?)";
    String SQL_SELECT_DESKTIOP_INSPECTION = "SELECT * from desktop_inspection_details_me WHERE n_ref_no=?";
    String SQL_UPDATE_DESKTIOP_INSPECTION = "UPDATE  desktop_inspection_details \n" +
            "SET  inspction_id  = ?,\n" +
            "  provide_offer  = ?,\n" +
            "  offer_type  = ?,\n" +
            "  app_cost_report  = ?,\n" +
            "  pre_accident_value  = ?,\n" +
            "  excess  = ?,\n" +
            "  acr  = ?,\n" +
            "  bold_tyre_penalty_amount  = ?,\n" +
            "  under_insurance_penalty_amount  = ?,\n" +
            "  payable_amount  = ?,\n" +
            "  desktop_offer  = ?,\n" +
            "  ari_salvage  = ?,\n" +
            "  settlement_method  = ?,\n" +
            "  inspection_remark  = ?,\n" +
            "  police_report_requested  = ?,\n" +
            "  special_remark  = ?,\n" +
            "  investigaed_claim  = ?,\n" +
            "  professional_fee  = ?,\n" +
            "  miles  = ?,\n" +
            "  telephone_charge  = ?,\n" +
            "  other_charge  = ?,\n" +
            "  special_deduction  = ?,\n" +
            "  reason  = ?,\n" +
            "  total_charge  = ?,\n" +
            "  advanced_amount  = ?\n" +
            "WHERE\n" +
            " n_ref_no  = ?";

}
