package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.DocumentHistoryDto;

import java.sql.Connection;

public interface DocumentHistoryDao {

    String INSERT_DOCUMENT_HISTORY = "INSERT INTO document_history VALUES (0,?,?,?,?,?,?)";

    String SELECT_ONE_FROM_DOCUMENT_HISTORY_BY_DETAILS = "SELECT\n" +
            "	1\n" +
            "FROM\n" +
            "	document_history\n" +
            "WHERE\n" +
            "	claim_no =?\n" +
            "AND NAME =?\n" +
            "AND datetime BETWEEN ?\n" +
            "AND ?\n" +
            "AND job_ref_no = ?";

    String DELETE_HISTORY = "DELETE FROM document_history WHERE doc_ref_no=?";

    boolean insertMaster(Connection connection, DocumentHistoryDto documentHistoryDto) throws Exception;

    boolean isDuplicateDocument(Connection connection, DocumentHistoryDto documentHistoryDto, String fiveMinBeforedatetime, String currentDatetime) throws Exception;

    boolean deleteHistory(Connection connection, Integer jobRefNo) throws Exception;
}
