package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.DrSupplementaryInspectionDetailsDto;

import java.math.BigDecimal;
import java.sql.Connection;

/**
 * Created by aki<PERSON> on 5/21/18.
 */
public interface DrSupplementaryInspectionDetailsDao extends BaseDao<DrSupplementaryInspectionDetailsDto> {

    String SQL_INSERT_DR_SUPPLEMENTARY = " INSERT INTO dr_inspection_details VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    String SQL_SELECT_DR_SUPPLEMENTARY = " SELECT * FROM dr_inspection_details WHERE n_ref_no=?";

    String SQL_SELECT_DR_SUPPLEMENTARY_INSPECTION_DETAILS = "SELECT\n" +
            "	sum_insured,\n" +
            "	pre_accident_value,\n" +
            "	excess,\n" +
            "	acr,\n" +
            "	bold_tyre_penalty_amount,\n" +
            "	under_insurance_penalty_amount,\n" +
            "	payable_amount,\n" +
            "	assessor_remark,\n" +
            "	inspection_remark,\n" +
            "	ari_and_salvage \n" +
            "FROM\n" +
            "	dr_inspection_details \n" +
            "WHERE\n" +
            "	n_ref_no =?";

    String SQL_SELECT_DR_SUPPLEMENTARY_ASSESSOR_FEE = "SELECT\n" +
            "	professional_fee,\n" +
            "	miles,\n" +
            "	telephone_charge,\n" +
            "	other_charge,\n" +
            "	special_deduction,\n" +
            "	reason,\n" +
            "	total_charge \n" +
            "FROM\n" +
            "	dr_inspection_details \n" +
            "WHERE\n" +
            "	n_ref_no =?";
    String SQL_UPDATE_DR_SUPPLEMENTARY = " UPDATE dr_inspection_details \n" +
            "SET  inspection_id  = ?,\n" +
            "  sum_insured  = ?,\n" +
            "  pre_accident_value  = ?,\n" +
            "  excess  = ?,\n" +
            "  acr  = ?,\n" +
            "  bold_tyre_penalty_amount  = ?,\n" +
            "  under_insurance_penalty_amount  = ?,\n" +
            "  payable_amount  = ?,\n" +
            "  assessor_remark  = ?,\n" +
            "  inspection_remark  = ?,\n" +
            "  ari_and_salvage  = ?,\n" +
            "  professional_fee  = ?,\n" +
            "  miles  = ?,\n" +
            "  telephone_charge  = ?,\n" +
            "  other_charge  = ?,\n" +
            "  special_deduction  = ?,\n" +
            "  reason  = ?,\n" +
            "  total_charge  = ?\n" +
            "WHERE\n" +
            "  n_ref_no  = ?\n";

    String SQL_SELECT_ACR_BY_REF_NO = "SELECT acr FROM dr_inspection_details WHERE n_ref_no = ?";

    DrSupplementaryInspectionDetailsDto getDrSupplementaryInspectionAssessorFeeDetails(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto, Object id);

    DrSupplementaryInspectionDetailsDto getDrSupplementaryInspectionDetails(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto, Object id);

    BigDecimal getAcr(Connection connection, Integer refNo);
}
