package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.MessageContentDetails;
import com.misyn.mcms.utility.Email;

import java.sql.Connection;
import java.util.List;

/**
 * Created by a<PERSON><PERSON> on 8/25/18.
 */
public interface EmailDao {
    String SQL_INSERT_INTO_EMAIL = "INSERT INTO email_log VALUES(0,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    String SQL_UPDATE_MAIL_STAUS = "UPDATE email_log set send_status=? WHERE mail_id=?";
    String SQL_SELECT_ALL_FROM_MESSAGE_CONTENT_DETAILS = "SELECT * FROM message_content_details WHERE message_content_id=? ";
    String SQL_SELECT_ALL_FROM_FINANCE_DEPARTMENT = "SELECT * FROM finance_department_mails WHERE record_status=1";

    Email insertEmail(Connection connection, Email email) throws Exception;

    void updateEmail(Connection connection, Email email) throws Exception;

    MessageContentDetails searchMessageContentDetail(Connection connection, Integer id);

    List<String> getFinanceDepartmentMailList(Connection connection);
}
