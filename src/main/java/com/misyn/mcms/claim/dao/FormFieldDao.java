package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.FormFieldDto;

import java.sql.Connection;
import java.util.List;

public interface FormFieldDao extends BaseDao<FormFieldDto> {

    final static String SQL_SEARCH_ALL_FORM_RELATED_FIELDS = "SELECT * FROM claim_form_fields WHERE form_name_id = ? AND field_required= ?";

    final static String SQL_SEARCH_FIELD_USING_DTO_FIELD = "SELECT * FROM claim_form_fields WHERE form_name_id= ? AND dto_field_name= ? AND field_required = 'Y'";

    List<FormFieldDto> getFormRelatedFields(Connection connection, FormFieldDto formFieldDto) throws Exception;

    FormFieldDto getDtoFieldRelatedField(Connection connection, FormFieldDto formFieldDto) throws Exception;
}
