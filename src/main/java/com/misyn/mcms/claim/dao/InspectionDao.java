package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ClaimInspectionTypeDto;
import com.misyn.mcms.claim.dto.InspectionDto;
import com.misyn.mcms.utility.ListBoxItem;

import java.sql.Connection;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 5/17/18.
 */
public interface InspectionDao extends BaseDao<InspectionDto> {

    String SQL_SELECT_INSPECTION_TYPE_BY_INSPECTION_ID = "SELECT * FROM claim_inspection_type where inspection_type_id=?";

    String SQL_SELECT_INSPECTION_TYPE_DESC_BY_INSPECTION_TYPE_ID = "SELECT inspection_type_desc FROM claim_inspection_type where inspection_type_id=?";

    String SQL_SELECT_INSPECTION_TYPE_LIST = "SELECT * FROM claim_inspection_type WHERE inspection_type_id <> 0";

    String SQL_SELECT_ASSESSOR_TYPE_BY_REF_NO = "SELECT DISTINCT(assessor_type) FROM usr_mst WHERE v_emp_no IN(SELECT assessor_code FROM claim_assign_assesor WHERE ref_no=?)";

    String SQL_SELECT_INSPECTION_TYPE_BY_REF_NO_AND_CLAIM_NO = "SELECT n_inspection_type\n" +
            "FROM claim_inspection_info_main\n" +
            "WHERE n_ref_no =? AND n_claim_no = ?";

    String SQL_SELECT_INSPECTION_TYPE_FOR_ON_OR_OFF_SITE = "SELECT n_ref_no\n" +
            "            FROM claim_inspection_info_main\n" +
            "           WHERE n_claim_no = ? AND n_inspection_type IN(1,2)";

    String SQL_DESKTOP_REASSIGN_REASONS = "SELECT * FROM desktop_reassign_reason WHERE V_REC_STATUS = 'A'";

    String REASSIGN_REASONS = "SELECT N_ID, V_REASON FROM claim_assessor_reassign_reason WHERE V_REC_STATUS = 'A'";

    String SELECT_REASSIGN_INSPECTION = "SELECT inspection_type_id, inspection_type_desc, inspection_type_code FROM claim_inspection_type cit\n" +
            "INNER JOIN claim_assign_assesor AS caa ON caa.insepction_id = cit.inspection_type_id WHERE caa.ref_no = ?";

    public ClaimInspectionTypeDto getInspectionTypeDto(Connection connection, Object id) throws Exception;

    public List<InspectionDto> getInspectionTypeByTypeId(Connection connection, Object id) throws Exception;

    String getAssessorTypeByRefNo(Connection connection, String refNo) throws Exception;

    Integer getInspectionType(Connection connection, Integer refNo, Integer claimNo) throws Exception;

    Integer getRefNoForOnOrOffSite(Connection connection, Integer claimNo) throws Exception;

    List<ListBoxItem> getReassignReasons(Connection connection, boolean isDesktop) throws Exception;

    String getInspectionTypeDesc(Connection connection, int inspectionTypeId);

    List<InspectionDto> searchInspectionForReassign(Connection connection, int refId) throws Exception;
}
