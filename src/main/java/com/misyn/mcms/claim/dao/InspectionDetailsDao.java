package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.motorengineer.MotorEngineerDetailsDto;

import java.sql.Connection;
import java.util.List;

/**
 * Created by a<PERSON><PERSON> on 5/16/18.
 */
public interface InspectionDetailsDao extends BaseDao<InspectionDetailsDto> {

    String SQL_INSERT_CLAIM_INSPECTION_INFO_MAIN = "INSERT INTO claim_inspection_info_main\n"
            + "	(n_ref_no," +
            " v_job_no," +
            " n_claim_no," +
            " v_make_confirm," +
            " v_model_confirm," +
            " v_eng_no_confirm," +
            " v_chassis_no," +
            " v_year_make_confirm," +
            " d_inspect_datetime," +
            " n_pav, v_damage_details," +
            " v_pad, v_genun_of_accid, " +
            " v_first_statement_rqed," +
            " v_first_statement_req_reason," +
            " v_invest_rqed," +
            " v_assessor_remark," +
            " n_inspection_type," +
            " n_job_type," +
            " v_assigned_location," +
            " v_current_location, " +
            " v_place_of_inspection," +
            " n_mileage,n_cost_of_call," +
            " n_other_fee," +
            " n_total_assessor_fee," +
            " v_fee_desc," +
            " n_record_status," +
            " v_inpuser," +
            " d_inpdatetime," +
            " v_ass_fee_apr_status," +
            " v_ass_esti_apr_status," +
            " v_assign_rte_user," +
            " d_assign_rte_datetime," +
            " v_chassis_no_confirm," +
            " n_not_checked_reason," +
            " is_vehicle_available," +
            " v_approve_assign_rte_user," +
            " d_approve_assign_datetime," +
            " assessor_fee_detail_id)\n"
            + "	VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'P', 'P', ?, ?, ?, ?, ?, ?, ?,?)";

    String SQL_SELECT_ALL_TO_GRID = "SELECT\n" +
            "	 t3.ref_no,\n" +
            "	 t1.N_CLIM_NO,\n" +
            "	 t1.V_VEHICLE_NO,\n" +
            "	 t3.job_id,\n" +
            "	 t2.inspection_type_desc,\n" +
            "	 t3.record_status,\n" +
            "	 t3.assign_datetime,\n" +
            "	 t3.inp_userid,\n" +
            "	 t3.is_online_inspection,\n" +
            "	 t1.N_POL_REF_NO,\n" +
            "	 t1.N_INTIMATION_TYPE,\n" +
            "	 t4.v_status_desc,\n" +
            "	 t5.v_assign_rte_user,\n" +
            "	 t5.d_assign_rte_datetime,\n" +
            "(SELECT v_usrid FROM usr_mst WHERE v_emp_no=t3.assessor_code GROUP BY v_emp_no) AS assign_assessor\n" +
            " FROM\n" +
            "	 claim_claim_info_main AS t1\n" +
            "INNER JOIN claim_assign_assesor AS t3 ON t3.claim_no = t1.N_CLIM_NO\n" +
            "INNER JOIN claim_inspection_type AS t2 ON t2.inspection_type_id = t3.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t3.job_status = t4.n_ref_id \n" +
            "LEFT JOIN claim_inspection_info_main AS t5 ON t5.n_ref_no = t3.ref_no";


    String SQL_SELECT_COUNT_TO_GRID = "SELECT\n" +
            "	COUNT(*) as cnt\n" +
            "FROM\n" +
            "	claim_claim_info_main AS t1\n" +
            "INNER JOIN claim_assign_assesor AS t3 ON t3.claim_no = t1.N_CLIM_NO\n" +
            "INNER JOIN claim_inspection_type AS t2 ON t2.inspection_type_id = t3.insepction_id \n" +
            "LEFT JOIN claim_inspection_info_main AS t5 ON t5.n_ref_no = t3.ref_no";

    String SQL_SELECT_ALL_SUBMITTED_TO_GRID = "SELECT\n"
            + "\tt1.ref_no,\n"
            + "\tt2.N_CLIM_NO,\n"
            + "\tt2.V_VEHICLE_NO,\n"
            + "\tt1.priority,\n"
            + "\tt2.V_PRIORITY,\n"
            + "\tt1.job_id,\n"
            + "\tt3.inspection_type_desc,\n"
            + "\tt1.record_status,\n"
            + "\tt0.v_ass_esti_apr_status,\n"
            + "\tt0.v_ass_fee_apr_status,\n"
            + "\tt0.d_inpdatetime,\n"
            + "\tt0.d_assign_rte_datetime,\n"
            + "\tt0.is_vehicle_available,\n"
            + "\tt0.v_assign_rte_user,\n"
            + "\tt1.insepction_id,\n"
            + "\tt1.assign_datetime,\n"
            + "\tt2.N_POL_REF_NO,\n"
            + "\tt2.N_INTIMATION_TYPE,\n"
            + "\tt4.v_status_desc,\n"
            + "\tt2.N_CLAIM_STATUS,\n"
            + "\tt0.v_approve_assign_rte_user,\n"
            + "\tt0.d_approve_assign_datetime\n"
            + "FROM\n"
            + "\tclaim_assign_assesor AS t1\n"
            + "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n"
            + "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n"
            + "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id "
            + "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status";

    String SQL_SELECT_ALL_SUBMITTED_AND_FORWARDED_TO_GRID = "SELECT\n"
            + "\tt1.ref_no,\n"
            + "\tt2.N_CLIM_NO,\n"
            + "\tt2.V_VEHICLE_NO,\n"
            + "\tt2.V_PRIORITY,\n"
            + "\tt1.job_id,\n"
            + "\tt3.inspection_type_desc,\n"
            + "\tt1.record_status,\n"
            + "\tt0.v_ass_esti_apr_status,\n"
            + "\tt0.v_ass_fee_apr_status,\n"
            + "\tt0.d_inpdatetime,\n"
            + "\tt0.d_approve_assign_datetime,\n"
            + "\tt0.is_vehicle_available,\n"
            + "\tt0.v_approve_assign_rte_user,\n"
            + "\tt1.insepction_id,\n"
            + "\tt1.assign_datetime,\n"
            + "\tt2.N_POL_REF_NO,\n"
            + "\tt2.N_INTIMATION_TYPE,\n"
            + "\tt4.v_status_desc,\n"
            + "\tt2.N_CLAIM_STATUS\n"
            + "FROM\n"
            + "\tclaim_assign_assesor AS t1\n"
            + "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n"
            + "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n"
            + "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id "
            + "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status";

    String SQL_SELECT_ALL_FWD_DESKTOP_TO_GRID = "SELECT\n"
            + "\tt1.ref_no,\n"
            + "\tt1.priority,\n"
            + "\tt2.N_CLIM_NO,\n"
            + "\tt2.V_VEHICLE_NO,\n"
            + "\tt1.job_id,\n"
            + "\tt3.inspection_type_desc,\n"
            + "\tt1.record_status,\n"
            + "\tt0.v_ass_esti_apr_status,\n"
            + "\tt0.v_ass_fee_apr_status,\n"
            + "\tt0.d_inpdatetime,\n"
            + "\tt0.d_assign_rte_datetime,\n"
            + "\tt0.d_fwd_tc_desktop_datetime,\n"
            + "\tt0.v_assign_rte_user,\n"
            + "\tt1.insepction_id,\n"
            + "\tt1.assign_datetime,\n"
            + "\tt1.inp_userid,\n"
            + "\tt2.N_POL_REF_NO,\n"
            + "\tt2.N_INTIMATION_TYPE,\n"
            + "\tt4.v_status_desc\n"
            + "FROM\n"
            + "\tclaim_assign_assesor AS t1\n"
            + "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n"
            + "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n"
            + "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id "
            + "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status";

    String SQL_SELECT_SUBMITTED_COUNT_TO_GRID = "SELECT\n"
            + "\tCOUNT(*) as cnt\n"
            + "FROM\n"
            + "\tclaim_assign_assesor AS t1\n"
            + "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n"
            + "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n"
            + "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id "
            + "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status";

    String SQL_SELECT_ON_SITE_OFFER_LIST = "SELECT\n" +
            "\tt1.ref_no,\n" +
            "\tt2.N_CLIM_NO,\n" +
            "\tt2.V_VEHICLE_NO,\n" +
            "\tt1.job_id,\n" +
            "\tt3.inspection_type_desc,\n" +
            "\tt1.record_status,\n" +
            "\tt0.v_ass_esti_apr_status,\n" +
            "\tt0.v_ass_fee_apr_status,\n" +
            "\tt0.d_inpdatetime,\n" +
            "\tt0.d_assign_rte_datetime,\n" +
            "\tt0.v_assign_rte_user,\n" +
            "\tt1.insepction_id,\n" +
            "\tt1.assign_datetime,\n" +
            "\tt2.N_POL_REF_NO,\n" +
            "\tt2.N_INTIMATION_TYPE,\n" +
            "\tt4.v_status_desc,\n" +
            "\tt2.N_CLAIM_STATUS\n" +
            "FROM\n" +
            "\tclaim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN onsite_inspection_details AS t5 ON t5.n_ref_no = t0.n_ref_no ";

    String SQL_COUNT_ON_SITE_OFFER_LIST = "SELECT\n" +
            "\tcount(*) as cnt\n" +
            "FROM\n" +
            "\tclaim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN onsite_inspection_details AS t5 ON t5.n_ref_no = t0.n_ref_no ";

    String SQL_SELECT_BY_REF_NO = "SELECT * FROM claim_inspection_info_main where n_ref_no=?";

    String SQL_SELECT_FROM_ME_BY_REF_NO = "SELECT * FROM claim_inspection_info_main_me where n_ref_no=?";

    String SQL_UPDATE_INSPECTION_DEATIL_MASTER = "UPDATE  claim_inspection_info_main \n" +
            "SET \n" +
            "  v_job_no  = ?,\n" +
            "  n_claim_no  = ?,\n" +
            "  v_make_confirm  = ?,\n" +
            "  v_model_confirm  = ?,\n" +
            "  v_eng_no_confirm  =?,\n" +
            "  v_chassis_no  = ?,\n" +
            "  v_year_make_confirm  = ?,\n" +
            "  d_inspect_datetime  = ?,\n" +
            "  n_pav  = ?,\n" +
            "  v_damage_details  = ?,\n" +
            "  v_pad  = ?,\n" +
            "  v_genun_of_accid  = ?,\n" +
            "  v_first_statement_rqed  = ?,\n" +
            "  v_first_statement_req_reason  = ?,\n" +
            "  v_invest_rqed  = ?,\n" +
            "  v_assessor_remark  = ?,\n" +
            "  n_inspection_type  = ?,\n" +
            "  n_job_type  = ?,\n" +
            "  v_assigned_location  = ?,\n" +
            "  v_current_location  = ?,\n" +
            "  v_place_of_inspection  = ?,\n" +
            "  n_mileage  = ?,\n" +
            "  n_cost_of_call  = ?,\n" +
            "  n_other_fee  = ?,\n" +
            "  n_total_assessor_fee  = ?,\n" +
            "  v_fee_desc  = ?,\n" +
            "  n_record_status  = ?,\n" +
            "  v_inpuser  = ?,\n" +
            "  d_inpdatetime  = ?,\n" +
            "  v_assign_rte_user  = ?,\n" +
            "  d_assign_rte_datetime  = ?,\n" +
            "  assessor_fee_detail_id  = ?\n" +
            "WHERE\n" +
            "  n_ref_no  = ?";

    String SQL_SELECT_CLAIM_LIST_BY_VEHICLE_NO = "SELECT N_CLIM_NO FROM claim_claim_info_main WHERE V_VEHICLE_NO=?";

//    String SQL_SELECT_INSPECTION_LIST_BY_CLAIM_NO = "SELECT\n" +
//            "claim_inspection_info_main.n_ref_no,\n" +
//            "claim_inspection_info_main.n_inspection_type,\n" +
//            "claim_inspection_info_main.n_pav,\n" +
//            "claim_inspection_info_main.v_ass_esti_apr_status,\n" +
//            "claim_inspection_info_main.v_ass_fee_apr_status,\n" +
//            "t3.job_id,\n" +
//            "t1.V_VEHICLE_NUMBER,\n" +
//            "t1.V_POL_NUMBER,\n" +
//            "t2.D_ACCID_DATE,\n" +
//            "t5.inspection_type_desc,\n" +
//            "t1.N_POL_REF_NO,\n" +
//            "claim_inspection_info_main.v_ass_esti_apr_datetime AS approve_date_time,\n" +
//            "(SELECT v_status_desc FROM claim_status_para WHERE n_ref_id=t3.record_status) AS status_desc,\n" +
//            "claim_inspection_info_main.v_assign_rte_user AS rte_user,\n" +
//            "(SELECT v_usrid FROM usr_mst WHERE v_emp_no=t3.assessor_code GROUP BY v_emp_no) AS assessor_user\n" +
//            " FROM\n" +
//            "claim_vehicle_info_main AS t1\n" +
//            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_POL_REF_NO = t1.N_POL_REF_NO\n" +
//            "INNER JOIN claim_assign_assesor AS t3 ON t3.claim_no = t2.N_CLIM_NO\n" +
//            "INNER JOIN claim_inspection_info_main ON claim_inspection_info_main.n_ref_no = t3.ref_no\n" +
//            "INNER JOIN claim_inspection_type AS t5 ON claim_inspection_info_main.n_inspection_type = t5.inspection_type_id\n" +
//            "WHERE\n" +
//            "t2.N_CLIM_NO = ? and t3.ref_no NOT IN (?)";

    String SQL_SELECT_INSPECTION_LIST_BY_CLAIM_NO = "SELECT\n" +
            "	claim_inspection_info_main.n_ref_no,\n" +
            "	t3.insepction_id,\n" +
            "	claim_inspection_info_main.n_pav,\n" +
            "	claim_inspection_info_main.n_record_status,\n" +
            "	claim_inspection_info_main.v_ass_esti_apr_status,\n" +
            "	claim_inspection_info_main.v_ass_fee_apr_status,\n" +
            "	claim_inspection_info_main.v_ass_fee_apr_status,\n" +
            "	t3.job_id,\n" +
            "	t1.V_VEHICLE_NUMBER,\n" +
            "	t1.V_POL_NUMBER,\n" +
            "	t2.D_ACCID_DATE,\n" +
            "	t5.inspection_type_desc,\n" +
            "	t1.N_POL_REF_NO,\n" +
            "	claim_inspection_info_main.v_ass_esti_apr_datetime AS approve_date_time,\n" +
            "	(\n" +
            "		SELECT\n" +
            "			v_status_desc\n" +
            "		FROM\n" +
            "			claim_status_para\n" +
            "		WHERE\n" +
            "			n_ref_id = t3.record_status\n" +
            "	) AS status_desc,\n" +
            "	claim_inspection_info_main.v_assign_rte_user AS rte_user,\n" +
            "   CASE\n" +
            "   WHEN claim_inspection_info_main.n_record_status = 80 or\n" +
            "   (claim_inspection_info_main.n_record_status = 9 and claim_inspection_info_main.v_ass_esti_apr_user = v_approve_assign_rte_user)\n" +
            "   THEN claim_inspection_info_main.v_approve_assign_rte_user\n" +
            "   ELSE \"N/A\"\n" +
            "   END AS v_approve_assign_rte_user,\n" +
            "	(\n" +
            "		SELECT\n" +
            "			v_usrid\n" +
            "		FROM\n" +
            "			usr_mst\n" +
            "		WHERE\n" +
            "			v_emp_no = t3.assessor_code\n" +
            "		GROUP BY\n" +
            "			v_emp_no\n" +
            "	) AS assessor_user\n" +
            "FROM\n" +
            "	claim_vehicle_info_main AS t1\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_POL_REF_NO = t1.N_POL_REF_NO\n" +
            "LEFT JOIN claim_assign_assesor AS t3 ON t3.claim_no = t2.N_CLIM_NO\n" +
            "LEFT JOIN claim_inspection_info_main ON claim_inspection_info_main.n_ref_no = t3.ref_no\n" +
            "LEFT JOIN claim_inspection_type AS t5 ON t3.insepction_id = t5.inspection_type_id\n" +
            "WHERE\n" +
            "	t2.N_CLIM_NO = ? AND t3.ref_no NOT IN (?)";

//    String SQL_SELECT_INSPECTION_LIST_BY_CLAIM_NO_WITH_OUT_REF = "SELECT\n" +
//            "claim_inspection_info_main.n_ref_no,\n" +
//            "claim_inspection_info_main.n_inspection_type,\n" +
//            "claim_inspection_info_main.n_pav,\n" +
//            "claim_inspection_info_main.v_ass_esti_apr_status,\n" +
//            "claim_inspection_info_main.v_ass_fee_apr_status,\n" +
//            "claim_inspection_info_main.v_ass_fee_apr_status,\n" +
//            "t3.job_id,\n" +
//            "t1.V_VEHICLE_NUMBER,\n" +
//            "t1.V_POL_NUMBER,\n" +
//            "t2.D_ACCID_DATE,\n" +
//            "t5.inspection_type_desc,\n" +
//            "t1.N_POL_REF_NO,\n" +
//            "claim_inspection_info_main.v_ass_esti_apr_datetime AS approve_date_time,\n" +
//            "(SELECT v_status_desc FROM claim_status_para WHERE n_ref_id=t3.record_status) AS status_desc,\n" +
//            "claim_inspection_info_main.v_assign_rte_user AS rte_user,\n" +
//            "(SELECT v_usrid FROM usr_mst WHERE v_emp_no=t3.assessor_code GROUP BY v_emp_no) AS assessor_user\n" +
//            " FROM\n" +
//            "claim_vehicle_info_main AS t1\n" +
//            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_POL_REF_NO = t1.N_POL_REF_NO\n" +
//            "INNER JOIN claim_assign_assesor AS t3 ON t3.claim_no = t2.N_CLIM_NO\n" +
//            "INNER JOIN claim_inspection_info_main ON claim_inspection_info_main.n_ref_no = t3.ref_no\n" +
//            "INNER JOIN claim_inspection_type AS t5 ON claim_inspection_info_main.n_inspection_type = t5.inspection_type_id\n" +
//            "WHERE\n" +
//            "t2.N_CLIM_NO = ? ";

    String SQL_SELECT_INSPECTION_LIST_BY_CLAIM_NO_WITH_OUT_REF = "SELECT\n" +
            "	claim_inspection_info_main.n_ref_no,\n" +
            "	t3.insepction_id,\n" +
            "	claim_inspection_info_main.n_pav,\n" +
            "	claim_inspection_info_main.n_record_status,\n" +
            "	claim_inspection_info_main.v_ass_esti_apr_status,\n" +
            "	claim_inspection_info_main.v_ass_fee_apr_status,\n" +
            "	claim_inspection_info_main.v_ass_fee_apr_status,\n" +
            "   CASE\n" +
            "   WHEN claim_inspection_info_main.n_record_status = 80 or\n" +
            "   (claim_inspection_info_main.n_record_status = 9 and claim_inspection_info_main.v_ass_esti_apr_user = v_approve_assign_rte_user)\n" +
            "   THEN claim_inspection_info_main.v_approve_assign_rte_user\n" +
            "   ELSE \"N/A\"\n" +
            "   END AS v_approve_assign_rte_user,\n" +
            "	t3.job_id,\n" +
            "	t1.V_VEHICLE_NUMBER,\n" +
            "	t1.V_POL_NUMBER,\n" +
            "	t2.D_ACCID_DATE,\n" +
            "	t5.inspection_type_desc,\n" +
            "	t1.N_POL_REF_NO,\n" +
            "	claim_inspection_info_main.v_ass_esti_apr_datetime AS approve_date_time,\n" +
            "	(\n" +
            "		SELECT\n" +
            "			v_status_desc\n" +
            "		FROM\n" +
            "			claim_status_para\n" +
            "		WHERE\n" +
            "			n_ref_id = t3.record_status\n" +
            "	) AS status_desc,\n" +
            "	claim_inspection_info_main.v_assign_rte_user AS rte_user,\n" +
            "	(\n" +
            "		SELECT\n" +
            "			v_usrid\n" +
            "		FROM\n" +
            "			usr_mst\n" +
            "		WHERE\n" +
            "			v_emp_no = t3.assessor_code\n" +
            "		GROUP BY\n" +
            "			v_emp_no\n" +
            "	) AS assessor_user\n" +
            "FROM\n" +
            "	claim_vehicle_info_main AS t1\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_POL_REF_NO = t1.N_POL_REF_NO\n" +
            "LEFT JOIN claim_assign_assesor AS t3 ON t3.claim_no = t2.N_CLIM_NO\n" +
            "LEFT JOIN claim_inspection_info_main ON claim_inspection_info_main.n_ref_no = t3.ref_no\n" +
            "LEFT JOIN claim_inspection_type AS t5 ON t3.insepction_id = t5.inspection_type_id\n" +
            "WHERE\n" +
            "	t2.N_CLIM_NO = ?";

//    String SQL_SELECT_INSPECTION_JOB_LIST_BY_CLAIM_NO = "SELECT\n" +
//            "t3.job_id,\n" +
//            "t3.ref_no,\n" +
//            "t3.insepction_id,\n" +
//            "t4.inspection_type_desc,\n" +
//            "t1.V_VEHICLE_NUMBER,\n" +
//            "t1.V_POL_NUMBER,\n" +
//            "t2.D_ACCID_DATE,\n" +
//            "t1.N_POL_REF_NO,\n" +
//            "t2.N_CLIM_NO,\n" +
//            "t3.record_status,\n" +
//            "claim_inspection_info_main.v_assign_rte_user,\n" +
//            "claim_inspection_info_main.v_ass_esti_apr_datetime,\n" +
//            "(SELECT v_usrid FROM usr_mst WHERE v_emp_no=t3.assessor_code GROUP BY v_emp_no) AS assessor_user,\n" +
//            "t5.v_status_desc\n" +
//            "FROM\n" +
//            "claim_vehicle_info_main AS t1\n" +
//            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_POL_REF_NO = t1.N_POL_REF_NO\n" +
//            "INNER JOIN claim_assign_assesor AS t3 ON t3.claim_no = t2.N_CLIM_NO\n" +
//            "INNER JOIN claim_inspection_type AS t4 ON t3.insepction_id = t4.inspection_type_id\n" +
//            "INNER JOIN claim_inspection_info_main ON claim_inspection_info_main.n_ref_no = t3.ref_no\n" +
//            "INNER JOIN claim_status_para AS t5 ON t3.record_status = t5.n_ref_id\n" +
//            "WHERE\n" +
//            "t2.N_CLIM_NO = ?";

    String SQL_SELECT_INSPECTION_JOB_LIST_BY_CLAIM_NO = "SELECT\n" +
            "	t3.job_id,\n" +
            "	t3.ref_no,\n" +
            "	t3.insepction_id,\n" +
            "	t4.inspection_type_desc,\n" +
            "	t1.V_VEHICLE_NUMBER,\n" +
            "	t1.V_POL_NUMBER,\n" +
            "	t2.D_ACCID_DATE,\n" +
            "	t1.N_POL_REF_NO,\n" +
            "	t2.N_CLIM_NO,\n" +
            "	t3.record_status,\n" +
            "	claim_inspection_info_main.v_assign_rte_user,\n" +
            "   CASE\n" +
            "   WHEN claim_inspection_info_main.n_record_status = 80 or\n" +
            "   (claim_inspection_info_main.n_record_status = 9 and claim_inspection_info_main.v_ass_esti_apr_user = v_approve_assign_rte_user)\n" +
            "   THEN claim_inspection_info_main.v_approve_assign_rte_user\n" +
            "   ELSE \"N/A\"\n" +
            "   END AS v_approve_assign_rte_user,\n" +
            "	claim_inspection_info_main.v_ass_esti_apr_datetime,\n" +
            "	(\n" +
            "		SELECT\n" +
            "			v_usrid\n" +
            "		FROM\n" +
            "			usr_mst\n" +
            "		WHERE\n" +
            "			v_emp_no = t3.assessor_code\n" +
            "		GROUP BY\n" +
            "			v_emp_no\n" +
            "	) AS assessor_user,\n" +
            "	t5.v_status_desc\n" +
            "FROM\n" +
            "	claim_vehicle_info_main AS t1\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_POL_REF_NO = t1.N_POL_REF_NO\n" +
            "LEFT JOIN claim_assign_assesor AS t3 ON t3.claim_no = t2.N_CLIM_NO\n" +
            "LEFT JOIN claim_inspection_type AS t4 ON t3.insepction_id = t4.inspection_type_id\n" +
            "LEFT JOIN claim_inspection_info_main ON claim_inspection_info_main.n_ref_no = t3.ref_no\n" +
            "LEFT JOIN claim_status_para AS t5 ON t3.record_status = t5.n_ref_id\n" +
            "WHERE\n" +
            "	t2.N_CLIM_NO = ?";

    String SQL_SELECT_INSPECTION_LIST_BY_CLAIM_NO_WITHOUTREF = "SELECT\n" +
            "t3.job_id,\n" +
            "t3.ref_no,\n" +
            "t1.V_VEHICLE_NUMBER,\n" +
            "t1.V_POL_NUMBER,\n" +
            "t2.D_ACCID_DATE,\n" +
            "t5.inspection_type_desc,\n" +
            "t1.N_POL_REF_NO\n" +
            "FROM\n" +
            "claim_vehicle_info_main AS t1\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_POL_REF_NO = t1.N_POL_REF_NO\n" +
            "INNER JOIN claim_assign_assesor AS t3 ON t3.claim_no = t2.N_CLIM_NO\n" +
            "INNER JOIN claim_inspection_info_main ON claim_inspection_info_main.n_ref_no = t3.ref_no\n" +
            "INNER JOIN claim_inspection_type AS t5 ON claim_inspection_info_main.n_inspection_type = t5.inspection_type_id\n" +
            "WHERE\n" +
            "t2.N_CLIM_NO = ? ";


    String SQL_SELECT_INSPECTION_TYPE_LIST_BY_CLAIM_NO = "SELECT\n" +
            "t1.inspection_type_id,\n" +
            "t1.inspection_type_desc,\n" +
            "t2.claim_no,t2.job_id\n" +
            "FROM\n" +
            "claim_inspection_type AS t1\n" +
            "INNER JOIN claim_assign_assesor AS t2 ON t1.inspection_type_id = t2.insepction_id\n" +
            "WHERE\n" +
            "t2.claim_no = ?";

    String SQL_SELECT_ONSITE_OFFER_LIST_FORWARDED = "SELECT\n" +
            "\tt1.ref_no,\n" +
            "\tt2.N_CLIM_NO,\n" +
            "\tt2.V_VEHICLE_NO,\n" +
            "\tt2.V_PRIORITY,\n" +
            "\tt1.job_id,\n" +
            "\tt3.inspection_type_desc,\n" +
            "\tt1.record_status,\n" +
            "\tt0.v_ass_esti_apr_status,\n" +
            "\tt0.v_ass_fee_apr_status,\n" +
            "\tt0.d_inpdatetime,\n" +
            "\tt0.d_approve_assign_datetime,\n" +
            "\tt0.v_approve_assign_rte_user,\n" +
            "\tt1.insepction_id,\n" +
            "\tt1.assign_datetime,\n" +
            "\tt2.N_POL_REF_NO,\n" +
            "\tt2.N_INTIMATION_TYPE,\n" +
            "\tt4.v_status_desc,\n" +
            "\tt2.N_CLAIM_STATUS\n" +
            "FROM\n" +
            "\tclaim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN onsite_inspection_details AS t5 ON t5.n_ref_no = t0.n_ref_no ";

    String SQL_UPDATE_INSPECTION_DETAILS_STATUS_BY_REF_NO = "update claim_inspection_info_main set n_record_status=? where n_ref_no=?";
    String SQL_UPDATE_INSPECTION_DETAILS_STATUS_AND_AUTH_STAT_BY_JOB_NO = "update claim_inspection_info_main set n_record_status=?, v_ass_esti_apr_status=? where v_job_no=?";
    String SQL_UPDATE_INSPECTION_DETAILS_STATUS_AND_AUTH_STAT_BY_REF_NO = "update claim_inspection_info_main set n_record_status=?, v_ass_esti_apr_status=? where n_ref_no=?";
    String SQL_UPDATE_INSPECTION_DETAILS_STATUS_BY_REF_NO_ME = "update claim_inspection_info_main_me set n_record_status=? where n_ref_no=?";
    String CLAIM_INSPECTION_INFO_MAIN_ME_UPDATE_REMARKS = "UPDATE claim_inspection_info_main_me SET v_assessor_remark =? WHERE n_ref_no =?";

    String SQL_SELECT_LATEST_ACR_VALUE = "SELECT * FROM claim_inspection_info_main WHERE n_claim_no=? AND n_ref_no <> ?   ORDER BY n_ref_no DESC LIMIT 1";


    String SQL_SELECT_LATEST_ONSITE_INSPECTION = "SELECT * FROM claim_inspection_info_main WHERE n_inspection_type IN (1,2) AND n_claim_no=? ORDER BY n_ref_no desc LIMIT 1";

    String SQL_SELECT_APPROVED_ON_OR_OFF_SITE_INSPECTION = "SELECT 1 FROM claim_inspection_info_main_me WHERE n_claim_no= ? AND v_ass_esti_apr_status ='A' AND n_inspection_type IN(1,2)";

    String SQL_UPDATE_V_ASS_FEE_STATUS_BY_N_REF_NO_ME = "UPDATE claim_inspection_info_main_me SET v_ass_fee_apr_status='H' WHERE n_ref_no=?";

    String SQL_UPDATE_V_ASS_FEE_STATUS_BY_N_REF_NO = "UPDATE claim_inspection_info_main SET v_ass_fee_apr_status='H' WHERE n_ref_no=?";

    String SQL_SELECT_ON_SITE_OFFER_LIST_ASSESSOR_DETAILS = "SELECT\n" +
            "\tt1.ref_no,\n" +
            "\tt2.N_CLIM_NO,\n" +
            "\tt2.V_VEHICLE_NO,\n" +
            "\tt1.priority,\n" +
            "\tt2.V_PRIORITY,\n" +
            "\tt1.job_id,\n" +
            "\tt3.inspection_type_desc,\n" +
            "\tt1.record_status,\n" +
            "\tt0.v_ass_esti_apr_status,\n" +
            "\tt0.v_ass_fee_apr_status,\n" +
            "\tt0.d_inpdatetime,\n" +
            "\tt0.d_assign_rte_datetime,\n" +
            "\tt0.v_assign_rte_user,\n" +
            "\tt0.v_approve_assign_rte_user,\n" +
            "\tt0.d_approve_assign_datetime,\n" +
            "\tt1.insepction_id,\n" +
            "\tt1.assign_datetime,\n" +
            "\tt2.N_POL_REF_NO,\n" +
            "\tt2.N_INTIMATION_TYPE,\n" +
            "\tt0.is_vehicle_available,\n" +
            "\tt4.v_status_desc,\n" +
            "\tt2.N_CLAIM_STATUS\n" +
            "FROM\n" +
            "\tclaim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN onsite_inspection_details AS t5 ON t5.n_ref_no = t0.n_ref_no ";

    String SQL_SELECT_GARAGE_OFFER_LIST_ASSESSOR_DETAILS = "SELECT\n" +
            "	t1.ref_no,\n" +
            "	t2.N_CLIM_NO,\n" +
            "	t2.V_VEHICLE_NO,\n" +
            "	t1.job_id,\n" +
            "	t3.inspection_type_desc,\n" +
            "	t1.record_status,\n" +
            "	t0.v_ass_esti_apr_status,\n" +
            "	t0.v_ass_fee_apr_status,\n" +
            "	t0.d_inpdatetime,\n" +
            "	t0.d_assign_rte_datetime,\n" +
            "	t0.v_assign_rte_user,\n" +
            "   t0.v_approve_assign_rte_user,\n" +
            "   t0.d_approve_assign_datetime,\n" +
            "	t1.insepction_id,\n" +
            "	t1.assign_datetime,\n" +
            "	t2.N_POL_REF_NO,\n" +
            "	t2.N_INTIMATION_TYPE,\n" +
            "	t0.is_vehicle_available,\n" +
            "	t4.v_status_desc,\n" +
            "	t2.N_CLAIM_STATUS,\n" +
            "   t1.priority,\n" +
            "   t2.V_PRIORITY\n" +
            "FROM\n" +
            "	claim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN garage_inspection_details AS t5 ON t5.n_ref_no = t0.n_ref_no ";

    String SQL_SELECT_ON_SITE_OFFER_LIST_RTE_DETAILS = "SELECT\n" +
            "	t1.ref_no,\n" +
            "	t2.N_CLIM_NO,\n" +
            "	t2.V_VEHICLE_NO,\n" +
            "   t1.priority,\n" +
            "   t2.V_PRIORITY,\n" +
            "	t1.job_id,\n" +
            "	t3.inspection_type_desc,\n" +
            "	t1.record_status,\n" +
            "	t0.v_ass_esti_apr_status,\n" +
            "	t0.v_ass_fee_apr_status,\n" +
            "	t0.d_inpdatetime,\n" +
            "	t0.d_assign_rte_datetime,\n" +
            "	t0.v_assign_rte_user,\n" +
            "   t0.v_approve_assign_rte_user,\n" +
            "   t0.d_approve_assign_datetime,\n" +
            "	t1.insepction_id,\n" +
            "	t1.assign_datetime,\n" +
            "	t2.N_POL_REF_NO,\n" +
            "	t2.N_INTIMATION_TYPE,\n" +
            "	t0.is_vehicle_available,\n" +
            "	t4.v_status_desc,\n" +
            "	t2.N_CLAIM_STATUS\n" +
            "FROM\n" +
            "	claim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN onsite_inspection_details_me AS t5 ON t5.n_ref_no = t0.n_ref_no ";

    String SQL_SELECT_DESKTOP_OFFER_LIST_RTE_DETAILS = "SELECT\n" +
            "	t1.ref_no,\n" +
            "	t2.N_CLIM_NO,\n" +
            "	t2.V_VEHICLE_NO,\n" +
            "	t1.job_id,\n" +
            "	t3.inspection_type_desc,\n" +
            "	t1.record_status,\n" +
            "	t0.v_ass_esti_apr_status,\n" +
            "	t0.v_ass_fee_apr_status,\n" +
            "	t0.d_inpdatetime,\n" +
            "	t0.d_assign_rte_datetime,\n" +
            "	t0.v_assign_rte_user,\n" +
            "   t0.v_approve_assign_rte_user,\n" +
            "   t0.d_approve_assign_datetime,\n" +
            "	t1.insepction_id,\n" +
            "	t1.assign_datetime,\n" +
            "	t2.N_POL_REF_NO,\n" +
            "	t2.N_INTIMATION_TYPE,\n" +
            "	t4.v_status_desc,\n" +
            "	t0.is_vehicle_available,\n" +
            "   t1.priority,\n" +
            "   t2.V_PRIORITY,\n" +
            "	t2.N_CLAIM_STATUS\n" +
            "FROM\n" +
            "	claim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN desktop_inspection_details_me AS t5 ON t5.n_ref_no = t0.n_ref_no ";

    String SQL_SELECT_GARAGE_OFFER_LIST_RTE_DETAILS = "SELECT\n" +
            "	t1.ref_no,\n" +
            "	t2.N_CLIM_NO,\n" +
            "	t2.V_VEHICLE_NO,\n" +
            "	t1.job_id,\n" +
            "	t3.inspection_type_desc,\n" +
            "	t1.record_status,\n" +
            "	t0.v_ass_esti_apr_status,\n" +
            "	t0.v_ass_fee_apr_status,\n" +
            "	t0.d_inpdatetime,\n" +
            "	t0.d_assign_rte_datetime,\n" +
            "	t0.v_assign_rte_user,\n" +
            "   t0.v_approve_assign_rte_user,\n" +
            "   t0.d_approve_assign_datetime,\n" +
            "	t1.insepction_id,\n" +
            "	t1.assign_datetime,\n" +
            "	t2.N_POL_REF_NO,\n" +
            "	t2.N_INTIMATION_TYPE,\n" +
            "	t0.is_vehicle_available,\n" +
            "	t4.v_status_desc,\n" +
            "	t2.N_CLAIM_STATUS,\n" +
            "   t1.priority,\n" +
            "   t2.V_PRIORITY\n" +
            "FROM\n" +
            "	claim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN garage_inspection_details_me AS t5 ON t5.n_ref_no = t0.n_ref_no ";

    String SQL_COUNT_ON_SITE_OFFER_LIST_ASSESSOR_DETAILS = "SELECT\n" +
            "\tcount(*) as cnt\n" +
            "FROM\n" +
            "\tclaim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN onsite_inspection_details AS t5 ON t5.n_ref_no = t0.n_ref_no ";

    String SQL_COUNT_GARAGE_OFFER_LIST_ASSESSOR_DETAILS = "SELECT\n" +
            "	count(*) as cnt\n" +
            "FROM\n" +
            "	claim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN garage_inspection_details AS t5 ON t5.n_ref_no = t0.n_ref_no ";

    String SQL_COUNT_ON_SITE_OFFER_LIST_RTE_DETAILS = "SELECT\n" +
            "	count(*) as cnt\n" +
            "FROM\n" +
            "	claim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN onsite_inspection_details_me AS t5 ON t5.n_ref_no = t0.n_ref_no ";

    String SQL_COUNT_DESKTOP_OFFER_LIST_ASSESSOR_DETAILS = "SELECT\n" +
            "	count(*) as cnt\n" +
            "FROM\n" +
            "	claim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN desktop_inspection_details_me AS t5 ON t5.n_ref_no = t0.n_ref_no ";

    String SQL_COUNT_DESKTOP_OFFER_LIST_RTE_DETAILS = "SELECT\n" +
            "	count(*) as cnt\n" +
            "FROM\n" +
            "	claim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN desktop_inspection_details_me AS t5 ON t5.n_ref_no = t0.n_ref_no ";

    String SQL_COUNT_GARAGE_OFFER_LIST_RTE_DETAILS = "SELECT\n" +
            "	count(*) as cnt\n" +
            "FROM\n" +
            "	claim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN garage_inspection_details_me AS t5 ON t5.n_ref_no = t0.n_ref_no ";


    String SQL_SELECT_ON_SITE_ME_OFFER_LIST = "SELECT\n" +
            "\tt1.ref_no,\n" +
            "\tt2.N_CLIM_NO,\n" +
            "\tt2.V_VEHICLE_NO,\n" +
            "\tt1.job_id,\n" +
            "\tt3.inspection_type_desc,\n" +
            "\tt1.record_status,\n" +
            "\tt0.v_ass_esti_apr_status,\n" +
            "\tt0.v_ass_fee_apr_status,\n" +
            "\tt0.d_inpdatetime,\n" +
            "\tt0.d_assign_rte_datetime,\n" +
            "\tt0.v_assign_rte_user,\n" +
            "\tt1.insepction_id,\n" +
            "\tt1.assign_datetime,\n" +
            "\tt2.N_POL_REF_NO,\n" +
            "\tt2.N_INTIMATION_TYPE,\n" +
            "\tt4.v_status_desc,\n" +
            "\tt2.N_CLAIM_STATUS\n" +
            "FROM\n" +
            "\tclaim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN onsite_inspection_details_me AS t5 ON t5.n_ref_no = t0.n_ref_no ";

    String SQL_COUNT_ON_SITE_ME_OFFER_LIST = "SELECT\n" +
            "\tcount(*) as cnt\n" +
            "FROM\n" +
            "\tclaim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_inspection_info_main AS t0 ON t1.ref_no = t0.n_ref_no\n" +
            "INNER JOIN claim_claim_info_main AS t2 ON t2.N_CLIM_NO = t1.claim_no\n" +
            "INNER JOIN claim_inspection_type AS t3 ON t3.inspection_type_id = t1.insepction_id \n" +
            "INNER JOIN claim_status_para AS t4 ON t4.n_ref_id = t1.job_status\n" +
            "INNER JOIN onsite_inspection_details_me AS t5 ON t5.n_ref_no = t0.n_ref_no ";

    String SQL_RTE_APPROVED = "SELECT 1 FROM claim_inspection_info_main_me WHERE v_ass_esti_apr_status='A' AND n_ref_no=?";

    String SQL_ASSESSOR_APPROVED = "SELECT 1 FROM claim_inspection_info_main WHERE n_record_status IN(8,9,10,80) AND n_ref_no=?";

    String SQL_SELECT_ASSIGN_ALL_ASSESSORS_BY_CLAIM_NO = "SELECT\n" +
            "	t2.v_firstname,\n" +
            "	t2.v_lastname,\n" +
            "	t2.v_mobile \n" +
            "FROM\n" +
            "	claim_assign_assesor AS t1\n" +
            "	INNER JOIN usr_mst AS t2 ON t1.assessor_code = t2.v_emp_no \n" +
            "WHERE\n" +
            "	t1.claim_no = ? GROUP BY t2.v_emp_no";

    String SELECT_ONE_BY_NOT_IN_ON_SITE_AND_APPROVED = "SELECT\n" +
            "	1 \n" +
            "FROM\n" +
            "	claim_assign_assesor \n" +
            "WHERE\n" +
            "	claim_no = ? \n" +
            "	AND record_status IN ( 9 ) \n" +
            "	AND insepction_id NOT IN ( 1, 2 )";

    String SELECT_RTE_ASSIGN_USERS_FOR_CLAIM = "SELECT DISTINCT V_REPORT_TO FROM usr_mst AS um\n" +
            "INNER JOIN claim_assign_assesor AS caa\n" +
            "WHERE caa.assessor_code = um.v_emp_no\n" +
            "AND claim_no= ?\n" +
            "AND v_emp_no <> ''";

    String SELECT_GENUIN_OF_ACCID_BY_ASSESSOR = "SELECT v_genun_of_accid FROM claim_inspection_info_main WHERE n_ref_no = ?";

    String SELECT_GENUIN_OF_ACCID_BY_RTE = "SELECT v_genun_of_accid FROM claim_inspection_info_main_me WHERE n_ref_no = ?";

    String GET_FORWARDED_AUTH_LIMIT = "SELECT\n" +
            "n_auth_level FROM usr_mst AS um\n" +
            "INNER JOIN claim_inspection_info_main AS ciim\n" +
            "WHERE ciim.v_approve_assign_rte_user = um.v_usrid AND ciim.n_ref_no = ?";

    String GET_REPORTING_AUTH_LIMIT = "SELECT\n" +
            "n_auth_level FROM usr_mst AS um\n" +
            "INNER JOIN claim_inspection_info_main AS ciim\n" +
            "WHERE ciim.v_assign_rte_user = um.v_usrid AND ciim.n_ref_no = ?";

    String GET_LATEST_INSPECTION = "SELECT ref_no FROM claim_assign_assesor WHERE claim_no = ? ORDER BY ref_no DESC LIMIT 1";

    String SQL_SELECT_ASSESSOR_PENDING_ALL_TO_GRID = "SELECT\n" +
            "t3.ref_no,\n" +
            "t1.N_CLIM_NO,\n" +
            "t1.V_VEHICLE_NO,\n" +
            "t3.job_id,\n" +
            "t2.inspection_type_desc,\n" +
            "t3.record_status,\n" +
            "t3.assign_datetime,\n" +
            "t3.is_online_inspection,\n" +
            "t1.N_POL_REF_NO,\n" +
            "t1.N_INTIMATION_TYPE,\n" +
            "t4.v_status_desc,\n" +
            "t5.v_assign_rte_user,\n" +
            "t5.d_assign_rte_datetime,\n" +
            "   (\n" +
            "       SELECT\n" +
            "       v_usrid\n" +
            "       FROM\n" +
            "       usr_mst\n" +
            "       WHERE\n" +
            "       v_emp_no = t3.assessor_code\n" +
            "       GROUP BY\n" +
            "       v_emp_no\n" +
            "   ) AS assign_assessor\n" +
            "   FROM\n" +
            "   claim_claim_info_main AS t1\n" +
            "INNER JOIN claim_assign_assesor AS t3 ON t3.claim_no = t1.N_CLIM_NO\n" +
            "INNER JOIN claim_inspection_type AS t2 ON t2.inspection_type_id = t3.insepction_id\n" +
            "INNER JOIN claim_status_para AS t4 ON t3.job_status = t4.n_ref_id\n" +
            "LEFT JOIN claim_inspection_info_main AS t5 ON t5.n_ref_no = t3.ref_no\n" +
            "INNER JOIN usr_mst AS t6 ON t6.v_emp_no = t3.assessor_code\n" +
            "INNER JOIN rte_pending_claim_detail AS t7 on t1.N_CLIM_NO = t7.claim_no";

    String SELECT_INSPECTION_TYPE_BY_INSPECTION_ID = "SELECT\n" +
            "\tinspection_type_desc \n" +
            "FROM\n" +
            "\tclaim_inspection_type \n" +
            "WHERE\n" +
            "\tinspection_type_id = ?";

    String SELECT_IS_VEHICLE_NOT_AVAILABLE_ONSITE_OFFSITE = "SELECT 1 FROM claim_inspection_info_main ciim WHERE n_claim_no = ? AND n_inspection_type IN(1,2) AND n_record_status = 8 AND is_vehicle_available ='N'";

    DataGridDto getJobDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate);

    DataGridDto getSubmittedInspectionDetailsGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, String assignUser, String status);

    DataGridDto getFwdDesktopInspectionDetailsGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, int recordStatus);

    List<Integer> getClaimListByPolNo(String vehicleNo, Connection connection) throws Exception;

    List<PreviousClaimsDto> getPreviousClaimList(Integer claimNo, Integer refNo, Connection connection) throws Exception;

    List<PreviousClaimsDto> getInspectionList(Integer claimNo, Connection connection);

    List<PreviousClaimsDto> getPreviousClaimList(Integer claimNo, Connection connection) throws Exception;

    boolean updateRecordStatusAndAuthStatusByRefNo(Connection connection, int statusId, int refNo, String authStatus) throws Exception;

    boolean updateRecordStatusByRefNo(Connection connection, int statusId, int refNo) throws Exception;

    boolean updateAssessorFeeAuthByRefNo(Connection connection, String assessorFeeAuthStatus, String assessorFeeAuthUserId, String assessorFeeAuthDatetime, int refNo) throws Exception;

    boolean updateInspectionDetailAuthByRefNo(Connection connection, String inspectionDetailsAuthStatus, String inspectionDetailsAuthUserId, String inspectionDetailsAuthDatetime, int refNo) throws Exception;

    List<ClaimInspectionTypeDto> getClaimInspectionTypeDtoList(Connection connection, Integer claimNo) throws Exception;

    void updateForwardTcDesktopUser(Connection connection, int refNo, String forwardUserName, String sysDateTime) throws Exception;

    void updateDesktopInformDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception;

    InspectionDetailsDto searchAndOrderRefNo(Connection connection, Integer claimNo, Integer refNo) throws Exception;

    boolean addRteRemarks(Connection connection, int refNo, String rteRemarks) throws Exception;

    boolean updateAssessorFeeAuthByRefNoMe(Connection connection, String assessorFeeAuthStatus, String assessorFeeAuthUserId, String assessorFeeAuthDatetime, int refNo) throws Exception;

    boolean updateRecordStatusByRefNoMe(Connection connection, int i, int refNo) throws Exception;

    InspectionDetailsDto getLatestInspection(Connection connection, Integer claimNo);

    boolean isOnSiteOrOffSIteInspectionApproved(Connection connection, Integer claimNo) throws Exception;

    void updateAssFeeAprStatusMe(Connection connection, Integer refNo) throws Exception;

    void updateAssFeeAprStatus(Connection connection, Integer refNo) throws Exception;

    DataGridDto getSubmittedInspectionDetailsOfferGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, Integer offerType, String isRteOrAssessorDetails, String type, String InspectionId);

    boolean isRteApproved(Connection connection, Integer refNo) throws Exception;

    boolean isAssessorApproved(Connection connection, Integer refNo) throws Exception;

    List<AssessorDto> getAssignAssessorsByClaim(Connection connection, Integer claimNo);

    InspectionDetailsDto searchByRefNo(Connection connection, Integer keyId);

    boolean isApprovedInspectionNotInOnsite(Connection connection, int claimNo);

    MotorEngineerDetailsDto getInspectionReportDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, Object id);

    MotorEngineerDetailsDto getInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, Object id);

    MotorEngineerDetailsDto getAssessorFeeInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, Object id);

    DataGridDto getInspectionDetailsOfferGridDtoByUser(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, Integer offerType, List<RtePendingClaimsDto> allPendingClaims, Integer status);

    boolean updateForwardDetails(Connection connection, String forwardUser, String forwardDateTime, int refNo) throws Exception;

    DataGridDto getSubmittedInspectionDetailsForwardedGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, UserDto user, Integer offerType);

    List<String> getAssignRteUsersForClaim(Connection connection, Integer claimNo) throws Exception;

    String getConsistencyByAssessorDetails(Connection connection, Integer refNo);

    String getConsistencyByRteDetails(Connection connection, Integer refNo);

    Integer getForwardedAuthLimit(Connection connection, int refNo, boolean isReporting) throws Exception;

    Integer getLatestInspectionRefNo(Connection connection, Integer claimNo) throws Exception;

    DataGridDto getAssessorPendingJobDataGridDto(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate);

    String getInspectionTypeById(Connection connection, Integer inspectionId) throws Exception;

    boolean getIsVehicleNotAvailableOnSiteOffSite(Connection connection, Integer claimNo) throws Exception;
}
