package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.InvestigationDetailsDto;

import java.sql.Connection;
import java.util.List;

public interface InvestigationDetailsDao extends BaseDao<InvestigationDetailsDto> {
    String INSERT_INVESTIGATION_DETAILS = "INSERT INTO claim_investigation_details VALUES (0,?,?,?,?,?,?,?,?,?" +
            ",?,?,?,?,?,?,?,?,?" +
            ",?,?,?,?,?,?,?,?,?,?" +
            ",?)";
    String UPDATE_INVESTIGATION_DETAILS = "UPDATE claim_investigation_details SET \n" +
            "V_INVESTIGATION_STATUS =? ,\n" +
            "V_IS_ACCIDENT =? ,\n" +
            "V_IS_THEFT =? ,\n" +
            "V_IS_FIRE =? ,\n" +
            "N_ASSIGN_INVESTIGATOR_USER_REF_ID =? ,\n" +
            "V_INVEST_ARRANGE_USER =? ,\n" +
            "D_INVEST_ARRANGE_DATE_TIME =? ,\n" +
            "V_IS_FIRST_STATEMENT =? ,\n" +
            "V_IS_DL =? ,\n" +
            "V_IS_CLAIM_FORM =? ,\n" +
            "V_IS_PHOTOS =?," +
            "V_REASON=?" +
            " WHERE N_INVEST_TXN_NO =?\n";

    String UPDATE_INVESTIGATION_COMPLETE = "UPDATE claim_investigation_details SET \n" +
            "V_INVESTIGATION_STATUS =? ,\n" +
            "V_INVEST_COMPLETED_USER =? ,\n" +
            "D_INVEST_COMPLETED_DATE_TIME =? ,\n" +
            "N_PROF_FEE =? ,\n" +
            "N_TRAVEL_FEE =? ,\n" +
            "N_OTHER_FEE =? ,\n" +
            "N_TOTAL_FEE =? ,\n" +
            "V_PAYMENT_STATUS =? ,\n" +
            "V_PAYMENT_INPUT_USER =? ,\n" +
            "D_PAYMENT_INPUT_DATE_TIME =? ,\n" +
            "V_PAYMENT_APRV_USER =? ,\n" +
            "D_PAYMENT_APRV_DATE_TIME =? \n" +
            " WHERE N_INVEST_TXN_NO =?\n";

    String UPDATE_INVESTIGATION_PAYMENT = "UPDATE claim_investigation_details SET \n" +
            "N_PROF_FEE =? ,\n" +
            "N_TRAVEL_FEE =? ,\n" +
            "N_OTHER_FEE =? ,\n" +
            "N_TOTAL_FEE =? ,\n" +
            "V_PAYMENT_STATUS =? ,\n" +
            "V_PAYMENT_INPUT_USER =? ,\n" +
            "D_PAYMENT_INPUT_DATE_TIME =? ,\n" +
            "V_PAYMENT_APRV_USER =? ,\n" +
            "D_PAYMENT_APRV_DATE_TIME =? \n" +
            " WHERE N_INVEST_TXN_NO =?\n";

    String UPDATE_INVESTIGATION_CANCEL = "UPDATE claim_investigation_details SET \n" +
            "V_INVESTIGATION_STATUS =? ,\n" +
            "V_INVEST_ARRANGE_USER =? ,\n" +
            "D_INVEST_ARRANGE_DATE_TIME =? \n" +
            " WHERE N_INVEST_TXN_NO =?\n";

    String UPDATE_INVESTIGATION_ASSIGN_USER = "UPDATE claim_investigation_details SET \n" +
            "V_INVEST_REQ_USER =? ,\n" +
            "D_INVEST_REQ_DATE_TIME =? \n" +
            " WHERE N_INVEST_TXN_NO =?\n";

    String UPDATE_INVESTIGATION_REQUEST_APPROVED = "UPDATE claim_investigation_details SET \n" +
            "V_INVESTIGATION_STATUS =? ,\n" +
            "V_INVEST_REQ_APRVD_USER =? ,\n" +
            "D_INVEST_REQ_APRVD_DATE_TIME =? \n" +
            " WHERE N_INVEST_TXN_NO =?\n";

    String UPDATE_INVESTIGATION_STATUS = "UPDATE claim_investigation_details SET \n" +
            "V_INVESTIGATION_STATUS =? \n" +
            " WHERE N_INVEST_TXN_NO =?\n";

    String UPDATE_INVESTIGATION_PAYMENT_STATUS = "UPDATE claim_investigation_details SET \n" +
            "V_PAYMENT_STATUS =? \n" +
            " WHERE N_INVEST_TXN_NO =?\n";

    String SEARCH_INVESTIGATION_DETAILS = "SELECT\n" +
            "t1.N_INVEST_TXN_NO,\n" +
            "t1.V_INVEST_JOB_NO,\n" +
            "t1.N_CLAIM_NO,\n" +
            "t1.V_INVESTIGATION_STATUS,\n" +
            "t1.V_IS_ACCIDENT,\n" +
            "t1.V_IS_THEFT,\n" +
            "t1.V_IS_FIRE,\n" +
            "t1.N_ASSIGN_INVESTIGATOR_USER_REF_ID,\n" +
            "t1.V_INVEST_ARRANGE_USER,\n" +
            "t1.D_INVEST_ARRANGE_DATE_TIME,\n" +
            "t1.V_INVEST_COMPLETED_USER,\n" +
            "t1.D_INVEST_COMPLETED_DATE_TIME,\n" +
            "t1.V_IS_FIRST_STATEMENT,\n" +
            "t1.V_IS_DL,\n" +
            "t1.V_IS_CLAIM_FORM,\n" +
            "t1.V_IS_PHOTOS,\n" +
            "t1.V_REASON,\n" +
            "t2.V_NAME,\n" +
            "t1.N_PROF_FEE,\n" +
            "t1.N_TRAVEL_FEE,\n" +
            "t1.N_OTHER_FEE,\n" +
            "t1.N_TOTAL_FEE,\n" +
            "t1.V_PAYMENT_STATUS,\n" +
            "t1.V_PAYMENT_INPUT_USER,\n" +
            "t1.D_PAYMENT_INPUT_DATE_TIME,\n" +
            "t1.V_PAYMENT_APRV_USER,\n" +
            "t1.D_PAYMENT_APRV_DATE_TIME,\n" +
            "t1.V_INVEST_REQ_USER,\n" +
            "t1.D_INVEST_REQ_DATE_TIME,\n" +
            "t1.V_INVEST_REQ_APRVD_USER,\n" +
            "t1.D_INVEST_REQ_APRVD_DATE_TIME\n" +
            "FROM\n" +
            "claim_investigation_details AS t1\n" +
            "INNER JOIN claim_assessor AS t2 ON t1.N_ASSIGN_INVESTIGATOR_USER_REF_ID = t2.N_REF_NO\n" +
            "WHERE\n" +
            "t1.N_INVEST_TXN_NO = ?";

    String SEARCH_INVESTIGATION_DETAILS_USING_CLAIM_NUMBER = "SELECT\n" +
            "t1.N_INVEST_TXN_NO,\n" +
            "t1.V_INVEST_JOB_NO,\n" +
            "t1.N_CLAIM_NO,\n" +
            "t1.V_INVESTIGATION_STATUS,\n" +
            "t1.V_IS_ACCIDENT,\n" +
            "t1.V_IS_THEFT,\n" +
            "t1.V_IS_FIRE,\n" +
            "t1.N_ASSIGN_INVESTIGATOR_USER_REF_ID,\n" +
            "t1.V_INVEST_ARRANGE_USER,\n" +
            "t1.D_INVEST_ARRANGE_DATE_TIME,\n" +
            "t1.V_INVEST_COMPLETED_USER,\n" +
            "t1.D_INVEST_COMPLETED_DATE_TIME,\n" +
            "t1.V_IS_FIRST_STATEMENT,\n" +
            "t1.V_IS_DL,\n" +
            "t1.V_IS_CLAIM_FORM,\n" +
            "t1.V_IS_PHOTOS,\n" +
            "t1.V_REASON,\n" +
            "t1.V_PAYMENT_STATUS,\n" +
            "t2.V_NAME\n" +
            "FROM\n" +
            "claim_investigation_details AS t1\n" +
            "INNER JOIN claim_assessor AS t2 ON t1.N_ASSIGN_INVESTIGATOR_USER_REF_ID = t2.N_REF_NO\n" +
            "WHERE\n" +
            "t1.N_CLAIM_NO = ? AND t1.V_INVESTIGATION_STATUS IN('C','CAN')";
    String GET_MAX_INVESTIGATION_TXN_ID_BY_CLAIM_NO = "SELECT IFNULL(MAX(N_INVEST_TXN_NO),0) as maxTxnId FROM claim_investigation_details WHERE N_CLAIM_NO =? AND V_INVESTIGATION_STATUS IN('AR','CH_REQ_INVEST','DM_REQ_INVEST','INVEST_APPROVED')";

    String SELECT_CLAIM_INVESTIGATION_SEQUENCE = "SELECT * FROM claim_investigation_sequence";
    String UPDATE_CLAIM_INVESTIGATION_SEQUENCE = "UPDATE claim_investigation_sequence SET invest_job_no=?";


    String SELECT_INVESTIGATION_DETAILS_NOT_IN_COMPLETE_OR_CANCEL_BY_ARRANGE_DATE = "	SELECT\n" +
            "		V_INVEST_JOB_NO,\n" +
            "		N_CLAIM_NO,\n" +
            "		V_INVEST_ARRANGE_USER,\n" +
            "		D_INVEST_ARRANGE_DATE_TIME,\n" +
            "		V_INVESTIGATION_STATUS\n" +
            "		FROM\n" +
            "		claim_investigation_details \n" +
            "		WHERE\n" +
            "		V_INVESTIGATION_STATUS IN('AR') AND D_INVEST_ARRANGE_DATE_TIME < ?";

    List<InvestigationDetailsDto> searchAll(Connection connection, Integer claimNo) throws Exception;

    Integer getMaxInvestigationTxnIdByClaimNo(Connection connection, Integer claimNo);

    InvestigationDetailsDto updateInvestigationCompleteDetails(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception;

    InvestigationDetailsDto updateInvestigationPaymentDetails(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception;

    InvestigationDetailsDto updateInvestigationArrangedDetails(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception;

    InvestigationDetailsDto updateInvestigationStatus(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception;

    InvestigationDetailsDto updateInvestigationPaymentStatus(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception;

    InvestigationDetailsDto updateInvestigationRequestApprove(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception;

    InvestigationDetailsDto updateInvestigationCancelDetails(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception;

    InvestigationDetailsDto updateInvestigationAssignUser(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception;

    List<InvestigationDetailsDto> getInvestigationListStillPendingAfterFiveDays(Connection connection, String dateTime) throws Exception;
}
