package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.InvestigationReasonDetailsDto;

import java.sql.Connection;
import java.util.List;

public interface InvestigationReasonDetailsDao extends BaseDao<InvestigationReasonDetailsDto> {

    String SEARCH_ALL_INVESTIGETIONS_BY_TXN_NO = "SELECT\n" +
            "t1.N_INVES_REASON_REF_NO,\n" +
            "t1.V_INVES_REASON,\n" +
            "t2.N_TXN_NO,\n" +
            "t2.N_INVES_REASON_REF_NO,\n" +
            "t2.V_IS_CHECK,\n" +
            "t2.N_INVEST_TXN_NO\n" +
            "FROM\n" +
            "claim_investigation_reason_type AS t1\n" +
            "LEFT JOIN claim_investigation_reason_details AS t2 ON t2.N_INVES_REASON_REF_NO = t1.N_INVES_REASON_REF_NO AND t2.N_INVEST_TXN_NO = ?";
    String DELETE_INVESTIGETIONS_BY_TXN_NO = "DELETE FROM claim_investigation_reason_details WHERE N_INVEST_TXN_NO =?";
    String INSERT_INVESTIGETION_DETAILS = "INSERT INTO claim_investigation_reason_details VALUES(0,?,?,?)";

//    public InvestigationReasonDetailsDto searchAll(Connection connection, Integer TxnNo) throws Exception;

    void deleteInvestigetionByTxnNo(Connection connection, Integer txInvestigationTxnNonNo) throws Exception;

    List<InvestigationReasonDetailsDto> searchAllByInvestigationTxnNo(Connection connection, Integer txInvestigationTxnNonNo) throws Exception;
}
