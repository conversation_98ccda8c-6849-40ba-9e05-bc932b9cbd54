package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.LargeClaimEmailHistoryDto;
import com.misyn.mcms.claim.dto.LargeClaimEmailReceiversDto;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface LargeClaimEmailDao {

    String GET_AUTHORITY_LIMITS = "SELECT * FROM large_claim_authority_limit WHERE N_ACCESS_USER_TYPE = ?";

    String GET_MAIL_ADDRESSESS = "SELECT * FROM large_claim_email_receivers";

    String IS_MAIL_SENT = "SELECT 1 FROM large_claim_email_history WHERE N_CLAIM_NO = ?";

    String GET_HISTORY_RECORDS = "SELECT * FROM large_claim_email_history WHERE N_CLAIM_NO = ?";

    String SAVE_HISTORY = "INSERT INTO large_claim_email_history VALUES (0,?,?,?,?,?,?)";

    BigDecimal getAuthorityLimits(Connection connection, Integer accessUserType) throws Exception;

    List<LargeClaimEmailReceiversDto> getAutomatedMailReceivers(Connection connection) throws Exception;

    boolean isMailAlreadySent(Connection connection, Integer claimNo) throws Exception;

    List<LargeClaimEmailHistoryDto> getHistoryRecords(Connection connection, Integer claimNo) throws Exception;

    void saveHistory(Connection connection, LargeClaimEmailHistoryDto emailHistoryDto) throws Exception;
}
