/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.LeaveApply;
import com.misyn.mcms.dbconfig.ConnectionPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class LeaveApplyManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(LeaveApplyManager.class);
    private static LeaveApplyManager leaveApplyManager = null;
    private ConnectionPool cp = null;

    public LeaveApplyManager() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public static synchronized LeaveApplyManager getInstance() {
        if (leaveApplyManager == null) {
            leaveApplyManager = new LeaveApplyManager();
        }
        return leaveApplyManager;
    }

    public synchronized List<LeaveApply> getLeaveList(String searchKey, int n_accessusrtype) {//n_accessusrtype->13=Claim handler, 9-JE
        List<LeaveApply> leaveList = new ArrayList<LeaveApply>();
        Connection conn = null;
        PreparedStatement ps = null;


        String strSql = "SELECT\n"
                + "t1.v_usrid,\n"
                + "IFNULL(t2.D_FROM_DATE,'') AS D_FROM_DATE,\n"
                + "IFNULL(t2.D_TO_DATE,'') AS D_TO_DATE,\n"
                + "t1.n_usrcode\n"
                + "FROM\n"
                + "usr_mst AS t1\n"
                + "LEFT JOIN claim_user_leave AS t2 ON t1.n_usrcode = t2.N_USRCODE\n"
                + "WHERE\n"
                + "t1.n_accessusrtype IN(" + n_accessusrtype + ") " + searchKey;


        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement(strSql);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                LeaveApply la = new LeaveApply();
                la.setV_user_id(rs.getString("t1.v_usrid"));
                la.setN_user_code(rs.getInt("N_USRCODE"));
                la.setD_to_date(rs.getString("D_TO_DATE"));
                la.setD_from_date(rs.getString("D_FROM_DATE"));
                //la.setV_first_name(rs.getString("t1.v_firstname"));
                // la.setV_last_name(rs.getString("t1.v_lastname"));
                leaveList.add(la);
            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return leaveList;
    }

    public synchronized int saveLeaveApply(LeaveApply leaveApply) {
        int result = 0;

        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement("SELECT N_USRCODE FROM claim_user_leave WHERE N_USRCODE=?");
            ps.setInt(1, leaveApply.getN_user_code());
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                ps1 = conn.prepareStatement("UPDATE claim_user_leave SET D_FROM_DATE=?,D_TO_DATE=?,N_USRCODE=? WHERE N_USRCODE=?");
                ps1.setString(1, leaveApply.getD_from_date());
                ps1.setString(2, leaveApply.getD_to_date());
                ps1.setInt(3, leaveApply.getN_user_code());
                ps1.setInt(4, leaveApply.getN_user_code());
                result = ps1.executeUpdate();
            } else {
                ps1 = conn.prepareStatement("INSERT INTO claim_user_leave VALUES(?,?,?)");
                ps1.setInt(1, leaveApply.getN_user_code());
                ps1.setString(2, leaveApply.getD_from_date());
                ps1.setString(3, leaveApply.getD_to_date());
                result = ps1.executeUpdate();
            }

            rs.close();


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
                if (ps1 != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return result;
    }

    public synchronized LeaveApply getUserDetails(int user_code) {
        LeaveApply leaveApply = null;
        Connection conn = null;
        PreparedStatement ps = null;

        try {
            conn = getJDBCConnection();
            ps = conn.prepareStatement("SELECT\n"
                    + "usr_mst.n_usrcode,\n"
                    + "usr_mst.v_usrid,\n"
                    + "IFNULL(claim_user_leave.D_FROM_DATE,' ') AS D_FROM_DATE,\n"
                    + "IFNULL(claim_user_leave.D_TO_DATE,' ') AS D_TO_DATE\n"
                    + "FROM\n"
                    + "usr_mst\n"
                    + "Left JOIN claim_user_leave ON usr_mst.n_usrcode = claim_user_leave.N_USRCODE\n"
                    + "WHERE usr_mst.n_usrcode=?");
            ps.setInt(1, user_code);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                leaveApply = new LeaveApply();
                leaveApply.setN_user_code(rs.getInt("n_usrcode"));
                leaveApply.setD_from_date(rs.getString("D_FROM_DATE"));
                leaveApply.setD_to_date(rs.getString("D_TO_DATE"));
                leaveApply.setV_user_id(rs.getString("v_usrid"));

            }
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return leaveApply;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}
