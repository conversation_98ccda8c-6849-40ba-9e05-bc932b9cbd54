package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.McmsClaimOfflineReserveClaimDto;

import java.sql.Connection;

public interface McmsClaimOfflineReserveClaimDao extends BaseDao<McmsClaimOfflineReserveClaimDto> {
    String MCMS_CLAIM_OFFLINE_RESERVE_CLAIM_INSERT = "INSERT INTO claim_offline_reserve_claim VALUES(0,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    String MCMS_CLAIM_OFFLINE_RESERVE_CLAIM_UPDATE = " UPDATE claim_offline_reserve_claim SET \n" +
            "OV_CLAIM_NO =?,\n" +
            "ON_BILL_AMOUNT =?,\n" +
            "ON_ALLOWED_AMOUNT =?,\n" +
            "ON_DEP_PER =?,\n" +
            "ON_PA_ESTIMATE_AMOUNT =?,\n" +
            "OV_REPORT_TYPE =?,\n" +
            "OV_IDENTIFICATION_NO =?,\n" +
            "OD_DATE_OF_ACCSSES_SUB =?,\n" +
            "OD_DATE_OF_APPOINTMENT =?,\n" +
            "OV_TYPE =?,\n" +
            "OD_DATE_OF_ACCSSESSMENT =?,\n" +
            "OV_PANEL_CATEGORY =?,\n" +
            "OV_INSTITUTION_BRANCH =?,\n" +
            "OV_INSTITUTION_CODE =?,\n" +
            "OV_IDENTIFICATION_CODE =?,\n" +
            "OV_PANEL_TYPE =?,\n" +
            "OD_RECEIVED_DATE =?,\n" +
            "OV_LOSS_TYPE =?,\n" +
            "CV_REQUIRED =?,\n" +
            "OV_RISK_NO =?,\n" +
            "D_INSERT_DATE_TIME =?,\n" +
            "N_RETRY_ATTEMPT =?,\n" +
            "V_ISFS_UPDATE_STAT =?,\n" +
            "D_ISFS_UPDATE_DATE_TIME  =? WHERE N_RESERVE_CLAIM_ID =?\n";

    String UPDATE_OFFLINE_RESERVE_CLAIM_POLICY_CHANNEL_TYPE = "UPDATE claim_offline_reserve_claim SET V_POLICY_CHANNEL_TYPE = ? WHERE N_CLAIM_NO = ?";

    void updateReserveClaimPolicyChannelType(Connection connection, Integer claimNo, String policyChannelType) throws Exception;
}
