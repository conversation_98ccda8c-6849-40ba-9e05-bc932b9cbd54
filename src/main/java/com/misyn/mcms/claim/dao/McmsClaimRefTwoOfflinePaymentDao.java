package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.McmsClaimOfflinePaymentDto;

import java.sql.Connection;

public interface McmsClaimRefTwoOfflinePaymentDao extends BaseDao<McmsClaimOfflinePaymentDto> {
    String MCMS_CLAIM_OFFLINE_PAYMENT_INSERT = "INSERT INTO claim_ref_two_offline_payment VALUES(0,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    String MCMS_CLAIM_OFFLINE_PAYMENT_UPDATE = "UPDATE claim_ref_two_offline_payment SET \n" +
            "OV_PAYEE_TYPE =?,\n" +
            "OV_IDENTIFICATION_NO =?,\n" +
            "OV_CLAIM_NO =?,\n" +
            "ON_PAID_AMOUNT =?,\n" +
            "OV_INSTITUTION_BRANCH =?,\n" +
            "OV_INSTITUTION_CODE =?,\n" +
            "ON_TOTAL_PAYABLE =?,\n" +
            "OV_IDENTIFICATION_CODE =?,\n" +
            "OV_VOUCHER_FLAG =?,\n" +
            "D_INSERT_DATE_TIME =?,\n" +
            "N_RETRY_ATTEMPT =?,\n" +
            "V_ISFS_UPDATE_STAT =?,\n" +
            "D_ISFS_UPDATE_DATE_TIME =? WHERE N_PAYMENT_ID =?\n";

    String UPDATE_OFFLINE_PAYMENT_POLICY_CHANNEL_TYPE = "UPDATE claim_ref_two_offline_payment SET V_POLICY_CHANNEL_TYPE = ? WHERE N_CLAIM_NO = ?";

    void updatePaymentPolicyChannelType(Connection connection, Integer claimNo, String policyChannelType) throws Exception;
}

