package com.misyn.mcms.claim.dao;

import java.sql.Connection;
import java.util.List;

public interface MofaLevelHierarchyDao {

    String SELECT_ALL_MANDATORY_LEVEL = "SELECT\n" +
            "	mofa_level \n" +
            "FROM\n" +
            "	mofa_level_hierarchy \n" +
            "WHERE\n" +
            "	is_mandatory = 'Y' \n" +
            "	AND STATUS = 'A'";

    String SELECT_MAX_MOFA_LEVEL = "SELECT\n" +
            "	mofa_level \n" +
            "FROM\n" +
            "	mofa_level_hierarchy \n" +
            "WHERE\n" +
            "	STATUS = 'A' \n" +
            "ORDER BY\n" +
            "	mofa_level DESC \n" +
            "	LIMIT 1";

    List<Integer> getAllMandatoryMofaLevel(Connection connection);

    Integer getMofaMaxLevel(Connection connection);
}
