package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.OfflineIntimationDto;

import java.sql.Connection;

public interface OfflineIntimationDao {
    String INSERT_CLAIM_OFFLINE_INTIMATION = "INSERT INTO claim_offline_intimation VALUES(0,?,?,?,?,?,?,?,?,?," +
            "?,?,?,?,?,?,?,?,?,?," +
            "?,?,?,?,?,?,?,?,?,?," +
            "?,?,?,?,?,?,?,?)";

    String UPDATE_CLAIM_OFFLINE_INTIMATION = "UPDATE claim_offline_intimation SET OV_POLICY_NO=?,V_ISFS_UPDATE_STAT=?,N_RETRY_ATTEMPS=?,OV_RISK_NO=?,V_POLICY_CHANNEL_TYPE=? WHERE OV_INT_NO=?";

    String UPDATE_CLAIM_ISF_STATUS = "UPDATE claim_claim_info_main SET V_ISFS_UPDATE_STATUS = ? WHERE N_CLIM_NO = ?";

    void saveOfflineIntimation(Connection connection, OfflineIntimationDto offlineIntimationDto) throws Exception;

    void updateOfflineIntimation(Connection connection, OfflineIntimationDto offlineIntimationDto) throws Exception;

    void updateIsfUpdateStatus(Connection connection, String status, Integer claimNo) throws Exception;


}
