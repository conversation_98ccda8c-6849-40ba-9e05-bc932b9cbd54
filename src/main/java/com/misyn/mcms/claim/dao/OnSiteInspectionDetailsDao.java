package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.OnSiteInspectionDetailsDto;

import java.math.BigDecimal;
import java.sql.Connection;

/**
 * Created by aki<PERSON> on 5/21/18.
 */
public interface OnSiteInspectionDetailsDao extends BaseDao<OnSiteInspectionDetailsDto> {

    String SQL_INSERT_INTO_ONSITE_INSPECTION_DETAILS = "INSERT INTO onsite_inspection_details VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

    String SQL_SELECT_ONSITE_INSPECTION_DETAILS = "SELECT * FROM  onsite_inspection_details WHERE n_ref_no=?";

    String SQL_UPDATE_ONSITE_INSPECTION_DETAILS = "UPDATE  onsite_inspection_details \n" +
            "SET  inspection_id  = ?,\n" +
            "  cost_part  = ?,\n" +
            "  cost_labour  = ?,\n" +
            "  excess  = ?,\n" +
            "  acr  = ?,\n" +
            "  bold_tyre_penalty  = ?,\n" +
            "  bold_percent  = ?,\n" +
            "  bold_tire_penalty_amount  = ?,\n" +
            "  under_insurad_penalty  = ?,\n" +
            "  under_penalty_percent  = ?,\n" +
            "  under_penalty_amount  = ?,\n" +
            "  provide_offer  = ?,\n" +
            "  offer_type  = ?,\n" +
            "  payable_amount  = ?,\n" +
            "  request_ari  = ?\n" +
            "WHERE\n" +
            "  n_ref_no  = ?\n";

    String SQL_SELECT_ACR_BY_REF_NO = "SELECT acr FROM onsite_inspection_details WHERE n_ref_no = ?";


    BigDecimal getAcr(Connection connection, Integer refNo);
}
