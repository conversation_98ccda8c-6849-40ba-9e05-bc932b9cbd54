package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.PendingInvestigationDetailsDto;

import java.sql.Connection;
import java.sql.SQLException;

public interface PendingInvestigationDetailsDao extends BaseDao<PendingInvestigationDetailsDto> {

    String SQL_INSERT_MASTER = "INSERT INTO `pending_investigation_details` (`claim_no`, `investigation_job_no`, `notification_send_date` )\n" +
            "VALUES\n" +
            "	(?, ?, ? );";

    String SQL_SELECT_ONE_BY_CLAIM_NO_AND_SEND_DATE = "SELECT 1 FROM pending_investigation_details WHERE claim_no = ? AND notification_send_date = ?";

    boolean isNotificationSend(Connection connection, Integer claimNo) throws SQLException;
}
