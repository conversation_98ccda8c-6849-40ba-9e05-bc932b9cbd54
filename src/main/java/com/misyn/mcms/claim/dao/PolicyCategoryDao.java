package com.misyn.mcms.claim.dao;

import java.sql.Connection;
import java.util.Map;

public interface PolicyCategoryDao {

    String SELECT_SERVICE_FACTOR_CATEGORY = "SELECT f.V_CODE, f.V_SERVICE_FACTOR_NAME FROM service_factor_detail f";
    String SELECT_COVER_CATEGORY = "SELECT f.V_CODE, f.V_COVER_NAME FROM cover_detail f";
    String SELECT_BENEFIT_CATEGORY = "SELECT f.V_CODE,f.V_BENEFIT_NAME  FROM benefit_detail f";
    String SELECT_CONDITION_CATEGORY = "SELECT f.V_CODE,f.V_C_E_NAME FROM condition_and_exclusion_detail f";
    String SELECT_SPECIAL_CATEGORY = "SELECT f.V_CODE,f.V_NAME FROM special_package_detail f";

    Map<String, String> getServiceFactorCategoryDtoList(Connection connection);

    Map<String, String> getCoverCategoryDtoList(Connection connection);

    Map<String, String> getBenefitCategoryDtoList(Connection connection);

    Map<String, String> getConditionCategoryDtoList(Connection connection);

    Map<String, String> getSpecialCategoryDtoList(Connection connection);


}
