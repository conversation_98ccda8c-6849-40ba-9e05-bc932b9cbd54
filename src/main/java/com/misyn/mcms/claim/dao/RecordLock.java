/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dao;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
public class RecordLock implements Serializable {

    private static RecordLock recordLock = null;
    private Map<Integer, String> mapLockViewAssignAssessor = null;
    private Map<Integer, String> mapLockViewMoterEngineer = null;


    public RecordLock() {
        mapLockViewAssignAssessor = new HashMap<Integer, String>();
        mapLockViewMoterEngineer = new HashMap<Integer, String>();
    }

    public static RecordLock getInstance() {
        if (recordLock == null) {
            recordLock = new RecordLock();
        }
        return recordLock;
    }

    //Start Assessor Coordinator
    public synchronized boolean isAssignAssessorRecordLock(int n_claimNo, String v_usrid) {
        boolean b = false;
        if (mapLockViewAssignAssessor.containsKey(n_claimNo)) {
            if (mapLockViewAssignAssessor.get(n_claimNo).equalsIgnoreCase(v_usrid)) {
                b = false;
            } else {
                b = true;
            }
        }
        return b;
    }

    public synchronized void setAssignAssessorRecordLock(int n_claimNo, String v_usrid) {
        if (!isAssignAssessorRecordLock(n_claimNo, v_usrid)) {
            mapLockViewAssignAssessor.put(n_claimNo, v_usrid);
        }
    }

    private synchronized boolean isRecordLock(int n_claimNo, String v_usrid) {
        boolean b = false;
        if (mapLockViewAssignAssessor.containsKey(n_claimNo)) {
            b = true;
        }
        return b;
    }

    public synchronized void setAssignAssessorRecordUnLock(String v_usrid) {
        int n_claimNo = 0;
        Map.Entry me = null;
        Iterator i = mapLockViewAssignAssessor.entrySet().iterator();
        while (i.hasNext()) {
            me = (Map.Entry) i.next();
            n_claimNo = (Integer) me.getKey();
            if (this.isRecordLock(n_claimNo, v_usrid)) {
                if (mapLockViewAssignAssessor.get(n_claimNo).equalsIgnoreCase(v_usrid)) {
                    mapLockViewAssignAssessor.remove(n_claimNo);
                }
            }
        }
    }

    public synchronized String getLockedUserId(int n_claimNo) {
        String usrid = mapLockViewAssignAssessor.get(n_claimNo);
        return usrid;
    }

    public synchronized void setAssignAssessorRecordUnLockAll() {
        mapLockViewAssignAssessor.clear();
    }
    //End Assessor Coordinator


    //Start Engineer Coordinator
    public synchronized boolean isEngineerRecordLock(int n_job_ref, String v_usrid) {
        boolean b = false;
        if (mapLockViewMoterEngineer.containsKey(n_job_ref)) {
            if (mapLockViewMoterEngineer.get(n_job_ref).equalsIgnoreCase(v_usrid)) {
                b = false;
            } else {
                b = true;
            }
        }
        return b;
    }

    public synchronized void setEngineerRecordLock(int n_job_ref, String v_usrid) {
        if (!isEngineerRecordLock(n_job_ref, v_usrid)) {
            mapLockViewMoterEngineer.put(n_job_ref, v_usrid);
        }
    }

    private synchronized boolean isRecordLockEngineer(int n_job_ref, String v_usrid) {
        boolean b = false;
        if (mapLockViewMoterEngineer.containsKey(n_job_ref)) {
            b = true;
        }
        return b;
    }

    public synchronized void setEngineerRecordUnLock(String v_usrid) {
        int n_job_ref = 0;
        Map.Entry me = null;
        Iterator i = mapLockViewMoterEngineer.entrySet().iterator();
        while (i.hasNext()) {
            me = (Map.Entry) i.next();
            n_job_ref = (Integer) me.getKey();
            if (this.isRecordLockEngineer(n_job_ref, v_usrid)) {
                if (mapLockViewMoterEngineer.get(n_job_ref).equalsIgnoreCase(v_usrid)) {
                    mapLockViewMoterEngineer.remove(n_job_ref);
                }
            }
        }
    }

    public synchronized String getEngineerLockedUserId(int n_job_ref) {
        String usrid = mapLockViewMoterEngineer.get(n_job_ref);
        return usrid;
    }

    public synchronized void setEngineerRecordUnLockAll() {
        mapLockViewMoterEngineer.clear();
    }
    //End Moter Engineer Coordinator
}
