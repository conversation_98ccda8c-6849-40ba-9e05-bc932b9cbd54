package com.misyn.mcms.claim.dao;


import com.misyn.mcms.claim.dto.ReminderPrintDetailsDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.sql.Connection;
import java.util.List;

public interface ReminderPrintDetailsDao {
    String INSERT_INTO_CLAIM_REMINDER_LETTER_PRINT_DETAILS = "INSERT INTO claim_reminder_letter_print_details VALUES(0,?,?,?)";
    String SELECT_ALL_CLAIM_REMINDER_LETTER_PRINT_DETAILS_BY_N_DOC_TYPE_ID = "SELECT\n" +
            "t1.N_REMIN_DETAILS_REF_ID,\n" +
            "t1.N_REMIN_SUMMARY_REF_ID,\n" +
            "t1.N_DOC_TYPE_ID,\n" +
            "t1.V_CHECK_REMINDER_PRINT,\n" +
            "t2.V_DOC_TYPE_NAME\n" +
            "FROM\n" +
            "claim_reminder_letter_print_details AS t1\n" +
            "INNER JOIN claim_document_type AS t2 ON t1.N_DOC_TYPE_ID = t2.N_DOC_TYPE_ID\n" +
            "WHERE\n" +
            "t1.N_REMIN_SUMMARY_REF_ID=?";

    String SELECT_CLAIM_REMINDER_LETTER_PRINT_DETAILS_BY_N_REMIN_SUMMARY_REF_ID_N_DOC_TYPE_ID = "SELECT\n" +
            "t1.N_REMIN_DETAILS_REF_ID,\n" +
            "t1.N_REMIN_SUMMARY_REF_ID,\n" +
            "t1.N_DOC_TYPE_ID,\n" +
            "t1.V_CHECK_REMINDER_PRINT,\n" +
            "t2.V_REMIN_DOC_DISPLAY_NAME,\n" +
            "t2.V_DOC_TYPE_NAME\n" +
            "FROM\n" +
            "claim_reminder_letter_print_details AS t1\n" +
            "INNER JOIN claim_document_type AS t2 ON t1.N_DOC_TYPE_ID = t2.N_DOC_TYPE_ID\n" +
            "WHERE\n" +
            "t1.N_REMIN_SUMMARY_REF_ID=? AND t1.N_DOC_TYPE_ID=?";

    ReminderPrintDetailsDto insertMaster(Connection connection, ReminderPrintDetailsDto reminderPrintDetailsDto) throws MisynJDBCException;

    ReminderPrintDetailsDto searchByRemindSummaryIdAndDocumentTypeId(Connection connection, Integer reminderSummaryRefId, Integer documentTypeId);

    List<ReminderPrintDetailsDto> getReminderPrintDetailsList(Connection connection, Integer reminderSummaryRefId);
}
