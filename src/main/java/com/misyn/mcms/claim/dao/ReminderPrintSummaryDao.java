package com.misyn.mcms.claim.dao;


import com.misyn.mcms.claim.dto.ReminderPrintSummaryDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;

import java.sql.Connection;
import java.util.List;

public interface ReminderPrintSummaryDao {
    String INSERT_INTO_CLAIM_REMINDER_LETTER_PRINT_SUMMARY = "INSERT INTO claim_reminder_letter_print_summary VALUES(0,?,?,?,?,?,?)";
    String SELECT_CLAIM_REMINDER_LETTER_PRINT_SUMMARY_BY_N_REMIN_SUMMARY_REF_ID = "SELECT * FROM claim_reminder_letter_print_summary WHERE  N_REMIN_SUMMARY_REF_ID=?";
    String SELECT_MAX_CLAIM_REMINDER_LETTER_PRINT_SUMMARY = "SELECT IFNULL(MAX(N_REMIN_SUMMARY_REF_ID),0) AS maxId  FROM claim_reminder_letter_print_summary WHERE N_CLAIM_NO=?";
    String SELECT_MAX_CLAIM_REMINDER_ID_PRINT_SUMMARY = "SELECT COUNT(N_REMIN_SUMMARY_REF_ID) AS maxIds  FROM claim_reminder_letter_print_summary WHERE N_CLAIM_NO =?";
    String SELECT_ALL_CLAIM_REMINDER_LETTER_PRINT_SUMMARY_BY_CLAIM_NO = "SELECT * FROM claim_reminder_letter_print_summary WHERE N_CLAIM_NO=? ORDER BY D_GENERATE_DATE_TIME DESC";

    ReminderPrintSummaryDto insertMaster(Connection connection, ReminderPrintSummaryDto reminderPrintSummaryDto) throws MisynJDBCException;

    ReminderPrintSummaryDto getReminderPrintSummaryDto(Connection connection, Integer reminderSummaryRefId);

    Integer getMaxReminderSummaryRefIdByClaimNo(Connection connection, Integer claimNo);

    Integer getMaxReminderIdByClaimNo(Connection connection, Integer claimNo);

    List<ReminderPrintSummaryDto> searchAllByClaimNo(Connection connection, Integer claimNo);
}
