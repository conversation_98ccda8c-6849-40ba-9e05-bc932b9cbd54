package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.*;

import java.sql.Connection;
import java.util.List;

/**
 * Created by a<PERSON><PERSON> on 5/21/18.
 */
public interface RequestAriDao extends BaseDao<RequestAriDto> {

    String SQL_INSERT_REQUEST_API_DETAILS = "INSERT INTO request_ari_details VALUES(0,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

    String SQL_SELECT_REQUEST_API_DETAILS_BY_CLAIM_NO = "SELECT * FROM request_ari_details where claim_no=?";

    String SQL_SELECT_REQUEST_API_DETAILS_BY_CLAIM_NO_PENDING = "SELECT * FROM request_ari_details where claim_no=? AND status IN ('P','U')";

    String SQL_SELECT_REQUEST_API_DETAILS_BY_CLAIM_NO_COMPLETED = "SELECT * FROM request_ari_details where claim_no=? AND status IN ('C')";

    String SQL_UPDATE_JOB_COMPLETED = "UPDATE request_ari_details set status=?,assigned_assesr_code=?,assigned_assessor_name=?,ref_id=?,assiging_datetime=? where request_ari_id=?";

    String SQL_UPDATE_DETAILS = "UPDATE request_ari_details set status=?,customer_name=?,contact_no=?,address_1=?,address_2=?,address_2=? where request_ari_id=?";

    String SQL_UPDATE_DETAILS_FROM_BRANCH = "UPDATE request_ari_details set status=?,customer_name=?,contact_no=?,address_1=?,address_2=?,address_3=?,document_upload_user=? where request_ari_id=?";

    String SQL_SELECT_REQUEST_API_DETAILS_BY_CLAIM_NO_ONLY_PENDING = "SELECT * FROM request_ari_details where claim_no=? AND status NOT IN ('D', 'R') ORDER BY request_ari_id DESC LIMIT 1";

    String SQL_SELECT_REQUEST_DATA_GRID = "SELECT\n" +
            "t1.claim_no,\n" +
            "t2.vehicle_no,\n" +
            "t2.customer_name,\n" +
            "t2.ref_id,\n" +
            "t2.contact_no,\n" +
            "t2.accident_date,\n" +
            "t2.requested_user,\n" +
            "t2.requested_date,\n" +
            "t2.assigned_assesr_code,\n" +
            "t2.request_ari_id,\n" +
            "t2.status,\n" +
            "t2.remark,\n" +
            "t2.assigned_assessor_name,\n" +
            "t2.assessor_submitted_datetime,\n" +
            "t2.assiging_datetime,\n" +
            "t4.V_PRIORITY,\n" +
            "CASE\n" +
            "WHEN t2.assiging_datetime = '1980-01-01 00:00:00'\n" +
            "THEN 'N/A'\n" +
            "ELSE\n" +
            "DATEDIFF(CURDATE(),t2.assiging_datetime)\n" +
            "END AS assigned_days\n" +
            "FROM\n" +
            "request_ari_details AS t2\n" +
            "INNER JOIN claim_assign_assesor AS t1 ON t2.ref_id = t1.ref_no\n" +
            "INNER JOIN claim_claim_info_main as t4 ON t1.claim_no = t4.N_CLIM_NO";
    String SQL_COUNT_REQUEST_DATA = "SELECT\n" +
            "count(*)  as cnt\n" +
            "FROM\n" +
            "request_ari_details AS t2\n" +
            "INNER JOIN claim_assign_assesor AS t1 ON t2.ref_id = t1.ref_no \n";

    String SQL_COUNT_REQUEST_DATA_BY_SPCOOD = "SELECT\n" +
            "count(*)  as cnt\n" +
            "FROM\n" +
            "request_ari_details AS t2\n" +
            "INNER JOIN claim_assign_assesor AS t1 ON t2.ref_id = t1.ref_no \n" +
            "LEFT JOIN claim_assign_claim_handler AS t3 ON t3.N_CLAIM_NO = t1.claim_no \n" +
            "LEFT JOIN claim_calculation_sheet_main as t4 ON t4.N_CLAIM_NO = t1.claim_no";

    String SQL_COUNT_REQUEST_DATA_BY_SCRUTINIZING = "SELECT\n" +
            "count(*)  as cnt\n" +
            "FROM\n" +
            "request_ari_details AS t2\n" +
            "INNER JOIN claim_assign_assesor AS t1 ON t2.ref_id = t1.ref_no \n" +
            "LEFT JOIN claim_assign_claim_handler AS t3 ON t3.N_CLAIM_NO = t1.claim_no \n" +
            "LEFT JOIN claim_calculation_sheet_main as t4 ON t4.N_CLAIM_NO = t1.claim_no \n" +
            "LEFT JOIN claim_supply_order_summary AS t5 ON t1.claim_no = t5.n_claim_no";

    String SQL_UPDATE_JOB_REVOKE = "UPDATE request_ari_details set status=?,remark=?,requested_user=?,requested_date=?,revoke_user_id=?,revoke_datetime=? WHERE request_ari_id=?";

    String SQL_UPDATE_JOB_STATUS_BY_REF_NO = "UPDATE request_ari_details set status=? WHERE ref_id=?";//No Usages

    String SQL_UPDATE_JOB_STATUS_ANDREF_ID_BY_REF_NO = "UPDATE request_ari_details set status=?, ref_id = 0 WHERE ref_id=?";//No Usages

    String SQL_UPDATE_JOB_STATUS_AND_DATE_BY_REF_NO = "UPDATE request_ari_details set status=?,assessor_submitted_datetime=? WHERE ref_id=?";

    String SQL_SELECT_REQUEST_DATA_GRID_PENDING = "SELECT\n" +
            "t2.claim_no,\n" +
            "t2.vehicle_no,\n" +
            "t2.customer_name,\n" +
            "t2.ref_id,\n" +
            "t2.contact_no,\n" +
            "t2.accident_date,\n" +
            "t2.requested_user,\n" +
            "t2.requested_date,\n" +
            "t2.assigned_assesr_code,\n" +
            "t2.request_ari_id,\n" +
            "t2.status,\n" +
            "t2.remark,\n" +
            "t2.assigned_assessor_name,\n" +
            "t2.document_upload_user,\n" +
            "t4.V_PRIORITY,\n" +
            "t4.D_ACCID_DATE,\n" +
            "DATEDIFF(CURDATE(), t2.requested_date) as days_from_request\n" +
            "FROM\n" +
            "request_ari_details AS t2\n" +
            "INNER JOIN usr_mst AS t3 ON t2.requested_user = t3.v_usrid\n" +
            "INNER JOIN claim_claim_info_main as t4 ON t2.claim_no = t4.N_CLIM_NO";

    String SQL_COUNT_REQUEST_DATA_PENDING = "SELECT\n" +
            "count(*)  as cnt\n" +
            "FROM\n" +
            "request_ari_details AS t2\n" +
            "INNER JOIN usr_mst AS t3 ON t2.requested_user = t3.v_usrid";

    String SQL_SELECT_USER_LIST_BY_ACCESSS_USER_TYPE = "SELECT * FROM usr_mst WHERE n_accessusrtype=?";

    String SQL_INSERT_INTO_REQUEST_ARI_REMARK = "INSERT INTO requset_ari_remark VALUES(0,?,?,?,?,?) ";

    String SQL_SELECT_ALL_REQUEST_ARI_LIST = "SELECT * FROM requset_ari_remark WHERE request_ari_id=?";

    String SQL_SELECT_SEARCH_BY_REQUEST_ID = "SELECT * FROM request_ari_details WHERE request_ari_id=?";

    String SQL_DELETE_REQUEST_ARI = "DELETE FROM request_ari_details WHERE claim_no = ? AND status =?";

    String SQL_IS_ARI_REQUESTED_BY_CLAIM_NO = "SELECT 1 FROM request_ari_details WHERE claim_no=? AND `status` IN ('P', 'U')";

    String SQL_UPDATE_STATUS_BY_CLAIM_NO_AND_STATUS = "UPDATE request_ari_details \n" +
            "SET STATUS =? \n" +
            "WHERE\n" +
            "	claim_no = ? \n" +
            "	AND STATUS IN ( ?,? )";

    String SQL_SELECT_REQUEST_DATA_GRID_BY_SPCOOD = "SELECT\n" +
            "t1.claim_no,\n" +
            "t2.request_ari_id,\n" +
            "t2.vehicle_no,\n" +
            "t2.customer_name,\n" +
            "t2.ref_id,\n" +
            "t2.contact_no,\n" +
            "t2.accident_date,\n" +
            "t2.requested_user,\n" +
            "t2.requested_date,\n" +
            "t2.assigned_assesr_code,\n" +
            "t2.request_ari_id,\n" +
            "t2.status,\n" +
            "t2.remark,\n" +
            "t2.assigned_assessor_name,\n" +
            "t2.assessor_submitted_datetime,\n" +
            "t2.assiging_datetime,\n" +
            "CASE\n" +
            "WHEN t2.assiging_datetime = '1980-01-01 00:00:00'\n" +
            "THEN 'N/A'\n" +
            "ELSE\n" +
            "DATEDIFF(CURDATE(),t2.assiging_datetime)\n" +
            "END AS assigned_days\n" +
            "FROM\n" +
            "request_ari_details AS t2\n" +
            "INNER JOIN claim_assign_assesor AS t1 ON t2.ref_id = t1.ref_no \n" +
            "LEFT JOIN claim_assign_claim_handler AS t3 ON t3.N_CLAIM_NO = t1.claim_no \n" +
            "LEFT JOIN claim_calculation_sheet_main as t4 ON t4.N_CLAIM_NO = t1.claim_no";

    String SQL_SELECT_REQUEST_DATA_GRID_BY_SCRUTINIZING = "SELECT\n" +
            "t1.claim_no,\n" +
            "t2.request_ari_id,\n" +
            "t2.vehicle_no,\n" +
            "t2.customer_name,\n" +
            "t2.ref_id,\n" +
            "t2.contact_no,\n" +
            "t2.accident_date,\n" +
            "t2.requested_user,\n" +
            "t2.requested_date,\n" +
            "t2.assigned_assesr_code,\n" +
            "t2.request_ari_id,\n" +
            "t2.status,\n" +
            "t2.remark,\n" +
            "t2.assigned_assessor_name,\n" +
            "t2.assessor_submitted_datetime,\n" +
            "t2.assiging_datetime,\n" +
            "CASE\n" +
            "WHEN t2.assiging_datetime = '1980-01-01 00:00:00'\n" +
            "THEN 'N/A'\n" +
            "ELSE\n" +
            "DATEDIFF(CURDATE(),t2.assiging_datetime)\n" +
            "END AS assigned_days\n" +
            "FROM\n" +
            "request_ari_details AS t2\n" +
            "INNER JOIN claim_assign_assesor AS t1 ON t2.ref_id = t1.ref_no \n" +
            "LEFT JOIN claim_assign_claim_handler AS t3 ON t3.N_CLAIM_NO = t1.claim_no \n" +
            "LEFT JOIN claim_calculation_sheet_main as t4 ON t4.N_CLAIM_NO = t1.claim_no \n" +
            "LEFT JOIN claim_supply_order_summary AS t5 ON t1.claim_no = t5.n_claim_no";

    String GET_LATEST_ARI_REQUEST_DETAILS = "SELECT * from request_ari_details WHERE claim_no = ? AND status IN ('P','U') AND request_ari_reason <> 0 ORDER BY request_ari_id DESC LIMIT 1";

    String UPDATE_PARTIALLY_REVIEW = "UPDATE request_ari_details SET status = ? WHERE ref_id = ?";

    String SQL_IS_SALVAGE_OR_ARI_REQUESTED_BY_CLAIM_NO = "SELECT request_ari_reason FROM request_ari_details WHERE claim_no= ? AND `status` IN ('P', 'U') AND ref_id = 0 ORDER BY request_ari_id DESC LIMIT 1";

    String SQL_IS_SALVAGE_OR_ARI_ARRANGED_BY_CLAIM_NO = "SELECT t1.insepction_id\n" +
            "FROM\n" +
            "request_ari_details AS t2\n" +
            "INNER JOIN claim_assign_assesor AS t1 ON t2.ref_id = t1.ref_no \n" +
            "WHERE ((t2.`status`IN('S','C','PR') AND t1.record_status IN (8,29,10,80)) OR\n" +
            "(t2.`status` = 'FR' AND t1.record_status = 10)) AND t1.claim_no = ? ORDER BY t1.ref_no DESC LIMIT 1";

    String SQL_IS_ARI_REQUESTED_FOR_CLAIM_NO_BY_SPC_OR_SCR = "SELECT 1 FROM request_ari_details WHERE claim_no=? AND `status` IN ('P', 'U') AND request_ari_reason <> 2";

    String GET_ARI_REQUEST_USER = "SELECT inp_userid FROM request_ari_details WHERE claim_no = ?";

    public List<RequestAriDto> searchAll(Connection connection, Integer claimNo) throws Exception;

    public RequestAriDto searchByClaimNo(Connection connection, Integer claimNo) throws Exception;

    public RequestAriDto searchByCompletedClaimNo(Connection connection, Integer claimNo) throws Exception;

    public void updateStatusByClaimNo(Connection connection, RequestAriDto requestAriDto) throws Exception;

    public DataGridDto getRequestDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

    public boolean updateStatusAndRevokeByRef(Connection connection, RequestAriDto requestAriDto) throws Exception;

    public boolean updateStatusByRefId(Connection connection, RequestAriDto requestAriDto) throws Exception;

    public DataGridDto getRequestDataGridDtoPending(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

    public RequestAriDto searchByClaimNoPending(Connection connection, Integer claimNo) throws Exception;

    public List<UserDto> getUserListByAccessUserType(Connection connection, Integer accessUserType) throws Exception;

    public RequestAriRemarkDto saveRequestRemark(Connection connection, RequestAriRemarkDto requestAriRemarkDto) throws Exception;

    public List<RequestAriRemarkDto> getRemarkListByRemarkId(Connection connection, Integer remarkId) throws Exception;

    public boolean updateStatusAndDateByRefId(Connection connection, RequestAriDto requestAriDto) throws Exception;

    public RequestAriDto searchRequestAriDto(Connection connection, Integer refId) throws Exception;

    public boolean checkAndDeleteRequestARI(Connection connection, Integer claimNo) throws Exception;

    public boolean searchByClaimNoisAriRequested(Connection connection, Integer claimNo) throws Exception;

    boolean updateStatusByClaimNoAndStatus(Connection connection, String updateStatus, int claimNo, String searchStatus1, String searchStatus2) throws Exception;

    DataGridDto getRequestDataGridDtoByUser(Connection connection, List<FieldParameterDto> parameterList, int i, int start, int length, String columnOrder, String orderColumnName, boolean isSpcood, String userId);

    RequestAriDto updateRequestAri(Connection connection, RequestAriDto requestAriDto);

    RequestAriDto getLatestAriRequest(Connection connection, Integer claimNo) throws Exception;

    void updatePartiallyReview(Connection connection, boolean isPartiallyReview, int refNo) throws Exception;

    String getInputUserId(Connection connection, int claimNo) throws Exception;

    int searchByClaimNoisSalvageORARIRequestedBySpcOrScr(Connection connection, Integer claimNoToSearch)throws Exception;

    int searchByClaimNoisSalvageORARIArrangedBySpcOrScr(Connection connection, Integer claimNo)throws Exception;

    public boolean updateStatusAndRefIdByRefId(Connection connection, RequestAriDto requestAriDto) throws Exception;
}
