package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.*;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

public interface ReserveAdjustmentDao extends BaseDao<ClaimReserveAdjustmentTypeDto> {

    String IS_ALREADY_SAVED =
            "SELECT 1 FROM claim_reserve_adjustment_type WHERE claim_reserve_adjustment_id = ?";

    String GET_ALL_PERIOD_TYPE = "SELECT period_id, months_count, label, record_status FROM reserve_period WHERE record_status = 'A' ORDER BY period_id ASC";

    String IS_RESERVE_ADJUSTMENT_ACTIVE =
            "SELECT 1 FROM claim_reserve_adjustment_type WHERE period_id = ? AND category_id = ? AND record_status = 'A' LIMIT 1";

    String RESERVE_CATEGORY_TYPE =
            "SELECT category_id, description FROM reserve_category WHERE record_status = 'A'";

    String RESERVE_PERIOD_TYPE =
            "SELECT period_id, months_count, label FROM reserve_period WHERE record_status = 'A'";

    String RESERVE_ADJUSTMENT_INSERT =
            "INSERT INTO claim_reserve_adjustment_type (" +
                    "period_id, category_id, amount, amount_min, amount_max, " +
                    "record_status, input_date_time, input_user, last_modified_date_time, last_modified_user" +
                    ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    String RESERVE_ADJUSTMENT_UPDATE =
            "UPDATE claim_reserve_adjustment_type SET " +
                    "period_id = ?, " +
                    "category_id = ?, " +
                    "amount = ?, " +
                    "amount_currency = ?, " +
                    "moving_acr_above = ?, " +
                    "moving_advance_or_acr_above = ?, " +
                    "moving_acr_less_than = ?, " +
                    "non_moving = ?, " +
                    "record_status = ?, " +
                    "last_modified_date_time = ?, " +
                    "last_modified_user = ? " +
                    "WHERE claim_reserve_adjustment_id = ?";

    String SQL_SELECT_BASE =
            "SELECT " +
                    "t1.claim_reserve_adjustment_id, " +
                    "t1.period_id, " +
                    "t1.category_id, " +
                    "t1.amount_currency, " +
                    "t1.moving_acr_above, " +
                    "t1.moving_advance_or_acr_above, " +
                    "t1.moving_acr_less_than, " +
                    "t1.non_moving, " +
                    "t1.amount, " +
                    "t1.record_status, " +
                    "t1.input_date_time, " +
                    "t1.input_user, " +
                    "t1.last_modified_date_time, " +
                    "t1.last_modified_user, " +
                    "p.label AS period_label, " +
                    "c.amount_min AS amount_min, " +
                    "c.amount_max AS amount_max, " +
                    "c.description AS category_label " +
                    "FROM claim_reserve_adjustment_type t1 " +
                    "JOIN reserve_period p ON t1.period_id = p.period_id " +
                    "JOIN reserve_category c ON t1.category_id = c.category_id ";

    String SQL_COUNT_BASE =
            "SELECT COUNT(*) as cnt FROM claim_reserve_adjustment_type t1 ";

    boolean isAlreadySaved(Connection conn, Integer claimReserveAdjustmentId) throws SQLException;

    void insert(Connection conn, ClaimReserveAdjustmentTypeDto dto) throws SQLException;

    void update(Connection conn, ClaimReserveAdjustmentTypeDto dto) throws SQLException;

    DataGridDto getReserveAdjustmentDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) throws Exception;

    boolean isActiveRecordExists(Connection conn, int periodId, int categoryId);

    List<ReserveCategoryDto> getReserveCategories(Connection conn);

    List<ReservePeriodDto> getReservePeriods(Connection conn);
}
