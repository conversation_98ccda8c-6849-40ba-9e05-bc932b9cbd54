package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.RtePendingClaimsDto;

import java.sql.Connection;
import java.util.List;

public interface RtePendingClaimDetailDao {

    String SAVE_PENDING_JOBS_FOR_RTE = "INSERT INTO rte_pending_claim_detail VALUES(0,?,?,?)";

    String REMOVE_PENDING_JOBS_FOR_RTE = "DELETE FROM rte_pending_claim_detail WHERE claim_no = ?";

    String GET_ALL_PENDING_CLAIMS = "SELECT * from rte_pending_claim_detail WHERE access_user_type = ? GROUP BY claim_no";

    String CHECK_RTE_PENDING_JOBS = "SELECT * from rte_pending_claim_detail WHERE claim_no = ?";

    String CHECK_IF_RTE_JOBS_PENDING = "SELECT 1 from rte_pending_claim_detail WHERE claim_no = ?";

    String UPDATE_RTE_PENDING_CLAIM = "UPDATE rte_pending_claim_detail SET user_id = ?, access_user_type = ? WHERE claim_no = ?";

    void savePendingJobs(Connection connection, Integer claimNo, Integer accessUserType, String userId);

    void removePendingJobs(Connection connection, Integer claimNo);

    List<RtePendingClaimsDto> getAllPendingClaimsFromSparePartsCoord(Connection connection);

    List<RtePendingClaimsDto> getAllPendingClaimsFromScrutinizingTeam(Connection connection);

    RtePendingClaimsDto checkRtePendingJobs(Connection connection, Integer claimNo);

    boolean checkIfRteJobsPending(Connection connection, Integer claimNo);

    void updateAssignUser(Connection connection, Integer claimNo, String assignUser, Integer accessUserType) throws Exception;
}
