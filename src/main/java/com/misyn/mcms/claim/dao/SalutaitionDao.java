package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.SalutationDto;

import java.sql.Connection;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018-04-24.
 */
public interface SalutaitionDao extends BaseDao<SalutationDto> {
     String SEARCH_BY_SALUTATATION_ID = "SELECT salutation_id,salutation_name FROM claim_salutation WHERE salutation_id=?;";

     List<SalutationDto> searchAll(Connection jdbcConnection);
}
