package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.SmsDto;
import com.misyn.mcms.claim.dto.SmsParameterDto;

import java.sql.Connection;

/**
 * Created by a<PERSON>la on 4/27/18.
 */
public interface SmsDao extends BaseDao<SmsDto> {
    String SQL_INSERT_TO_SMS_TABLE = "INSERT INTO claim_sms_message VALUES(0,?,?,?,?,?,?,?,?,?,?)";

    String SQL_SELECT_SMS_PARAMETER = "SELECT * FROM sms_parameters WHERE sms_parameter_id =?";

    SmsParameterDto getSmsParameter(Connection connection, int smsParameterId) throws Exception;
}
