package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.PopupItemDto;
import com.misyn.mcms.claim.dto.SparePartDatabaseDto;

import java.sql.Connection;
import java.util.List;

public interface SparePartDatabaseDao extends BaseDao<SparePartDatabaseDto> {
    String CLAIM_SPARE_PART_DATABASE_INSERT = "INSERT INTO claim_spare_part_database VALUES (0,?,?,?,?,?,?,?,?,?)";

    String CLAIM_SPARE_PART_DATABASE_SEARCH = "SELECT\n" +
            "t1.txn_id,\n" +
            "t1.vehicle_make,\n" +
            "t1.vehicle_model,\n" +
            "t1.manufacture_year,\n" +
            "t1.spare_part_ref_no,\n" +
            "t1.supplier_id,\n" +
            "t1.price,\n" +
            "t1.proceed_date,\n" +
            "t1.input_date_time,\n" +
            "t1.input_user_id,\n" +
            "t2.v_spare_part_name,\n" +
            "t3.V_SUPPLER_NAME,\n" +
            "t3.V_SUPPLIER_ADDRESS_LINE1,\n" +
            "t3.V_SUPPLIER_ADDRESS_LINE2,\n" +
            "t3.V_SUPPLIER_ADDRESS_LINE3,\n" +
            "t3.V_CONTACT_NO,\n" +
            "t3.V_EMAIL,\n" +
            "t3.V_CONTACT_PERSON\n" +
            "FROM\n" +
            "spare_part_item_mst AS t2\n" +
            "INNER JOIN claim_spare_part_database AS t1 ON t1.spare_part_ref_no = t2.n_spare_part_ref_no\n" +
            "INNER JOIN supplier_details_mst AS t3 ON t1.supplier_id = t3.N_SUPPLIER_ID\n" +
            "WHERE t1.txn_id =? ";

    String GET_VEHICLE_MAKE = "SELECT DISTINCT(V_VEHICLE_MAKE) FROM claim_vehicle_info_main ORDER BY V_VEHICLE_MAKE";
    String GET_VEHICLE_MODEL = "SELECT DISTINCT(V_VEHICLE_MODEL) FROM claim_vehicle_info_main WHERE V_VEHICLE_MAKE=? ORDER BY V_VEHICLE_MODEL";

    DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate);

    List<PopupItemDto> getVehickeMake(Connection connection);

    List<PopupItemDto> getVehickeModel(Connection connection, String vehicleMake);


}
