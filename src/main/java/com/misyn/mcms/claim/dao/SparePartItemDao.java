package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.SparePartItemDto;

import java.sql.Connection;
import java.util.List;

public interface SparePartItemDao extends BaseDao<SparePartItemDto> {
    String SPARE_PART_ITEMS_INSERT = "INSERT INTO spare_part_item_mst VALUES (0,?,?,?,?)";

    String SPARE_PART_ITEMS_UPDATE = "UPDATE spare_part_item_mst SET \n" +
            "v_spare_part_name = ?,\n" +
            "v_record_status = ?,\n" +
            "v_input_user_id = ?,\n" +
            "v_input_date_time = ? WHERE n_spare_part_ref_no = ?";
    String SPARE_PART_ITEMS_SEARCH = "SELECT * FROM spare_part_item_mst WHERE n_spare_part_ref_no = ?";

    String VALIDATE_SPARE_PART_NAME = "SELECT v_spare_part_name FROM spare_part_item_mst WHERE v_spare_part_name like ?";

    DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

    String validateSparePartNmae(Connection connection, String sparePartName) throws Exception;
}
