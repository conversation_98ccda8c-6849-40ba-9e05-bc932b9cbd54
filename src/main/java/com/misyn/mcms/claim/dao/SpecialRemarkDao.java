package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.SpecialRemarkDto;

import java.sql.Connection;
import java.util.List;

public interface SpecialRemarkDao {
    String INSERT_CLAIM_SPECIAL_REMARK = "INSERT INTO claim_special_remark VALUES(0,?,?,?,?,?,?)";

    String SEAERCH_CLAIM_SPECIAL_REMARKS = "SELECT * FROM claim_special_remark WHERE claim_no= ? and department_id= ? ORDER BY input_date_time DESC";
    String SEAERCH_CLAIM_SPECIAL_REMARKS_BY_CLAIM_NO = "SELECT * FROM claim_special_remark WHERE claim_no= ? ORDER BY input_date_time DESC";

    String SEARCH_CLAIM_SPECIAL_REMARK_BY_DIVISION = "SELECT\n" +
            "\tt2.department_name,\n" +
            "\tt1.txn_id,\n" +
            "\tt1.claim_no,\n" +
            "\tt1.department_id,\n" +
            "\tt1.section_name,\n" +
            "\tt1.remark,\n" +
            "\tt1.input_user_id,\n" +
            "\tt1.input_date_time\n" +
            "FROM\n" +
            "\tclaim_department AS t2\n" +
            "INNER JOIN claim_special_remark AS t1 ON t1.department_id = t2.department_id\n" +
            "WHERE\n" +
            "\tt1.claim_no = ?\n" +
            "ORDER BY\n" +
            "\tt1.input_date_time DESC";

    SpecialRemarkDto insertMaster(Connection connection, SpecialRemarkDto specialRemarkDto) throws Exception;

    List<SpecialRemarkDto> searchRemarksByClaimNo(Connection connection, Integer claimNo, Integer departmentNo) throws Exception;

    List<SpecialRemarkDto> searchRemarksByClaimNoMultipleDepartmentId(Connection connection, Integer claimNo, String departmentNo) throws Exception;

    List<SpecialRemarkDto> searchRemarksByClaimNo(Connection connection, Integer claimNo) throws Exception;

    List<SpecialRemarkDto> searchDepartmentRemarksByClaimNo(Connection connection, Integer claimNo) throws Exception;

}
