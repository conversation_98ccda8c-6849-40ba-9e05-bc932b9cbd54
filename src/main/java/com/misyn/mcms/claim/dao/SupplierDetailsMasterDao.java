package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.SupplierDetailsMasterDto;

import java.sql.Connection;
import java.util.List;

public interface SupplierDetailsMasterDao extends BaseDao<SupplierDetailsMasterDto> {
    String SUPPLIER_DETAILS_MASTER_INSERT = "INSERT INTO supplier_details_mst VALUES(0,?,?,?,?,?,?,?,?,?)";
    String SUPPLIER_DETAILS_MASTER_UPDATE = "UPDATE supplier_details_mst SET \n" +
            "V_SUPPLER_NAME =?,\n" +
            "V_SUPPLIER_ADDRESS_LINE1 =?,\n" +
            "V_SUPPLIER_ADDRESS_LINE2 =?,\n" +
            "V_SUPPLIER_ADDRESS_LINE3 =?,\n" +
            "V_CONTACT_NO =?,\n" +
            "V_CONTACT_PERSON =?,\n" +
            "V_EMAIL =?,\n" +
            "V_RECORD_STATUS =?,\n" +
            "V_INPUT_USER_ID =?  WHERE N_SUPPLIER_ID =?\n";
    String SUPPLIER_DETAILS_MASTER_SEARCH = "SELECT * FROM supplier_details_mst WHERE N_SUPPLIER_ID =? ORDER BY V_SUPPLER_NAME";
    String SUPPLIER_DETAILS_MASTER_SEARCH_ALL = "SELECT * FROM supplier_details_mst ORDER BY V_SUPPLER_NAME ";

    DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

}
