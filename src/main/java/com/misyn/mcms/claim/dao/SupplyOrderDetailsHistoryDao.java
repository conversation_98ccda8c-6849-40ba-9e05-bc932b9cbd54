package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.SupplyOrderDetailsDto;

import java.sql.Connection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface SupplyOrderDetailsHistoryDao {

    String SEARCH_CLAIM_SUPPLY_ORDER_DETAILS_HISTORY = "SELECT\n" +
            "t1.n_ref_no,\n" +
            "t1.n_supply_order_ref_no,\n" +
            "t1.n_spare_part_ref_no,\n" +
            "t1.n_qunatity,\n" +
            "t1.n_individual_price,\n" +
            "t1.n_total_amount,\n" +
            "t1.n_oa_rate,\n" +
            "t2.v_spare_part_name\n" +
            "FROM\n" +
            "claim_supply_order_details_history AS t1\n" +
            "INNER JOIN spare_part_item_mst AS t2 ON t1.n_spare_part_ref_no = t2.n_spare_part_ref_no\n" +
            "WHERE\n" +
            "t1.n_supply_order_detail_ref_no = ? ORDER BY t1.n_index_no, t1.n_ref_no ASC";

    List<SupplyOrderDetailsDto> searchAllByRefNo(Connection connection, Integer supplyOrderRefNo) throws Exception;
}
