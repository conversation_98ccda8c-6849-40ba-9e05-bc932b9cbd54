package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.SupplyOrderSummaryDto;

import java.sql.Connection; /**
 * <AUTHOR>
 */
public interface SupplyOrderHistoryDao {

    String SEARCH_CLAIM_SUPPLY_ORDER_HISTORY_BY_SUPPLY_ORDER_REF_NO = "SELECT\n" +
            "t1.*,\n" +
            "t.V_COMPANY_NAME\n" +
            "FROM\n" +
            "claim_supply_order_history AS t1\n" +
            "INNER JOIN claim_company_details_main AS t ON t1.n_supplier_id = t.N_REF_NO\n" +
            "WHERE\n" +
            "t1.n_ref_no = ?";

    String SHIFT_MASTER_TO_HISTORY = "INSERT INTO claim_supply_order_history(\n" +
            "SELECT\n" +
            "0,\n" +
            "n_supply_order_ref_no,\n" +
            "n_claim_no,\n" +
            "n_supplier_id,\n" +
            "v_supply_order_serial_no,\n" +
            "v_supply_order_status,\n" +
            "v_supplier_email,\n" +
            "v_supplier_contact_no,\n" +
            "n_total_amount,\n" +
            "n_total_owners_account_amount,\n" +
            "n_other_deduction,\n" +
            "n_policy_excess,\n" +
            "n_final_amount,\n" +
            "v_supply_order_remark,\n" +
            "v_other_remark,\n" +
            "v_work_shop_name,\n" +
            "v_work_shop_address1,\n" +
            "v_work_shop_address2,\n" +
            "v_work_shop_address3,\n" +
            "v_work_shop_contact_no,\n" +
            "v_apprv_assign_spare_part_coordinator,\n" +
            "d_apprv_assign_spare_part_coordinator_date_time,\n" +
            "v_input_user_id,\n" +
            "d_input_date_time,\n" +
            "v_apprv_assign_scrutinizing_user_id,\n" +
            "d_apprv_assign_scrutinizing_date_time,\n" +
            "v_apprv_scrutinizing_user_id,\n" +
            "v_apprv_scrutinizing_date_time,\n" +
            "v_apprv_assign_special_team_user_id,\n" +
            "d_apprv_assign_special_team_date_time,\n" +
            "v_apprv_special_team_user_id,\n" +
            "v_apprv_special_team_date_time,\n" +
            "v_apprv_claim_handler_user_id,\n" +
            "d_apprv_claim_handler_date_time,\n" +
            "v_generate_user_id,\n" +
            "d_generate_date_time,\n" +
            "v_is_generate,\n" +
            "n_vat_amount,\n" +
            "v_vat_status,\n" +
            "(select n_supply_order_detail_ref_no from supply_order_detail_history_sequence)\n" +
            "FROM\n" +
            "claim_supply_order_summary\n" +
            "WHERE\n" +
            "n_supply_order_ref_no = ?\n" +
            ")";

    String SHIFT_DO_DETAILS_TO_HISTORY = "insert into claim_supply_order_details_history(\n" +
            "select \n" +
            "0,\n" +
            "(select n_supply_order_detail_ref_no from supply_order_detail_history_sequence),\n" +
            "n_supply_order_ref_no,\n" +
            "n_spare_part_ref_no,\n" +
            "n_qunatity,\n" +
            "n_individual_price,\n" +
            "n_oa_rate,\n" +
            "n_total_amount,\n" +
            "n_index_no\n" +
            "from claim_supply_order_details where n_supply_order_ref_no=?)";

    String UPDATE_MAX_DETAIL_ID = "update supply_order_detail_history_sequence set n_supply_order_detail_ref_no = n_supply_order_detail_ref_no+1";

    String SEARCH_TAKAFUL_CLAIM_SUPPLY_ORDER_HISTORY_BY_SUPPLY_ORDER_REF_NO = "SELECT\n" +
            "t1.*,\n" +
            "t.V_COMPANY_NAME\n" +
            "FROM\n" +
            "claim_supply_order_history AS t1\n" +
            "INNER JOIN takaful_claim_company_details_main AS t ON t1.n_supplier_id = t.N_REF_NO\n" +
            "WHERE\n" +
            "t1.n_ref_no = ?";

    SupplyOrderSummaryDto searchByRefNo(Connection connection, Integer supplyOrderRefNo, String policyChannelType) throws Exception;

    void shiftDoToHistory(Connection connection, Integer refNo) throws Exception;

    void shiftDoDetailsToHistory(Connection connection, Integer supplyOrderRefNo) throws Exception;
}
