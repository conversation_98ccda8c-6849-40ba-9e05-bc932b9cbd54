package com.misyn.mcms.claim.dao;

import java.sql.Connection; /**
 * <AUTHOR>
 */
public interface SupplyOrderRequestDao {
    String INSERT_DO_REQUEST = "INSERT INTO claim_supply_order_request VALUES(0,?,?)";

    String IS_AVAILABLE_PENDING_DO = "SELECT N_REF_NO FROM claim_supply_order_request WHERE N_CLAIM_NO = ?";

    String UPDATE_DO_REQUEST = "UPDATE claim_supply_order_request SET N_REF_NO = ? WHERE N_CLAIM_NO = ?";

    Integer getPendingDO(Connection connection, Integer claimNo) throws Exception;

    void newDoRequest(Connection connection, Integer claimNo, Integer refNo) throws Exception;

    void updateDoRequest(Connection connection, Integer claimNo, Integer supplyOrderRefNo) throws Exception;
}
