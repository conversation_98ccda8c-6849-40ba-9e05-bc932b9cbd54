package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.TATDetailDto;

import java.sql.Connection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface TATConfigDao {

    String IS_TAT_DETAILS_AVAILABLE = "SELECT COUNT(*) AS count FROM tat_details " + "WHERE tatId = ? ";

    String INSERT_TAT_DETAILS = "INSERT INTO tat_details(taskName, minTime, maxTime, visibility, status ) VALUES( ?, ?, ?, ?, ?);";

    String UPDATE_TAT_DETAILS = "UPDATE tat_details\n" +
            "SET\n" +
            "    `taskName` = ?,\n" +
            "    `minTime` = ?,\n" +
            "    `maxTime` = ?,\n" +
            "    `visibility` = ?\n" +
            "WHERE\n" +
            "    `tatId` = ? \n";
    String SEARCH_TAT_DETAILS = "SELECT * FROM tat_details WHERE tatID = ? AND status = 'ACTIVE' ";

    String GET_ALL_RECORD_COUNT = "SELECT COUNT(*) FROM tat_details WHERE status = 'ACTIVE'";

    String FILTERED_RECORD_COUNT = "SELECT COUNT(*) FROM tat_details WHERE status = 'ACTIVE' AND (taskName LIKE ? OR minTime LIKE ? OR maxTime LIKE ? OR visibility LIKE ?)";

    String FILTER_TAT_DETAILS = "SELECT * FROM tat_details WHERE status = 'ACTIVE'";
    String APPEND_TAT_ID = " AND tatId = ?";
    String APPEND_TASK_NAME = " AND taskName LIKE ?";

    String DELETE_TAT_DETAILS = "UPDATE tat_details\n" +
            "SET\n" +
            "    `status` = ?,\n" +
            "    `deleteReason` = ?\n" +
            "WHERE\n" +
            "    `tatId` = ? \n";

    Integer saveByID(Connection connection, TATDetailDto tatDetailDto) throws Exception;

    void updateByID(Connection connection, TATDetailDto tatDetailDto) throws Exception;

    boolean isAvailable(Connection connection, TATDetailDto tatDetailDto) throws Exception;

    DataGridDto findAllTATData(Connection connection, int start, int length, String sortColumn, String sortDirection, String searchValue) throws Exception;

    TATDetailDto searchTATData(int tatId, Connection connection) throws Exception;

    void deleteTATData(int tatId, String deleteReason, Connection connection) throws Exception;

    List<TATDetailDto> filterTATDetails(int taskId, String taskName, Connection connection) throws Exception;
}
