package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.ThirdPartyDto;

import java.sql.Connection;
import java.util.List;
import java.util.Map;

public interface ThirdPartyDao {

    String INSERT_CLAIM_THIRD_PARTY_DETAILS = "INSERT INTO claim_third_party_details VALUES(0,?,?,?,?,?,?,?,?,?)";
    String UPDATE_CLAIM_THIRD_PARTY_DETAILS = "UPDATE claim_third_party_details SET " +
            "third_party_involved=?,\n" +
            "loss_type=?,\n" +
            "item_type=?,\n" +
            "vehicle_no=?,\n" +
            "intend_claim=?,\n" +
            "remark=?,\n" +
            "inp_user_id=?,\n" +
            "inp_date_time=? " +
            "WHERE txn_id=?";

    String SELECT_CLAIM_THIRD_PARTY_DETAILS = "SELECT * FROM claim_third_party_details WHERE claim_no=?";
    String DELETE_CLAIM_THIRD_PARTY_MAIN = "DELETE FROM claim_third_party_details WHERE claim_no=?";
    String INSERT_CLAIM_THIRD_PARTY_ASSESSOR_DETAILS = "INSERT INTO claim_third_party_details VALUES(0,?,?,?,?,?,?,?,?,?)";
    String INSERT_CLAIM_THIRD_PARTY_MOTOR_ENG_DETAILS = "INSERT INTO claim_third_party_details VALUES(0,?,?,?,?,?,?,?,?,?)";

    ThirdPartyDto insertMaster(Connection connection, ThirdPartyDto thirdPartyDto) throws Exception;

    ThirdPartyDto updateMaster(Connection connection, ThirdPartyDto thirdPartyDto) throws Exception;

    List<ThirdPartyDto> searchAll(Connection connection, Integer claimNo) throws Exception;

    Integer insertThirdPartyList(Connection connection, Map<Integer, ThirdPartyDto> thirdPartyList, Integer claimNo) throws Exception;

    public Map<Integer, ThirdPartyDto> searchAllClaimsThirdParty(Connection connection, Integer claimNo) throws Exception;

    Integer updateMasterList(Connection connection, Map<Integer, ThirdPartyDto> thirdPartyList) throws Exception;

    void removeClaimThirdParty(Connection connection, Integer claimNo) throws Exception;

    public ThirdPartyDto insertAssessorMaster(Connection connection, ThirdPartyDto thirdPartyDto) throws Exception;

    public ThirdPartyDto insertMotorEnfMaster(Connection connection, ThirdPartyDto thirdPartyDto) throws Exception;


}
