package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.TireCondtionDto;

import java.sql.Connection;
import java.util.List;

/**
 * Created by aki<PERSON> on 5/16/18.
 */
public interface TireCondtionDao extends BaseDao<TireCondtionDto> {

    String SQL_INSERT_TIRE_CONDTION_VALUES = "INSERT INTO claim_tire_condition VALUES(?,?,?,?,?,?,?,?,?,?)";

    String SQL_UPDATE_TIRE_CONDTION_VALUES = "UPDATE claim_tire_condition SET\n"
            + "		V_RF=?,\n"
            + "		V_LF=?,\n"
            + "		V_RR=?,\n"
            + "		V_RL=?,\n"
            + "		V_RRI=?,\n"
            + "		V_LRI=?,\n"
            + "		V_OTHER=?\n"
            + "WHERE N_CLIM_NO = ? AND N_POSITION = ? AND N_REF_NO = ?";

    String SQL_INSERT_TIRE_CONDTION_MOTOR_ENG_VALUES = "INSERT INTO claim_tire_condition_motor_eng VALUES(?,?,?,?,?,?,?,?,?)";

    String SQL_SQL_TIRE_CONDTION_MOTOR_ENG_VALUES = "SELECT * FROM claim_tire_condition WHERE N_CLIM_NO=? AND N_POSITION=? AND N_REF_NO = ?";

    public TireCondtionDto insertMotorEngMaster(Connection connection, TireCondtionDto tireCondtionDto) throws Exception;

    public TireCondtionDto searchTireCondtionByClaimNoAndPosition(Connection connection, TireCondtionDto tireCondtionDto) throws Exception;

    public List<TireCondtionDto> searchByClaimNoAndRefNo(Connection connection, int claimNo, int refNo) throws Exception;

}
