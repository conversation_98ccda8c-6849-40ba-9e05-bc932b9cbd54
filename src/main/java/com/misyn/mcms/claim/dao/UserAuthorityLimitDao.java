package com.misyn.mcms.claim.dao;


import com.misyn.mcms.claim.dto.PopupItemDto;
import com.misyn.mcms.claim.dto.UserAuthorityLimitDto;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;

public interface UserAuthorityLimitDao extends BaseDao<UserAuthorityLimitDto> {

    String SELECT_BY_LIMIT_AND_DEPARTMENT_ID = "SELECT\n" +
            "	* \n" +
            "FROM\n" +
            "	user_authority_limit \n" +
            "WHERE\n" +
            "	? > from_limit \n" +
            "	AND ? <= to_limit \n" +
            "	AND department_id = ?";

    String SELECT_BY_LEVEL_CODE_AND_DEPARTMENT_ID = "SELECT\n" +
            "	* \n" +
            "FROM\n" +
            "	user_authority_limit\n" +
            "WHERE\n" +
            "	level_code = ?\n" +
            "	AND department_id =?";

    String SELECT_LEVEL_CODE_BY_LIMIT_AND_DEPARTMENT_ID = "SELECT\n" +
            "	level_code \n" +
            "FROM\n" +
            "	user_authority_limit \n" +
            "WHERE\n" +
            "	? > from_limit \n" +
            "	AND ? <= to_limit \n" +
            "	AND department_id = ?";

    String SELECT_ALL_FROM_USER_AUTHORITY_LIMIT_AND_ASSIGN_MOFA_LEVEL_MST = "SELECT\n" +
            "	t2.* \n" +
            "FROM\n" +
            "	calculation_sheet_assign_mofa_level_mst AS t1\n" +
            "	INNER JOIN user_authority_limit AS t2 ON t1.assign_user_mofa_level = t2.level_code \n" +
            "WHERE\n" +
            "	t1.calsheet_id = ? \n" +
            "	AND t1.assign_user_id = ? \n" +
            "	AND t2.department_id = ?";

    String SELECT_AUTHORITY_LEVELS = "SELECT level_code, level_name FROM user_authority_limit WHERE department_id = ?";

    UserAuthorityLimitDto searchByLimitAndDepartmentId(Connection connection, BigDecimal limit, int mofaTeamDepartmentId) throws Exception;

    UserAuthorityLimitDto searchByLevelCodeAndDepartmentId(Connection connection, int levelCode, int mofaTeamDepartmentId) throws Exception;

    int getMofaLevel(Connection connection, BigDecimal limit, int mofaTeamDepartmentId) throws Exception;

    UserAuthorityLimitDto getUserAuthorityDetailsByMofaDetails(Connection connection, Integer calSheetId, String assignUserId, int departmentId) throws Exception;

    List<PopupItemDto> getAllMofaLevels(Connection connection) throws Exception;
}
