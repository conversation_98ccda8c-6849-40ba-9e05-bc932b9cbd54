package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.ApproveAssessorPaymentClaimWiseDao;
import com.misyn.mcms.claim.dto.ApproveAssessorPaymentClaimWiseDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
public class ApproveAssessorPaymentClaimWiseDaoImpl implements ApproveAssessorPaymentClaimWiseDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApproveAssessorPaymentClaimWiseDaoImpl.class);

    @Override
    public ApproveAssessorPaymentClaimWiseDto insertMaster(Connection connection, ApproveAssessorPaymentClaimWiseDto approveAssessorPaymentClaimWiseDto) throws Exception {
        PreparedStatement ps = null;

        try {
            ps = connection.prepareStatement(SQL_INSERT_MASTER);
            ps.setObject(1, approveAssessorPaymentClaimWiseDto.getClaimNo());
            ps.setObject(2, approveAssessorPaymentClaimWiseDto.getAssessorCode());
            ps.setObject(3, approveAssessorPaymentClaimWiseDto.getRefNo());
            ps.setObject(4, approveAssessorPaymentClaimWiseDto.getTotalOtherAmount());
            ps.setObject(5, approveAssessorPaymentClaimWiseDto.getTotalScheduleAmount());

            if (ps.executeUpdate() > 0) {
                return approveAssessorPaymentClaimWiseDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ApproveAssessorPaymentClaimWiseDto updateMaster(Connection connection, ApproveAssessorPaymentClaimWiseDto approveAssessorPaymentClaimWiseDto) throws Exception {
        return null;
    }

    @Override
    public ApproveAssessorPaymentClaimWiseDto insertTemporary(Connection connection, ApproveAssessorPaymentClaimWiseDto approveAssessorPaymentClaimWiseDto) throws Exception {
        return null;
    }

    @Override
    public ApproveAssessorPaymentClaimWiseDto updateTemporary(Connection connection, ApproveAssessorPaymentClaimWiseDto approveAssessorPaymentClaimWiseDto) throws Exception {
        return null;
    }

    @Override
    public ApproveAssessorPaymentClaimWiseDto insertHistory(Connection connection, ApproveAssessorPaymentClaimWiseDto approveAssessorPaymentClaimWiseDto) throws Exception {
        PreparedStatement ps = null;

        try {
            ps = connection.prepareStatement(SQL_INSERT_HISTORY);
            ps.setObject(1, approveAssessorPaymentClaimWiseDto.getClaimNo());
            ps.setObject(2, approveAssessorPaymentClaimWiseDto.getAssessorCode());
            ps.setObject(3, approveAssessorPaymentClaimWiseDto.getRefNo());
            ps.setObject(4, approveAssessorPaymentClaimWiseDto.getTotalOtherAmount());
            ps.setObject(5, approveAssessorPaymentClaimWiseDto.getTotalScheduleAmount());
            ps.setObject(6, approveAssessorPaymentClaimWiseDto.getAction());
            ps.setObject(7, approveAssessorPaymentClaimWiseDto.getInputDatetime());

            if (ps.executeUpdate() > 0) {
                return approveAssessorPaymentClaimWiseDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ApproveAssessorPaymentClaimWiseDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public ApproveAssessorPaymentClaimWiseDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ApproveAssessorPaymentClaimWiseDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public ApproveAssessorPaymentClaimWiseDto getDetailByAssessorCodeAndClaimNo(Connection connection, int claimNo, String assessorCode) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_ALL_BY_CLAIM_NO_AND_ASSESSOR_CODE);
            ps.setObject(1, claimNo);
            ps.setObject(2, assessorCode);
            rs = ps.executeQuery();
            while (rs.next()) {
                ApproveAssessorPaymentClaimWiseDto approveAssessorPaymentClaimWiseDto = new ApproveAssessorPaymentClaimWiseDto();
                approveAssessorPaymentClaimWiseDto.setTxnId(rs.getInt("txn_id"));
                approveAssessorPaymentClaimWiseDto.setClaimNo(rs.getInt("claim_no"));
                approveAssessorPaymentClaimWiseDto.setAssessorCode(rs.getString("assessor_code"));
                approveAssessorPaymentClaimWiseDto.setRefNo(rs.getInt("ref_no"));
                approveAssessorPaymentClaimWiseDto.setTotalOtherAmount(rs.getBigDecimal("total_other_amount"));
                approveAssessorPaymentClaimWiseDto.setTotalScheduleAmount(rs.getBigDecimal("total_schedule_amount"));

                return approveAssessorPaymentClaimWiseDto;

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public void updateByclaimNoAndAssessorCode(Connection connection, ApproveAssessorPaymentClaimWiseDto approveAssessorPaymentClaimWiseDto) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_BY_CLAIM_NO_AND_ASSESSOR_CODE);
            ps.setObject(1, approveAssessorPaymentClaimWiseDto.getTotalOtherAmount());
            ps.setObject(2, approveAssessorPaymentClaimWiseDto.getTotalScheduleAmount());
            ps.setObject(3, approveAssessorPaymentClaimWiseDto.getClaimNo());
            ps.setObject(4, approveAssessorPaymentClaimWiseDto.getAssessorCode());
            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            ps.close();
        }
    }
}
