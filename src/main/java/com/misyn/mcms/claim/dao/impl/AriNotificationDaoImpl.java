package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AriNotificationDao;
import com.misyn.mcms.claim.dto.AriNotificationDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class AriNotificationDaoImpl implements AriNotificationDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(AriNotificationDaoImpl.class);

    @Override
    public void saveAriNotification(Connection connection, Integer claimNo, String inputUser, String assignUser) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(SAVE_ARI_NOTIFICATION);
            ps.setInt(1, claimNo);
            ps.setString(2, Utility.sysDateTime());
            ps.setString(3, inputUser);
            ps.setString(4, assignUser);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean isAvailableUnsentNotification(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(IS_UNSENT_NOTIFICATION_AVAILABLE);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            return rs.next();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void removeAvailableRecordsByClaimNo(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(REMOVE_AVAILABLE_RECORDS);
            ps.setInt(1, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<AriNotificationDto> getPendingNotifications(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<AriNotificationDto> ariNotificationDtos = new ArrayList<>();
        try {
            ps = connection.prepareStatement(GET_ALL_ARI_NOTIFICATION);
            rs = ps.executeQuery();
            while (rs.next()) {
                AriNotificationDto ariNotificationDto = new AriNotificationDto();
                ariNotificationDto.setId(rs.getInt("txn_id"));
                ariNotificationDto.setClaimNo(rs.getInt("claim_no"));
                ariNotificationDto.setInputDate(Utility.getDate(rs.getString("input_date_time"), AppConstant.DATE_TIME_FORMAT));
                ariNotificationDto.setInputUser(rs.getString("input_user"));
                ariNotificationDto.setAssignUser(rs.getString("assign_user"));
                ariNotificationDtos.add(ariNotificationDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return ariNotificationDtos;
    }
}
