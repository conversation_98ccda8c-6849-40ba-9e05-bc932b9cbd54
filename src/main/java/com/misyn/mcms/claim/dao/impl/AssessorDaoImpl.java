package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.AssessorDao;
import com.misyn.mcms.claim.dto.AssessorDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class AssessorDaoImpl extends AbstractBaseDao<AssessorDaoImpl> implements AssessorDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(AssessorDaoImpl.class);

    @Override
    public AssessorDto insertMaster(Connection connection, AssessorDto assessorDto) throws Exception {
        return null;
    }

    @Override
    public AssessorDto insertTemporary(Connection connection, AssessorDto assessorDto) throws Exception {
        return null;
    }

    @Override
    public AssessorDto insertHistory(Connection connection, AssessorDto assessorDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_HISTORY);
            ps.setString(++index, assessorDto.getCode());
            ps.setBigDecimal(++index, assessorDto.getTotalOtherAmount());
            ps.setBigDecimal(++index, assessorDto.getTotalScheduleAmount());
            ps.setInt(++index, assessorDto.getClaimNo());
            ps.setInt(++index, assessorDto.getJobRefNo());
            ps.setString(++index, Utility.sysDateTime());
            ps.setString(++index, assessorDto.getAction());

            if (ps.executeUpdate() > 0) {
                return assessorDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public AssessorDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_BY_REF_NO);
            ps.setObject(1, id);

            rs = ps.executeQuery();
            if (rs.next()) {
                AssessorDto assessor = new AssessorDto();
                assessor.setUserName(rs.getString("V_NAME"));
                return assessor;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            ps.close();
        }
        return null;
    }

    @Override
    public AssessorDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<AssessorDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public AssessorDto updateMaster(Connection connection, AssessorDto assessorDto) throws Exception {
        return null;
    }

    @Override
    public AssessorDto updateTemporary(Connection connection, AssessorDto assessorDto) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public List<AssessorDto> getAssessorListByDivisionCode(Connection connection, String divisionCode) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<AssessorDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_CLAIM_ASSESSOR_BY_DIVISION_CODE);
            rs = ps.executeQuery();
            while (rs.next()) {
                AssessorDto assessorDto = getAssessorDetails(rs);
                list.add(assessorDto);
            }
        } catch (Exception e) {

        }
        return list;
    }

    @Override
    public String getAssessorMobileNo(Connection connection, String assessorCode) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        AssessorDto assessorDto = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_MOBILE_NO_BY_ASSESSOR_CODE);
            ps.setString(1, assessorCode);

            rs = ps.executeQuery();
            if (rs.next()) {
                return rs.getString("t1.v_mobile");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return AppConstant.STRING_EMPTY;
    }

    @Override
    public String getAssessorName(Connection connection, String assessorCode) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        AssessorDto assessorDto = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_MOBILE_NO_BY_ASSESSOR_CODE);
            ps.setString(1, assessorCode);

            rs = ps.executeQuery();
            if (rs.next()) {
                return rs.getString("t2.V_NAME");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            ps.close();
        }
        return AppConstant.STRING_EMPTY;

    }

    @Override
    public AssessorDto getAssessorUserName(Connection connection, String assessorCode) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        AssessorDto assessorDto = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_ALL_FROM_USER_MST);
            ps.setString(1, assessorCode);

            rs = ps.executeQuery();
            if (rs.next()) {
                AssessorDto assessor = new AssessorDto();
                assessor.setUserName(rs.getString("t1.v_usrid"));
                assessor.setReportingCode(rs.getString("t1.V_REPORT_TO"));
                return assessor;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            ps.close();
        }
        return null;
    }

    @Override
    public UserDto searchUserByUserId(Connection connection, String userId) throws Exception {
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        ResultSet rs1 = null;
        ResultSet rs2 = null;
        try {
            ps1 = connection.prepareStatement(SQL_SELECT_USER_BY_USER_ID);
            ps2 = connection.prepareStatement(SQL_SELECT_USER_BY_USER_ID);
            ps1.setString(1, userId);

            rs1 = ps1.executeQuery();
            if (rs1.next()) {
                UserDto user = new UserDto();
                user.setFirstName(rs1.getString("v_firstname"));
                user.setLastName(rs1.getString("v_lastname"));
                user.setReportingTo(rs1.getString("V_REPORT_TO") == null ? AppConstant.EMPTY_STRING : rs1.getString("V_REPORT_TO"));

                ps2.setString(1, user.getReportingTo());
                rs2 = ps2.executeQuery();
                if (rs2.next()) {
                    user.setMobile(rs2.getString("v_mobile"));
                    user.setReportingToName(rs2.getString("v_firstname") + " " + rs2.getString("v_lastname"));
                }
                return user;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            rs1.close();
            rs2.close();
            ps1.close();
            ps2.close();
        }
        return null;
    }

    @Override
    public UserDto searchUserByEmployeeNo(Connection connection, String assessorCode) throws Exception {
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        ResultSet rs1 = null;
        ResultSet rs2 = null;
        try {
            ps1 = connection.prepareStatement(SQL_SELECT_USER_BY_EMP_NO);
            ps2 = connection.prepareStatement(SQL_SELECT_USER_BY_USER_ID);
            ps1.setString(1, assessorCode);

            rs1 = ps1.executeQuery();
            if (rs1.next()) {
                UserDto user = new UserDto();
                user.setFirstName(rs1.getString("v_firstname"));
                user.setLastName(rs1.getString("v_lastname"));
                user.setReportingTo(rs1.getString("V_REPORT_TO") == null ? AppConstant.EMPTY_STRING : rs1.getString("V_REPORT_TO"));

                ps2.setString(1, user.getReportingTo());
                rs2 = ps2.executeQuery();
                if (rs2.next()) {
                    user.setMobile(rs2.getString("v_mobile"));
                    user.setReportingToName(rs2.getString("v_firstname") + " " + rs2.getString("v_lastname"));
                }
                return user;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            assert rs1 != null;
            rs1.close();
            assert rs2 != null;
            rs2.close();
            ps1.close();
            ps2.close();
        }
        return null;
    }

    @Override
    public AssessorDto searchByCode(Connection connection, String code) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        AssessorDto assessorDto = new AssessorDto();
        try {
            ps = connection.prepareStatement(SQL_SELECT_ALL_BY_V_CODE);
            ps.setString(1, code);
            rs = ps.executeQuery();
            while (rs.next()) {
                assessorDto = getClaimAssessorDetail(rs);

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return assessorDto;
    }

    @Override
    public void updateTotalOtherAndScheduleAmount(Connection connection, AssessorDto assessorDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_OTHER_AND_SCHEDULE_AMOUNT);
            ps.setBigDecimal(++index, assessorDto.getTotalOtherAmount());
            ps.setBigDecimal(++index, assessorDto.getTotalScheduleAmount());
            ps.setString(++index, assessorDto.getCode());
            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            ps.close();
        }
    }

    private AssessorDto getClaimAssessorDetail(ResultSet rs) {
        AssessorDto assessorDto = new AssessorDto();
        try {
            assessorDto.setCode(rs.getString("V_CODE"));
            assessorDto.setName(rs.getString("V_NAME"));
            assessorDto.setTotalOtherAmount(null == rs.getBigDecimal("N_TOT_OTHER_AMT") ? BigDecimal.ZERO : rs.getBigDecimal("N_TOT_OTHER_AMT"));
            assessorDto.setTotalScheduleAmount(null == rs.getBigDecimal("N_TOT_SCHEDULE_AMT") ? BigDecimal.ZERO : rs.getBigDecimal("N_TOT_SCHEDULE_AMT"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return assessorDto;

    }


    private AssessorDto getAssessorDetails(ResultSet rst) {
        AssessorDto assessorDto = new AssessorDto();
        try {
            assessorDto.setCode(rst.getString("V_CODE"));
            assessorDto.setName(rst.getString("V_NAME"));
            assessorDto.setFirstName(rst.getString("usr_mst.v_firstname"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return assessorDto;

    }

    @Override
    public List<AssessorDto> getAssessorListByRteCode(Connection connection, String rteCode) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<AssessorDto> assessorList = new ArrayList<>();
        try {
            if (rteCode.equals(AppConstant.STRING_EMPTY)) {
                ps = connection.prepareStatement(SQL_GET_ASSESSOR_LIST_BY_RTE);
            } else {
                ps = connection.prepareStatement(SQL_GET_ASSESSOR_LIST_BY_RTE.concat(" AND V_REPORT_TO = ?"));
                ps.setString(1, rteCode);
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                AssessorDto assessorDto = getAssessorDetails(rs);
                assessorList.add(assessorDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return assessorList;
    }

}
