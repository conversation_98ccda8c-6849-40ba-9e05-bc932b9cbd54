package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AssessorPaymentDeductionDetailDao;
import com.misyn.mcms.claim.dto.AssessorPaymentDeductionDetailDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
public class AssessorPaymentDeductionDetailDaoImpl implements AssessorPaymentDeductionDetailDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(AssessorPaymentDeductionDetailDaoImpl.class);

    @Override
    public AssessorPaymentDeductionDetailDto insertMaster(Connection connection, AssessorPaymentDeductionDetailDto assessorPaymentDeductionDetailDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_ASSESSOR_PAYMENT_DEDUCTION_DETAIL_MST);
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getClaimNo());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getRefNo());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getBeforeDeductionOtherAmount());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getBeforeDeductionScheduleAmount());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getDeduction());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getOtherAmount());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getScheduleAmount());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getInputdatetime());
            if (ps.executeUpdate() > 0) {
                return assessorPaymentDeductionDetailDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return null;
    }

    @Override
    public AssessorPaymentDeductionDetailDto updateMaster(Connection connection, AssessorPaymentDeductionDetailDto assessorPaymentDeductionDetailDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_ASSESSOR_PAYMENT_DEDUCTION_DETAIL_MST);
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getClaimNo());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getBeforeDeductionOtherAmount());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getBeforeDeductionScheduleAmount());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getDeduction());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getOtherAmount());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getScheduleAmount());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getInputdatetime());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getRefNo());
            if (ps.executeUpdate() > 0) {
                return assessorPaymentDeductionDetailDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return null;
    }

    @Override
    public AssessorPaymentDeductionDetailDto insertTemporary(Connection connection, AssessorPaymentDeductionDetailDto assessorPaymentDeductionDetailDto) throws Exception {
        return null;
    }

    @Override
    public AssessorPaymentDeductionDetailDto updateTemporary(Connection connection, AssessorPaymentDeductionDetailDto assessorPaymentDeductionDetailDto) throws Exception {
        return null;
    }

    @Override
    public AssessorPaymentDeductionDetailDto insertHistory(Connection connection, AssessorPaymentDeductionDetailDto assessorPaymentDeductionDetailDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_ASSESSOR_PAYMENT_DEDUCTION_DETAIL_HST);
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getClaimNo());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getRefNo());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getBeforeDeductionOtherAmount());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getBeforeDeductionScheduleAmount());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getDeduction());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getOtherAmount());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getScheduleAmount());
            ps.setObject(++index, assessorPaymentDeductionDetailDto.getInputdatetime());
            if (ps.executeUpdate() > 0) {
                return assessorPaymentDeductionDetailDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public AssessorPaymentDeductionDetailDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_FROM_ASSESSOR_PAYMENT_DEDUCTION_DETAIL_MST_BY_REF_NO);
            ps.setObject(1, id);

            rs = ps.executeQuery();
            if (rs.next()) {
                AssessorPaymentDeductionDetailDto assessorPaymentDeductionDetailDto = getAssessorPaymentDeductionDetailDto(rs);
                return assessorPaymentDeductionDetailDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            ps.close();
        }
        return null;
    }

    private AssessorPaymentDeductionDetailDto getAssessorPaymentDeductionDetailDto(ResultSet rs) {
        AssessorPaymentDeductionDetailDto assessorPaymentDeductionDetailDto = new AssessorPaymentDeductionDetailDto();
        try {
            assessorPaymentDeductionDetailDto.setClaimNo(rs.getInt("claim_no"));
            assessorPaymentDeductionDetailDto.setRefNo(rs.getInt("ref_no"));
            assessorPaymentDeductionDetailDto.setBeforeDeductionOtherAmount(rs.getBigDecimal("before_deduction_other_amount"));
            assessorPaymentDeductionDetailDto.setBeforeDeductionScheduleAmount(rs.getBigDecimal("before_deduction_schedule_amount"));
            assessorPaymentDeductionDetailDto.setDeduction(rs.getBigDecimal("deduction"));
            assessorPaymentDeductionDetailDto.setOtherAmount(rs.getBigDecimal("other_amount"));
            assessorPaymentDeductionDetailDto.setScheduleAmount(rs.getBigDecimal("schedule_amount"));
            assessorPaymentDeductionDetailDto.setInputdatetime(rs.getString("input_datetime"));
        } catch (SQLException e) {
            LOGGER.error(e.getMessage(), e);
        }
        return assessorPaymentDeductionDetailDto;
    }

    @Override
    public AssessorPaymentDeductionDetailDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<AssessorPaymentDeductionDetailDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public boolean isAlreadySaved(Connection connection, int refNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_ONE_FROM_ASSESSOR_PAYMENT_DEDUCTION_DETAIL_MST_BY_REF_NO);
            ps.setObject(1, refNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return false;
    }
}
