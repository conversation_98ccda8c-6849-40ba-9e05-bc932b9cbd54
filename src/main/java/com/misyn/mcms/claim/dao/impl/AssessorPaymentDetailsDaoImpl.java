package com.misyn.mcms.claim.dao.impl;


import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.AssessorPaymentDetailsDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.PaymentStatus;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
public class AssessorPaymentDetailsDaoImpl extends AbstractBaseDao<AssessorPaymentDetailsDaoImpl> implements AssessorPaymentDetailsDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(AssessorPaymentDetailsDaoImpl.class);


    @Override
    public AssessorPaymentDetailsDto insertMaster(Connection connection, AssessorPaymentDetailsDto assessorPaymentDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_INTO_ASSESSOR_PAYMENT_MST, Statement.RETURN_GENERATED_KEYS);
            ps.setInt(++index, assessorPaymentDetailsDto.getKeyId());
            ps.setInt(++index, assessorPaymentDetailsDto.getClaimNo());
            ps.setString(++index, assessorPaymentDetailsDto.getType());
            ps.setInt(++index, assessorPaymentDetailsDto.getMilleage());
            ps.setBigDecimal(++index, assessorPaymentDetailsDto.getCostOfCall());
            ps.setBigDecimal(++index, assessorPaymentDetailsDto.getOtherFee());
            ps.setBigDecimal(++index, assessorPaymentDetailsDto.getDeductionFee());
            ps.setBigDecimal(++index, assessorPaymentDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, assessorPaymentDetailsDto.getTravelFee());
            ps.setBigDecimal(++index, assessorPaymentDetailsDto.getTotalFee());
            ps.setString(++index, assessorPaymentDetailsDto.getPaymentStatus().getPaymentStaus());
            ps.setString(++index, assessorPaymentDetailsDto.getName());
            ps.setString(++index, assessorPaymentDetailsDto.getRemark());
            ps.setString(++index, assessorPaymentDetailsDto.getVoucherNo());
            ps.setInt(++index, assessorPaymentDetailsDto.getRecordStatus());
            ps.setString(++index, assessorPaymentDetailsDto.getApprvUser());
            ps.setString(++index, assessorPaymentDetailsDto.getApprvDatetime());
            ps.setString(++index, assessorPaymentDetailsDto.getInpUserId());
            ps.setString(++index, assessorPaymentDetailsDto.getInpDate());
            ps.setString(++index, AppConstant.NO);
            ps.setString(++index, AppConstant.ZERO);
            ps.setString(++index, AppConstant.NO);
            ps.setString(++index, assessorPaymentDetailsDto.getToDate());
            ps.setString(++index, assessorPaymentDetailsDto.getFromDate());

            if (ps.executeUpdate() > 0) {
                ResultSet rsKeys = ps.getGeneratedKeys();
                if (rsKeys.next()) {
                    int autoGeneratedId = rsKeys.getInt(1);
                    assessorPaymentDetailsDto.setTxnId(autoGeneratedId);
                    return assessorPaymentDetailsDto;
                }
                rsKeys.close();


            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return assessorPaymentDetailsDto;
    }

    @Override
    public AssessorPaymentDetailsDto updateMaster(Connection connection, AssessorPaymentDetailsDto assessorPaymentDetailsDto) throws Exception {
        return null;
    }

    @Override
    public AssessorPaymentDetailsDto insertTemporary(Connection connection, AssessorPaymentDetailsDto assessorPaymentDetailsDto) throws Exception {
        return null;
    }

    @Override
    public AssessorPaymentDetailsDto updateTemporary(Connection connection, AssessorPaymentDetailsDto assessorPaymentDetailsDto) throws Exception {
        return null;
    }

    @Override
    public AssessorPaymentDetailsDto insertHistory(Connection connection, AssessorPaymentDetailsDto assessorPaymentDetailsDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public AssessorPaymentDetailsDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            ps = connection.prepareStatement(SQL_SELECT_PAYMENT_MST_BY_ID);
            ps.setObject(1, id);
            rs = ps.executeQuery();
            if (rs.next()) {
                AssessorPaymentDetailsDto assessorPaymentDetailsDto = getAssessorPaymentDetailsDto(rs);
                return assessorPaymentDetailsDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public AssessorPaymentDetailsDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<AssessorPaymentDetailsDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<AssessorPaymentDetailsDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_FROM_ASSESSOR_PAYMENT_MST);
            rs = ps.executeQuery();
            while (rs.next()) {
                AssessorPaymentDetailsDto assessorPaymentDetailsDto = getAssessorPaymentDetailsDto(rs);
                assessorPaymentDetailsDto.setVehicleNumber(rs.getString("t2.V_VEHICLE_NO"));
                assessorPaymentDetailsDto.setDateOfAccident(rs.getString("t2.D_ACCID_DATE").concat(" ").concat(rs.getString("t2.T_ACCID_TIME")));
                assessorPaymentDetailsDto.setPlaceOfInspection(rs.getString("t6.v_place_of_inspection"));
                assessorPaymentDetailsDto.setPolNumber(rs.getString("t2.V_POL_NUMBER"));
                assessorPaymentDetailsDto.setIsfClaimNo(rs.getString("t2.V_ISF_CLAIM_NO"));
                list.add(assessorPaymentDetailsDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    private AssessorPaymentDetailsDto getAssessorPaymentDetailsDto(ResultSet rst) {
        AssessorPaymentDetailsDto assessorPaymentDetailsDto = new AssessorPaymentDetailsDto();
        try {

            assessorPaymentDetailsDto.setTxnId(rst.getInt("txn_id"));
            assessorPaymentDetailsDto.setKeyId(rst.getInt("key_id"));
            assessorPaymentDetailsDto.setType(rst.getString("type"));
            assessorPaymentDetailsDto.setMilleage(rst.getInt("milleage"));
            assessorPaymentDetailsDto.setCostOfCall(rst.getBigDecimal("cost_of_call"));
            assessorPaymentDetailsDto.setOtherFee(rst.getBigDecimal("other_fee"));
            assessorPaymentDetailsDto.setDeductionFee(rst.getBigDecimal("deduction_fee"));
            assessorPaymentDetailsDto.setProfessionalFee(rst.getBigDecimal("professional_fee"));
            assessorPaymentDetailsDto.setTravelFee(rst.getBigDecimal("travel_fee"));
            assessorPaymentDetailsDto.setTotalFee(rst.getBigDecimal("total_fee"));
            if ("P".equals(rst.getString("payment_status"))) {
                assessorPaymentDetailsDto.setPaymentStatus(PaymentStatus.Pending);
            } else if ("R".equals(rst.getString("payment_status"))) {
                assessorPaymentDetailsDto.setPaymentStatus(PaymentStatus.Reject);
            } else {
                assessorPaymentDetailsDto.setPaymentStatus(PaymentStatus.Approve);
            }
            assessorPaymentDetailsDto.setRecordStatus(rst.getInt("record_status"));
            assessorPaymentDetailsDto.setInpDate(rst.getString("inp_datetime"));
            assessorPaymentDetailsDto.setInpUserId(rst.getString("inp_userid"));
            assessorPaymentDetailsDto.setClaimNo(rst.getInt("claim_no"));
            assessorPaymentDetailsDto.setName(rst.getString("name"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        return assessorPaymentDetailsDto;
    }

    @Override
    public void updateStausByIdAndStatus(Connection connection, Integer txnId, AssessorPaymentDetailsDto assessorPaymentDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_PAYMENT_BY_ID);
            ps.setString(++index, assessorPaymentDetailsDto.getPaymentStatus().getPaymentStaus());
            ps.setInt(++index, assessorPaymentDetailsDto.getMilleage());
            ps.setBigDecimal(++index, assessorPaymentDetailsDto.getCostOfCall());
            ps.setBigDecimal(++index, assessorPaymentDetailsDto.getOtherFee());
            ps.setBigDecimal(++index, assessorPaymentDetailsDto.getDeductionFee());
            ps.setBigDecimal(++index, assessorPaymentDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, assessorPaymentDetailsDto.getTravelFee());
            ps.setBigDecimal(++index, assessorPaymentDetailsDto.getTotalFee());
            ps.setString(++index, assessorPaymentDetailsDto.getInpUserId());
            ps.setString(++index, assessorPaymentDetailsDto.getInpDate());
            ps.setInt(++index, txnId);
            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("System Error", e);
        } finally {
            ps.close();
        }

    }

    @Override
    public void updateStausByIdAndStatus(Connection connection, Integer txnId, String status, String apprvUser, String apprvDatetime, Integer key, String from_date, String to_date) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_PAYMENT_STATUS_BY_ID);
            ps.setString(1, status);
            ps.setString(2, apprvUser);
            ps.setString(3, apprvDatetime);
            ps.setString(4, AppConstant.NO);
            ps.setInt(5, key);
            ps.setString(6, AppConstant.NO);
            ps.setString(7, from_date);
            ps.setString(8, to_date);
            ps.setInt(9, txnId);
            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("System Error", e);
        } finally {
            ps.close();
        }

    }

    @Override
    public void updateStausByIdAndStatus(Connection connection, Integer txnId, String status, String remark, String apprvUser, String apprvDatetime) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_PAYMENT_STATUS_REMARK_BY_ID);
            ps.setString(1, status);
            ps.setString(2, remark);
            ps.setString(3, apprvUser);
            ps.setString(4, apprvDatetime);
            ps.setInt(5, txnId);
            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("System Error", e);
        } finally {
            ps.close();
        }

    }

    @Override
    public AssessorPaymentDetailsDto searchByKeyAndType(Connection connection, Integer key, String type) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            ps = connection.prepareStatement(SQL_SELECT_PAYMENT_MST_BY_KEY_AND_TYPE);
            ps.setInt(1, key);
            ps.setString(2, type);
            rs = ps.executeQuery();
            if (rs.next()) {
                return getAssessorPaymentDetailsDto(rs);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public BigDecimal getTotalAmount(Connection connection, String paymentStaus, String status, String name, String fromDate, String toDate, String vehicleNumber, String claimNumber, String jobNumber, String inspectionType, String rteCode, String claimType, String policyChannelType) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = SQL_SELECT_TOTAL_AMOUNT.concat(" WHERE ");
        boolean isWhere = false;

        if ("A".equalsIgnoreCase(claimType)) {
            isWhere = true;
            sql = sql.concat(" t2.V_ISF_CLAIM_NO IS NOT NULL AND t2.V_ISF_CLAIM_NO  <> ''").concat(" AND ");
        } else if ("P".equalsIgnoreCase(claimType)) {
            isWhere = true;
            sql = sql.concat(" (t2.V_ISF_CLAIM_NO IS  NULL OR t2.V_ISF_CLAIM_NO  = '') ").concat(" AND ");
        }

        if (!paymentStaus.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t1.type='").concat(paymentStaus).concat("' AND ");
        }

        if (!status.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t1.payment_status='").concat(status).concat("' AND ");
        }

        if (!name.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t4.v_usrid LIKE '%").concat(name).concat("%' AND ");
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            isWhere = true;
            sql = sql.concat("  DATE_FORMAT(t1.inp_datetime,'%Y-%m-%d') BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "' AND ");
        }

        if (!vehicleNumber.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t2.V_VEHICLE_NO LIKE '%").concat(vehicleNumber).concat("%' AND ");
        }

        if (!claimNumber.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t2.N_CLIM_NO LIKE '%").concat(claimNumber).concat("%' AND ");
        }

        if (!jobNumber.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t3.job_id LIKE '%").concat(jobNumber).concat("%' AND ");
        }

        if (!inspectionType.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t3.insepction_id =").concat(inspectionType).concat(" AND ");
        }

        if (!rteCode.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t4.V_REPORT_TO='").concat(rteCode).concat("' AND ");
        }

        if (!policyChannelType.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t2.V_POLICY_CHANNEL_TYPE='").concat(policyChannelType).concat("' AND ");
        }

        if (!isWhere) {
            sql = sql.substring(0, sql.lastIndexOf(" WHERE "));
        } else {
            sql = sql.substring(0, sql.lastIndexOf(" AND "));
        }

        try {
            ps = connection.prepareStatement(sql);

            rs = ps.executeQuery();
            if (rs.next()) {
                return rs.getBigDecimal("totalFee");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return BigDecimal.ZERO;
    }

    @Override
    public List<AssessorPaymentDetailsDto> searchAll(Connection connection, String paymentStaus, String status, String name, String fromDate, String toDate, String vehicleNumber, String claimNumber, String jobNumber, String inspectionType, String rteCode, boolean isGridSearch, String claimType, String policyChannelType) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<AssessorPaymentDetailsDto> list = new ArrayList<>();
        String sql = SQL_SELECT_FROM_ASSESSOR_PAYMENT_MST.concat(" WHERE ");
        boolean isWhere = false;

        if ("A".equalsIgnoreCase(claimType)) {
            isWhere = true;
            sql = sql.concat(" t2.V_ISF_CLAIM_NO IS NOT NULL AND t2.V_ISF_CLAIM_NO  <> ''").concat(" AND ");
        } else if ("P".equalsIgnoreCase(claimType)) {
            isWhere = true;
            sql = sql.concat(" (t2.V_ISF_CLAIM_NO IS  NULL OR t2.V_ISF_CLAIM_NO  = '') ").concat(" AND ");
        }

        if (!paymentStaus.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t1.type='").concat(paymentStaus).concat("' AND ");
        }

        if (!status.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t1.payment_status='").concat(status).concat("' AND ");
        }

        if (!name.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t4.v_usrid LIKE '%").concat(name).concat("%' AND ");
        }

        if (!vehicleNumber.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t2.V_VEHICLE_NO LIKE '%").concat(vehicleNumber).concat("%' AND ");
        }

        if (!claimNumber.isEmpty()) {
            isWhere = true;
            if (isGridSearch) {
                fromDate = AppConstant.STRING_EMPTY;
                toDate = AppConstant.STRING_EMPTY;
            }
            sql = sql.concat(" t2.N_CLIM_NO LIKE '%").concat(claimNumber).concat("%' AND ");
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            isWhere = true;
            sql = sql.concat("  DATE_FORMAT(t1.inp_datetime,'%Y-%m-%d') BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "' AND ");
        }

        if (!jobNumber.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t3.job_id LIKE '%").concat(jobNumber).concat("%' AND ");
        }

        if (!inspectionType.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t3.insepction_id =").concat(inspectionType).concat(" AND ");
        }

        if (!rteCode.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t4.V_REPORT_TO='").concat(rteCode).concat("' AND ");
        }

        if (!policyChannelType.isEmpty()) {
            isWhere = true;
            sql = sql.concat(" t2.V_POLICY_CHANNEL_TYPE='").concat(policyChannelType).concat("' AND ");
        }

        if (!isWhere) {
            sql = sql.substring(0, sql.lastIndexOf(" WHERE "));
        } else {
            sql = sql.substring(0, sql.lastIndexOf(" AND "));
        }

        try {
            ps = connection.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                AssessorPaymentDetailsDto assessorPaymentDetailsDto = getAssessorPaymentDetailsDto(rs);
                assessorPaymentDetailsDto.setVehicleNumber(rs.getString("t2.V_VEHICLE_NO"));
                assessorPaymentDetailsDto.setDateOfAccident(rs.getString("t2.D_ACCID_DATE").concat(" ").concat(rs.getString("t2.T_ACCID_TIME")));
                assessorPaymentDetailsDto.setPlaceOfInspection(rs.getString("t6.v_place_of_inspection"));
                assessorPaymentDetailsDto.setJobNo(rs.getString("t3.job_id"));
                assessorPaymentDetailsDto.setAllocatedProfessionalfee(null == rs.getBigDecimal("t5.before_deduction_schedule_amount") ? BigDecimal.ZERO : rs.getBigDecimal("t5.before_deduction_schedule_amount"));
                assessorPaymentDetailsDto.setApprovedProfessionalfee(null == rs.getBigDecimal("t5.schedule_amount") ? BigDecimal.ZERO : rs.getBigDecimal("t5.schedule_amount"));
                assessorPaymentDetailsDto.setPolNumber(rs.getString("t2.V_POL_NUMBER"));
                assessorPaymentDetailsDto.setIsfClaimNo(rs.getString("t2.V_ISF_CLAIM_NO"));
                assessorPaymentDetailsDto.setIsfPending((null == rs.getString("t2.V_ISF_CLAIM_NO") || AppConstant.STRING_EMPTY.equalsIgnoreCase(rs.getString("t2.V_ISF_CLAIM_NO"))) || (assessorPaymentDetailsDto.getTotalFee().signum() == 0));
                list.add(assessorPaymentDetailsDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public DataGridDto getAssessorPaymentDetailsDataGridDto(Connection connection, Map<String, Object> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        DataGridDto dataGridDto = new DataGridDto();
        boolean isWhere = false;
        List paymentList = new ArrayList(200);

        String claimType= String.valueOf(parameterList.get("claimType"));
        String paymentStatus= String.valueOf(parameterList.get("paymentStatus"));
        String status= String.valueOf(parameterList.get("status"));
        String name= String.valueOf(parameterList.get("name"));
        String vehicleNumber= String.valueOf(parameterList.get("vehicleNumber"));
        String claimNumber= String.valueOf(parameterList.get("claimNumber"));
        String jobNumber= String.valueOf(parameterList.get("jobNumber"));
        String inspectionType= String.valueOf(parameterList.get("inspectionType"));
        String rteCode= String.valueOf(parameterList.get("rteCode"));
        String policyChannelType= String.valueOf(parameterList.get("policyChannelType"));
        boolean isApprovalTeam= (boolean) parameterList.get("isApprovalTeam");

        StringBuilder countBuilder=new StringBuilder(SQL_COUNT_FROM_ASSESSOR_PAYMENT_MST_PAGINATION);
        StringBuilder selectBuilder=new StringBuilder(SQL_SELECT_FROM_ASSESSOR_PAYMENT_MST_PAGINATION);
        StringBuilder sqlBuilder = new StringBuilder().append(" WHERE ");


        if ("A".equalsIgnoreCase(claimType)) {
            isWhere = true;
            sqlBuilder.append(" t2.V_ISF_CLAIM_NO IS NOT NULL AND t2.V_ISF_CLAIM_NO <> ''").append(" AND ");
        } else if ("P".equalsIgnoreCase(claimType)) {
            isWhere = true;
            sqlBuilder.append(" (t2.V_ISF_CLAIM_NO IS NULL OR t2.V_ISF_CLAIM_NO = '') ").append(" AND ");
        }

        if (!paymentStatus.isEmpty()) {
            isWhere = true;
            sqlBuilder.append(" t1.type='").append(paymentStatus).append("' AND ");
        }

        if (!status.isEmpty()) {
            isWhere = true;
            sqlBuilder.append(" t1.payment_status='").append(status).append("' AND ");
        }

        if (!name.isEmpty()) {
            isWhere = true;
            sqlBuilder.append(" t4.v_usrid LIKE '%").append(name).append("%' AND ");
        }

        if (!vehicleNumber.isEmpty()) {
            isWhere = true;
            sqlBuilder.append(" t2.V_VEHICLE_NO LIKE '%").append(vehicleNumber).append("%' AND ");
        }

        if (!claimNumber.isEmpty()) {
            isWhere = true;
            fromDate = AppConstant.STRING_EMPTY;
            toDate = AppConstant.STRING_EMPTY;
            sqlBuilder.append(" t2.N_CLIM_NO LIKE '%").append(claimNumber).append("%' AND ");
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            isWhere = true;
            sqlBuilder.append(" DATE_FORMAT(t1.inp_datetime,'%Y-%m-%d') BETWEEN '").append(fromDate).append("' AND '").append(toDate).append("' AND ");
        }

        if (!jobNumber.isEmpty()) {
            isWhere = true;
            sqlBuilder.append(" t3.job_id LIKE '%").append(jobNumber).append("%' AND ");
        }

        if (!inspectionType.isEmpty()) {
            isWhere = true;
            sqlBuilder.append(" t3.insepction_id =").append(inspectionType).append(" AND ");
        }

        if (!rteCode.isEmpty()) {
            isWhere = true;
            sqlBuilder.append(" t4.V_REPORT_TO='").append(rteCode).append("' AND ");
        }

        if (!policyChannelType.isEmpty()) {
            isWhere = true;
            sqlBuilder.append(" t2.V_POLICY_CHANNEL_TYPE='").append(policyChannelType).append("' AND ");
        }

        if (isWhere) {
            sqlBuilder.delete(sqlBuilder.lastIndexOf(" AND "), sqlBuilder.length());
        }
        String counter=countBuilder.append(sqlBuilder).toString();
        if(length != -1){
            sqlBuilder.append(" LIMIT ").append(length).append(" OFFSET ").append(start);
        }



        String selector = selectBuilder.append(sqlBuilder).toString();



        try {
            ps = connection.prepareStatement(selector);
            rs = ps.executeQuery();
            int count=0;

            while (rs.next()) {
                AssessorPaymentDetailsGridDto assessorPaymentDetailsGridDto = getAssessorPaymentDetailsDataGridDto(rs);

                assessorPaymentDetailsGridDto.setVehicleNumber(null == rs.getString("t2.V_VEHICLE_NO") ?AppConstant.EMPTY_STRING : rs.getString("t2.V_VEHICLE_NO") );

                assessorPaymentDetailsGridDto.setPlaceOfInspection(null == rs.getString("t6.v_place_of_inspection") ? AppConstant.EMPTY_STRING : rs.getString("t6.v_place_of_inspection"));
                assessorPaymentDetailsGridDto.setJobNumber(null == rs.getString("t3.job_id") ? AppConstant.EMPTY_STRING:rs.getString("t3.job_id") );
                assessorPaymentDetailsGridDto.setAllocatedProfessionalFee(null == rs.getBigDecimal("t5.before_deduction_schedule_amount") ? BigDecimal.ZERO : rs.getBigDecimal("t5.before_deduction_schedule_amount"));
                assessorPaymentDetailsGridDto.setApprovedProfessionalFee(null == rs.getBigDecimal("t5.schedule_amount") ? BigDecimal.ZERO : rs.getBigDecimal("t5.schedule_amount"));
                assessorPaymentDetailsGridDto.setPolicyNumber(null==rs.getString("t2.V_POL_NUMBER")? AppConstant.EMPTY_STRING:rs.getString("t2.V_POL_NUMBER"));
                assessorPaymentDetailsGridDto.setIsfClaimNumber(null == rs.getString("t2.V_ISF_CLAIM_NO") ? AppConstant.EMPTY_STRING : rs.getString("t2.V_ISF_CLAIM_NO") );
                assessorPaymentDetailsGridDto.setIsfPending((null == rs.getString("t2.V_ISF_CLAIM_NO") || AppConstant.STRING_EMPTY.equalsIgnoreCase(rs.getString("t2.V_ISF_CLAIM_NO"))) || (assessorPaymentDetailsGridDto.getTotalFee().signum() == 0));
                assessorPaymentDetailsGridDto.setIs_approval_team(isApprovalTeam);
                paymentList.add(assessorPaymentDetailsGridDto);
            }
            ps = connection.prepareStatement(counter);
            rs = ps.executeQuery();
            if (rs.next()) {
                count=rs.getInt("total_records");
            }
            dataGridDto.setDraw(drawRandomId);
            dataGridDto.setRecordsTotal(count);
            dataGridDto.setRecordsFiltered(count);
            dataGridDto.setData(paymentList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return dataGridDto;
    }


    private AssessorPaymentDetailsGridDto getAssessorPaymentDetailsDataGridDto(ResultSet rst) {
        AssessorPaymentDetailsGridDto assessorPaymentDetailsGridDto = new AssessorPaymentDetailsGridDto();
        try {
            assessorPaymentDetailsGridDto.setTxtId(getIntOrDefault(rst, "txn_id"));
            assessorPaymentDetailsGridDto.setRefNumber(getIntOrDefault(rst, "key_id"));
            assessorPaymentDetailsGridDto.setType(getStringOrDefault(rst, "type"));
            assessorPaymentDetailsGridDto.setMileage(rst.getInt("milleage"));
            assessorPaymentDetailsGridDto.setCostOfCall(getBigDecimalOrDefault(rst, "cost_of_call"));
            assessorPaymentDetailsGridDto.setOtherFee(getBigDecimalOrDefault(rst, "other_fee"));
            assessorPaymentDetailsGridDto.setDeductions(getBigDecimalOrDefault(rst, "deduction_fee"));
            assessorPaymentDetailsGridDto.setTravelFee(getBigDecimalOrDefault(rst, "travel_fee"));
            assessorPaymentDetailsGridDto.setTotalFee(getBigDecimalOrDefault(rst, "total_fee"));

            String paymentStatus = getStringOrDefault(rst, "payment_status");
            if ("P".equals(paymentStatus)) {
                assessorPaymentDetailsGridDto.setPaymentStatus(String.valueOf(PaymentStatus.Pending));
            } else if ("R".equals(paymentStatus)) {
                assessorPaymentDetailsGridDto.setPaymentStatus(String.valueOf(PaymentStatus.Reject));
            } else {
                assessorPaymentDetailsGridDto.setPaymentStatus(String.valueOf(PaymentStatus.Approve));
            }

            assessorPaymentDetailsGridDto.setClaimNumber(getIntOrDefault(rst, "claim_no"));
            assessorPaymentDetailsGridDto.setAssessorName(getStringOrDefault(rst, "name"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        return assessorPaymentDetailsGridDto;
    }

    private String getStringOrDefault(ResultSet rst, String columnName) throws SQLException {
        String value = rst.getString(columnName);
        return value == null ? AppConstant.EMPTY_STRING : value;
    }

    private int getIntOrDefault(ResultSet rst, String columnName) throws SQLException {
        int value = rst.getInt(columnName);
        return rst.wasNull() ? AppConstant.ZERO_INT : value;
    }

    private BigDecimal getBigDecimalOrDefault(ResultSet rst, String columnName) throws SQLException {
        BigDecimal value = rst.getBigDecimal(columnName);
        return value == null ? BigDecimal.ZERO : value;
    }



    @Override
    public String getPaymentStatus(Connection connection, Integer keyId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String paymentStatus = null;

        try {
            ps = connection.prepareStatement(SQL_SELECT_PAYMENT_STATUS_BY_KEY_ID);
            ps.setInt(1, keyId);
            rs = ps.executeQuery();
            if (rs.next()) {
                paymentStatus = rs.getString("payment_status");
                return paymentStatus;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return null;
    }

    @Override
    public void updateHoldPayment(Connection connection, Integer keyId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_PAYMENT_STATUS_BY_KEY_ID);
            ps.setInt(1, keyId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public List<AssessorPaymentDetailForEmailDto> getPendingEmailList(Connection connection, Integer generateOccurrency) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<AssessorPaymentDetailForEmailDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_FROM_PENDING_EMAIL_STATUS);
            ps.setInt(1, generateOccurrency);
            rs = ps.executeQuery();
            while (rs.next()) {
                AssessorPaymentDetailForEmailDto assessorPaymentDetailForEmailDto = new AssessorPaymentDetailForEmailDto();

                assessorPaymentDetailForEmailDto.setKeyId(rs.getInt("t1.key_id"));
                assessorPaymentDetailForEmailDto.setRefNo(rs.getInt("t2.n_ref_no"));
                assessorPaymentDetailForEmailDto.setClaimNo(rs.getInt("t1.claim_no"));
                assessorPaymentDetailForEmailDto.setRegistrationNo(rs.getString("t4.V_VEHICLE_NO"));
                assessorPaymentDetailForEmailDto.setJobDesc(rs.getString("t3.inspection_type_desc"));
                assessorPaymentDetailForEmailDto.setJobType(rs.getInt("t2.n_job_type"));
                assessorPaymentDetailForEmailDto.setVoucherNo(rs.getString("t1.voucher_no"));
                assessorPaymentDetailForEmailDto.setVoucherDate(rs.getString("t1.apprv_datetime"));
                assessorPaymentDetailForEmailDto.setCostOfCall(null == rs.getBigDecimal("t1.cost_of_call") ? new BigDecimal(0) : rs.getBigDecimal("t1.cost_of_call"));
                assessorPaymentDetailForEmailDto.setOtherCharges(null == rs.getBigDecimal("t1.other_fee") ? new BigDecimal(0) : rs.getBigDecimal("t1.other_fee"));
                assessorPaymentDetailForEmailDto.setDeductionFee(null == rs.getBigDecimal("t1.deduction_fee") ? new BigDecimal(0) : rs.getBigDecimal("t1.deduction_fee"));
                assessorPaymentDetailForEmailDto.setProfessionalFee(null == rs.getBigDecimal("t1.professional_fee") ? new BigDecimal(0) : rs.getBigDecimal("t1.professional_fee"));
                assessorPaymentDetailForEmailDto.setTravelFee(null == rs.getBigDecimal("t1.travel_fee") ? new BigDecimal(0) : rs.getBigDecimal("t1.travel_fee"));
                assessorPaymentDetailForEmailDto.setVoucherAmount(null == rs.getBigDecimal("t1.total_fee") ? new BigDecimal(0) : rs.getBigDecimal("t1.total_fee"));
                assessorPaymentDetailForEmailDto.setName(rs.getString("t1.name"));
                assessorPaymentDetailForEmailDto.setApproveDatetime(rs.getString("t1.apprv_datetime"));
                assessorPaymentDetailForEmailDto.setApproveUser(null == rs.getString("t1.apprv_user") ? AppConstant.STRING_EMPTY : rs.getString("t1.apprv_user"));
                assessorPaymentDetailForEmailDto.setInputUser(null == rs.getString("t1.inp_userid") ? AppConstant.STRING_EMPTY : rs.getString("t1.inp_userid"));
                assessorPaymentDetailForEmailDto.setFromDate(null == rs.getString("t1.from_date") ? AppConstant.STRING_EMPTY : rs.getString("t1.from_date"));
                assessorPaymentDetailForEmailDto.setToDate(null == rs.getString("t1.to_date") ? AppConstant.STRING_EMPTY : rs.getString("t1.to_date"));
                assessorPaymentDetailForEmailDto.setBeforeDeductionScheduleAmount(null == rs.getBigDecimal("t5.before_deduction_schedule_amount") ? BigDecimal.ZERO : rs.getBigDecimal("t5.before_deduction_schedule_amount"));
                assessorPaymentDetailForEmailDto.setScheduleAmount(null == rs.getBigDecimal("t5.schedule_amount") ? BigDecimal.ZERO : rs.getBigDecimal("t5.schedule_amount"));
                assessorPaymentDetailForEmailDto.setPolicyChannelType(null == rs.getString("t4.V_POLICY_CHANNEL_TYPE") ? AppConstant.STRING_EMPTY : rs.getString("t4.V_POLICY_CHANNEL_TYPE"));
                assessorPaymentDetailForEmailDto.setIsfClaimNo(null == rs.getString("t4.V_ISF_CLAIM_NO") ? AppConstant.STRING_EMPTY : rs.getString("t4.V_ISF_CLAIM_NO"));
                list.add(assessorPaymentDetailForEmailDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return list;
    }

    @Override
    public List<AssessorPaymentDetailsDto> getPendingEmailAndSuccessVoucherGeneration(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<AssessorPaymentDetailsDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_FROM_PENDING_EMAIL_AND_VOUCHER_GENERATED);
            rs = ps.executeQuery();
            while (rs.next()) {
                AssessorPaymentDetailsDto assessorPaymentDetailsDto = new AssessorPaymentDetailsDto();

                assessorPaymentDetailsDto.setTxnId(rs.getInt("txn_id"));
                assessorPaymentDetailsDto.setGenerateOccurrency(rs.getInt("generate_occurrency"));

                list.add(assessorPaymentDetailsDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return list;
    }

    @Override
    public Integer getCountForAssessorPaymentListByGenerateOccurrency(Connection connection, Integer generateOccurrency) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        Integer count = 0;
        List<AssessorPaymentDetailsDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_COUNT_FROM_PENDING_EMAIL_LIST_BY_GENERATE_OCCURRENCY);
            ps.setInt(1, generateOccurrency);
            rs = ps.executeQuery();
            if (rs.next()) {
                count = rs.getInt("count");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return count;
    }

    @Override
    public Integer getCountForAssessorPaymentListByVoucherNo(Connection connection, Integer generateOccurrency) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        Integer count = 0;
        List<AssessorPaymentDetailsDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_COUNT_FROM_PENDING_EMAIL_LIST_BY_GENERATE_OCCURRENCY_AND_VOUCHER_NO_IS_NOT_NULL);
            ps.setInt(1, generateOccurrency);
            rs = ps.executeQuery();
            if (rs.next()) {
                count = rs.getInt("count");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return count;
    }

    @Override
    public boolean updateEmailStatus(Connection connection, Integer generateOccurrency) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        boolean isUpdated = false;

        try {
            ps = connection.prepareStatement(SQL_UPDATE_EMAIL_SEND_STATUS_BY_GENERATE_OCCURRENCY);
            ps.setInt(1, generateOccurrency);
            if (ps.executeUpdate() > 0) {
                isUpdated = true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return isUpdated;
    }

    @Override
    public List<UserDto> getAssessorListByReportingRte(Connection connection, String rteName) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<UserDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_ASSESSORS_BY_REPORTING_TO);
            ps.setString(1, rteName);
            rs = ps.executeQuery();
            while (rs.next()) {
                UserDto user = new UserDto();
                user.setUserId(rs.getString("v_usrid"));
                list.add(user);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

}
