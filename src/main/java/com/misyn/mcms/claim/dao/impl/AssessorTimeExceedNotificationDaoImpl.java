package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.AssessorTimeExceedNotificationDao;
import com.misyn.mcms.claim.dto.AssessorTimeExceedNotificationDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class AssessorTimeExceedNotificationDaoImpl extends AbstractBaseDao<AssessorTimeExceedNotificationDaoImpl> implements AssessorTimeExceedNotificationDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(AssessorAllocationDaoImpl.class);

    @Override
    public AssessorTimeExceedNotificationDto insertMaster(Connection connection, AssessorTimeExceedNotificationDto assessorTimeExceedNotificationDto) throws Exception {
int index = 0;
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_INSERT_TO_ASSESSOR_NOTIFICATION, Statement.RETURN_GENERATED_KEYS);
            ps.setString(++index, assessorTimeExceedNotificationDto.getCallCenterUser());
            ps.setString(++index, assessorTimeExceedNotificationDto.getInspectionAssessor());
            ps.setString(++index, assessorTimeExceedNotificationDto.getCreatedBy());
            ps.setDate(++index, assessorTimeExceedNotificationDto.getCreatedDate());
            ps.setString(++index, assessorTimeExceedNotificationDto.getColourCode());
            ps.setString(++index, assessorTimeExceedNotificationDto.getStatus());
            ps.setString(++index, assessorTimeExceedNotificationDto.getAssignedDateTime());
            ps.setInt(++index, assessorTimeExceedNotificationDto.getDuration());
            ps.setString(++index, assessorTimeExceedNotificationDto.getAssessorType());
            ps.setInt(++index, assessorTimeExceedNotificationDto.getInspectionType());
            ps.setInt(++index, assessorTimeExceedNotificationDto.getClaimNo());

            if (ps.executeUpdate() > 0) {
                ResultSet rsKeys = ps.getGeneratedKeys();
                if (rsKeys.next()) {
                    int autoGeneratedId = rsKeys.getInt(1);
                    assessorTimeExceedNotificationDto.setId(autoGeneratedId);
                    return assessorTimeExceedNotificationDto;
                }
                rsKeys.close();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;

    }

    @Override
    public AssessorTimeExceedNotificationDto updateMaster(Connection connection, AssessorTimeExceedNotificationDto assessorTimeExceedNotificationDto) throws Exception {
        return null;
    }


    @Override
    public AssessorTimeExceedNotificationDto insertTemporary(Connection connection, AssessorTimeExceedNotificationDto assessorTimeExceedNotificationDto) throws Exception {
        return null;
    }

    @Override
    public AssessorTimeExceedNotificationDto updateTemporary(Connection connection, AssessorTimeExceedNotificationDto assessorTimeExceedNotificationDto) throws Exception {
        return null;
    }

    @Override
    public AssessorTimeExceedNotificationDto insertHistory(Connection connection, AssessorTimeExceedNotificationDto assessorTimeExceedNotificationDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public AssessorTimeExceedNotificationDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public AssessorTimeExceedNotificationDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<AssessorTimeExceedNotificationDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public List<AssessorTimeExceedNotificationDto> fetchAll(Connection connection) throws Exception {
        List<AssessorTimeExceedNotificationDto> dtoList = new ArrayList<>();

        try (PreparedStatement ps = connection.prepareStatement(SELECT_ALL_FROM_ASSESSOR_NOTIFICATION);
             ResultSet rs = ps.executeQuery()) {

            while (rs.next()) {
                dtoList.add(setValues(rs));
            }
        } catch (Exception e) {
            LOGGER.error("Error fetching notifications: ", e);
            throw e;
        }
        return dtoList;
    }

    private AssessorTimeExceedNotificationDto setValues(ResultSet rs) throws Exception {
        AssessorTimeExceedNotificationDto dto = new AssessorTimeExceedNotificationDto();
        dto.setId(rs.getInt("id"));
        dto.setCallCenterUser(rs.getString("call_center_user"));
        dto.setInspectionAssessor(rs.getString("inspection_assessor"));
        dto.setCreatedDate(rs.getDate("created_date"));
        dto.setCreatedBy(rs.getString("created_by"));
        dto.setColourCode(rs.getString("colour_code"));
        dto.setStatus(rs.getString("status"));
        dto.setAssignedDateTime(rs.getString("assigned_date_time"));
        dto.setDuration(rs.getInt("duration"));
        dto.setAssessorType(rs.getString("assessor_type"));
        dto.setInspectionType(rs.getInt("inspection_type"));
        dto.setClaimNo(rs.getInt("claim_no"));

        return dto;
    }

    @Override
    public void updateStatus(Connection connection, int id){
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_TO_SENT_NOTIFICATION);
            ps.setObject(++index, id);
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }
}
