package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.AuthAssignRteDao;
import com.misyn.mcms.claim.dto.AuthAssignRteDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
public class AuthAssignRteDaoImpl extends AbstractBaseDao<AuthAssignRteDaoImpl> implements AuthAssignRteDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuthAssignRteDaoImpl.class);

    @Override
    public AuthAssignRteDto getRteLevelDetails(Connection connection, String usrName) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SQL_GET_AUTH_LIMITS);
            ps.setString(1, usrName);
            rs = ps.executeQuery();
            if (rs.next()) {
                AuthAssignRteDto authAssignRteDto = new AuthAssignRteDto();
                authAssignRteDto.setId(rs.getInt("n_id"));
                authAssignRteDto.setUserId(rs.getString("v_usrid"));
                authAssignRteDto.setRteLvl2(rs.getString("rte_lvl_2"));
                authAssignRteDto.setRteLvl3(rs.getString("rte_lvl_3"));
                authAssignRteDto.setRteLvl4(rs.getString("rte_lvl_4"));
                return authAssignRteDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return null;
    }
}
