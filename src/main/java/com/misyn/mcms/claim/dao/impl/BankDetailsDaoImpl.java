package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.admin.admin.dto.BankDetailsDto;
import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.BankDetailsDao;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
public class BankDetailsDaoImpl extends AbstractBaseDao<BankDetailsDaoImpl> implements BankDetailsDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(BankDetailsDaoImpl.class);


    @Override
    public Map<Integer, String> findAllInstrumentTypeByID(Connection connection) throws Exception {
        LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_ALL_INSTRUMENT_TYPE);
            rs = ps.executeQuery();
            while (rs.next()) {
                int id = rs.getInt("N_ID");
                String name = rs.getString("V_CODE");
                map.put(id, name);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error("Error occurred while checking doc availability", e);
            throw new Exception("Error occurred while checking doc availability", e);
        }
        return map;
    }

    @Override
    public Integer findLastIndex(Connection connection, BankDetailsDto bankDetailsDto) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        int index = 0;
        Integer id=null;
        try {
            ps = connection.prepareStatement(SELECT_LAST_INDEX);
            ps.setInt(++index, bankDetailsDto.getClaimNo());

            rs = ps.executeQuery();

            if (rs.next()) {
                id=rs.getInt("id") ;
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while checking last index", e);
            throw new Exception("Error occurred while checking last index", e);
        }
        return id;

    }

    @Override
    public Integer saveByID(Connection connection, BankDetailsDto bankDetailsDto) throws Exception {
        PreparedStatement ps;
        Integer generatedId = -1; // Initialize with a default value (negative value indicates failure)
        try {
            ps = connection.prepareStatement(INSERT_CLAIM_BANK_DETAILS, Statement.RETURN_GENERATED_KEYS);
            int index = 0;
            ps.setInt(++index, 0); // id (will be auto-generated)
            ps.setInt(++index, bankDetailsDto.getClaimNo());
            ps.setInt(++index, bankDetailsDto.getInstrumentType());
            ps.setInt(++index, bankDetailsDto.getPayeeType());
            ps.setString(++index, bankDetailsDto.getPayeeName());
            ps.setString(++index, AppConstant.YES);
            ps.setString(++index, bankDetailsDto.getInputUser());
            ps.setString(++index, bankDetailsDto.getLastUpdateUser());
            ps.setTimestamp(++index, Timestamp.valueOf(LocalDateTime.now())); // input_date_time
            ps.setTimestamp(++index, Timestamp.valueOf(LocalDateTime.now())); // last_update_date_time
            ps.setString(++index, AppConstant.NO); // verify_status
            ps.setString(++index, bankDetailsDto.getUploadStatus()); // upload_status
            ps.setInt(++index, bankDetailsDto.getDocRefNo()); // doc_ref_no

            int rowsAffected = ps.executeUpdate();
            if (rowsAffected > 0) {
                // Retrieve the auto-generated ID (primary key)
                ResultSet generatedKeys = ps.getGeneratedKeys();
                if (generatedKeys.next()) {
                    generatedId = generatedKeys.getInt(1); // Get the value of the auto-generated ID
                }
                generatedKeys.close();
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error("Error occurred while saving bank details", e);
            throw new Exception("Error occurred while saving bank details", e);
        }
        return generatedId;
    }


    @Override
    public void deleteByID(Connection connection, BankDetailsDto bankDetailsDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(DELETE_CLAIM_BANK_DETAILS);
            ps.setInt(++index, bankDetailsDto.getId());
            ps.setInt(++index, bankDetailsDto.getClaimNo());
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting bank details", e);
            throw new Exception("Error occurred while deleting bank details", e);
        }

    }

    @Override
    public void verifyByID(Connection connection, BankDetailsDto bankDetailsDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_BANK_DETAILS_VERIFICATION);
            ps.setInt(++index, bankDetailsDto.getInstrumentType()); // instrument_type
            ps.setInt(++index, bankDetailsDto.getPayeeType()); // payee_type
            ps.setString(++index, bankDetailsDto.getPayeeName()); // payee_name
            ps.setString(++index, AppConstant.YES); // update_status
            ps.setString(++index, bankDetailsDto.getLastUpdateUser()); // last_update_user
            ps.setTimestamp(++index, Timestamp.valueOf(LocalDateTime.now())); // last_update_date_time
            ps.setString(++index, AppConstant.YES);//verify_status
            ps.setString(++index, bankDetailsDto.getVerifyUser());//verify_user
            ps.setTimestamp(++index, Timestamp.valueOf(LocalDateTime.now())); // verify_date_time
            ps.setInt(++index, bankDetailsDto.getId()); // id
            ps.setInt(++index, bankDetailsDto.getClaimNo()); // claimNo
            ps.executeUpdate();

            ps.close();
        } catch (Exception e) {
            LOGGER.error("Error occurred while checking doc availability", e);
            throw new Exception("Error occurred while checking doc availability", e);
        }

    }

    @Override
    public void rejectByID(Connection connection, BankDetailsDto bankDetailsDto) throws Exception{
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_BANK_DETAILS_REJECTION);

            ps.setString(++index, AppConstant.REJECT);//verify_status
            ps.setString(++index, bankDetailsDto.getRejectUser());//reject_user
            ps.setTimestamp(++index, Timestamp.valueOf(LocalDateTime.now())); // reject_date_time
            ps.setInt(++index, bankDetailsDto.getId());
            ps.setInt(++index, bankDetailsDto.getClaimNo());
            ps.executeUpdate();

            ps.close();
        } catch (Exception e) {
            LOGGER.error("Error occurred while checking doc availability", e);
            throw new Exception("Error occurred while checking doc availability", e);
        }
    }

    @Override
    public void updateDocRefNO(Connection connection, BankDetailsDto bankDetailsDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_BANK_DETAILS_DOC_REF_NO);
            ps.setInt(++index, bankDetailsDto.getDocRefNo());//doc_ref_no
            ps.setString(++index,AppConstant.YES);//upload_status
            ps.setString(++index,AppConstant.NO);//verify_status
            ps.setTimestamp(++index, Timestamp.valueOf(LocalDateTime.now()));//doc_update_date_time
            ps.setInt(++index, bankDetailsDto.getId());
            ps.setInt(++index, bankDetailsDto.getClaimNo());
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error("Error occurred while checking doc availability", e);
            throw new Exception("Error occurred while checking doc availability", e);
        }
    }

    @Override
    public Integer findDocRefNO(Connection connection, BankDetailsDto bankDetailsDto) throws Exception{
        PreparedStatement ps;
        ResultSet rs;
        int index = 0;
        Integer docRef=null;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_BANK_DETAILS_DOC_REF_NO);
            ps.setInt(++index, bankDetailsDto.getId());
            ps.setInt(++index, bankDetailsDto.getClaimNo());

            rs = ps.executeQuery();

            if (rs.next()) {
                docRef=rs.getInt("doc_ref_no") ;
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while checking doc availability", e);
            throw new Exception("Error occurred while checking doc availability", e);
        }
        return docRef;

    }

    @Override
    public boolean isAvailable(Connection connection, BankDetailsDto bankDetailsDto) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        int index = 0;
        try {
            ps = connection.prepareStatement(IS_BANK_DETAILS_AVAILABLE);
            ps.setInt(++index, bankDetailsDto.getId());
            ps.setInt(++index, bankDetailsDto.getClaimNo());
            rs = ps.executeQuery();

            if (rs.next() && rs.getInt("count") > 0) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while checking bank details availability", e);
            throw new Exception("Error occurred while checking bank details availability", e);
        }
        return false;
    }

    @Override
    public boolean isDocUploaded(Connection connection, BankDetailsDto bankDetailsDto) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        int index = 0;
        try {
            ps = connection.prepareStatement(IS_DOC_AVAILABLE);
            ps.setInt(++index, bankDetailsDto.getId());
            ps.setInt(++index, bankDetailsDto.getClaimNo());
            rs = ps.executeQuery();

            if (rs.next() && rs.getString("IS_DOC_AVAILABLE").equals(AppConstant.YES)) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while checking doc availability", e);
            throw new Exception("Error occurred while checking doc availability", e);
        }
        return false;
    }


    @Override
    public boolean isVerified(Connection connection, BankDetailsDto bankDetailsDto) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        int index = 0;
        try {
            ps = connection.prepareStatement(IS_BANK_DETAILS_VERIFIED);
            ps.setInt(++index, bankDetailsDto.getId());
            ps.setInt(++index, bankDetailsDto.getClaimNo());
            ps.setString(++index, AppConstant.YES);
            rs = ps.executeQuery();

            if (rs.next() && rs.getInt("count") > 0) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while checking bank details verification", e);
            throw new Exception("Error occurred while checking bank details verification", e);
        }
        return false;
    }


    @Override
    public Integer updateByID(Connection connection, BankDetailsDto bankDetailsDto) throws Exception {
        PreparedStatement ps;
        Integer updatedRecordId = 0; // Initialize the updated record ID
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_BANK_DETAILS);
            ps.setInt(++index, bankDetailsDto.getInstrumentType()); // instrument_type
            ps.setInt(++index, bankDetailsDto.getPayeeType()); // payee_type
            ps.setString(++index, bankDetailsDto.getPayeeName()); // payee_name
            ps.setString(++index, AppConstant.YES); // update_status
            ps.setString(++index, bankDetailsDto.getUploadStatus()); // upload_status
            ps.setString(++index, bankDetailsDto.getVerifyStatus()); // verify_status
            ps.setString(++index, bankDetailsDto.getLastUpdateUser()); // last_update_user
            ps.setTimestamp(++index, Timestamp.valueOf(LocalDateTime.now())); // last_update_date_time
            ps.setInt(++index, bankDetailsDto.getId()); // id
            ps.setInt(++index, bankDetailsDto.getClaimNo()); // claimNo
            int rowsUpdated = ps.executeUpdate(); // Execute the SQL update statement
            ps.close();

            if (rowsUpdated > 0) {
                // Retrieve the ID of the updated record
                updatedRecordId = bankDetailsDto.getId(); // Assuming 'id' is the primary key of the table
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating bank details", e);
            throw new Exception("Error occurred while updating bank details", e);
        }

        return updatedRecordId; // Return the updated record ID
    }



    @Override
    public BankDetailsDto findByID(Connection connection, BankDetailsDto bankDetailsDto) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        int index = 0;
        BankDetailsDto dto = new BankDetailsDto();

        try {
            ps = connection.prepareStatement(SELECT_CLAIM_BANK_DETAILS);
            ps.setInt(++index, bankDetailsDto.getClaimNo());
            ps.setInt(++index, bankDetailsDto.getInstrumentType());
            ps.setInt(++index, bankDetailsDto.getPayeeType());
            ps.setString(++index, bankDetailsDto.getPayeeName());
            rs = ps.executeQuery();

            while (rs.next()) {

                dto.setId(rs.getInt("id"));
                dto.setInstrumentType(rs.getInt("instrument_type"));
                dto.setPayeeType(rs.getInt("payee_type"));
                dto.setPayeeName(rs.getString("payee_name"));
                dto.setVerifyStatus(rs.getString("verify_status"));
                dto.setDocRefNo(1);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("No bank details found for the given criteria.");
        }
        return dto;
    }

    @Override
    public List<BankDetailsDto> findAllByID(Connection connection, BankDetailsDto bankDetailsDto) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        int index = 0;
        List<BankDetailsDto> bankDetailsDtos = new ArrayList<>();

        try {
            ps = connection.prepareStatement(SELECT_CLAIM_BANK_DETAILS_BY_CLAIM_NO);
            ps.setInt(++index, bankDetailsDto.getClaimNo());
            rs = ps.executeQuery();

            while (rs.next()) {
                BankDetailsDto dto = new BankDetailsDto();
                dto.setId(rs.getInt("id"));
                dto.setInstrumentType(rs.getInt("instrument_type"));
                dto.setPayeeType(rs.getInt("payee_type"));
                dto.setPayeeName(rs.getString("payee_name"));
                dto.setVerifyStatus(rs.getString("verify_status"));
                dto.setUploadStatus(rs.getString("upload_status"));
                dto.setUpdateStatus(rs.getString("update_status"));
                dto.setDocRefNo(rs.getInt("doc_ref_no"));
                bankDetailsDtos.add(dto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("No bank details found for the given criteria.");
        }

        return bankDetailsDtos;
    }

    @Override
    public List<BankDetailsDto> findByPayeeTypeAndPayeeName(Connection connection, BankDetailsDto bankDetailsDto) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        int index = 0;
        List<BankDetailsDto> bankDetailsDtos = new ArrayList<>();

        try {
            ps = connection.prepareStatement(SELECT_CLAIM_BANK_DETAILS_BY_PAYEE_TYPE_AND_PAYEE_NAME);
            ps.setInt(++index, bankDetailsDto.getClaimNo());
            ps.setInt(++index, bankDetailsDto.getPayeeType());
            ps.setString(++index, bankDetailsDto.getPayeeName());
            rs = ps.executeQuery();

            while (rs.next()) {
                BankDetailsDto dto = new BankDetailsDto();
                dto.setId(rs.getInt("id"));
                dto.setInstrumentType(rs.getInt("instrument_type"));
                dto.setPayeeType(rs.getInt("payee_type"));
                dto.setPayeeName(rs.getString("payee_name"));
                dto.setVerifyStatus(rs.getString("verify_status"));
                dto.setUploadStatus(rs.getString("upload_status"));
                dto.setUpdateStatus(rs.getString("update_status"));
                dto.setDocRefNo(rs.getInt("doc_ref_no"));
                bankDetailsDtos.add(dto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("No bank details found for the given criteria.");
        }

        return bankDetailsDtos;
    }

    @Override
    public BankDetailsDto findInsuredBankDetails(Connection connection, BankDetailsDto bankDetailsDto) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        int index = 0;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_BANK_DETAILS_OF_INSURED);
            ps.setString(++index, bankDetailsDto.getPolicyNumber());
            ps.setInt(++index, AppConstant.INSURED_PAYEE_TYPE);  // Assuming this is the placeholder for b.payee_type
            ps.setInt(++index, bankDetailsDto.getEndCount());
            ps.setString(++index, bankDetailsDto.getCustomerNIC());  // Assuming this is the placeholder for i.N_CLAIM_STATUS
            ps.setString(++index, "SETTLE");  // Assuming this is the placeholder for i.N_CLAIM_STATUS
            rs = ps.executeQuery();

            while (rs.next()) {
                bankDetailsDto.setId(rs.getInt("id"));
                bankDetailsDto.setInstrumentType(rs.getInt("instrument_type"));
                bankDetailsDto.setPayeeType(rs.getInt("payee_type"));
                bankDetailsDto.setPayeeName(rs.getString("payee_name"));
                bankDetailsDto.setVerifyStatus(rs.getString("verify_status"));
                bankDetailsDto.setUploadStatus(rs.getString("upload_status"));
                bankDetailsDto.setUpdateStatus(rs.getString("update_status"));
                bankDetailsDto.setDocRefNo(rs.getInt("doc_ref_no"));
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("No bank details found for the given criteria.");
        }

        return bankDetailsDto;
    }


}
