package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.BillCheckDao;
import com.misyn.mcms.claim.dto.BillCheckDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class BillCheckDaoImpl implements BillCheckDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(BillCheckDaoImpl.class);

    @Override
    public BillCheckDto insert(Connection connection, BillCheckDto billCheckDto) throws MisynJDBCException {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT);
            ps.setInt(++index, billCheckDto.getClaimNo());
            ps.setInt(++index, billCheckDto.getCalculationSheetNo());
            ps.setInt(++index, billCheckDto.getDocumentUploadRefNo());
            ps.setString(++index, billCheckDto.getBillCheckUserId());
            ps.setString(++index, billCheckDto.getBillCheckDateTime());
            if (ps.executeUpdate() > 0) {
                return billCheckDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e);
        }
        return null;
    }

    @Override
    public BillCheckDto update(Connection connection, BillCheckDto billCheckDto) throws MisynJDBCException {
        return null;
    }

    @Override
    public BillCheckDto searchOne(Connection connection, int txnId) {
        try {

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public List<Integer> getEngineeringDocuments(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Integer> engineeringDocuments = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ENGINEERING_DOCUMENTS);
            rs = ps.executeQuery();
            while (rs.next()) {
                engineeringDocuments.add(rs.getInt("n_doc_type_id"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return engineeringDocuments;
    }
}
