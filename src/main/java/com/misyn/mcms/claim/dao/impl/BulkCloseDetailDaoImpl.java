package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.BulkCloseDetailDao;
import com.misyn.mcms.claim.dto.BulkCloseDetailDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
public class BulkCloseDetailDaoImpl implements BulkCloseDetailDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(BulkCloseDetailDaoImpl.class);

    @Override
    public BulkCloseDetailDto insert(Connection connection, BulkCloseDetailDto bulkCloseDetailDto) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(SQL_INSERT)) {
            ps.setInt(1, bulkCloseDetailDto.getClaimNo());
            ps.setInt(2, bulkCloseDetailDto.getDays());
            ps.setString(3, bulkCloseDetailDto.getUserId());
            ps.setString(4, bulkCloseDetailDto.getDateTime());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return bulkCloseDetailDto;
    }

    @Override
    public boolean deleteByClaimNo(Connection connection, Integer claimNo) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(SQL_DELETE_BY_CLAIM_NO)) {
            ps.setInt(1, claimNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public BulkCloseDetailDto getBulkCloseDetails(Connection connection, Integer claimNoToSearch) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        BulkCloseDetailDto bulkCloseDetailDto = new BulkCloseDetailDto();
        try {
            ps = connection.prepareStatement(SQL_GET_BY_CLAIM_NO);
            ps.setInt(1, claimNoToSearch);
            rs = ps.executeQuery();
            if (rs.next()) {
                bulkCloseDetailDto.setId(rs.getInt("N_ID"));
                bulkCloseDetailDto.setClaimNo(claimNoToSearch);
                bulkCloseDetailDto.setDays(rs.getInt("N_DAYS"));
                bulkCloseDetailDto.setUserId(rs.getString("V_USER_ID"));
                bulkCloseDetailDto.setDateTime(
                        Utility.getDate(null != rs.getString("D_DATE_TIME") &&
                                !rs.getString("D_DATE_TIME").isEmpty() ?
                                rs.getString("D_DATE_TIME") :
                                AppConstant.DEFAULT_DATE_TIME, AppConstant.DATE_TIME_FORMAT)
                );
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return bulkCloseDetailDto;
    }
}
