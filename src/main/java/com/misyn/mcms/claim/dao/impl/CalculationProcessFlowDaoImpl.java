package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.CalculationProcessFlowDao;
import com.misyn.mcms.claim.dto.CalculationProcessFlowDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class CalculationProcessFlowDaoImpl implements CalculationProcessFlowDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(CalculationProcessFlowDaoImpl.class);

    @Override
    public CalculationProcessFlowDto insert(Connection connection, CalculationProcessFlowDto calculationProcessFlowDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT);
            ps.setInt(++index, calculationProcessFlowDto.getCalSheetId());
            ps.setInt(++index, calculationProcessFlowDto.getClaimNo());
            ps.setInt(++index, calculationProcessFlowDto.getCalSheetStatus());
            ps.setString(++index, calculationProcessFlowDto.getTask());
            ps.setString(++index, calculationProcessFlowDto.getAssignUserId());
            ps.setString(++index, calculationProcessFlowDto.getInpUserId());
            ps.setString(++index, calculationProcessFlowDto.getInpDateTime());
            if (ps.executeUpdate() > 0) {
                return calculationProcessFlowDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e);
        }
        return null;
    }

    @Override
    public List<CalculationProcessFlowDto> searchAllByCalSheetId(Connection connection, Integer calSheetId) {
        List<CalculationProcessFlowDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_SELECT_ALL);
            ps.setInt(1, calSheetId);
            rs = ps.executeQuery();
            while (rs.next()) {
                CalculationProcessFlowDto calculationProcessFlowDto = new CalculationProcessFlowDto();
                calculationProcessFlowDto.setTxnId(rs.getInt("t1.txn_id"));
                calculationProcessFlowDto.setCalSheetId(rs.getInt("t1.cal_sheet_id"));
                calculationProcessFlowDto.setClaimNo(rs.getInt("t1.claim_no"));
                calculationProcessFlowDto.setCalSheetStatus(rs.getInt("t1.cal_sheet_status"));
                calculationProcessFlowDto.setInpUserId(rs.getString("t1.inp_user_id"));
                calculationProcessFlowDto.setInpDateTime(rs.getString("t1.inp_date_time"));
                calculationProcessFlowDto.setAssignUserId(rs.getString("t1.assign_user_id"));
                calculationProcessFlowDto.setTask(rs.getString("t1.task"));
                calculationProcessFlowDto.setCalSheetStatusDesc(rs.getString("t2.v_status_desc"));
                calculationProcessFlowDto.setTaskCompletedDateTime(rs.getString("t1.inp_date_time"));
                if (!list.isEmpty()) {
                    CalculationProcessFlowDto prevCalculationProcessFlowDto = list.get(index - 1);
                    calculationProcessFlowDto.setInpDateTime(prevCalculationProcessFlowDto.getTaskCompletedDateTime());
                }
                list.add(calculationProcessFlowDto);
                index++;
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }
}
