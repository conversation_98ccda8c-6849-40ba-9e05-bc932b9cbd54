package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.CalculationSheetAssignMofaLevelDetailDao;
import com.misyn.mcms.claim.dto.CalculationSheetAssignMofaLevelDetailDto;
import com.misyn.mcms.claim.dto.CalculationSheetMofaLevelHistoryDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class CalculationSheetAssignMofaLevelDetailDaoImpl implements CalculationSheetAssignMofaLevelDetailDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(CalculationSheetAssignMofaLevelDetailDaoImpl.class);

    @Override
    public CalculationSheetAssignMofaLevelDetailDto insertMaster(Connection connection, CalculationSheetAssignMofaLevelDetailDto calculationSheetAssignMofaLevelDetailDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(INSERT_INTO_CALCULATION_SHEET_ASSIGN_MOFA_LEVEL_MST);
            ps.setInt(++index, calculationSheetAssignMofaLevelDetailDto.getCalsheetId());
            ps.setInt(++index, calculationSheetAssignMofaLevelDetailDto.getClaimNo());
            ps.setString(++index, calculationSheetAssignMofaLevelDetailDto.getInputUserId());
            ps.setInt(++index, calculationSheetAssignMofaLevelDetailDto.getInputMofaLevel());
            ps.setString(++index, calculationSheetAssignMofaLevelDetailDto.getAssignUserId());
            ps.setInt(++index, calculationSheetAssignMofaLevelDetailDto.getAssignMofaLevel());
            ps.setString(++index, calculationSheetAssignMofaLevelDetailDto.getAssignDatetime());
            if (ps.executeUpdate() > 0) {
                return calculationSheetAssignMofaLevelDetailDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System error");
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public CalculationSheetAssignMofaLevelDetailDto updateMaster(Connection connection, CalculationSheetAssignMofaLevelDetailDto calculationSheetAssignMofaLevelDetailDto) throws Exception {
        return null;
    }

    @Override
    public CalculationSheetAssignMofaLevelDetailDto insertTemporary(Connection connection, CalculationSheetAssignMofaLevelDetailDto calculationSheetAssignMofaLevelDetailDto) throws Exception {
        return null;
    }

    @Override
    public CalculationSheetAssignMofaLevelDetailDto updateTemporary(Connection connection, CalculationSheetAssignMofaLevelDetailDto calculationSheetAssignMofaLevelDetailDto) throws Exception {
        return null;
    }

    @Override
    public CalculationSheetAssignMofaLevelDetailDto insertHistory(Connection connection, CalculationSheetAssignMofaLevelDetailDto calculationSheetAssignMofaLevelDetailDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(INSERT_INTO_CALCULATION_SHEET_ASSIGN_MOFA_LEVEL_HST);
            ps.setInt(++index, calculationSheetAssignMofaLevelDetailDto.getCalsheetId());
            ps.setInt(++index, calculationSheetAssignMofaLevelDetailDto.getClaimNo());
            ps.setString(++index, calculationSheetAssignMofaLevelDetailDto.getInputUserId());
            ps.setInt(++index, calculationSheetAssignMofaLevelDetailDto.getInputMofaLevel());
            ps.setString(++index, calculationSheetAssignMofaLevelDetailDto.getAssignUserId());
            ps.setInt(++index, calculationSheetAssignMofaLevelDetailDto.getAssignMofaLevel());
            ps.setString(++index, calculationSheetAssignMofaLevelDetailDto.getAssignDatetime());
            if (ps.executeUpdate() > 0) {
                return calculationSheetAssignMofaLevelDetailDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System error");
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(DELETE_BY_CALSHEET_ID);
            ps.setObject(++index, id);
            if (ps.executeUpdate() > 0) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System error");
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public CalculationSheetAssignMofaLevelDetailDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public CalculationSheetAssignMofaLevelDetailDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<CalculationSheetAssignMofaLevelDetailDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public String getAssignMofaUserByAssignMofaLevelAndCalsheetId(Connection connection, Integer calSheetId, int levelCode, int accessUserType) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_ASSIGN_MOFA_USER_BY_ASSIGN_MOFA_LEVEL_AND_CALSHEET_ID);
            ps.setObject(1, levelCode);
            ps.setObject(2, calSheetId);
            ps.setObject(3, accessUserType);
            rs = ps.executeQuery();
            while (rs.next()) {
                return rs.getString("assign_user_id");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public List<CalculationSheetMofaLevelHistoryDto> getApprovedMofaListByCalsheetId(Connection connection, Integer calsheetId) {
        PreparedStatement ps;
        ResultSet rs;
        List<CalculationSheetMofaLevelHistoryDto> list = new ArrayList();

        try {
            ps = connection.prepareStatement(SELECT_APPROVED_MOFA_LEVEL);
            ps.setObject(1, calsheetId);
            rs = ps.executeQuery();

            while (rs.next()) {
                CalculationSheetMofaLevelHistoryDto dto = new CalculationSheetMofaLevelHistoryDto();

                dto.setMofaUserId(rs.getString("t1.input_user_id"));
                dto.setMofaLevel(rs.getInt("t1.input_user_mofa_level"));
                dto.setApprovedDateTime(null == rs.getString("t1.assign_datetime")
                        || AppConstant.STRING_EMPTY.equalsIgnoreCase(rs.getString("t1.assign_datetime"))
                        ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t1.assign_datetime"), AppConstant.DATE_TIME_FORMAT));
                dto.setFromLimit(rs.getBigDecimal("t2.from_limit"));
                dto.setToLimit(rs.getBigDecimal("t2.to_limit"));

                list.add(dto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }
}
