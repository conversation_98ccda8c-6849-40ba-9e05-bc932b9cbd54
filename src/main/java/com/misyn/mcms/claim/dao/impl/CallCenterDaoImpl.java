package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.CallCenterDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.PolicyChannelType;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class CallCenterDaoImpl extends AbstractBaseDao<CallCenterDaoImpl> implements CallCenterDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(CallCenterDaoImpl.class);

    private final static int PREFIX_CLAIM_NO = 500000000;

    @Override
    public ClaimsDto insertMaster(Connection connection, ClaimsDto claimsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            claimsDto.setRefNo(this.getSequenceClaimId(connection));
            claimsDto.setClaimNo(claimsDto.getRefNo() + PREFIX_CLAIM_NO);

            ps = connection.prepareStatement(INSERT_CLAIM_INFO_MAIN);
            ps.setInt(++index, claimsDto.getRefNo());
            ps.setInt(++index, claimsDto.getClaimNo());
            ps.setString(++index, claimsDto.getIntimationNo());
            ps.setString(++index, claimsDto.getIsfClaimNo());
            ps.setString(++index, claimsDto.getPreferredLanguage());
            ps.setString(++index, claimsDto.getPolicyBranch());
            ps.setString(++index, claimsDto.getPolicyType());
            ps.setString(++index, claimsDto.getPolicyNumber());
            ps.setString(++index, claimsDto.getVehicleNo());
            ps.setInt(++index, claimsDto.getReleshipInsurd());
            ps.setInt(++index, claimsDto.getReporterTitle());
            ps.setString(++index, claimsDto.getReporterName());
            ps.setString(++index, claimsDto.getReporterId());
            ps.setString(++index, claimsDto.getCliNo());
            ps.setString(++index, claimsDto.getOtherContNo());
            ps.setString(++index, claimsDto.getInsurdMobNo());
            ps.setString(++index, claimsDto.getIsSameReportDriver());
            ps.setInt(++index, claimsDto.getDriverStatus());
            ps.setInt(++index, claimsDto.getDriverTitle());
            ps.setString(++index, claimsDto.getDriverName());
            ps.setInt(++index, claimsDto.getDriverReleshipInsurd());
            ps.setString(++index, claimsDto.getDriverNic());
            ps.setString(++index, claimsDto.getDlNo());
            ps.setString(++index, claimsDto.getDriverLicenceType());
            ps.setString(++index, claimsDto.getReporterRemark());
            ps.setInt(++index, claimsDto.getIntimationType());
            ps.setInt(++index, claimsDto.getLateIntimateReason());
            ps.setString(++index, claimsDto.getAccidDate());
            ps.setString(++index, claimsDto.getAccidTime());
            ps.setString(++index, claimsDto.getDateTimeOfReport());
            ps.setString(++index, claimsDto.getTimeOfReport());
            ps.setString(++index, claimsDto.getIsCatEvent());
            ps.setInt(++index, claimsDto.getCatEventCode());
            ps.setString(++index, claimsDto.getCatEvent());
            ps.setInt(++index, claimsDto.getCauseOfLoss());
            ps.setString(++index, claimsDto.getAccidDesc());
            ps.setString(++index, claimsDto.getPlaceOfAccid());
            ps.setInt(++index, claimsDto.getDistrictCode());
            ps.setInt(++index, claimsDto.getNearestCity());
            ps.setString(++index, claimsDto.getCurrentLocation());
            ps.setInt(++index, claimsDto.getNearPoliceStation());
            ps.setString(++index, claimsDto.getIsFirstStatementReq());
            ps.setInt(++index, claimsDto.getFirstStatementReqReason());
            ps.setString(++index, claimsDto.getFirstStatementRemark());
            ps.setString(++index, claimsDto.getInspectionType());
            ps.setInt(++index, claimsDto.getInspectionTypeReason());
            ps.setInt(++index, claimsDto.getDraftReason());
            ps.setString(++index, claimsDto.getFollowCallUserId());
            ps.setString(++index, claimsDto.getFollowCallDoneDateTime());
            ps.setInt(++index, claimsDto.getFollowCallContactPersonTitle());
            ps.setString(++index, claimsDto.getFollowCallContactPersonName());
            ps.setString(++index, claimsDto.getFollowCallContactNumber());
            ps.setInt(++index, claimsDto.getFollowCallAgnetServiceRate());
            ps.setInt(++index, claimsDto.getFollowCallAssessorServiceRate());
            ps.setString(++index, claimsDto.getNcbProm());
            ps.setInt(++index, claimsDto.getNcbReason());
            ps.setString(++index, claimsDto.getNcbRemark());
            ps.setString(++index, claimsDto.getLomoProm());
            ps.setInt(++index, claimsDto.getLomoReason());
            ps.setString(++index, claimsDto.getCallUser());
            ps.setInt(++index, claimsDto.getClaimStatus());
            ps.setString(++index, claimsDto.getVehicleColor());
            ps.setString(++index, claimsDto.getCoverNoteNo());
            ps.setInt(++index, claimsDto.getPolRefNo());
            ps.setString(++index, claimsDto.getIsfsUpdateStatus());
            ps.setString(++index, claimsDto.getPolChkStatus());
            ps.setString(++index, claimsDto.getIsNoDamage());
            ps.setString(++index, claimsDto.getDamageRemark());
            ps.setString(++index, claimsDto.getIsHugeDamage());
            ps.setString(++index, claimsDto.getHugeRemark());
            ps.setString(++index, claimsDto.getDamageNotGiven());
            ps.setString(++index, claimsDto.getPrintLetter());
            ps.setString(++index, claimsDto.getNegPremOut());
            ps.setString(++index, claimsDto.getLandMark());
            ps.setInt(++index, claimsDto.getVehClsId());
            ps.setString(++index, claimsDto.getIsDoubt());
            ps.setString(++index, claimsDto.getIsDoubtRemark());
            ps.setString(++index, claimsDto.getIsFutherDamage());
            ps.setString(++index, claimsDto.getDraftRemark());
            ps.setInt(++index, claimsDto.getAccessusrType());
            ps.setString(++index, claimsDto.getIsFollowupCallDone());
            ps.setString(++index, claimsDto.getRecStatus());
            ps.setString(++index, claimsDto.getInpUser());
            ps.setString(++index, claimsDto.getInpTime());
            ps.setString(++index, claimsDto.getDriverDetailNotRelevant());
            ps.setString(++index, claimsDto.getDriverDetailSubmit());
            ps.setString(++index, claimsDto.getVehicleNoLastDigit());
            ps.setString(++index, claimsDto.getPolicyNumberLastDigit());
            ps.setString(++index, claimsDto.getPolicyDto().getPolicyChannelType());
            ps.setString(++index, claimsDto.getPriority());
            ps.setString(++index, claimsDto.getIsTheftAndFound());
            if (ps.executeUpdate() > 0) {
                this.updateSequenceClaimId(connection, claimsDto.getRefNo());
                return claimsDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ClaimsDto updateMaster(Connection connection, ClaimsDto claimsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_INFO_MAIN);
            ps.setString(++index, claimsDto.getIntimationNo());
            ps.setString(++index, claimsDto.getIsfClaimNo());
            ps.setString(++index, claimsDto.getPreferredLanguage());
            ps.setString(++index, claimsDto.getPolicyBranch());
            ps.setString(++index, claimsDto.getPolicyType());
            ps.setString(++index, claimsDto.getPolicyNumber());
            ps.setString(++index, claimsDto.getVehicleNo());
            ps.setInt(++index, claimsDto.getReleshipInsurd());
            ps.setInt(++index, claimsDto.getReporterTitle());
            ps.setString(++index, claimsDto.getReporterName());
            ps.setString(++index, claimsDto.getReporterId());
            ps.setString(++index, claimsDto.getCliNo());
            ps.setString(++index, claimsDto.getOtherContNo());
            ps.setString(++index, claimsDto.getInsurdMobNo());
            ps.setString(++index, claimsDto.getIsSameReportDriver());
            ps.setInt(++index, claimsDto.getDriverStatus());
            ps.setInt(++index, claimsDto.getDriverTitle());
            ps.setString(++index, claimsDto.getDriverName());
            ps.setInt(++index, claimsDto.getDriverReleshipInsurd());
            ps.setString(++index, claimsDto.getDriverNic());
            ps.setString(++index, claimsDto.getDlNo());
            ps.setString(++index, claimsDto.getDriverLicenceType());
            ps.setString(++index, claimsDto.getReporterRemark());
            ps.setInt(++index, claimsDto.getIntimationType());
            ps.setInt(++index, claimsDto.getLateIntimateReason());
            ps.setString(++index, claimsDto.getAccidDate());
            ps.setString(++index, claimsDto.getAccidTime());
            ps.setString(++index, claimsDto.getDateOfReport());
            ps.setString(++index, claimsDto.getTimeOfReport());
            ps.setString(++index, claimsDto.getIsCatEvent());
            ps.setInt(++index, claimsDto.getCatEventCode());
            ps.setString(++index, claimsDto.getCatEvent());
            ps.setInt(++index, claimsDto.getCauseOfLoss());
            ps.setString(++index, claimsDto.getAccidDesc());
            ps.setString(++index, claimsDto.getPlaceOfAccid());
            ps.setInt(++index, claimsDto.getDistrictCode());
            ps.setInt(++index, claimsDto.getNearestCity());
            ps.setString(++index, claimsDto.getCurrentLocation());
            ps.setInt(++index, claimsDto.getNearPoliceStation());
            ps.setString(++index, claimsDto.getIsFirstStatementReq());
            ps.setInt(++index, claimsDto.getFirstStatementReqReason());
            ps.setString(++index, claimsDto.getFirstStatementRemark());
            ps.setString(++index, claimsDto.getInspectionType());
            ps.setInt(++index, claimsDto.getInspectionTypeReason());
            ps.setInt(++index, claimsDto.getDraftReason());
            /*ps.setString(++index, claimsDto.getFollowCallUserId());
            ps.setString(++index, claimsDto.getFollowCallDoneDateTime());
            ps.setInt(++index, claimsDto.getFollowCallContactPersonTitle());
            ps.setString(++index, claimsDto.getFollowCallContactPersonName());
            ps.setString(++index, claimsDto.getFollowCallContactNumber());
            ps.setInt(++index, claimsDto.getFollowCallAgnetServiceRate());
            ps.setInt(++index, claimsDto.getFollowCallAssessorServiceRate());
            ps.setString(++index, claimsDto.getNcbProm());
            ps.setInt(++index, claimsDto.getNcbReason());
            ps.setString(++index, claimsDto.getNcbRemark());
            ps.setString(++index, claimsDto.getLomoProm());
            ps.setInt(++index, claimsDto.getLomoReason());*/
            ps.setString(++index, claimsDto.getCallUser());
            ps.setInt(++index, claimsDto.getClaimStatus());
            ps.setString(++index, claimsDto.getVehicleColor());
            ps.setString(++index, claimsDto.getCoverNoteNo());
            ps.setInt(++index, claimsDto.getPolRefNo());
            ps.setString(++index, claimsDto.getIsfsUpdateStatus());
            ps.setString(++index, claimsDto.getPolChkStatus());
            ps.setString(++index, claimsDto.getIsNoDamage());
            ps.setString(++index, claimsDto.getDamageRemark());
            ps.setString(++index, claimsDto.getIsHugeDamage());
            ps.setString(++index, claimsDto.getHugeRemark());
            ps.setString(++index, claimsDto.getDamageNotGiven());
            ps.setString(++index, claimsDto.getPrintLetter());
            ps.setString(++index, claimsDto.getNegPremOut());
            ps.setString(++index, claimsDto.getLandMark());
            ps.setInt(++index, claimsDto.getVehClsId());
            ps.setString(++index, claimsDto.getIsDoubt());
            ps.setString(++index, claimsDto.getIsDoubtRemark());
            ps.setString(++index, claimsDto.getIsFutherDamage());
            ps.setString(++index, claimsDto.getDraftRemark());
            ps.setInt(++index, claimsDto.getAccessusrType());
            ps.setString(++index, claimsDto.getRecStatus());
            ps.setString(++index, claimsDto.getInpUser());
            ps.setString(++index, claimsDto.getInpTime());
            ps.setString(++index, claimsDto.getVehicleNoLastDigit());
            ps.setString(++index, claimsDto.getPolicyNumberLastDigit());
            ps.setInt(++index, claimsDto.getClaimNo());
            if (ps.executeUpdate() > 0) {
                return claimsDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ClaimsDto insertTemporary(Connection connection, ClaimsDto claimsDto) throws Exception {
        return null;
    }

    @Override
    public ClaimsDto updateTemporary(Connection connection, ClaimsDto claimsDto) throws Exception {
        return null;
    }

    @Override
    public ClaimsDto insertHistory(Connection connection, ClaimsDto claimsDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimsDto searchMaster(Connection connection, Object claimId) throws Exception {
        ClaimsDto claimsDto = new ClaimsDto();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_INFO_MAIN_CLAIM_NO);
            ps.setInt(1, (Integer) claimId);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimsDto = getClaimsDto(rs);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return claimsDto;
    }

    @Override
    public ClaimsDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimsDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    public DataGridDto getClaimDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List claimList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();

        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();


        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND CONCAT(DATE_FORMAT(t1.D_DATE_OF_REPORT, '%Y-%m-%d'), TIME_FORMAT(t1.T_TIME_OF_REPORT,' %H:%i')) BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        final String SEL_SQL = "SELECT\n" +
                "	t1.N_POL_REF_NO,\n" +
                "	t1.n_ref_no,\n" +
                "	t1.V_VEHICLE_NO AS v_vehicle_number,\n" +
                "	t1.N_CLIM_NO,\n" +
                "	t1.N_INTIMATION_TYPE,\n" +
                "	t1.V_CALL_USER,\n" +
                "	t1.D_DATE_OF_REPORT,\n" +
                "	t1.T_TIME_OF_REPORT,\n" +
                "	t1.V_COVER_NOTE_NO,\n" +
                "	t1.V_REPORTER_NAME,\n" +
                "	t1.D_ACCID_DATE,\n" +
                "	t1.T_ACCID_TIME,\n" +
                "	t1.V_PLACE_OF_ACCID,\n" +
                "	t1.N_CLAIM_STATUS,\n" +
                "	t1.N_INTIMATION_TYPE,\n" +
                "	t1.V_POL_NUMBER,\n" +
                "	t1.N_FOLLOW_CALL_AGNET_SERVICE_RATE,\n" +
                "	t1.V_FOLLOW_CALL_ASSESSOR_SERVICE_RATE,\n" +
                "	t1.V_FOLLOW_CALL_USER_ID,\n" +
                "	t1.V_IS_FOLLOWUP_CALL_DONE,\n" +
                "   t1.V_PRIORITY,\n" +
                "   t1.V_CLI_NO,\n" +
                "   t1.V_OTHER_CONT_NO,\n" +
                "   t1.V_POLICY_CHANNEL_TYPE,\n" +
                "   t1.V_ISF_CLAIM_NO,\n" +
                "	t3.V_CUST_NAME,\n" +
                "	t3.V_CUST_NIC,\n" +
                "	t3.V_CHASSIS_NO,\n" +
                "	t3.V_ENGINE_NO,\n" +
                "	t2.v_status_desc\n" +
                "FROM\n" +
                "	claim_claim_info_main AS t1\n" +
                "INNER JOIN claim_vehicle_info_main AS t3 ON t1.N_POL_REF_NO = t3.N_POL_REF_NO \n" +
                "INNER JOIN claim_status_para AS t2 ON t1.N_CLAIM_STATUS = t2.n_ref_id ".concat(SQL_SEARCH).concat(SQL_ORDER);

        final String COUNT_SQL = "SELECT \n" +
                "                 	count(t1.N_CLIM_NO) AS cnt,   \n" +
                "                  t1.V_POL_NUMBER AS POL_NUMBER\n" +
                "                 FROM \n" +
                "                 	claim_claim_info_main AS t1  \n" +
                "                   INNER JOIN claim_vehicle_info_main AS t3 ON t1.N_POL_REF_NO = t3.N_POL_REF_NO \n" +
                "                 	INNER JOIN claim_status_para AS t2 ON t1.N_CLAIM_STATUS = t2.n_ref_id".concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ClaimsDto claimsDto = getClaimForView(rs);
                    claimsDto.setIndex(++index);
                    claimList.add(claimsDto);
                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(claimList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public Integer updateMasterFollowUp(Connection connection, ClaimsDto claimsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_FOLLOW_UP_CALL_INFO_MAIN);

            ps.setString(++index, claimsDto.getFollowCallUserId());
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, claimsDto.getFollowCallContactPersonTitle());
            ps.setString(++index, claimsDto.getFollowCallContactPersonName());
            ps.setString(++index, claimsDto.getFollowCallContactNumber());
            ps.setInt(++index, claimsDto.getFollowCallAgnetServiceRate());
            ps.setInt(++index, claimsDto.getFollowCallAssessorServiceRate());
            ps.setString(++index, claimsDto.getNcbProm());
            ps.setInt(++index, claimsDto.getNcbReason());
            ps.setString(++index, claimsDto.getNcbRemark());
            ps.setString(++index, claimsDto.getLomoProm());
            ps.setInt(++index, claimsDto.getLomoReason());
            ps.setString(++index, claimsDto.getCallUser());
            ps.setInt(++index, claimsDto.getClaimNo());
            return ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public ClaimsDto updatePolicyDetails(Connection connection, ClaimsDto claimsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_POLICY_BY_CLAIM_NO);
            ps.setString(++index, claimsDto.getPolicyBranch());
            ps.setString(++index, claimsDto.getPolicyType());
            ps.setString(++index, claimsDto.getPolicyNumber());
            ps.setString(++index, claimsDto.getVehicleNo());
            ps.setString(++index, claimsDto.getInsurdMobNo());
            ps.setInt(++index, claimsDto.getPolRefNo());
            ps.setString(++index, claimsDto.getInpUser());
            ps.setString(++index, claimsDto.getPolicyChannelType());
            ps.setString(++index, claimsDto.getVehicleNoLastDigit());
            ps.setString(++index, claimsDto.getPolicyNumberLastDigit());
            ps.setInt(++index, claimsDto.getClaimNo());

            if (ps.executeUpdate() > 0) {
                return claimsDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ClaimsDto updateClaimStatus(Connection connection, ClaimsDto claimsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CALL_STATUS);
            ps.setInt(++index, claimsDto.getClaimStatus());
            ps.setInt(++index, claimsDto.getClaimNo());


            if (ps.executeUpdate() > 0) {
                return claimsDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public List<ClaimsDto> getPolicyClaimList(Connection connection, String vehicleNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ClaimsDto> list = new ArrayList<>();

        try {
            ps = connection.prepareStatement(SELECT_CLAIM_INFO_MAIN_VEHICLE_NO);
            ps.setString(1, vehicleNo);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimsDto claimsDto = getClaimsDto(rs);
                claimsDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                claimsDto.setTotalAcr(rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT"));
                claimsDto.setClaimReserve(rs.getBigDecimal("t2.N_RESERVE_AMOUNT"));
                list.add(claimsDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
        }
        return list;
    }

    @Override
    public List<ClaimsDto> getPolicyClaimList(Connection connection, String vehicleNo, Integer claimNo, String dateOfReport) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ClaimsDto> list = new ArrayList<>();

        try {
            ps = connection.prepareStatement(SELECT_CLAIM_INFO_MAIN_BY_VEHICLE_NO_AND_CLAIM_NO);
            ps.setString(1, vehicleNo);
            ps.setInt(2, claimNo);
            ps.setString(3, dateOfReport);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimsDto claimsDto = getClaimsDto(rs);
                claimsDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                claimsDto.setTotalAcr(rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT"));
                claimsDto.setCloseStatus(rs.getString("t2.v_close_status"));
                list.add(claimsDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
        }
        return list;
    }

    @Override
    public List<PopupItemDto> getClaimNumberList(Connection connection, String policyNo, String vehicleNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<PopupItemDto> list = new ArrayList<>();
        String sql = AppConstant.EMPTY_STRING;
        String parameter = AppConstant.STRING_EMPTY;
        try {
            if (AppConstant.EMPTY_STRING.equalsIgnoreCase(vehicleNo)) {
                sql = SELECT_CLAIM_INFO_MAIN_BY_POLICY_NUMBER;
                parameter = policyNo;
            } else {
                sql = SELECT_CLAIM_INFO_MAIN_BY_VEHICLE_NUMBER;
                parameter = vehicleNo;
            }
            ps = connection.prepareStatement(sql);
            ps.setString(1, parameter);
            rs = ps.executeQuery();
            while (rs.next()) {
                PopupItemDto popupItemDto = new PopupItemDto();
                popupItemDto.setValue(String.valueOf(rs.getInt("t1.N_CLIM_NO")));
                popupItemDto.setLabel(String.valueOf(rs.getInt("t1.N_CLIM_NO")).concat(" - (").concat(rs.getString("t1.D_ACCID_DATE")).concat(")"));
                list.add(popupItemDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (SQLException e) {
                LOGGER.error(e.getMessage());
            }
        }
        return list;
    }

    @Override
    public boolean getClaimNoByVehicleNoAndAccidentDate(Connection connection, String vehicleNo, String accidentDate) {
        PreparedStatement ps = null;
        ResultSet rs = null;


        try {
            ps = connection.prepareStatement(SELECT_CLAIM_VEHICLE_BY_CLAIM_NO_AND_ACCIDENT_DATE);
            ps.setString(1, vehicleNo);
            ps.setString(2, accidentDate);
            rs = ps.executeQuery();
            if (rs.next()) {

                return rs.getInt("t1.N_CLIM_NO") > 0;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (SQLException e) {
                LOGGER.error(e.getMessage());
            }
        }
        return false;
    }

    @Override
    public ClaimsDto searchNotifation(Connection connection, Object claimId) throws Exception {
        ClaimsDto claimsDto = new ClaimsDto();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_UNDERWRITING_DETAIL_FOR_NOTIFICATION);
            ps.setInt(1, (Integer) claimId);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimsDto.getPolicyDto().setVehicleNumber(rs.getString("cvim.V_VEHICLE_NUMBER"));
                claimsDto.getPolicyDto().setCoverNoteNo(rs.getString("cvim.V_COVER_NOTE_NO"));
                claimsDto.setAccidDate(Utility.getDate(rs.getString("ccim.D_ACCID_DATE"), AppConstant.DATE_FORMAT).equals(AppConstant.DEFAULT_DATE)
                        ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("ccim.D_ACCID_DATE"), AppConstant.DATE_FORMAT));
                claimsDto.getPolicyDto().setPolicyChannelType(rs.getString("ccim.V_POLICY_CHANNEL_TYPE"));
                claimsDto.getPolicyDto().setCategoryDescription(rs.getString("cvim.V_CATEGORY_DESC"));
                claimsDto.getPolicyDto().setProduct(rs.getString("cvim.V_PRODUCT"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimsDto;
    }

    @Override
    public Boolean updateDoubtClaim(Connection connection, String status, String remark, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_DOUBT_CLAIM);
            ps.setString(++index, status);
            ps.setString(++index, remark);
            ps.setInt(++index, claimNo);

            if (ps.executeUpdate() > 0) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return false;
    }

    @Override
    public Integer updatePolicyStatusByRefNo(Connection connection, String status, Integer polRefNo) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_POLICY_STAUS_BY_POLICY_REF_NO);
            ps.setString(++index, status);
            ps.setInt(++index, polRefNo);

            index = ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return index;
    }

    @Override
    public Boolean updateLastIntimateDate(Connection connection, Integer polRefNo, String latestIntimateDate) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_LATEST_INTIMATE_DATE_BY_POL_REF_NO);
            ps.setString(++index, latestIntimateDate);
            ps.setInt(++index, polRefNo);

            if (ps.executeUpdate() > 0) {
                return true;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return false;
    }

    @Override
    public Boolean updateDriverDetails(Connection connection, ClaimsDto claimsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_DRIVER_DETAILS);
            ps.setInt(++index, claimsDto.getDriverStatus());
            ps.setInt(++index, claimsDto.getDriverTitle());
            ps.setString(++index, claimsDto.getDriverName());
            ps.setInt(++index, claimsDto.getDriverReleshipInsurd());
            ps.setString(++index, claimsDto.getDriverNic());
            ps.setString(++index, claimsDto.getDlNo());
            ps.setString(++index,claimsDto.getDriverLicenceType());
            ps.setString(++index, claimsDto.getReporterRemark());
            ps.setString(++index, claimsDto.getDriverDetailNotRelevant());
            ps.setString(++index, claimsDto.getDriverDetailSubmit());
            ps.setInt(++index, claimsDto.getClaimNo());
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return false;
    }

    @Override
    public DriverDetailDto getDriverDetails(Connection connection, Integer claimNo) throws Exception {
        DriverDetailDto driverDetailDto = new DriverDetailDto();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_DRIVER_DETAILS);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                driverDetailDto = setDriverDetails(rs);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        if (ps != null) {
            ps.close();
        }
        if (rs != null) {
            rs.close();
        }
        return driverDetailDto;
    }

    @Override
    public void markPriority(Connection connection, Integer claimNo, String priorityValue) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_PRIORITY);
            ps.setString(1, priorityValue);
            ps.setInt(2, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public String getVehicleNoByClaimNo(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        UserDto userDto = new UserDto();
        try {
            ps = connection.prepareStatement(SELECT_VEHICLE_NO_BY_CLAIM_NO);
            ps.setObject(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                return rs.getString("V_VEHICLE_NO");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public List<ClaimsDto> getPreviousClaims(Connection connection, String vehicleNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ClaimsDto> list = new ArrayList<>();

        try {
            ps = connection.prepareStatement(SELECT_PREVIOUS_CLAIMS);
            ps.setString(1, vehicleNo);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimsDto claimsDto = new ClaimsDto();
                claimsDto.setClaimNo(rs.getInt("N_CLIM_NO"));
                claimsDto.setPolicyNumber(rs.getString("V_POL_NUMBER"));
                claimsDto.setAccidDate(Utility.getDate(rs.getString("t1.D_ACCID_DATE"), AppConstant.DATE_FORMAT).equals(AppConstant.DEFAULT_DATE)
                        ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t1.D_ACCID_DATE"), AppConstant.DATE_FORMAT));
                claimsDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                claimsDto.setTotalAcr(rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT"));
                claimsDto.setClaimReserve(rs.getBigDecimal("t2.N_RESERVE_AMOUNT"));
                claimsDto.setCloseStatus(rs.getString("V_CLOSE_STATUS"));
                list.add(claimsDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
        }
        return list;
    }

    @Override
    public PreviousClaimsDto getPreviousClaimDetails(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(GET_PREVIOUS_CLAIM_DETAILS);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                PreviousClaimsDto previousClaimsDto = new PreviousClaimsDto();
                previousClaimsDto.setInspectionType("N/A");
                previousClaimsDto.setJobNo("N/A");
                previousClaimsDto.setVehicleNo(rs.getString("t2.V_VEHICLE_NUMBER"));
                previousClaimsDto.setPolicyNo(rs.getString("t2.V_POL_NUMBER"));
                previousClaimsDto.setDateOfAccident(rs.getString("t1.D_ACCID_DATE"));
                return previousClaimsDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return null;
    }

    @Override
    public String getPolicyChannelType(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        String policyChannelType = AppConstant.STRING_EMPTY;
        try {
            ps = connection.prepareStatement(GET_POLICY_CHANNEL_TYPE);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                policyChannelType = rs.getString("V_POLICY_CHANNEL_TYPE");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return policyChannelType;
    }

    @Override
    public String getPolicyChannelTypeByVehicleNo(Connection connection, String vehicleNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        String policyChannelType = AppConstant.STRING_EMPTY;
        try {
            ps = connection.prepareStatement(GET_POLICY_CHANNEL_TYPE_BY_VEHICLE_NO);
            ps.setString(1, vehicleNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                policyChannelType = rs.getString("V_POLICY_CHANNEL_TYPE");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return policyChannelType;
    }

    @Override
    public boolean markTheftAndFound(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(MARK_THEFT_AND_FOUND);
            ps.setInt(1, claimNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean isTheftClaim(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        boolean isTheft = false;
        try {
            ps = connection.prepareStatement(IS_THEFT_CLAIM);
            ps.setInt(1, AppConstant.THEFT);
            ps.setInt(2, claimNo);
            rs = ps.executeQuery();
            isTheft = rs.next();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return isTheft;
    }

    private DriverDetailDto setDriverDetails(ResultSet rs) {
        DriverDetailDto driverDetailDto = new DriverDetailDto();
        try {
            driverDetailDto.setClaimNo(rs.getInt("N_CLIM_NO"));
            driverDetailDto.setDriverStatus(rs.getInt("N_DRIVER_STATUS"));
            driverDetailDto.setDriverTitle(rs.getInt("N_DRIVER_TITLE"));
            driverDetailDto.setDriverName(rs.getString("V_DRIVER_NAME"));
            driverDetailDto.setDriverReleshipInsurd(rs.getInt("N_DRIVER_RELESHIP_INSURD"));
            driverDetailDto.setDriverNic(rs.getString("V_DRIVER_NIC"));
            driverDetailDto.setDlNo(rs.getString("V_DL_NO"));
            driverDetailDto.setDriverLicenceType(rs.getString("V_DL_TYPE"));
            driverDetailDto.setReporterRemark(rs.getString("V_REPORTER_REMARK"));
            driverDetailDto.setDriverDetailNotRelevant(rs.getString("V_DRIVER_DETAIL_NOT_RELEVANT"));
            driverDetailDto.setDriverDetailSubmit(rs.getString("V_DRIVER_DETAIL_SUBMIT"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return driverDetailDto;
    }

    private ClaimsDto getClaimForView(ResultSet rs) {
        ClaimsDto claimsDto = new ClaimsDto();
        try {
            claimsDto.setPolRefNo(rs.getInt("t1.N_POL_REF_NO"));
            claimsDto.setRefNo(rs.getInt("t1.N_REF_NO"));
            claimsDto.setVehicleNo(rs.getString("v_vehicle_number"));
            claimsDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
            claimsDto.setIntimationType(rs.getInt("t1.N_INTIMATION_TYPE"));
            claimsDto.setCallUser(rs.getString("t1.V_CALL_USER"));
            claimsDto.setDateOfReport(rs.getString("t1.D_DATE_OF_REPORT"));
            claimsDto.setTimeOfReport(Utility.getCustomDateFormat(rs.getString("t1.T_TIME_OF_REPORT"), AppConstant.TIME_FORMAT, AppConstant.TIME_WITH_OUT_SECOND_FORMAT));
            claimsDto.setCoverNoteNo(rs.getString("t1.V_COVER_NOTE_NO"));
            claimsDto.setReporterName(rs.getString("t1.V_REPORTER_NAME"));
            claimsDto.setPolicyNumber(rs.getString("t1.V_POL_NUMBER"));
            claimsDto.setAccidDate(rs.getString("t1.D_ACCID_DATE"));
            claimsDto.setAccidTime(Utility.getCustomDateFormat(rs.getString("t1.T_ACCID_TIME"), AppConstant.TIME_FORMAT, AppConstant.TIME_WITH_OUT_SECOND_FORMAT));
            claimsDto.setPlaceOfAccid(rs.getString("t1.V_PLACE_OF_ACCID"));
            claimsDto.setClaimStatus(rs.getInt("t1.N_CLAIM_STATUS"));
            claimsDto.setClaimStatusDesc(rs.getString("t2.v_status_desc"));
            claimsDto.setChassisNo(rs.getString("t3.V_CHASSIS_NO"));
            claimsDto.setFollowCallAgnetServiceRate(rs.getInt("t1.N_FOLLOW_CALL_AGNET_SERVICE_RATE"));
            claimsDto.setFollowCallAssessorServiceRate(rs.getInt("t1.V_FOLLOW_CALL_ASSESSOR_SERVICE_RATE"));
            claimsDto.setFollowCallUserId(rs.getString("t1.V_FOLLOW_CALL_USER_ID"));
            claimsDto.setIsFollowupCallDone(rs.getString("t1.V_IS_FOLLOWUP_CALL_DONE"));
            claimsDto.setPriority(null == rs.getString("t1.V_PRIORITY") || rs.getString("t1.V_PRIORITY").isEmpty() ? AppConstant.PRIORITY_NORMAL : rs.getString("t1.V_PRIORITY"));
            claimsDto.setIsfClaimNo(null == rs.getString("t1.V_ISF_CLAIM_NO") || rs.getString("t1.V_ISF_CLAIM_NO").isEmpty() ? AppConstant.EMPTY_STRING : rs.getString("t1.V_ISF_CLAIM_NO"));
            claimsDto.setCliNo(rs.getString("t1.V_CLI_NO"));
            claimsDto.setOtherContNo(rs.getString("t1.V_OTHER_CONT_NO"));
            claimsDto.setPolicyChannelType(rs.getString("t1.V_POLICY_CHANNEL_TYPE"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimsDto;
    }

    private ClaimsDto getClaimsDto(ResultSet rs) {
        ClaimsDto claimsDto = new ClaimsDto();
        try {
            claimsDto.setRefNo(rs.getInt("t1.N_REF_NO"));
            claimsDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
            claimsDto.setIntimationNo(rs.getString("t1.V_INTIMATION_NO"));
            claimsDto.setIsfClaimNo(rs.getString("t1.V_ISF_CLAIM_NO"));
            claimsDto.setPreferredLanguage(rs.getString("t1.V_PREFERRED_LANGUAGE"));
            claimsDto.setPolicyBranch(rs.getString("t1.V_POL_BRANCH"));
            claimsDto.setPolicyType(rs.getString("t1.V_POL_TYPE"));
            claimsDto.setPolicyNumber(rs.getString("t1.V_POL_NUMBER"));
            claimsDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
            claimsDto.setReleshipInsurd(rs.getInt("t1.N_RELESHIP_INSURD"));
            claimsDto.setReporterTitle(rs.getInt("t1.N_REPORTER_TITLE"));
            claimsDto.setReporterName(rs.getString("t1.V_REPORTER_NAME"));
            claimsDto.setReporterId(rs.getString("t1.V_REPORTER_ID"));
            claimsDto.setCliNo(rs.getString("t1.V_CLI_NO"));
            claimsDto.setOtherContNo(rs.getString("t1.V_OTHER_CONT_NO"));
            claimsDto.setInsurdMobNo(rs.getString("t1.V_INSURD_MOB_NO"));
            claimsDto.setIsSameReportDriver(rs.getString("t1.V_IS_SAME_REPORT_DRIVER"));
            claimsDto.setDriverStatus(rs.getInt("t1.N_DRIVER_STATUS"));
            claimsDto.setDriverTitle(rs.getInt("t1.N_DRIVER_TITLE"));
            claimsDto.setDriverName(rs.getString("t1.V_DRIVER_NAME"));
            claimsDto.setDriverReleshipInsurd(rs.getInt("t1.N_DRIVER_RELESHIP_INSURD"));
            claimsDto.setDriverNic(rs.getString("t1.V_DRIVER_NIC"));
            claimsDto.setDlNo(rs.getString("t1.V_DL_NO"));
            claimsDto.setDriverLicenceType(rs.getString("t1.V_DL_TYPE"));
            claimsDto.setReporterRemark(rs.getString("t1.V_REPORTER_REMARK"));
            claimsDto.setIntimationType(rs.getInt("t1.N_INTIMATION_TYPE"));
            claimsDto.setLateIntimateReason(rs.getInt("t1.N_LATE_INTIMATE_REASON"));
            claimsDto.setAccidDate(Utility.getDate(rs.getString("t1.D_ACCID_DATE"), AppConstant.DATE_FORMAT).equals(AppConstant.DEFAULT_DATE)
                    ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t1.D_ACCID_DATE"), AppConstant.DATE_FORMAT));
            claimsDto.setAccidTime(Utility.getCustomDateFormat(rs.getString("t1.T_ACCID_TIME"), AppConstant.TIME_FORMAT, AppConstant.TIME_WITH_OUT_SECOND_FORMAT));
            claimsDto.setDateOfReport(rs.getString("t1.D_DATE_OF_REPORT"));
            claimsDto.setTimeOfReport(Utility.getCustomDateFormat(rs.getString("t1.T_TIME_OF_REPORT"), AppConstant.TIME_FORMAT, AppConstant.TIME_WITH_OUT_SECOND_FORMAT));
            claimsDto.setIsCatEvent(rs.getString("t1.V_IS_CAT_EVENT"));
            claimsDto.setCatEventCode(rs.getInt("t1.N_CAT_EVENT_CODE"));
            claimsDto.setCatEvent(rs.getString("t1.V_CAT_EVENT"));
            claimsDto.setCauseOfLoss(rs.getInt("t1.N_CAUSE_OF_LOSS"));
            claimsDto.setAccidDesc(rs.getString("t1.V_ACCID_DESC"));
            claimsDto.setPlaceOfAccid(rs.getString("t1.V_PLACE_OF_ACCID"));
            claimsDto.setDistrictCode(rs.getInt("t1.V_DISTRICT_CODE"));
            claimsDto.setNearestCity(rs.getInt("t1.N_NEAREST_CITY"));
            claimsDto.setCurrentLocation(rs.getString("t1.V_CURRENT_LOCATION"));
            claimsDto.setNearPoliceStation(rs.getInt("t1.N_NEAR_POLICE_STATION"));
            claimsDto.setIsFirstStatementReq(rs.getString("t1.V_IS_FIRST_STATEMENT_REQ"));
            claimsDto.setFirstStatementReqReason(rs.getInt("t1.N_FIRST_STATEMENT_REQ_REASON"));
            claimsDto.setFirstStatementRemark(rs.getString("t1.V_FIRST_STATEMENT_REMARK"));
            claimsDto.setInspectionType(rs.getString("t1.V_INSPECTION_TYPE"));
            claimsDto.setInspectionTypeReason(rs.getInt("t1.N_INSPECTION_TYPE_REASON"));
            claimsDto.setDraftReason(rs.getInt("t1.N_DRAFT_REASON"));
            claimsDto.setFollowCallUserId(rs.getString("t1.V_FOLLOW_CALL_USER_ID"));
            claimsDto.setFollowCallDoneDateTime(rs.getString("t1.D_FOLLOW_CALL_DONE_DATE_TIME"));
            claimsDto.setFollowCallContactPersonTitle(rs.getInt("t1.N_FOLLOW_CALL_CONTACT_PERSON_TITLE"));
            claimsDto.setFollowCallContactPersonName(rs.getString("t1.V_FOLLOW_CALL_CONTACT_PERSON_NAME"));
            claimsDto.setFollowCallContactNumber(rs.getString("t1.V_FOLLOW_CALL_CONTACT_NUMBER"));
            claimsDto.setFollowCallAgnetServiceRate(rs.getInt("t1.N_FOLLOW_CALL_AGNET_SERVICE_RATE"));
            claimsDto.setFollowCallAssessorServiceRate(rs.getInt("t1.V_FOLLOW_CALL_ASSESSOR_SERVICE_RATE"));
            claimsDto.setNcbProm(rs.getString("t1.V_NCB_PROM"));
            claimsDto.setNcbReason(rs.getInt("t1.N_NCB_REASON"));
            claimsDto.setNcbRemark(rs.getString("t1.V_NCB_REMARK"));
            claimsDto.setLomoProm(rs.getString("t1.V_LOMO_PROM"));
            claimsDto.setLomoReason(rs.getInt("t1.N_LOMO_REASON"));
            claimsDto.setCallUser(rs.getString("t1.V_CALL_USER"));
            claimsDto.setClaimStatus(rs.getInt("t1.N_CLAIM_STATUS"));
            claimsDto.setVehicleColor(rs.getString("t1.V_VEHICLE_COLOR"));
            claimsDto.setCoverNoteNo(rs.getString("t1.V_COVER_NOTE_NO"));
            claimsDto.setPolRefNo(rs.getInt("t1.N_POL_REF_NO"));
            claimsDto.setIsfsUpdateStatus(rs.getString("t1.V_ISFS_UPDATE_STATUS"));
            claimsDto.setPolChkStatus(rs.getString("t1.V_POL_CHK_STATUS"));
            claimsDto.setIsNoDamage(rs.getString("t1.V_IS_NO_DAMAGE"));
            claimsDto.setDamageRemark(rs.getString("t1.V_DAMAGE_REMARK"));
            claimsDto.setIsHugeDamage(rs.getString("t1.V_IS_HUGE_DAMAGE"));
            claimsDto.setHugeRemark(rs.getString("t1.V_HUGE_REMARK"));
            claimsDto.setDamageNotGiven(rs.getString("t1.V_DAMAGE_NOT_GIVEN"));
            claimsDto.setPrintLetter(rs.getString("t1.V_PRINT_LETTER"));
            claimsDto.setNegPremOut(rs.getString("t1.V_NEG_PREM_OUT"));
            claimsDto.setLandMark(rs.getString("t1.V_LAND_MARK"));
            claimsDto.setVehClsId(rs.getInt("t1.N_VEH_CLS_ID"));
            claimsDto.setIsDoubt(rs.getString("t1.V_IS_DOUBT"));
            claimsDto.setIsDoubtRemark(rs.getString("t1.V_IS_DOUBT_REMARK"));
            claimsDto.setIsFutherDamage(rs.getString("t1.V_IS_FUTHER_DAMAGE"));
            claimsDto.setDraftRemark(rs.getString("t1.V_DRAFT_REMARK"));
            claimsDto.setAccessusrType(rs.getInt("t1.N_ACCESSUSR_TYPE"));
            claimsDto.setRecStatus(rs.getString("t1.V_REC_STATUS"));
            claimsDto.setInpUser(rs.getString("t1.V_INPUSER"));
            claimsDto.setInpTime(rs.getString("t1.D_INPTIME"));
            claimsDto.setIsFollowupCallDone(rs.getString("t1.V_IS_FOLLOWUP_CALL_DONE"));
            claimsDto.setDriverDetailNotRelevant(null == rs.getString("t1.V_DRIVER_DETAIL_NOT_RELEVANT") ? "N" : rs.getString("t1.V_DRIVER_DETAIL_NOT_RELEVANT"));
            claimsDto.setDriverDetailSubmit(null == rs.getString("t1.V_DRIVER_DETAIL_SUBMIT") ? "N" : rs.getString("t1.V_DRIVER_DETAIL_SUBMIT"));
            claimsDto.setPolicyChannelType(null == rs.getString("t1.V_POLICY_CHANNEL_TYPE") || rs.getString("t1.V_POLICY_CHANNEL_TYPE").trim().isEmpty() ? PolicyChannelType.CONVENTIONAL.name() : rs.getString("t1.V_POLICY_CHANNEL_TYPE"));
            claimsDto.setIsTheftAndFound(null == rs.getString("t1.V_IS_THEFT_AND_FOUND") || rs.getString("t1.V_IS_THEFT_AND_FOUND").isEmpty() ? AppConstant.NO : rs.getString("t1.V_IS_THEFT_AND_FOUND"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimsDto;
    }

    private synchronized Integer getSequenceClaimId(Connection conn) {
        PreparedStatement ps = null;
        Integer sequenceId = 0;
        try {
            ps = conn.prepareStatement(SELECT_CLAIM_SEQUENCE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    sequenceId = rs.getInt("claim_no");
                    sequenceId++;
                }
                rs.close();
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return sequenceId;
    }

    private synchronized void updateSequenceClaimId(Connection connection, Integer sequenceId) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_SEQUENCE);
            ps.setInt(1, sequenceId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
    }
}
