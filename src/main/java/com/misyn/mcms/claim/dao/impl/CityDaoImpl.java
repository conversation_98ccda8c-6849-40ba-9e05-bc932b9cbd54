package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.CityDao;
import com.misyn.mcms.claim.dto.CityDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class CityDaoImpl extends BaseAbstract<CityDaoImpl> implements CityDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(CityDaoImpl.class);

    @Override
    public CityDto insertMaster(Connection connection, CityDto cityDto) throws Exception {
        return null;
    }

    @Override
    public CityDto insertTemporary(Connection connection, CityDto cityDto) throws Exception {
        return null;
    }

    @Override
    public CityDto insertHistory(Connection connection, CityDto cityDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public CityDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        CityDto cityDto = new CityDto();
        try {
            ps = connection.prepareStatement(SELECT_ALL_FROM_CITY_MASTER_BY_CITY_CODE);
            ps.setInt(1, Integer.parseInt(String.valueOf(id)));

            rs = ps.executeQuery();
            if (rs.next()) {
                cityDto = getCityDto(rs);
            }
        } catch (Exception e) {

        }
        return cityDto;

    }

    @Override
    public CityDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<CityDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<CityDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_FROM_CITY_MASTER);

            rs = ps.executeQuery();
            while (rs.next()) {
                CityDto cityDto = getCityDto(rs);
                list.add(cityDto);

            }
        } catch (Exception e) {

        }
        return list;
    }

    @Override
    public CityDto updateMaster(Connection connection, CityDto cityDto) throws Exception {
        return null;
    }

    @Override
    public CityDto updateTemporary(Connection connection, CityDto cityDto) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    private CityDto getCityDto(ResultSet rst) {
        CityDto cityDto = new CityDto();
        try {
            cityDto.setDistrictCode(rst.getString("V_DISTRICT_CODE"));
            cityDto.setGramaCode(rst.getInt("N_GRAMA_CODE"));
            cityDto.setGramaName(rst.getString("V_GRAMA_NAME"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return cityDto;
    }

    @Override
    public List<CityDto> getCityListByDistrictCode(Connection connection, String divisionCode) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<CityDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_FROM_CITY_MASTER_BY_DIVISION_CODE);
            ps.setString(1, divisionCode);

            rs = ps.executeQuery();
            while (rs.next()) {
                CityDto cityDto = getCityDto(rs);
                list.add(cityDto);
            }
        } catch (Exception e) {

        }
        return list;
    }
}
