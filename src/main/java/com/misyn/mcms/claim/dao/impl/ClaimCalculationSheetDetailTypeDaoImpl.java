package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.ClaimCalculationSheetDetailTypeDao;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetDetailTypeDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class ClaimCalculationSheetDetailTypeDaoImpl implements ClaimCalculationSheetDetailTypeDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimCalculationSheetDetailTypeDaoImpl.class);

    @Override
    public ClaimCalculationSheetDetailTypeDto insertMaster(Connection connection, ClaimCalculationSheetDetailTypeDto claimCalculationSheetDetailTypeDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_DETAIL_TYPE_INSERT);
            ps.setInt(++index, claimCalculationSheetDetailTypeDto.getNCalSheetDetailTypeId());
            ps.setString(++index, claimCalculationSheetDetailTypeDto.getVCalSheetDetailTypeDesc());

            if (ps.executeUpdate() > 0) {
                return claimCalculationSheetDetailTypeDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetDetailTypeDto updateMaster(Connection connection, ClaimCalculationSheetDetailTypeDto claimCalculationSheetDetailTypeDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_DETAIL_TYPE_UPDATE);
            ps.setString(++index, claimCalculationSheetDetailTypeDto.getVCalSheetDetailTypeDesc());
            ps.setInt(++index, claimCalculationSheetDetailTypeDto.getNCalSheetDetailTypeId());

            if (ps.executeUpdate() > 0) {
                return claimCalculationSheetDetailTypeDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetDetailTypeDto insertTemporary(Connection connection, ClaimCalculationSheetDetailTypeDto claimCalculationSheetDetailTypeDto) throws Exception {
        return null;
    }

    @Override
    public ClaimCalculationSheetDetailTypeDto updateTemporary(Connection connection, ClaimCalculationSheetDetailTypeDto claimCalculationSheetDetailTypeDto) throws Exception {
        return null;
    }

    @Override
    public ClaimCalculationSheetDetailTypeDto insertHistory(Connection connection, ClaimCalculationSheetDetailTypeDto claimCalculationSheetDetailTypeDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_DETAIL_TYPE_DELETE);
            ps.setObject(1, id);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimCalculationSheetDetailTypeDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimCalculationSheetDetailTypeDto claimCalculationSheetDetailTypeDto = new ClaimCalculationSheetDetailTypeDto();
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_DETAIL_TYPE_SEARCH);
            ps.setObject(1, id);
            rs = ps.executeQuery();
            if (rs.next()) {

                claimCalculationSheetDetailTypeDto.setNCalSheetDetailTypeId(rs.getInt("N_CAL_SHEET_DETAIL_TYPE_ID"));
                claimCalculationSheetDetailTypeDto.setVCalSheetDetailTypeDesc(rs.getString("V_CAL_SHEET_DETAIL_TYPE_DESC"));
                return claimCalculationSheetDetailTypeDto;
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetDetailTypeDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimCalculationSheetDetailTypeDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rst;
        List<ClaimCalculationSheetDetailTypeDto> claimCalculationSheetDetailTypeDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_DETAIL_TYPE_SEARCH_ALL);
            rst = ps.executeQuery();

            while (rst.next()) {
                ClaimCalculationSheetDetailTypeDto claimCalculationSheetDetailTypeDto = new ClaimCalculationSheetDetailTypeDto();
                claimCalculationSheetDetailTypeDto.setNCalSheetDetailTypeId(rst.getInt("N_CAL_SHEET_DETAIL_TYPE_ID"));
                claimCalculationSheetDetailTypeDto.setVCalSheetDetailTypeDesc(rst.getString("V_CAL_SHEET_DETAIL_TYPE_DESC"));

                claimCalculationSheetDetailTypeDtoList.add(claimCalculationSheetDetailTypeDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimCalculationSheetDetailTypeDtoList;
    }


    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }
}
