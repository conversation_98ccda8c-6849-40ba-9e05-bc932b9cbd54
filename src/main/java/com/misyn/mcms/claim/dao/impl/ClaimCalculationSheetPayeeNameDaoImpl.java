package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.ClaimCalculationSheetPayeeNameDao;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetPayeeNameDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
public class ClaimCalculationSheetPayeeNameDaoImpl implements ClaimCalculationSheetPayeeNameDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimCalculationSheetPayeeNameDaoImpl.class);

    @Override
    public ClaimCalculationSheetPayeeNameDto insertMaster(Connection connection, ClaimCalculationSheetPayeeNameDto claimCalculationSheetPayeeNameDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_PAYEE_NAME_INSERT);
            ps.setString(++index, claimCalculationSheetPayeeNameDto.getPayeeName());
            ps.setString(++index, claimCalculationSheetPayeeNameDto.getStatus());

            if (ps.executeUpdate() > 0) {
                return claimCalculationSheetPayeeNameDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetPayeeNameDto updateMaster(Connection connection, ClaimCalculationSheetPayeeNameDto claimCalculationSheetPayeeNameDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_PAYEE_NAME_UPDATE);
            ps.setString(++index, claimCalculationSheetPayeeNameDto.getPayeeName());
            ps.setString(++index, claimCalculationSheetPayeeNameDto.getStatus());
            ps.setInt(++index, claimCalculationSheetPayeeNameDto.getCalSheetPayeeNameId());

            if (ps.executeUpdate() > 0) {
                return claimCalculationSheetPayeeNameDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetPayeeNameDto insertTemporary(Connection connection, ClaimCalculationSheetPayeeNameDto claimCalculationSheetPayeeNameDto) throws Exception {
        return null;
    }

    @Override
    public ClaimCalculationSheetPayeeNameDto updateTemporary(Connection connection, ClaimCalculationSheetPayeeNameDto claimCalculationSheetPayeeNameDto) throws Exception {
        return null;
    }

    @Override
    public ClaimCalculationSheetPayeeNameDto insertHistory(Connection connection, ClaimCalculationSheetPayeeNameDto claimCalculationSheetPayeeNameDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_PAYEE_NAME_DELETE);
            ps.setObject(1, id);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimCalculationSheetPayeeNameDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimCalculationSheetPayeeNameDto claimCalculationSheetPayeeNameDto = new ClaimCalculationSheetPayeeNameDto();
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_PAYEE_NAME_SEARCH);
            ps.setObject(1, id);
            rs = ps.executeQuery();
            if (rs.next()) {

                claimCalculationSheetPayeeNameDto.setCalSheetPayeeNameId(rs.getInt("N_CAL_SHEET_PAYEE_NAME_ID"));
                claimCalculationSheetPayeeNameDto.setPayeeName(rs.getString("V_PAYEE_NAME"));
                claimCalculationSheetPayeeNameDto.setStatus(rs.getString("V_STATUS"));
                return claimCalculationSheetPayeeNameDto;
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetPayeeNameDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimCalculationSheetPayeeNameDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimCalculationSheetPayeeNameDto> claimCalculationSheetPayeeNameDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_PAYEE_NAME_SEARCH_ALL);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimCalculationSheetPayeeNameDto claimCalculationSheetPayeeNameDto = new ClaimCalculationSheetPayeeNameDto();
                claimCalculationSheetPayeeNameDto.setCalSheetPayeeNameId(rs.getInt("N_CAL_SHEET_PAYEE_NAME_ID"));
                claimCalculationSheetPayeeNameDto.setPayeeName(rs.getString("V_PAYEE_NAME"));
                claimCalculationSheetPayeeNameDto.setStatus(rs.getString("V_STATUS"));

                claimCalculationSheetPayeeNameDtoList.add(claimCalculationSheetPayeeNameDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimCalculationSheetPayeeNameDtoList;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public Map<Integer, String> getInstrumentTypeDetails(Connection connection) {
        return null;
    }
}
