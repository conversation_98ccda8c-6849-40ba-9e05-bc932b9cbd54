package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.ClaimCalculationSheetTypeDao;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetTypeDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class ClaimCalculationSheetTypeDaoImpl implements ClaimCalculationSheetTypeDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimCalculationSheetTypeDaoImpl.class);

    @Override
    public ClaimCalculationSheetTypeDto insertMaster(Connection connection, ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_TYPE_INSERT);
            ps.setString(++index, claimCalculationSheetTypeDto.getVCalSheetTypeDesc());

            if (ps.executeUpdate() > 0) {
                return claimCalculationSheetTypeDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetTypeDto updateMaster(Connection connection, ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_TYPE_UPDATE);
            ps.setString(++index, claimCalculationSheetTypeDto.getVCalSheetTypeDesc());
            ps.setInt(++index, claimCalculationSheetTypeDto.getNCalSheetTypeId());

            if (ps.executeUpdate() > 0) {
                return claimCalculationSheetTypeDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetTypeDto insertTemporary(Connection connection, ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto) throws Exception {
        return null;
    }

    @Override
    public ClaimCalculationSheetTypeDto updateTemporary(Connection connection, ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto) throws Exception {
        return null;
    }

    @Override
    public ClaimCalculationSheetTypeDto insertHistory(Connection connection, ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_TYPE_DELETE);
            ps.setObject(1, id);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimCalculationSheetTypeDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = new ClaimCalculationSheetTypeDto();
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_TYPE_SEARCH);
            ps.setObject(1, id);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimCalculationSheetTypeDto.setNCalSheetTypeId(rs.getInt("N_CAL_SHEET_TYPE_ID"));
                claimCalculationSheetTypeDto.setVCalSheetTypeDesc(rs.getString("V_CAL_SHEET_TYPE_DESC"));
                return claimCalculationSheetTypeDto;
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetTypeDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimCalculationSheetTypeDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimCalculationSheetTypeDto> claimCalculationSheetTypeDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_TYPE_SEARCH_ALL);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = new ClaimCalculationSheetTypeDto();
                claimCalculationSheetTypeDto.setNCalSheetTypeId(rs.getInt("N_CAL_SHEET_TYPE_ID"));
                claimCalculationSheetTypeDto.setVCalSheetTypeDesc(rs.getString("V_CAL_SHEET_TYPE_DESC"));

                claimCalculationSheetTypeDtoList.add(claimCalculationSheetTypeDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimCalculationSheetTypeDtoList;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public ClaimCalculationSheetTypeDto getCalsheetTypeDetailByCalSheetId(Connection connection, Integer calSheetId) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimCalculationSheetTypeDto claimCalculationSheetTypeDto = new ClaimCalculationSheetTypeDto();
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_TYPE_BY_CALSHEET_ID);
            ps.setObject(1, calSheetId);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimCalculationSheetTypeDto.setNCalSheetTypeId(rs.getInt("t1.N_CAL_SHEET_TYPE_ID"));
                claimCalculationSheetTypeDto.setVCalSheetTypeDesc(rs.getString("t1.V_CAL_SHEET_TYPE_DESC"));
                return claimCalculationSheetTypeDto;
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }
}
