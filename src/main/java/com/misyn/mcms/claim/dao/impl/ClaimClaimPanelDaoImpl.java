/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.ClaimClaimPanelDao;
import com.misyn.mcms.claim.dto.ClaimClaimPanelDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class ClaimClaimPanelDaoImpl implements ClaimClaimPanelDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimClaimPanelDaoImpl.class);

    @Override
    public ClaimClaimPanelDto insertMaster(Connection connection, ClaimClaimPanelDto t) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public ClaimClaimPanelDto updateMaster(Connection connection, ClaimClaimPanelDto t) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public ClaimClaimPanelDto insertTemporary(Connection connection, ClaimClaimPanelDto t) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public ClaimClaimPanelDto updateTemporary(Connection connection, ClaimClaimPanelDto t) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public ClaimClaimPanelDto insertHistory(Connection connection, ClaimClaimPanelDto t) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public ClaimClaimPanelDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SEARCH_CLAIM_PANLE_ID);
            ps.setObject(1, id);
            rs = ps.executeQuery();
            if (rs.next()) {
                ClaimClaimPanelDto claimClaimPanelDto = new ClaimClaimPanelDto();
                claimClaimPanelDto.setId(rs.getInt("N_ID"));
                claimClaimPanelDto.setInputDateTime(rs.getDate("D_INPUT_DATETIME"));
                claimClaimPanelDto.setInputUser(rs.getString("V_INPUT_USER"));
                claimClaimPanelDto.setPanelName(rs.getString("V_PANEL_NAME"));
                return claimClaimPanelDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ClaimClaimPanelDto searchTemporary(Connection connection, Object id) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List<ClaimClaimPanelDto> searchAll(Connection connection) throws Exception {
        List<ClaimClaimPanelDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SEARCH_CLAIM_PANLE);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimClaimPanelDto claimClaimPanelDto = new ClaimClaimPanelDto();
                claimClaimPanelDto.setId(rs.getInt("N_ID"));
                claimClaimPanelDto.setInputDateTime(rs.getDate("D_INPUT_DATETIME"));
                claimClaimPanelDto.setInputUser(rs.getString("V_INPUT_USER"));
                claimClaimPanelDto.setPanelName(rs.getString("V_PANEL_NAME"));


                list.add(claimClaimPanelDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

}
