/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.ClaimClaimPanelUserDao;
import com.misyn.mcms.claim.dto.ClaimClaimPanelUserDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.MainPanelDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class ClaimClaimPanelUserDaoImpl extends AbstractBaseDao<ClaimClaimPanelUserDaoImpl> implements ClaimClaimPanelUserDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimClaimPanelUserDaoImpl.class);

    @Override
    public ClaimClaimPanelUserDto insertMaster(Connection connection, ClaimClaimPanelUserDto claimClaimPanelUserDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(CLAIM_PANEL_USER_INSERT)) {
            ps.setString(++index, claimClaimPanelUserDto.getUserId());
            ps.setInt(++index, claimClaimPanelUserDto.getUserPanelId());
            ps.setString(++index, claimClaimPanelUserDto.getUserStatus());
            ps.setString(++index, claimClaimPanelUserDto.getInputUser());
            ps.setString(++index, claimClaimPanelUserDto.getInputDateTime());
            ps.setString(++index, claimClaimPanelUserDto.getInputUser());
            ps.setString(++index, claimClaimPanelUserDto.getInputDateTime());


            if (ps.executeUpdate() > 0) {
                return claimClaimPanelUserDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        }
        return null;
    }

    @Override
    public ClaimClaimPanelUserDto updateMaster(Connection connection, ClaimClaimPanelUserDto claimClaimPanelUserDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(CLAIM_PANEL_USER_UPDATE)) {
            ps.setString(++index, claimClaimPanelUserDto.getUserStatus());
            ps.setString(++index, claimClaimPanelUserDto.getInputDateTime());
            ps.setInt(++index, claimClaimPanelUserDto.getId());

            if (ps.executeUpdate() > 0) {
                return claimClaimPanelUserDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        }
        return null;
    }

    @Override
    public ClaimClaimPanelUserDto insertTemporary(Connection connection, ClaimClaimPanelUserDto t) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public ClaimClaimPanelUserDto updateTemporary(Connection connection, ClaimClaimPanelUserDto t) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public ClaimClaimPanelUserDto insertHistory(Connection connection, ClaimClaimPanelUserDto t) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public ClaimClaimPanelUserDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimClaimPanelUserDto claimClaimPanelDto = new ClaimClaimPanelUserDto();
        try {
            ps = connection.prepareStatement(CLAIM_PANEL_USER_SEARCH_ONE);
            ps.setObject(1, id);
            rs = ps.executeQuery();
            if (rs.next()) {

                claimClaimPanelDto.setId(rs.getInt("t1.N_ID"));
                claimClaimPanelDto.setInputDateTime(rs.getString("t1.D_INPUT_DATETIME"));
                claimClaimPanelDto.setInputUser(rs.getString("t1.V_INPUT_USER"));
                claimClaimPanelDto.setUserId(rs.getString("t1.V_USER_ID"));
                claimClaimPanelDto.setUserPanelId(rs.getInt("t1.N_PANEL_ID"));
                claimClaimPanelDto.setUserStatus(rs.getString("t1.V_USER_STATUS"));
                claimClaimPanelDto.setUserPanelName(rs.getString("t2.V_PANEL_NAME"));
                return claimClaimPanelDto;
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return claimClaimPanelDto;
    }

    @Override
    public ClaimClaimPanelUserDto searchTemporary(Connection connection, Object id) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List<ClaimClaimPanelUserDto> searchAll(Connection connection) throws Exception {
        List<ClaimClaimPanelUserDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(CLAIM_PANEL_USER_SEARCH_ALL);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimClaimPanelUserDto claimClaimPanelDto = new ClaimClaimPanelUserDto();
                claimClaimPanelDto.setId(rs.getInt("N_ID"));
                claimClaimPanelDto.setInputDateTime(rs.getString("V_USER_ID"));
                claimClaimPanelDto.setInputUser(rs.getString("N_PANEL_ID"));
                claimClaimPanelDto.setUserId(rs.getString("V_USER_STATUS"));
                claimClaimPanelDto.setUserPanelId(rs.getInt("V_INPUT_USER"));
                claimClaimPanelDto.setUserStatus(rs.getString("D_INPUT_DATETIME"));

                list.add(claimClaimPanelDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField) {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List claimList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();

//        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
//            SQL_SEARCH = SQL_SEARCH.concat(" AND DATE_FORMAT(t1.D_INPTIME,'%Y-%m-%d %H:%i') BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
//        }

        final String SEL_SQL = "SELECT\n" +
                "t1.N_ID,\n" +
                "t1.V_USER_ID,\n" +
                "t1.N_PANEL_ID,\n" +
                "t1.V_USER_STATUS,\n" +
                "t1.V_INPUT_USER,\n" +
                "t1.D_INPUT_DATETIME,\n" +
                "t2.V_PANEL_NAME\n" +
                "FROM\n" +
                "claim_claim_panel_user AS t1\n" +
                "INNER JOIN claim_claim_panel AS t2 ON t1.N_PANEL_ID = t2.N_ID ".concat(SQL_SEARCH).concat(SQL_ORDER);
        ;

        final String COUNT_SQL = "SELECT\n" +
                "count(t1.N_ID) AS cnt\n" +
                "FROM\n" +
                "claim_claim_panel_user AS t1\n" +
                "INNER JOIN claim_claim_panel AS t2 ON t1.N_PANEL_ID = t2.N_ID ".concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ClaimClaimPanelUserDto claimClaimPanelUserDto = getClaimPanelUser(rs);
                    claimClaimPanelUserDto.setIndex(++index);
                    claimList.add(claimClaimPanelUserDto);
                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(claimList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public DataGridDto getMainPanelDataGridDto(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String columnOrder, String orderColumnName) throws Exception {
        int index = start;
        DataGridDto dataGridDto = new DataGridDto();
        int count = 0;
        List mainPanelUserList = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        String SQL_ORDER = formatOrderSQL(start, length, columnOrder, orderColumnName).toString();

        final String SEL_SQL = GET_MAIN_PANEL_USERS_DATA_GRID.concat(" ").concat(SQL_SEARCH).concat(" ").concat("GROUP BY N_CLAIM_NO ").concat(SQL_ORDER);
        final String COUNT_SQL = COUNT_MAIN_PANEL_USERS_DATA_GRID.concat(" ").concat(SQL_SEARCH);
        try {
            ps = connection.prepareStatement(COUNT_SQL, rs.TYPE_SCROLL_SENSITIVE, rs.CONCUR_UPDATABLE);
            rs = ps.executeQuery();
            if (rs.next()) {
                count = rs.getInt("cnt");
            }
            ps.close();
            rs.close();
            ps = connection.prepareStatement(SEL_SQL, rs.TYPE_SCROLL_SENSITIVE, rs.CONCUR_UPDATABLE);
            rs = ps.executeQuery();
            while (rs.next()) {
                MainPanelDto mainPanelDto = populateMainPanelDto(rs);
                mainPanelDto.setIndex(++index);
                mainPanelUserList.add(mainPanelDto);
            }
            dataGridDto.setDraw(drawRandomId);
            dataGridDto.setRecordsTotal(count);
            dataGridDto.setRecordsFiltered(count);
            dataGridDto.setData(mainPanelUserList);
            ps.close();
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return dataGridDto;
    }

    @Override
    public boolean isAlreadySavedInPanel(Connection connection, Integer panelId, String userId) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(IS_ALREADY_IN_THE_PANEL);
            ps.setString(1, userId);
            ps.setInt(2, panelId);
            rs = ps.executeQuery();
            return rs.next();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    private MainPanelDto populateMainPanelDto(ResultSet rs) throws Exception {
        MainPanelDto mainPanelDto = new MainPanelDto();
        try {
            mainPanelDto.setClaimNo(rs.getInt("N_CLAIM_NO"));
            mainPanelDto.setForwardedUser(rs.getString("V_INPUT_USER"));
            mainPanelDto.setForwardedDateTime(Utility.getDate(rs.getString("INPUT_DATETIME"), AppConstant.DATE_TIME_FORMAT));
            mainPanelDto.setLastUpdatedDateTime(Utility.getDate(null == rs.getString("UPDATED_DATETIME") ? AppConstant.DEFAULT_DATE_TIME
                    : rs.getString("UPDATED_DATETIME"), AppConstant.DATE_TIME_FORMAT));
            mainPanelDto.setAssignedPanelMembers(rs.getInt("PANEL_MEMBERS"));
            mainPanelDto.setStatus(rs.getString("V_STATUS"));
            return mainPanelDto;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    private ClaimClaimPanelUserDto getClaimPanelUser(ResultSet rs) {
        ClaimClaimPanelUserDto claimClaimPanelDto = new ClaimClaimPanelUserDto();
        try {

            claimClaimPanelDto.setId(rs.getInt("t1.N_ID"));
            claimClaimPanelDto.setUserId(rs.getString("t1.V_USER_ID"));
            claimClaimPanelDto.setUserPanelId(rs.getInt("t1.N_PANEL_ID"));
            claimClaimPanelDto.setUserStatus(rs.getString("t1.V_USER_STATUS"));
            claimClaimPanelDto.setInputUser(rs.getString("t1.V_INPUT_USER"));
            claimClaimPanelDto.setInputDateTime(Utility.getDate(rs.getString("t1.D_INPUT_DATETIME"), AppConstant.DATE_TIME_FORMAT));
            claimClaimPanelDto.setUserPanelName(rs.getString("t2.V_PANEL_NAME"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimClaimPanelDto;
    }
}
