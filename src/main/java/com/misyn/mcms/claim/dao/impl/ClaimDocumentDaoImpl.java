package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.ClaimDocumentDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.DocumentStatusEnum;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
public class ClaimDocumentDaoImpl extends AbstractBaseDao<ClaimDocumentDaoImpl> implements ClaimDocumentDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimDocumentDaoImpl.class);


    @Override
    public ClaimDocumentDto insertMaster(Connection connection, ClaimDocumentDto claimDocumentDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(INSERT_CLAIM_UPLOAD_DOCUMENTS, Statement.RETURN_GENERATED_KEYS)) {
            ps.setInt(++index, claimDocumentDto.getJobRefNo());
            ps.setInt(++index, claimDocumentDto.getClaimNo());
            ps.setInt(++index, claimDocumentDto.getDocumentTypeId());
            ps.setInt(++index, claimDocumentDto.getDepartmentId());
            ps.setString(++index, claimDocumentDto.getIsMandatory());
            ps.setString(++index, claimDocumentDto.getDocumentStatus());
            ps.setString(++index, claimDocumentDto.getDocumentPath());
            ps.setString(++index, claimDocumentDto.getDocumentName());
            ps.setString(++index, claimDocumentDto.getIsDocumentUpload());
            ps.setString(++index, claimDocumentDto.getIsCheck());
            ps.setString(++index, claimDocumentDto.getCheckUser());
            ps.setString(++index, claimDocumentDto.getCheckDateTime());
            ps.setString(++index, claimDocumentDto.getHoldUser());
            ps.setString(++index, claimDocumentDto.getHoldDateTime());
            ps.setString(++index, claimDocumentDto.getRejectUser());
            ps.setString(++index, claimDocumentDto.getRejectDateTime());
            ps.setString(++index, claimDocumentDto.getBillSummaryRemark());
            ps.setString(++index, claimDocumentDto.getBillSummaryCheckUser());
            ps.setString(++index, claimDocumentDto.getBillSummaryCheckDateTime());
            ps.setString(++index, claimDocumentDto.getRemark());
            ps.setString(++index, claimDocumentDto.getCancelUser());
            ps.setString(++index, claimDocumentDto.getCancelDateTime());
            ps.setString(++index, claimDocumentDto.getCancelRemark());
            ps.setInt(++index, claimDocumentDto.getBillCheckRefNo());
            ps.setString(++index, claimDocumentDto.getInpStat());
            ps.setString(++index, claimDocumentDto.getInpUser());
            ps.setString(++index, claimDocumentDto.getInpDateTime());
            ps.setString(++index, claimDocumentDto.getDoSerialNo());

            if (ps.executeUpdate() > 0) {
                try (ResultSet rsKeys = ps.getGeneratedKeys()) {
                    if (rsKeys.next()) {
                        int autoGeneratedId = rsKeys.getInt(1);
                        claimDocumentDto.setRefNo(autoGeneratedId);
                        return claimDocumentDto;
                    }
                }
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimDocumentDto updateMaster(Connection connection, ClaimDocumentDto claimDocumentDto) throws Exception {
        return null;
    }

    @Override
    public ClaimDocumentDto insertTemporary(Connection connection, ClaimDocumentDto claimDocumentDto) throws Exception {
        return null;
    }

    @Override
    public ClaimDocumentDto updateTemporary(Connection connection, ClaimDocumentDto claimDocumentDto) throws Exception {
        return null;
    }

    @Override
    public ClaimDocumentDto insertHistory(Connection connection, ClaimDocumentDto claimDocumentDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;

        try {
            ps = connection.prepareStatement(DELETE_CLAIM_UPLOAD_DOCUMENTS_BY_REF_ID);
            ps.setObject(1, id);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimDocumentDto searchMaster(Connection connection, Object id) throws Exception {
        ClaimDocumentDto claimDocumentDto = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_REF_NO);
            ps.setInt(1, (Integer) id);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimDocumentDto = this.getClaimDocumentDto(rs);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
        }
        return claimDocumentDto;
    }

    @Override
    public ClaimDocumentDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimDocumentDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    private ClaimDocumentDto getClaimDocumentDto(ResultSet rs) {
        ClaimDocumentDto claimDocumentDto = new ClaimDocumentDto();
        try {
            if (claimDocumentDto.getJobRefNo() != null) {
                claimDocumentDto.setRefNo(rs.getInt("t1.N_REF_NO"));
                claimDocumentDto.setJobRefNo(rs.getInt("t1.N_JOB_REF_NO"));
                claimDocumentDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                claimDocumentDto.setDocumentTypeId(rs.getInt("t1.N_DOC_TYPE_ID"));
                claimDocumentDto.setDepartmentId(rs.getInt("t1.N_DEPARTMENT_ID"));
                claimDocumentDto.setIsMandatory(rs.getString("t1.V_IS_MANDATORY"));
                claimDocumentDto.setDocumentStatus(rs.getString("t1.V_DOC_STATUS"));
                claimDocumentDto.setDocumentPath(rs.getString("t1.V_DOC_PATH"));
                claimDocumentDto.setDocumentName(rs.getString("t1.V_DOC_NAME"));
                claimDocumentDto.setIsDocumentUpload(rs.getString("t1.V_IS_DOC_UPLOAD"));
                claimDocumentDto.setIsCheck(rs.getString("t1.V_IS_CHECK"));
                claimDocumentDto.setCheckUser(rs.getString("t1.V_CHECK_USER"));
                claimDocumentDto.setCheckDateTime(Utility.getCustomDateFormat(rs.getString("t1.D_CHECK_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimDocumentDto.setHoldUser(rs.getString("t1.V_HOLD_USER"));
                claimDocumentDto.setHoldDateTime(Utility.getCustomDateFormat(rs.getString("t1.D_HOLD_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimDocumentDto.setRejectUser(rs.getString("t1.V_REJECT_USER"));
                claimDocumentDto.setRejectDateTime(Utility.getCustomDateFormat(rs.getString("t1.D_REJECT_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimDocumentDto.setBillSummaryRemark(rs.getString("t1.V_BILL_SUMMARY_REMARK"));
                claimDocumentDto.setBillSummaryCheckUser(rs.getString("t1.V_BILL_SUMMARY_CHECK_USER"));
                claimDocumentDto.setBillSummaryCheckDateTime(rs.getString("t1.D_BILL_SUMMARY_CHECK_DATE_TIME"));
                claimDocumentDto.setRemark(rs.getString("t1.V_REMARK"));
                claimDocumentDto.setCancelUser(rs.getString("V_CANCEL_USER"));
                claimDocumentDto.setCancelDateTime(rs.getString("D_CANCEL_DATE_TIME"));
                claimDocumentDto.setCancelRemark(rs.getString("V_CANCEL_REMARK"));
                claimDocumentDto.setInpStat(rs.getString("t1.V_INP_STAT"));
                claimDocumentDto.setInpUser(rs.getString("t1.V_INP_USER"));
                claimDocumentDto.setInpDateTime(Utility.getCustomDateFormat(rs.getString("t1.D_INP_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimDocumentDto.setToolTip(this.getToolTip(claimDocumentDto.getDocumentName(), claimDocumentDto.getInpUser(), claimDocumentDto.getInpDateTime()));
                claimDocumentDto.getClaimDocumentTypeDto().setDocumentTypeId(rs.getInt("t1.N_DOC_TYPE_ID"));
                try {
                    claimDocumentDto.getClaimDocumentTypeDto().setDocumentTypeName(rs.getString("t2.V_DOC_TYPE_NAME"));
                } catch (Exception e) {
                    LOGGER.info(e.getMessage());
                }

            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimDocumentDto;
    }


    @Override
    public List<ClaimDocumentDto> getClaimDocumentDtoList(Connection connection, Integer jobRefNo, Integer departmentId) {
        List<ClaimDocumentDto> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_N_JOB_REF_NO);
            ps.setInt(1, jobRefNo);
            ps.setInt(2, departmentId);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimDocumentDto claimDocumentDto = this.getClaimDocumentDto(rs);
                ClaimDocumentTypeDto claimDocumentTypeDto = new ClaimDocumentTypeDto();
                claimDocumentTypeDto.setDocumentTypeId(rs.getInt("t2.N_DOC_TYPE_ID"));
                claimDocumentTypeDto.setDocumentTypeName(rs.getString("t2.V_DOC_TYPE_NAME"));
                claimDocumentDto.setClaimDocumentTypeDto(claimDocumentTypeDto);
                list.add(claimDocumentDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return list;
    }

    @Override
    public List<ClaimUploadViewDto> getClaimUploadViewDtoList(Connection connection, Integer claimNo, Integer jobRefNo, Integer departmentId, Integer inspectionTypeId) {
        List<ClaimUploadViewDto> claimUploadViewDtoList = new ArrayList<>();
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        ResultSet rs1 = null;
        ResultSet rs2;
        try {
            ps1 = connection.prepareStatement(SELECT_CLAIM_DOCUMENT_TYPE_BY_N_DEPARTMENT_ID_AND_INSPECTION_TYPE);
            ps2 = connection.prepareStatement(SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_N_JOB_REF_NO_AND_CLAIM_NO_AND_DOC_TYPE_ID);
            ps1.setInt(1, departmentId);
            ps1.setInt(2, inspectionTypeId);
            rs1 = ps1.executeQuery();
            while (rs1.next()) {
                ClaimUploadViewDto claimUploadViewDto = new ClaimUploadViewDto();
                ClaimDocumentTypeDto claimDocumentTypeDto = new ClaimDocumentTypeDto();
                claimDocumentTypeDto.setDocumentTypeId(rs1.getInt("t2.N_DOC_TYPE_ID"));
                claimDocumentTypeDto.setDocumentTypeName(rs1.getString("t2.V_DOC_TYPE_NAME"));
                claimUploadViewDto.setClaimDocumentTypeDto(claimDocumentTypeDto);

                ps2.setInt(1, jobRefNo);
                ps2.setInt(2, claimNo);
                ps2.setInt(3, claimDocumentTypeDto.getDocumentTypeId());
                rs2 = ps2.executeQuery();

                while (rs2.next()) {
                    ClaimDocumentDto claimDocumentDto = this.getClaimDocumentDto(rs2);
                    claimDocumentDto.setClaimDocumentTypeDto(claimDocumentTypeDto);
                    claimUploadViewDto.getClaimDocumentDtoList().add(claimDocumentDto);
                }
                rs2.close();
                claimUploadViewDtoList.add(claimUploadViewDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs1 != null) {
                    rs1.close();
                }
                if (ps1 != null) {
                    ps1.close();
                }
                if (ps2 != null) {
                    ps2.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return claimUploadViewDtoList;
    }

    @Override
    public boolean deleteMaster(Connection connection, Integer refId, Integer documentTypeId) throws Exception {
        PreparedStatement ps = null;

        try {
            ps = connection.prepareStatement(DELETE_CLAIM_UPLOAD_DOCUMENTS_BY_REF_ID);
            ps.setObject(1, refId);
            ps.setObject(2, documentTypeId);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }

    }

    @Override
    public List<ClaimDocumentDto> findAllByClaimNoAndInspectionJobNoAndDocumentTypeId(Connection connection, Integer claimNo, String inspectionJobNo, Integer documentTypeId) {
        List<ClaimDocumentDto> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        String SQL_DOCUMENT_TYPE = " AND t1.N_DOC_TYPE_ID=?";
        try {
            if (documentTypeId == AppConstant.ZERO_INT) {
                ps = connection.prepareStatement(SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_CLAIM_NO_AND_JOB_REF_NO);
                ps.setInt(1, claimNo);
                ps.setInt(2, claimNo);
                ps.setString(3, inspectionJobNo);
            } else {
                ps = connection.prepareStatement(SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_CLAIM_NO_AND_JOB_REF_NO.concat(SQL_DOCUMENT_TYPE));
                ps.setInt(1, claimNo);
                ps.setInt(2, claimNo);
                ps.setString(3, inspectionJobNo);
                ps.setInt(4, documentTypeId);
            }

            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimDocumentDto claimDocumentDto = this.getClaimDocumentDto(rs);
                ClaimDocumentTypeDto claimDocumentTypeDto = new ClaimDocumentTypeDto();
                claimDocumentTypeDto.setDocumentTypeId(rs.getInt("t2.N_DOC_TYPE_ID"));
                claimDocumentTypeDto.setDocumentTypeName(rs.getString("t2.V_DOC_TYPE_NAME"));
                claimDocumentDto.setClaimDocumentTypeDto(claimDocumentTypeDto);
                list.add(claimDocumentDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return list;
    }

    @Override
    public List<ClaimDocumentDto> findAllByClaimNoAndDocumentTypeId(Connection connection, Integer claimNo, Integer documentTypeId) {
        List<ClaimDocumentDto> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        String SQL_DOCUMENT_TYPE = " AND t1.N_DOC_TYPE_ID=?";
        try {
            if (documentTypeId == AppConstant.ZERO_INT) {
                ps = connection.prepareStatement(SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_CLAIM_NO);
                ps.setInt(1, claimNo);
            } else {
                ps = connection.prepareStatement(SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_CLAIM_NO.concat(SQL_DOCUMENT_TYPE));
                ps.setInt(1, claimNo);
                ps.setInt(2, documentTypeId);
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimDocumentDto claimDocumentDto = this.getClaimDocumentDto(rs);
                ClaimDocumentTypeDto claimDocumentTypeDto = new ClaimDocumentTypeDto();
                claimDocumentTypeDto.setDocumentTypeId(rs.getInt("t2.N_DOC_TYPE_ID"));
                claimDocumentTypeDto.setDocumentTypeName(rs.getString("t2.V_DOC_TYPE_NAME"));
                claimDocumentDto.setClaimDocumentTypeDto(claimDocumentTypeDto);
                list.add(claimDocumentDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return list;
    }

    @Override
    public List<ClaimDocumentDto> findAllByClaimNoAndDocumentTypeIdAndDocumetStatus(Connection connection, Integer claimNo, Integer documentTypeId, DocumentStatusEnum documentStatus) {
        List<ClaimDocumentDto> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_UPLOAD_DOCUMENTS_CLAIM_NO_AND_DOC_TYPE_AND_DOC_STATUS);
            ps.setInt(1, claimNo);
            ps.setInt(2, documentTypeId);
            ps.setString(3, documentStatus.getDocumentStatus());
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimDocumentDto claimDocumentDto = this.getClaimDocumentDto(rs);
                list.add(claimDocumentDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return list;
    }

    @Override
    public List<ClaimDocumentDto> findAllByClaimNoAndDocumentTypeIdAndInDocumentStatus(Connection connection, Integer claimNo, Integer documentTypeId) {
        List<ClaimDocumentDto> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_UPLOAD_DOCUMENTS_CLAIM_NO_AND_DOC_TYPE_AND_IN_DOC_STATUS);
            ps.setInt(1, claimNo);
            ps.setInt(2, documentTypeId);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimDocumentDto claimDocumentDto = this.getClaimDocumentDto(rs);
                list.add(claimDocumentDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return list;
    }

    @Override
    public List<ClaimDocumentDto> findAllByClaimNoAndInDocumentTypeIdAndInDocumentStatus(Connection connection, Integer claimNo, String documentTypeIds) {
        List<ClaimDocumentDto> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement("SELECT t1.*, t2.V_DOC_TYPE_NAME FROM claim_upload_documents AS t1 " +
                    "INNER JOIN claim_document_type AS t2 ON t1.N_DOC_TYPE_ID = t2.N_DOC_TYPE_ID " +
                    "WHERE t1.N_CLIM_NO=? " +
                    "AND t1.N_DOC_TYPE_ID IN(" + documentTypeIds + ")" +
                    "AND t1.V_DOC_STATUS IN('P','A')");
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimDocumentDto claimDocumentDto = this.getClaimDocumentDto(rs);
                list.add(claimDocumentDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return list;
    }

    @Override
    public boolean isUploadAllMandatoryDocument(Connection connection, Integer claimNo, Integer documentTypeId) {
        boolean result = false;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_IS_UPLOAD_ALL_MANDATORY_DOCUMNET);
            ps.setInt(1, claimNo);
            ps.setInt(2, documentTypeId);
            rs = ps.executeQuery();
            if (rs.next()) {
                result = true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return result;
    }

    @Override
    public boolean isCheckedAllMandatoryDocument(Connection connection, Integer claimNo, Integer documentTypeId) {
        boolean result = true;
        PreparedStatement ps1 = null;
        PreparedStatement ps2;
        ResultSet rs = null;
        ResultSet rs2;
        try {
            ps2 = connection.prepareStatement(SELECT_IS_FOUND_CHECK_ALL_MANDATORY_DOCUMNET);
            ps2.setInt(1, claimNo);
            ps2.setInt(2, documentTypeId);
            rs2 = ps2.executeQuery();
            if (!rs2.next()) {
                return false;
            }
            rs2.close();
            ps2.close();
            ps1 = connection.prepareStatement(SELECT_IS_CHECK_ALL_MANDATORY_DOCUMNET);
            ps1.setInt(1, claimNo);
            ps1.setInt(2, documentTypeId);
            rs = ps1.executeQuery();
            if (rs.next()) {
                result = false;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps1 != null) {
                    ps1.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return result;
    }

    @Override
    public ClaimDocumentDto updateDocumentCheckStatus(Connection connection, ClaimDocumentDto claimDocumentDto) throws MisynJDBCException {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_UPLOAD_DOCUMENTS_BY_REF_ID);
            ps.setString(++index, claimDocumentDto.getDocumentStatus());
            ps.setString(++index, claimDocumentDto.getIsCheck());
            ps.setString(++index, claimDocumentDto.getCheckUser());
            ps.setString(++index, claimDocumentDto.getCheckDateTime());
            ps.setInt(++index, claimDocumentDto.getRefNo());

            if (ps.executeUpdate() > 0) {
                return claimDocumentDto;

            } else {
                throw new MisynJDBCException("Can not be saved");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        } finally {
            try {
                if (ps != null)
                    ps.close();
            } catch (SQLException e) {
            }
        }

    }

    @Override
    public ClaimDocumentDto updateDocumentHoldStatus(Connection connection, ClaimDocumentDto claimDocumentDto) throws MisynJDBCException {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_UPLOAD_DOCUMENTS_HOLD_BY_REF_ID);
            ps.setString(++index, claimDocumentDto.getDocumentStatus());
            ps.setString(++index, claimDocumentDto.getHoldUser());
            ps.setString(++index, claimDocumentDto.getHoldDateTime());
            ps.setInt(++index, claimDocumentDto.getRefNo());
            if (ps.executeUpdate() > 0) {
                return claimDocumentDto;
            } else {
                throw new MisynJDBCException("Can not be saved");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        } finally {
            try {
                if (ps != null)
                    ps.close();
            } catch (SQLException e) {
            }
        }
    }

    @Override
    public ClaimDocumentDto updateDocumentRejectStatus(Connection connection, ClaimDocumentDto claimDocumentDto) throws MisynJDBCException {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_UPLOAD_DOCUMENTS_REJECT_BY_REF_ID);
            ps.setString(++index, claimDocumentDto.getDocumentStatus());
            ps.setString(++index, claimDocumentDto.getRejectUser());
            ps.setString(++index, claimDocumentDto.getRejectDateTime());
            ps.setString(++index, claimDocumentDto.getRemark());
            ps.setInt(++index, claimDocumentDto.getRefNo());
            if (ps.executeUpdate() > 0) {
                return claimDocumentDto;
            } else {
                throw new MisynJDBCException("Can not be saved");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        } finally {
            try {
                if (ps != null)
                    ps.close();
            } catch (SQLException e) {
            }
        }
    }

    @Override
    public ClaimDocumentDto updateDocumentBillCheckDetails(Connection connection, ClaimDocumentDto claimDocumentDto) throws MisynJDBCException {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_UPLOAD_DOCUMENTS_BILL_CHECK_BY_REF_ID);
            ps.setString(++index, claimDocumentDto.getDocumentStatus());
            ps.setString(++index, claimDocumentDto.getIsCheck());
            ps.setString(++index, claimDocumentDto.getCheckUser());
            ps.setString(++index, claimDocumentDto.getCheckDateTime());
            ps.setString(++index, claimDocumentDto.getBillSummaryRemark());
            ps.setString(++index, claimDocumentDto.getBillSummaryCheckUser());
            ps.setString(++index, claimDocumentDto.getBillSummaryCheckDateTime());
            ps.setInt(++index, claimDocumentDto.getRefNo());

            if (ps.executeUpdate() > 0) {
                return claimDocumentDto;

            } else {
                throw new MisynJDBCException("Can not be saved");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        } finally {
            try {
                if (ps != null)
                    ps.close();
            } catch (SQLException e) {
            }
        }
    }

    @Override
    public ClaimDocumentDto searchByBillCheckRefNo(Connection connection, Integer billCheckRefNo) throws MisynJDBCException {
        ClaimDocumentDto claimDocumentDto = null;
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_BILL_CHECK_REF_NO);
            ps.setInt(1, (Integer) billCheckRefNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimDocumentDto = this.getClaimDocumentDto(rs);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimDocumentDto;
    }

    @Override
    public Integer getBillApprovedDocTypeId(Connection connection, Integer documentTypeId) {
        PreparedStatement ps;
        ResultSet rs;
        Integer result = 0;
        try {
            ps = connection.prepareStatement("SELECT n_aprv_doc_type_id FROM claim_bill_check_documet WHERE n_doc_type_id=?");
            ps.setInt(1, (Integer) documentTypeId);
            rs = ps.executeQuery();
            if (rs.next()) {
                result = rs.getInt("n_aprv_doc_type_id");
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return result;
    }

    @Override
    public List<ClaimDocumentDto> findAllMandatoryDocumentByClaimNo(Connection connection, Integer claimNo) {
        List<ClaimDocumentDto> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_ALL_MANDATORY_DOC_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimDocumentDto claimDocumentDto = new ClaimDocumentDto();
                claimDocumentDto.setDocumentTypeId(rs.getInt("t1.N_DOC_TYPE_ID"));
                claimDocumentDto.setClaimNo(rs.getInt("t1.N_CLAIM_NO"));
                claimDocumentDto.setIsMandatory(rs.getString("t1.V_IS_MANDATORY"));
                claimDocumentDto.setDocumentStatus(rs.getString("V_DOC_STATUS"));
                list.add(claimDocumentDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return list;
    }

    @Override
    public ClaimDocumentLossTypeOldDto insertClaimDocumentLossTypeOld(Connection connection, ClaimDocumentLossTypeOldDto claimDocumentLossTypeOldDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(INSERT_CLAIM_DOCUMENT_LOSS_TYPE_OLD);
            ps.setInt(++index, claimDocumentLossTypeOldDto.getDocumentTypeId());
            ps.setInt(++index, claimDocumentLossTypeOldDto.getClaimNo());
            ps.setString(++index, claimDocumentLossTypeOldDto.getIsMandatory());
            ps.setString(++index, claimDocumentLossTypeOldDto.getOldStatus());
            ps.setString(++index, claimDocumentLossTypeOldDto.getInpUser());
            ps.setString(++index, claimDocumentLossTypeOldDto.getInpDateTime());

            if (ps.executeUpdate() > 0) {
                return claimDocumentLossTypeOldDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ClaimDocumentLossTypeOldDto updateClaimDocumentLossTypeOld(Connection connection, ClaimDocumentLossTypeOldDto claimDocumentLossTypeOldDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_DOCUMENT_LOSS_TYPE_OLD);
            ps.setString(++index, claimDocumentLossTypeOldDto.getIsMandatory());
            ps.setString(++index, claimDocumentLossTypeOldDto.getOldStatus());
            ps.setString(++index, claimDocumentLossTypeOldDto.getInpUser());
            ps.setString(++index, claimDocumentLossTypeOldDto.getInpDateTime());
            ps.setInt(++index, claimDocumentLossTypeOldDto.getDocumentTypeId());
            ps.setInt(++index, claimDocumentLossTypeOldDto.getClaimNo());

            if (ps.executeUpdate() > 0) {
                return claimDocumentLossTypeOldDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public List<ClaimDocumentLossTypeOldDto> allClaimDocumentLossTypeOldByClaimNo(Connection connection, Integer claimNo) throws Exception {
        List<ClaimDocumentLossTypeOldDto> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_DOCUMENT_LOSS_TYPE_OLD);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimDocumentLossTypeOldDto claimDocumentLossTypeOldDto = new ClaimDocumentLossTypeOldDto();
                claimDocumentLossTypeOldDto.setTxnNo(rs.getInt("N_TXN_NO"));
                claimDocumentLossTypeOldDto.setDocumentTypeId(rs.getInt("N_DOC_TYP_ID"));
                claimDocumentLossTypeOldDto.setClaimNo(rs.getInt("N_CLAIM_NO"));
                claimDocumentLossTypeOldDto.setIsMandatory(rs.getString("V_IS_MANDATORY"));
                claimDocumentLossTypeOldDto.setOldStatus(rs.getString("V_OLD_STATUS"));
                claimDocumentLossTypeOldDto.setInpUser(rs.getString("V_INP_USER_ID"));
                claimDocumentLossTypeOldDto.setInpDateTime(rs.getString("D_INP_DATE_TIME"));
                list.add(claimDocumentLossTypeOldDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return list;
    }

    @Override
    public void updateJobRefNoByClaimNo(Connection connection, Integer claimNo, Integer jobRefNo) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_JOB_REF_NO);
            ps.setInt(++index, jobRefNo);
            ps.setInt(++index, claimNo);

            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public List<Integer> getDocumentIdList(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Integer> docIds = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_UPLOADED_DOCUMENTS_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                docIds.add(rs.getInt("N_DOC_TYPE_ID"));
            }
            return docIds;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void updateDocumentBillCancelDetails(Connection connection, ClaimDocumentDto claimDocumentDto) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(UPDATE_CANCEL_DOCUMENT_DETAILS);
            ps.setString(1, claimDocumentDto.getDocumentStatus());
            ps.setString(2, claimDocumentDto.getCancelUser());
            ps.setString(3, claimDocumentDto.getCancelDateTime());
            ps.setString(4, claimDocumentDto.getCancelRemark());
            ps.setInt(5, claimDocumentDto.getRefNo());
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public String getDocumentUploadTime(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        String dateTime = null;
        try {
            ps = connection.prepareStatement(GET_LAST_BRANCH_DOCUMENT_UPLOAD_DATE_TIME);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                dateTime = Utility.getDate(rs.getString("D_INP_DATE_TIME"), AppConstant.DATE_TIME_FORMAT);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return dateTime;
    }

    @Override
    public ClaimDocumentDto insertBankDetails(Connection connection, ClaimDocumentDto claimDocumentDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(INSERT_CLAIM_UPLOAD_DOCUMENTS, Statement.RETURN_GENERATED_KEYS);
            ps.setInt(++index, claimDocumentDto.getJobRefNo());
            ps.setInt(++index, claimDocumentDto.getClaimNo());
            ps.setInt(++index, claimDocumentDto.getDocumentTypeId());
            ps.setInt(++index, claimDocumentDto.getDepartmentId());
            ps.setString(++index, claimDocumentDto.getIsMandatory());
            ps.setString(++index, AppConstant.APPROVE); // getDocumentStatus
            ps.setString(++index, claimDocumentDto.getDocumentPath());
            ps.setString(++index, claimDocumentDto.getDocumentName());
            ps.setString(++index, AppConstant.YES); // getIsDocumentUpload
            ps.setString(++index, AppConstant.YES); // getIsCheck
            ps.setString(++index, claimDocumentDto.getCheckUser());
            ps.setString(++index, claimDocumentDto.getCheckDateTime());
            ps.setString(++index, claimDocumentDto.getHoldUser());
            ps.setString(++index, claimDocumentDto.getHoldDateTime());
            ps.setString(++index, claimDocumentDto.getRejectUser());
            ps.setString(++index, claimDocumentDto.getRejectDateTime());
            ps.setString(++index, claimDocumentDto.getBillSummaryRemark());
            ps.setString(++index, claimDocumentDto.getBillSummaryCheckUser());
            ps.setString(++index, claimDocumentDto.getBillSummaryCheckDateTime());
            ps.setString(++index, claimDocumentDto.getRemark());
            ps.setString(++index, claimDocumentDto.getCancelUser());
            ps.setString(++index, claimDocumentDto.getCancelDateTime());
            ps.setString(++index, claimDocumentDto.getCancelRemark());
            ps.setInt(++index, claimDocumentDto.getBillCheckRefNo());
            ps.setString(++index, claimDocumentDto.getInpStat());
            ps.setString(++index, claimDocumentDto.getInpUser());
            ps.setString(++index, claimDocumentDto.getInpDateTime());

            if (ps.executeUpdate() > 0) {
                try (ResultSet rsKeys = ps.getGeneratedKeys()) {
                    if (rsKeys.next()) {
                        int autoGeneratedId = rsKeys.getInt(1);
                        claimDocumentDto.setRefNo(autoGeneratedId);
                        return claimDocumentDto;
                    }
                }
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ClaimDocumentDto getClaimDocumentByRefNo(Connection connection, Integer refNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement("SELECT V_DOC_PATH FROM claim_upload_documents  WHERE N_REF_NO=?");
            ps.setInt(1, refNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                ClaimDocumentDto claimDocumentDto = new ClaimDocumentDto();
                claimDocumentDto.setRefNo(refNo);
                claimDocumentDto.setDocumentPath(rs.getString("V_DOC_PATH"));
                return claimDocumentDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return null;
    }
}
