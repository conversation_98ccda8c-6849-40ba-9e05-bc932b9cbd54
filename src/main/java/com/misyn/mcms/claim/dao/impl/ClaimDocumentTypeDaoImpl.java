package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.ClaimDocumentTypeDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class ClaimDocumentTypeDaoImpl extends AbstractBaseDao<ClaimDocumentTypeDaoImpl> implements ClaimDocumentTypeDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimDocumentTypeDaoImpl.class);

    @Override
    public ClaimDocumentTypeDto searchByDocumentTypeId(Connection connection, Integer documentTypeId) {
        ClaimDocumentTypeDto claimDocumentTypeDto = null;
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_DOCUMENT_TYPE_BY_DOCUMENT_TYPE_ID);
            ps.setInt(1, documentTypeId);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimDocumentTypeDto = getClaimDocumentType(rs);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimDocumentTypeDto;
    }

    @Override
    public List<ClaimDocumentTypeDto> searchAll(Connection connection) {
        List<ClaimDocumentTypeDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_DOCUMENT_TYPE);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimDocumentTypeDto claimDocumentTypeDto = getClaimDocumentTypeDto(rs);
                list.add(claimDocumentTypeDto);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<ClaimDepartmentDto> searchClaimDocument(Connection connection) {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimDepartmentDto> claimDepartmentDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(CLAIM_DOCUMENT_NAME);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimDepartmentDto claimDepartmentDto = new ClaimDepartmentDto();
                claimDepartmentDto.setDepartmentId(rs.getInt("department_id"));
                claimDepartmentDto.setDepartmentName(rs.getString("department_name"));
                claimDepartmentDtoList.add(claimDepartmentDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimDepartmentDtoList;
    }

    @Override
    public List<DocReqFormDto> claimDocReqForm(Connection connection) {
        PreparedStatement ps;
        ResultSet rs;
        List<DocReqFormDto> docReqFormDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(CLAIM_DOC_REQ_FORM);
            rs = ps.executeQuery();

            while (rs.next()) {
                DocReqFormDto docReqFormDto = new DocReqFormDto();
                docReqFormDto.setDocReqFrom(rs.getInt("N_DOC_REQ_FROM"));
                docReqFormDto.setReqFromDesc(rs.getString("V_REQ_FROM_DESC"));
                docReqFormDtoList.add(docReqFormDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return docReqFormDtoList;
    }

    @Override
    public List<ClaimDocumentTypeDto> searchExcludeDocumentType(Connection connection, Integer documentTypeId) {
        List<ClaimDocumentTypeDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_DOCUMENT_TYPE_BY_NOT_DOCUMENT_TYPE_ID);
            ps.setInt(1, documentTypeId);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimDocumentTypeDto claimDocumentTypeDto = getClaimDocumentTypeDto(rs);
                list.add(claimDocumentTypeDto);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<ClaimDocumentTypeDto> getClaimDocumentTypeDtoList(Connection connection, Integer departmentId) {
        List<ClaimDocumentTypeDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_DOCUMENT_TYPE_BY_N_DEPARTMENT_ID);
            ps.setInt(1, departmentId);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimDocumentTypeDto claimDocumentTypeDto = getClaimDocumentTypeDto(rs);
                list.add(claimDocumentTypeDto);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<ClaimDocumentTypeDto> getClaimDocumentTypeDtoList(Connection connection, Integer departmentId, Integer inspectionTypeId) {
        List<ClaimDocumentTypeDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_DOCUMENT_TYPE_BY_N_DEPARTMENT_ID_AND_INSPECTION_TYPE);
            ps.setInt(1, departmentId);
            ps.setInt(2, inspectionTypeId);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimDocumentTypeDto claimDocumentTypeDto = getClaimDocumentTypeDto(rs);
                list.add(claimDocumentTypeDto);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public ClaimDocumentTypeDto insert(Connection connection, ClaimDocumentTypeDto claimDocumentTypeDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_DOCUMENT_TYPE_INSERT);
            ps.setString(++index, claimDocumentTypeDto.getDocumentTypeName());
            ps.setInt(++index, claimDocumentTypeDto.getDepartmentId());
            ps.setString(++index, claimDocumentTypeDto.getIsMandatory());
            ps.setString(++index, claimDocumentTypeDto.getIsPartialLoss());
            ps.setString(++index, claimDocumentTypeDto.getIsTotLoss());
            ps.setString(++index, claimDocumentTypeDto.getIsLumpSum());
            ps.setString(++index, claimDocumentTypeDto.getIsThirdPartyPropVehicle());
            ps.setString(++index, claimDocumentTypeDto.getIsThirdPartyDeath());
            ps.setString(++index, claimDocumentTypeDto.getIsThirdPartyInjuries());
            ps.setInt(++index, claimDocumentTypeDto.getDocReqFrom());
            ps.setString(++index, claimDocumentTypeDto.getReminderDocDisplayName());
            ps.setInt(++index, claimDocumentTypeDto.getSequenceOrder());
            ps.setString(++index, claimDocumentTypeDto.getBranchAllow());
            ps.setString(++index, claimDocumentTypeDto.getRecStatus());
            ps.setString(++index, claimDocumentTypeDto.getInpUserId());
            ps.setString(++index, claimDocumentTypeDto.getInpDateTime());

            if (ps.executeUpdate() > 0) {
                return claimDocumentTypeDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimDocumentTypeDto update(Connection connection, ClaimDocumentTypeDto claimDocumentTypeDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_DOCUMENT_TYPE_UPDATE);
            ps.setString(++index, claimDocumentTypeDto.getDocumentTypeName());
            ps.setInt(++index, claimDocumentTypeDto.getDepartmentId());
            ps.setString(++index, claimDocumentTypeDto.getIsMandatory());
            ps.setString(++index, claimDocumentTypeDto.getIsPartialLoss());
            ps.setString(++index, claimDocumentTypeDto.getIsTotLoss());
            ps.setString(++index, claimDocumentTypeDto.getIsLumpSum());
            ps.setString(++index, claimDocumentTypeDto.getIsThirdPartyPropVehicle());
            ps.setString(++index, claimDocumentTypeDto.getIsThirdPartyDeath());
            ps.setString(++index, claimDocumentTypeDto.getIsThirdPartyInjuries());
            ps.setInt(++index, claimDocumentTypeDto.getDocReqFrom());
            ps.setString(++index, claimDocumentTypeDto.getReminderDocDisplayName());
            ps.setString(++index, claimDocumentTypeDto.getRecStatus());
            ps.setString(++index, claimDocumentTypeDto.getInpUserId());
            ps.setString(++index, claimDocumentTypeDto.getInpDateTime());
            ps.setInt(++index, claimDocumentTypeDto.getDocumentTypeId());

            if (ps.executeUpdate() > 0) {
                return claimDocumentTypeDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField) {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List claimDocType = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();

        final String SEL_SQL = "SELECT * FROM claim_document_type ".concat(SQL_SEARCH).concat(SQL_ORDER);
        ;

        final String COUNT_SQL = "SELECT count(N_DOC_TYPE_ID) AS cnt FROM claim_document_type ".concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ClaimDocumentTypeDto claimDocumentTypeDto = getClaimDocumentType(rs);
                    claimDocumentTypeDto.setIndex(++index);
                    claimDocType.add(claimDocumentTypeDto);
                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(claimDocType);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public String validateDocTypeNmae(Connection connection, String docTypeName) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        String docTypeNames;
        try {
            ps = connection.prepareStatement(VALIDATE_DOC_TYPE_NAME);
            ps.setString(1, docTypeName);
            rs = ps.executeQuery();
            if (rs.next()) {
                docTypeNames = rs.getString("V_DOC_TYPE_NAME");
                return docTypeNames;
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    private ClaimDocumentTypeDto getClaimDocumentTypeDto(ResultSet rs) {
        ClaimDocumentTypeDto claimDocumentTypeDto = new ClaimDocumentTypeDto();
        try {
            claimDocumentTypeDto.setDocumentTypeId(rs.getInt("N_DOC_TYPE_ID"));
            claimDocumentTypeDto.setDocumentTypeName(rs.getString("V_DOC_TYPE_NAME"));
            claimDocumentTypeDto.setDepartmentId(rs.getInt("N_DEPARTMENT_ID"));
            claimDocumentTypeDto.setIsPartialLoss(rs.getString("V_IS_PARTIAL_LOSS"));
            claimDocumentTypeDto.setIsTotLoss(rs.getString("V_IS_TOT_LOSS"));
            claimDocumentTypeDto.setIsLumpSum(rs.getString("V_IS_LUMP_SUM"));
            claimDocumentTypeDto.setDocReqFrom(rs.getInt("N_DOC_REQ_FROM"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimDocumentTypeDto;
    }


    private ClaimDocumentTypeDto getClaimDocumentType(ResultSet rs) {
        ClaimDocumentTypeDto claimDocumentTypeDto = new ClaimDocumentTypeDto();
        try {
            claimDocumentTypeDto.setDocumentTypeId(rs.getInt("N_DOC_TYPE_ID"));
            claimDocumentTypeDto.setDocumentTypeName(rs.getString("V_DOC_TYPE_NAME"));
            claimDocumentTypeDto.setDepartmentId(rs.getInt("N_DEPARTMENT_ID"));
            claimDocumentTypeDto.setIsMandatory(rs.getString("V_IS_MANDATORY"));
            claimDocumentTypeDto.setIsPartialLoss(rs.getString("V_IS_PARTIAL_LOSS"));
            claimDocumentTypeDto.setIsTotLoss(rs.getString("V_IS_TOT_LOSS"));
            claimDocumentTypeDto.setIsLumpSum(rs.getString("V_IS_LUMP_SUM"));
            claimDocumentTypeDto.setIsThirdPartyPropVehicle(rs.getString("V_IS_THIRD_PARTY_PROP_VEHICLE"));
            claimDocumentTypeDto.setIsThirdPartyDeath(rs.getString("V_IS_THIRD_PARTY_DEATH"));
            claimDocumentTypeDto.setIsThirdPartyInjuries(rs.getString("V_IS_THIRD_PARTY_INJURIES"));
            claimDocumentTypeDto.setDocReqFrom(rs.getInt("N_DOC_REQ_FROM"));
            claimDocumentTypeDto.setReminderDocDisplayName(rs.getString("V_REMIN_DOC_DISPLAY_NAME"));
            claimDocumentTypeDto.setRecStatus(rs.getString("V_REC_STATUS"));
            claimDocumentTypeDto.setBranchAllow(rs.getString("V_IS_BRANCH_ALLOW"));
            claimDocumentTypeDto.setInpUserId(rs.getString("V_INP_USER_ID"));
            claimDocumentTypeDto.setInpDateTime(rs.getString("D_INP_DATE_TIME"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimDocumentTypeDto;
    }

}
