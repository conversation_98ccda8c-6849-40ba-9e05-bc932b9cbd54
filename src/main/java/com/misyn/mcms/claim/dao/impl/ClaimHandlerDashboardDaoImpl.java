package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.ClaimHandlerDashboardDao;
import com.misyn.mcms.claim.dto.ClaimStatusDetailDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class ClaimHandlerDashboardDaoImpl implements ClaimHandlerDashboardDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(AssessorDaoImpl.class);

    @Override
    public List<ClaimStatusDetailDto> getClaimHandlerStatusDetail(Connection connection, String fromDate, String toDate) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ClaimStatusDetailDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_COUNT_FROM_CLAIM_HANDLER_STATUS);
            ps.setString(1, fromDate);
            ps.setString(2, toDate);

            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimStatusDetailDto claimStatusDetailDto = new ClaimStatusDetailDto();
                claimStatusDetailDto.setStatusId(rs.getInt("n_ref_id"));
                claimStatusDetailDto.setStatus(rs.getString("para.v_status_desc"));
                claimStatusDetailDto.setCount(rs.getInt("REC_COUNT"));
                list.add(claimStatusDetailDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            ps.close();
        }
        return list;
    }

    @Override
    public List<ClaimStatusDetailDto> getCalsheetStatusDetail(Connection connection, String fromDate, String toDate) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ClaimStatusDetailDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_COUNT_FROM_CALSHEET_STATUS);
            ps.setString(1, fromDate);
            ps.setString(2, toDate);

            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimStatusDetailDto claimStatusDetailDto = new ClaimStatusDetailDto();
                claimStatusDetailDto.setStatusId(rs.getInt("n_ref_id"));
                claimStatusDetailDto.setStatus(rs.getString("para.v_status_desc"));
                claimStatusDetailDto.setCount(rs.getInt("REC_COUNT"));
                list.add(claimStatusDetailDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            ps.close();
        }
        return list;
    }
}
