package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.ClaimImageDao;
import com.misyn.mcms.claim.dto.ClaimImageDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class ClaimImageDaoImpl extends AbstractBaseDao<ClaimImageDaoImpl> implements ClaimImageDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimImageDaoImpl.class);

    @Override
    public ClaimImageDto insertMaster(Connection connection, ClaimImageDto claimImageDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(INSERT_CLAIM_UPLOAD_IMAGES);
            ps.setInt(++index, claimImageDto.getJobRefNo());
            ps.setInt(++index, claimImageDto.getClaimNo());
            ps.setInt(++index, claimImageDto.getDocumentTypeId());
            ps.setString(++index, claimImageDto.getDocumentPath());
            ps.setString(++index, claimImageDto.getDocumentName());
            ps.setString(++index, claimImageDto.getDocumentStatus());
            ps.setString(++index, claimImageDto.getInpStat());
            ps.setString(++index, claimImageDto.getInpUser());
            ps.setString(++index, claimImageDto.getInpDateTime());
            if (ps.executeUpdate() > 0) {
                return claimImageDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;

    }

    @Override
    public ClaimImageDto updateMaster(Connection connection, ClaimImageDto claimImageDto) throws Exception {
        return null;
    }

    @Override
    public ClaimImageDto insertTemporary(Connection connection, ClaimImageDto claimImageDto) throws Exception {
        return null;
    }

    @Override
    public ClaimImageDto updateTemporary(Connection connection, ClaimImageDto claimImageDto) throws Exception {
        return null;
    }

    @Override
    public ClaimImageDto insertHistory(Connection connection, ClaimImageDto claimImageDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {

        try (PreparedStatement ps = connection.prepareStatement(DELETE_FROM_CLAIM_UPLOAD_IMAGES)) {
            ps.setObject(1, id);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimImageDto searchMaster(Connection connection, Object id) throws Exception {
        ClaimImageDto claimImageDto = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_UPLOAD_IMAGES_BY_REF_NO);
            ps.setInt(1, (Integer) id);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimImageDto = this.getClaimImageDto(rs);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
        }
        return claimImageDto;
    }

    @Override
    public ClaimImageDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimImageDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public List<ClaimImageDto> getClaimImageDtoList(Connection connection, Integer claimNo, Integer jobRefNo) {
        List<ClaimImageDto> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_UPLOAD_IMAGES_BY_JOB_REF_NO_AND_CLAIM_NO);
            ps.setInt(1, jobRefNo);
            ps.setInt(2, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimImageDto claimImageDto = this.getClaimImageDto(rs);
                list.add(claimImageDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {

            }
        }
        return list;
    }

    @Override
    public List<ClaimImageDto> findAllClaimImageDtoByClaimNo(Connection connection, Integer claimNo) {
        List<ClaimImageDto> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_UPLOAD_IMAGES_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimImageDto claimImageDto = this.getClaimImageDto(rs);
                list.add(claimImageDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {

            }
        }
        return list;
    }

    @Override
    public List<ClaimImageDto> findAllClaimImageDtoByClaimNoAndInspectionType(Connection connection, Integer claimNo, String inspectionJobNo) {
        List<ClaimImageDto> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_UPLOAD_IMAGES_BY_CLAIM_NO_AND_JOB_NO_IN);
            ps.setInt(1, claimNo);
            ps.setInt(2, claimNo);
            ps.setString(3, inspectionJobNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimImageDto claimImageDto = this.getClaimImageDto(rs);
                list.add(claimImageDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {

            }
        }
        return list;
    }

    @Override
    public ClaimImageDto getClaimImageByRefNo(Connection connection, Integer refNo) {

        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement("SELECT V_DOC_PATH FROM claim_upload_images  WHERE N_REF_NO=?");
            ps.setInt(1, refNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                ClaimImageDto claimImageDto = new ClaimImageDto();
                claimImageDto.setRefNo(refNo);
                claimImageDto.setDocumentPath(rs.getString("V_DOC_PATH"));
                return claimImageDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return null;
    }

    private ClaimImageDto getClaimImageDto(ResultSet rs) {
        ClaimImageDto claimImageDto = new ClaimImageDto();
        try {
            claimImageDto.setRefNo(rs.getInt("t1.N_REF_NO"));
            claimImageDto.setJobRefNo(rs.getInt("t1.N_JOB_REF_NO"));
            claimImageDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
            claimImageDto.setDocumentTypeId(rs.getInt("t1.N_DOC_TYPE_ID"));
            claimImageDto.setDocumentPath(rs.getString("t1.V_DOC_PATH"));
            claimImageDto.setDocumentName(rs.getString("t1.V_DOC_NAME"));
            claimImageDto.setDocumentStatus(rs.getString("t1.V_DOC_STATUS"));
            claimImageDto.setInpStat(rs.getString("t1.V_INP_STAT"));
            claimImageDto.setInpUser(rs.getString("t1.V_INP_USER"));
            claimImageDto.setInpDateTime(Utility.getCustomDateFormat(rs.getString("t1.D_INP_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
            claimImageDto.setToolTip(this.getToolTip(claimImageDto.getDocumentName(), claimImageDto.getInpUser(), claimImageDto.getInpDateTime()));
            claimImageDto.setFileTypeEnum(this.getFileType(claimImageDto.getDocumentTypeId()));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimImageDto;
    }


}
