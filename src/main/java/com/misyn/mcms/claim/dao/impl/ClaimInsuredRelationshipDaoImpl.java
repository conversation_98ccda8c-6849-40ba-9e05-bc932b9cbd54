package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.ClaimInsuredRelationshipDao;
import com.misyn.mcms.claim.dto.ClaimInsuredRelationshipDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class ClaimInsuredRelationshipDaoImpl implements ClaimInsuredRelationshipDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimInsuredRelationshipDaoImpl.class);

    @Override
    public ClaimInsuredRelationshipDto insertMaster(Connection connection, ClaimInsuredRelationshipDto claimInsuredRelationshipDto) throws Exception {
        return null;
    }

    @Override
    public ClaimInsuredRelationshipDto updateMaster(Connection connection, ClaimInsuredRelationshipDto claimInsuredRelationshipDto) throws Exception {
        return null;
    }

    @Override
    public ClaimInsuredRelationshipDto insertTemporary(Connection connection, ClaimInsuredRelationshipDto claimInsuredRelationshipDto) throws Exception {
        return null;
    }

    @Override
    public ClaimInsuredRelationshipDto updateTemporary(Connection connection, ClaimInsuredRelationshipDto claimInsuredRelationshipDto) throws Exception {
        return null;
    }

    @Override
    public ClaimInsuredRelationshipDto insertHistory(Connection connection, ClaimInsuredRelationshipDto claimInsuredRelationshipDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimInsuredRelationshipDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public ClaimInsuredRelationshipDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimInsuredRelationshipDto> searchAll(Connection connection) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ClaimInsuredRelationshipDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement("SELECT salutation_id,salutation_name from claim_salutation;");

            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimInsuredRelationshipDto ClaimInsuredRelationshipDto = getClaimInsuredRelationshipDto(rs);
                list.add(ClaimInsuredRelationshipDto);

            }
        } catch (Exception e) {

        }
        return list;
    }

    private ClaimInsuredRelationshipDto getClaimInsuredRelationshipDto(ResultSet rst) {
        ClaimInsuredRelationshipDto salutationDto = new ClaimInsuredRelationshipDto();
        try {
            salutationDto.setId(rst.getInt(1));
            salutationDto.setDiscription(rst.getString(2));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return salutationDto;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }
}
