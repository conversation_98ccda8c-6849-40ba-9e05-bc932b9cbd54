/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.ClaimPanelAssignUserDao;
import com.misyn.mcms.claim.dto.ClaimPanelAssignUserDto;
import com.misyn.mcms.claim.dto.PanelDecisionDto;
import com.misyn.mcms.claim.dto.PanelMemberListDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class ClaimPanelAssignUserDaoImpl implements ClaimPanelAssignUserDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimPanelAssignUserDaoImpl.class);

    @Override
    public ClaimPanelAssignUserDto insertMaster(Connection connection, ClaimPanelAssignUserDto t) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(INSERT_PANEL_ASSIGN_USER);

            ps.setString(++index, t.getUserId());
            ps.setInt(++index, t.getClaimNo());
            ps.setString(++index, t.getInputUser());
            ps.setString(++index, t.getInputDateTime());
            ps.setString(++index, t.getStatus());
            ps.setString(++index, t.getDmRemark());
            ps.setInt(++index, t.getPanelId());

            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return t;
    }

    @Override
    public ClaimPanelAssignUserDto updateMaster(Connection connection, ClaimPanelAssignUserDto t) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public ClaimPanelAssignUserDto insertTemporary(Connection connection, ClaimPanelAssignUserDto t) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public ClaimPanelAssignUserDto updateTemporary(Connection connection, ClaimPanelAssignUserDto t) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public ClaimPanelAssignUserDto insertHistory(Connection connection, ClaimPanelAssignUserDto t) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public boolean deleteByClaimNo(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement("DELETE FROM claim_panel_assign_user WHERE N_CLAIM_NO=?");
            ps.setInt(++index, claimNo);
            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return true;
    }

    @Override
    public boolean isPanelApprovedOrRejected(Connection connection, Integer claimNo, String userName) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        boolean isApproved = false;
        try {
            ps = connection.prepareStatement(IS_ALL_PANEL_MEMBERS_APPROVED);
            ps.setString(1, userName);
            ps.setInt(2, claimNo);
            rs = ps.executeQuery();
            isApproved = !rs.next();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return isApproved;
    }

    @Override
    public List<ClaimPanelAssignUserDto> getAssignedUSersByClaimNo(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimPanelAssignUserDto> mainPanelDetails = new ArrayList<>();
        try {
            ps = connection.prepareStatement(GET_ASSIGNED_PANEL_MEMBERS);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimPanelAssignUserDto claimPanelAssignUserDto = new ClaimPanelAssignUserDto();
                claimPanelAssignUserDto.setUserId(rs.getString("V_USER_ID"));
                claimPanelAssignUserDto.setInputUser(rs.getString("V_INPUT_USER"));
                claimPanelAssignUserDto.setStatus(rs.getString("V_STATUS"));
                claimPanelAssignUserDto.setInputDateTime(null == rs.getString("D_INPUT_DATETIME") ? Utility.getDate(AppConstant.DEFAULT_DATE_TIME, AppConstant.DATE_TIME_FORMAT) : Utility.getDate(rs.getString("D_INPUT_DATETIME"), AppConstant.DATE_TIME_FORMAT));
                claimPanelAssignUserDto.setUpdatedDateTime(null == rs.getString("D_UPDATED_DATE_TIME") ? Utility.getDate(AppConstant.DEFAULT_DATE_TIME, AppConstant.DATE_TIME_FORMAT) : Utility.getDate(rs.getString("D_UPDATED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                mainPanelDetails.add(claimPanelAssignUserDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return mainPanelDetails;
    }

    @Override
    public void approveClaimByPanelUser(Connection connection, Integer claimNo, UserDto user, String remark) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(APPROVE_OR_REJECT_BY_MAIN_PANEL);
            ps.setString(1, AppConstant.APPROVE);
            ps.setString(2, Utility.sysDateTime());
            ps.setString(3, remark);
            ps.setString(4, user.getUserId());
            ps.setInt(5, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void rejectClaimByPanel(Connection connection, Integer claimNo, UserDto user, String remark) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(APPROVE_OR_REJECT_BY_MAIN_PANEL);
            ps.setString(1, AppConstant.REJECT);
            ps.setString(2, Utility.sysDateTime());
            ps.setString(3, remark);
            ps.setString(4, user.getUserId());
            ps.setInt(5, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public String getRejectedUser(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        String rejectedUser = null;
        try {
            ps = connection.prepareStatement(GET_REJECTED_USER_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                rejectedUser = rs.getString("V_USER_ID");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return rejectedUser;
    }

    @Override
    public boolean isPendingApproval(Connection connection, Integer claimNoToSearch, String v_usrid) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(IS_APPROVAL_PENDING_BY_PANEL_MEMBER);
            ps.setString(1, v_usrid);
            ps.setInt(2, claimNoToSearch);
            rs = ps.executeQuery();
            return rs.next();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean isMainPanelAssigned(Connection connection, Integer claimNoToSearch) throws Exception {
        PreparedStatement ps;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(IS_FORWARDED_TO_MAIN_PANEL);
            ps.setInt(1, claimNoToSearch);
            ps.setInt(2, claimNoToSearch);
            rs = ps.executeQuery();
            return rs.next();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void returnToDmaker(Connection connection, Integer claimNo, String userName, String remark) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(RETURN_BY_MAIN_PANEL);
            ps.setString(1, AppConstant.RETURN_BY_PANEL);
            ps.setString(2, Utility.sysDateTime());
            ps.setString(3, remark);
            ps.setInt(4, claimNo);
            ps.setString(5, userName);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PanelMemberListDto> getPanelMembersForClaim(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<PanelMemberListDto> panelMembers = new ArrayList<>();
        int id = 0;
        try {
            ps = connection.prepareStatement(SQL_GET_PANEL_MEMBERS_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                PanelMemberListDto listDto = new PanelMemberListDto();
                listDto.setId(++id);
                listDto.setUserName(rs.getString("V_USER_ID"));
                listDto.setLastUpdated(null == rs.getString("D_INPUT_DATETIME") ||
                        rs.getString("D_INPUT_DATETIME").isEmpty() ? "N/A" :
                        Utility.getDate(rs.getString("D_INPUT_DATETIME"), AppConstant.DATE_TIME_FORMAT));
                listDto.setStatus(null == rs.getString("V_STATUS") || rs.getString("V_STATUS").isEmpty() ? "N/A" : rs.getString("V_STATUS"));
                listDto.setActionDateTime(null == rs.getString("D_UPDATED_DATE_TIME") ||
                        rs.getString("D_UPDATED_DATE_TIME").isEmpty() ? "N/A" :
                        Utility.getDate(rs.getString("D_UPDATED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                listDto.setAssigned(null != rs.getString("N_CLAIM_NO") && !rs.getString("N_CLAIM_NO").isEmpty());
                listDto.setUserStatus(rs.getString("V_USER_STATUS"));
                panelMembers.add(listDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return panelMembers;
    }

    @Override
    public void updatePanelMembers(Connection connection, Integer claimNo, String name, String userName, boolean isInsert) throws Exception {
        PreparedStatement ps;
        PreparedStatement ps1;
        ResultSet rs;
        boolean isAvailable = false;
        try {
            ps = connection.prepareStatement(CHECK_USER_ALREADY_AVAILABLE);
            ps.setInt(1, claimNo);
            ps.setString(2, name);
            rs = ps.executeQuery();
            isAvailable = rs.next();
            if (!isAvailable && isInsert) {
                ps1 = connection.prepareStatement(INSERT_PANEL_ASSIGN_USER);
                ps1.setString(1, name);
                ps1.setInt(2, claimNo);
                ps1.setString(3, userName);
                ps1.setString(4, Utility.sysDateTime());
                ps1.setString(5, AppConstant.STRING_PENDING);
                ps1.setString(6, AppConstant.STRING_EMPTY);
                ps1.setInt(7, AppConstant.MAIN_PANEL);
                ps1.executeUpdate();
            } else if (isAvailable && !isInsert) {
                ps1 = connection.prepareStatement(DELETE_PANEL_ASSIGN_USER);
                ps1.setInt(1, claimNo);
                ps1.setString(2, name);
                ps1.executeUpdate();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PanelDecisionDto> getCurrentPanelDecision(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        PreparedStatement ps1;
        ResultSet rs1;
        List<PanelDecisionDto> panelDecisionDtos = new ArrayList<>();
        String panelDate = AppConstant.STRING_EMPTY;
        int loopIndex = 0;
        try {
            ps1 = connection.prepareStatement(GET_PANEL_DATE);
            ps1.setInt(1, claimNo);
            rs1 = ps1.executeQuery();
            if (rs1.next()) {
                panelDate = rs1.getString("PANEL_DATE");
            }
            ps = connection.prepareStatement(GET_CURRENT_PANEL_DECISION);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                PanelDecisionDto panelDecisionDto = new PanelDecisionDto();
                panelDecisionDto.setUserName(rs.getString("V_USER_ID"));
                panelDecisionDto.setAssignDateTime(Utility.getDate(rs.getString("D_INPUT_DATETIME"), AppConstant.DATE_TIME_FORMAT));
                panelDecisionDto.setDecision(rs.getString("V_STATUS"));
                panelDecisionDto.setDecisionDateTime(null == rs.getString("D_UPDATED_DATE_TIME") ? "N/A" : Utility.getDate(rs.getString("D_UPDATED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                panelDecisionDto.setPanelDate(panelDate);
                panelDecisionDto.setComment(null == rs.getString("V_REMARK") ? AppConstant.STRING_EMPTY : rs.getString("V_REMARK"));
                if (loopIndex == 0) {
                    panelDecisionDto.setDmRemark(null == rs.getString("V_DM_REMARK") ? "N/A" : rs.getString("V_DM_REMARK"));
                }
                ++loopIndex;
                panelDecisionDto.setHighlightColor("lightblue");
                panelDecisionDtos.add(panelDecisionDto);
                panelDate = AppConstant.STRING_EMPTY;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return panelDecisionDtos;
    }

    @Override
    public boolean checkAllMainPanelUserRejected(Connection connection, Integer claimNo) throws Exception {

        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(IS_ALL_MAIN_PANEL_REJECTED);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                return rs.getInt("pending_count") == AppConstant.ONE_INT;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public ClaimPanelAssignUserDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }


    @Override
    public ClaimPanelAssignUserDto searchByClaimNo(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimPanelAssignUserDto claimPanelAssignUserDto = null;
        try {
            ps = connection.prepareStatement("SELECT\n" +
                    "N_ID,\n" +
                    "V_USER_ID,\n" +
                    "N_CLAIM_NO,\n" +
                    "V_INPUT_USER,\n" +
                    "D_INPUT_DATETIME,\n" +
                    "N_PANEL_ID\n" +
                    "FROM\n" +
                    "claim_panel_assign_user\n" +
                    "WHERE\n" +
                    "N_CLAIM_NO = ? \n" +
                    "ORDER BY N_ID LIMIT 1");
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimPanelAssignUserDto = new ClaimPanelAssignUserDto();
                claimPanelAssignUserDto.setClaimNo(rs.getInt("N_CLAIM_NO"));
                claimPanelAssignUserDto.setId(rs.getInt("N_ID"));
                claimPanelAssignUserDto.setInputDateTime(rs.getString("D_INPUT_DATETIME"));
                claimPanelAssignUserDto.setInputUser(rs.getString("V_INPUT_USER"));
                claimPanelAssignUserDto.setUserId(rs.getString("V_USER_ID"));
                claimPanelAssignUserDto.setPanelId(rs.getInt("N_PANEL_ID"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return claimPanelAssignUserDto;
    }

    @Override
    public ClaimPanelAssignUserDto searchTemporary(Connection connection, Object id) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List<ClaimPanelAssignUserDto> searchAll(Connection connection) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

}
