package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.ClaimProcessFlowDao;
import com.misyn.mcms.claim.dto.ClaimProcessFlowDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class ClaimProcessFlowDaoImpl implements ClaimProcessFlowDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimProcessFlowDaoImpl.class);

    @Override
    public ClaimProcessFlowDto insert(Connection connection, ClaimProcessFlowDto claimProcessFlowDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_CLAIM_PROCESS_FLOW);
            ps.setInt(++index, claimProcessFlowDto.getClaimNo());
            ps.setInt(++index, claimProcessFlowDto.getClaimStatus());
            ps.setString(++index, claimProcessFlowDto.getTask());
            ps.setString(++index, claimProcessFlowDto.getAssignUserId());
            ps.setString(++index, claimProcessFlowDto.getIsVisible());
            ps.setString(++index, claimProcessFlowDto.getInpUserId());
            ps.setString(++index, claimProcessFlowDto.getInpDatetime());
            if (ps.executeUpdate() > 0) {
                return claimProcessFlowDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e);
        }
        return null;
    }

    @Override
    public List<ClaimProcessFlowDto> searchAllByClaimNo(Connection connection, Integer claimNo) {
        List<ClaimProcessFlowDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        int index = 0;

        try {
            PreparedStatement preparedStatement = ps = connection.prepareStatement(SELECT_ALL);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimProcessFlowDto claimProcessFlowDto = new ClaimProcessFlowDto();
                claimProcessFlowDto.setTask(rs.getString("task"));
                claimProcessFlowDto.setAssignUserId(rs.getString("assign_user_id"));
                claimProcessFlowDto.setIsVisible(rs.getString("is_visible"));
                claimProcessFlowDto.setInpUserId(rs.getString("inp_user_id"));
                claimProcessFlowDto.setInpDatetime(Utility.getDate(rs.getString("inp_date_time"), AppConstant.DATE_TIME_FORMAT));
                claimProcessFlowDto.setTaskCompletedDateTime(Utility.getDate(rs.getString("inp_date_time"), AppConstant.DATE_TIME_FORMAT));
                if (!list.isEmpty()) {
                    ClaimProcessFlowDto previousClaimProcessFlowDto = list.get(index - 1);
                    claimProcessFlowDto.setInpDatetime(previousClaimProcessFlowDto.getTaskCompletedDateTime());
                }
                list.add(claimProcessFlowDto);
                index++;
            }
            rs.close();
            ps.close();
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }
}
