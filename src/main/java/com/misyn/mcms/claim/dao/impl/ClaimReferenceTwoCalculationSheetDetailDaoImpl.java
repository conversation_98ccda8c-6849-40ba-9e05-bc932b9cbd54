package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.ClaimReferenceTwoCalculationSheetDetailDao;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetDetailDto;
import com.misyn.mcms.claim.dto.ClaimHandlerGridDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class ClaimReferenceTwoCalculationSheetDetailDaoImpl extends BaseAbstract<ClaimReferenceTwoCalculationSheetDetailDaoImpl> implements ClaimReferenceTwoCalculationSheetDetailDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimReferenceTwoCalculationSheetDetailDaoImpl.class);

    @Override
    public ClaimCalculationSheetDetailDto insertMaster(Connection connection, ClaimCalculationSheetDetailDto claimCalculationSheetDetailDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_DETAIL_INSERT);
            ps.setInt(++index, claimCalculationSheetDetailDto.getCalSheetId());
            ps.setInt(++index, claimCalculationSheetDetailDto.getItemNo());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getEstimatedAmount());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getBillAmount());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getApprovedAmount());
            ps.setString(++index, claimCalculationSheetDetailDto.getRemarks());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getNbtRate());
            ps.setString(++index, claimCalculationSheetDetailDto.getNbtType());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getNbtAmount());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getVatRate());
            ps.setString(++index, claimCalculationSheetDetailDto.getVatType());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getVatAmount());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getOa());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getOaAmount());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getTotalAmount());
            ps.setString(++index, claimCalculationSheetDetailDto.getBillChecked());
            ps.setString(++index, claimCalculationSheetDetailDto.getRecordType());
            ps.setString(++index, claimCalculationSheetDetailDto.getIsRemoveVat());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getRemoveVatRate());
            ps.setString(++index, claimCalculationSheetDetailDto.getIsAddNbt());
            ps.setString(++index, claimCalculationSheetDetailDto.getAddVatType());

            if (ps.executeUpdate() > 0) {
                return claimCalculationSheetDetailDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetDetailDto updateMaster(Connection connection, ClaimCalculationSheetDetailDto claimCalculationSheetDetailDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_DETAIL_UPDATE);
            ps.setInt(++index, claimCalculationSheetDetailDto.getCalSheetId());
            ps.setInt(++index, claimCalculationSheetDetailDto.getItemNo());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getEstimatedAmount());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getBillAmount());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getApprovedAmount());
            ps.setString(++index, claimCalculationSheetDetailDto.getRemarks());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getNbtRate());
            ps.setString(++index, claimCalculationSheetDetailDto.getNbtType());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getNbtAmount());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getVatRate());
            ps.setString(++index, claimCalculationSheetDetailDto.getVatType());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getVatAmount());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getOa());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getOaAmount());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getTotalAmount());
            ps.setString(++index, claimCalculationSheetDetailDto.getBillChecked());
            ps.setString(++index, claimCalculationSheetDetailDto.getRecordType());
            ps.setString(++index, claimCalculationSheetDetailDto.getIsRemoveVat());
            ps.setBigDecimal(++index, claimCalculationSheetDetailDto.getRemoveVatRate());
            ps.setString(++index, claimCalculationSheetDetailDto.getIsAddNbt());
            ps.setString(++index, claimCalculationSheetDetailDto.getVatType());
            ps.setInt(++index, claimCalculationSheetDetailDto.getCalSheetDetailId());

            if (ps.executeUpdate() > 0) {
                return claimCalculationSheetDetailDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetDetailDto insertTemporary(Connection connection, ClaimCalculationSheetDetailDto claimCalculationSheetDetailDto) throws Exception {
        return null;
    }

    @Override
    public ClaimCalculationSheetDetailDto updateTemporary(Connection connection, ClaimCalculationSheetDetailDto claimCalculationSheetDetailDto) throws Exception {
        return null;
    }

    @Override
    public ClaimCalculationSheetDetailDto insertHistory(Connection connection, ClaimCalculationSheetDetailDto claimCalculationSheetDetailDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_DETAIL_DELETE);
            ps.setObject(1, id);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void deleteByCalSheetNo(Connection connection, Integer calSheetId) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_DETAIL_DELETE_BY_CAL_SHEET_NO);
            ps.setInt(1, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimCalculationSheetDetailDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimCalculationSheetDetailDto claimCalculationSheetDetailDto = new ClaimCalculationSheetDetailDto();
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_DETAIL_SEARCH);
            ps.setObject(1, id);
            rs = ps.executeQuery();
            if (rs.next()) {

                claimCalculationSheetDetailDto.setCalSheetDetailId(rs.getInt("N_CAL_SHEET_DETAIL_ID"));
                claimCalculationSheetDetailDto.setCalSheetId(rs.getInt("N_CAL_SHEET_ID"));
                claimCalculationSheetDetailDto.setItemNo(rs.getInt("N_ITEM_NO"));
                claimCalculationSheetDetailDto.setEstimatedAmount(rs.getBigDecimal("N_ESTIMATED_AMOUNT"));
                claimCalculationSheetDetailDto.setBillAmount(rs.getBigDecimal("N_BILL_AMOUNT"));
                claimCalculationSheetDetailDto.setApprovedAmount(rs.getBigDecimal("N_APPROVED_AMOUNT"));
                claimCalculationSheetDetailDto.setRemarks(rs.getString("V_REMARKS"));
                claimCalculationSheetDetailDto.setNbtRate(rs.getBigDecimal("N_NBT_RATE"));
                claimCalculationSheetDetailDto.setNbtType(rs.getString("V_NBT_TYPE"));
                claimCalculationSheetDetailDto.setNbtAmount(rs.getBigDecimal("N_NBT_AMOUNT"));
                claimCalculationSheetDetailDto.setVatRate(rs.getBigDecimal("N_VAT_RATE"));
                claimCalculationSheetDetailDto.setVatType(rs.getString("V_VAT_TYPE"));
                claimCalculationSheetDetailDto.setVatAmount(rs.getBigDecimal("N_VAT_AMOUNT"));
                claimCalculationSheetDetailDto.setOa(rs.getBigDecimal("N_OA"));
                claimCalculationSheetDetailDto.setOaAmount(rs.getBigDecimal("N_OA_AMOUNT"));
                claimCalculationSheetDetailDto.setTotalAmount(rs.getBigDecimal("N_TOTAL_AMOUNT"));
                claimCalculationSheetDetailDto.setBillChecked(rs.getString("V_BILL_CHECKED"));
                claimCalculationSheetDetailDto.setRecordType(rs.getString("V_RECORD_TYPE"));
                claimCalculationSheetDetailDto.setIsRemoveVat(rs.getString("V_REMOVE_VAT"));
                claimCalculationSheetDetailDto.setRemoveVatRate(rs.getBigDecimal("N_REMOVE_VAT_RATE"));
                claimCalculationSheetDetailDto.setIsAddNbt(rs.getString("V_ADD_NBT"));
                claimCalculationSheetDetailDto.setAddVatType(rs.getString("V_ADD_VAT_TYPE"));

                return claimCalculationSheetDetailDto;
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetDetailDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimCalculationSheetDetailDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_DETAIL_SEARCH_ALL);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimCalculationSheetDetailDto claimCalculationSheetDetailDto = new ClaimCalculationSheetDetailDto();
                claimCalculationSheetDetailDto.setCalSheetDetailId(rs.getInt("N_CAL_SHEET_DETAIL_ID"));
                claimCalculationSheetDetailDto.setCalSheetId(rs.getInt("N_CAL_SHEET_ID"));
                claimCalculationSheetDetailDto.setItemNo(rs.getInt("N_ITEM_NO"));
                claimCalculationSheetDetailDto.setEstimatedAmount(rs.getBigDecimal("N_ESTIMATED_AMOUNT"));
                claimCalculationSheetDetailDto.setBillAmount(rs.getBigDecimal("N_BILL_AMOUNT"));
                claimCalculationSheetDetailDto.setApprovedAmount(rs.getBigDecimal("N_APPROVED_AMOUNT"));
                claimCalculationSheetDetailDto.setRemarks(rs.getString("V_REMARKS"));
                claimCalculationSheetDetailDto.setNbtRate(rs.getBigDecimal("N_NBT_RATE"));
                claimCalculationSheetDetailDto.setNbtType(rs.getString("V_NBT_TYPE"));
                claimCalculationSheetDetailDto.setNbtAmount(rs.getBigDecimal("N_NBT_AMOUNT"));
                claimCalculationSheetDetailDto.setVatRate(rs.getBigDecimal("N_VAT_RATE"));
                claimCalculationSheetDetailDto.setVatType(rs.getString("V_VAT_TYPE"));
                claimCalculationSheetDetailDto.setVatAmount(rs.getBigDecimal("N_VAT_AMOUNT"));
                claimCalculationSheetDetailDto.setOa(rs.getBigDecimal("N_OA"));
                claimCalculationSheetDetailDto.setOaAmount(rs.getBigDecimal("N_OA_AMOUNT"));
                claimCalculationSheetDetailDto.setTotalAmount(rs.getBigDecimal("N_TOTAL_AMOUNT"));
                claimCalculationSheetDetailDto.setBillChecked(rs.getString("V_BILL_CHECKED"));
                claimCalculationSheetDetailDto.setRecordType(rs.getString("V_RECORD_TYPE"));
                claimCalculationSheetDetailDto.setIsRemoveVat(rs.getString("V_REMOVE_VAT"));
                claimCalculationSheetDetailDto.setRemoveVatRate(rs.getBigDecimal("N_REMOVE_VAT_RATE"));
                claimCalculationSheetDetailDto.setIsAddNbt(rs.getString("V_ADD_NBT"));
                claimCalculationSheetDetailDto.setAddVatType(rs.getString("V_ADD_VAT_TYPE"));

                claimCalculationSheetDetailDtoList.add(claimCalculationSheetDetailDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimCalculationSheetDetailDtoList;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public List<ClaimCalculationSheetDetailDto> searchByCalSheetId(Connection connection, Integer calSheetId) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimCalculationSheetDetailDto> claimCalculationSheetDetailDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_DETAIL_SEARCH_ALL_BY_CAL_SHEET_NO);
            ps.setInt(1, calSheetId);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimCalculationSheetDetailDto claimCalculationSheetDetailDto = new ClaimCalculationSheetDetailDto();
                claimCalculationSheetDetailDto.setCalSheetDetailId(rs.getInt("N_CAL_SHEET_DETAIL_ID"));
                claimCalculationSheetDetailDto.setCalSheetId(rs.getInt("N_CAL_SHEET_ID"));
                claimCalculationSheetDetailDto.setItemNo(rs.getInt("N_ITEM_NO"));
                claimCalculationSheetDetailDto.setEstimatedAmount(rs.getBigDecimal("N_ESTIMATED_AMOUNT"));
                claimCalculationSheetDetailDto.setBillAmount(rs.getBigDecimal("N_BILL_AMOUNT"));
                claimCalculationSheetDetailDto.setApprovedAmount(rs.getBigDecimal("N_APPROVED_AMOUNT"));
                claimCalculationSheetDetailDto.setRemarks(rs.getString("V_REMARKS"));
                claimCalculationSheetDetailDto.setNbtRate(rs.getBigDecimal("N_NBT_RATE"));
                claimCalculationSheetDetailDto.setNbtType(rs.getString("V_NBT_TYPE"));
                claimCalculationSheetDetailDto.setNbtAmount(rs.getBigDecimal("N_NBT_AMOUNT"));
                claimCalculationSheetDetailDto.setVatRate(rs.getBigDecimal("N_VAT_RATE"));
                claimCalculationSheetDetailDto.setVatType(rs.getString("V_VAT_TYPE"));
                claimCalculationSheetDetailDto.setVatAmount(rs.getBigDecimal("N_VAT_AMOUNT"));
                claimCalculationSheetDetailDto.setOa(rs.getBigDecimal("N_OA"));
                claimCalculationSheetDetailDto.setOaAmount(rs.getBigDecimal("N_OA_AMOUNT"));
                claimCalculationSheetDetailDto.setTotalAmount(rs.getBigDecimal("N_TOTAL_AMOUNT"));
                claimCalculationSheetDetailDto.setBillChecked(rs.getString("V_BILL_CHECKED"));
                claimCalculationSheetDetailDto.setRecordType(rs.getString("V_RECORD_TYPE"));
                claimCalculationSheetDetailDto.setIsRemoveVat(rs.getString("V_REMOVE_VAT"));
                claimCalculationSheetDetailDto.setRemoveVatRate(rs.getBigDecimal("N_REMOVE_VAT_RATE"));
                claimCalculationSheetDetailDto.setIsAddNbt(rs.getString("V_ADD_NBT"));
                claimCalculationSheetDetailDto.setAddVatType(rs.getString("V_ADD_VAT_TYPE"));

                claimCalculationSheetDetailDtoList.add(claimCalculationSheetDetailDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return claimCalculationSheetDetailDtoList;
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, int type) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        String[] orderFields = {orderField};
        Object[] instances = {AppConstant.PRIORITY_HIGH, AppConstant.PRIORITY_NORMAL};
        final String SQL_ORDER = orderByCases(start, length, orderType, "t1.V_PRIORITY", orderFields, instances).toString();

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.D_ACCID_DATE BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        final String SEL_SQL = CALIM_CALCULATION_SHEET_DETAILS_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);

        final String COUNT_SQL = CALIM_CALCULATION_SHEET_DETAILS_GRID_COUNT.concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    if (6 == type || 7 == type) {
                        if (isPendingInspectionFound(conn, rs.getInt("t1.N_CLIM_NO"))) {
                            --count;
                            continue;
                        }
                    } else if (60 == type || 70 == type) {
                        if (!isPendingInspectionFound(conn, rs.getInt("t1.N_CLIM_NO"))) {
                            --count;
                            continue;
                        }
                    }
                    ClaimHandlerGridDto claimHandlerDto = populateClaimHandler(rs);
                    claimHandlerDto.setIndex(++index);
                    handlerList.add(claimHandlerDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public DataGridDto getClaimHandlerRtePendingDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.D_ACCID_DATE BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        final String SEL_SQL = CALIM_CALCULATION_SHEET_DETAILS_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);

        try {
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    if (isPendingInspectionFound(conn, rs.getInt("t1.N_CLIM_NO"))) {
                        ClaimHandlerGridDto claimHandlerDto = populateClaimHandler(rs);
                        claimHandlerDto.setIndex(++index);
                        handlerList.add(claimHandlerDto);
                        ++count;
                    }
                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    private ClaimHandlerGridDto populateClaimHandler(ResultSet rs) {
        ClaimHandlerGridDto claimHandlerDto = new ClaimHandlerGridDto();
        try {
            claimHandlerDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
            claimHandlerDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
            claimHandlerDto.setPolicyNumberValue(rs.getString("t1.V_POL_NUMBER"));
            claimHandlerDto.setPriority(null == rs.getString("t1.V_PRIORITY") || rs.getString("t1.V_PRIORITY").isEmpty() ? AppConstant.PRIORITY_NORMAL : rs.getString("t1.V_PRIORITY"));
            claimHandlerDto.setInspectionStatus(0);
            claimHandlerDto.setAssignDateTime(rs.getString("t1.D_ACCID_DATE"));
            claimHandlerDto.setDocumentStatus(null == rs.getString("t2.V_IS_ALL_DOC_UPLOAD") ? AppConstant.NO : rs.getString("t2.V_IS_ALL_DOC_UPLOAD"));
            claimHandlerDto.setDocumentCheck(null == rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS") ? AppConstant.NO : rs.getString("t2.V_IS_CHECK_ALL_MND_DOCS"));
            claimHandlerDto.setLiabilityStatus(null == rs.getString("t2.V_LIABILITY_APRV_STATUS") ? AppConstant.NO : rs.getString("t2.V_LIABILITY_APRV_STATUS"));
            claimHandlerDto.setClaimStatus(rs.getInt("t2.N_CLAIM_STATUS"));
            claimHandlerDto.setNoObjection(rs.getString("t3.V_NO_OBJECTION_STATUS"));
            claimHandlerDto.setPremiumOutstanding(rs.getString("t3.V_PREMIUM_OUTSTANDING_STATUS"));
            claimHandlerDto.setClaimStatusDesc(rs.getString("t4.v_status_desc"));
            claimHandlerDto.setTxnId(rs.getInt("t2.N_TXN_NO"));
            claimHandlerDto.setAcr(rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_APRV_TOT_ACR_AMOUNT").toString());
            claimHandlerDto.setPresentReverseAmount(rs.getBigDecimal("t2.N_RESERVE_AMOUNT") == null ? "0" : rs.getBigDecimal("t2.N_RESERVE_AMOUNT").toString());
            claimHandlerDto.setFileStore(rs.getString("t2.V_IS_FILE_STORE"));
            claimHandlerDto.setPartialLoss(rs.getInt("t2.N_LOSS_TYPE"));
            claimHandlerDto.setLiabilityAssignUser(rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_LIABILITY_APRV_ASSIGN_USER"));
            claimHandlerDto.setLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT).equals(AppConstant.DEFAULT_DATE)
                    ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.D_LIABILITY_APRV_ASSIGN_DATE_TIME"), AppConstant.DATE_FORMAT));

            claimHandlerDto.setIntLiabilityAssignUser(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_INIT_LIABILITY_ASSIGN_USER_ID"));

            claimHandlerDto.setIntLiabilityAssignDatetime(Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ? AppConstant.STRING_EMPTY : Utility.getDate(rs.getString("t2.V_INIT_LIABILITY_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
            claimHandlerDto.setInvestigationStatus(rs.getString("t2.V_INVESTIGATION_STATUS"));
            claimHandlerDto.setCalSheetInpUserId(rs.getString("t3.V_INPUT_USER"));
            claimHandlerDto.setScrutinizingTeamAssignUserId(rs.getString("t3.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t3.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID"));
            claimHandlerDto.setScrutinizingTeamAssignDateTime(AppConstant.DEFAULT_DATE_TIME.equals(rs.getString("t3.D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME")) ? "" :
                    Utility.getDate(rs.getString("t3.D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
            claimHandlerDto.setSparePartCoordinatorId(rs.getString("t3.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t3.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID"));
            claimHandlerDto.setPaymentApproveUserId(rs.getString("t3.V_APR_ASSIGN_USER") == null ? AppConstant.STRING_EMPTY : rs.getString("t3.V_APR_ASSIGN_USER"));
            claimHandlerDto.setMofaTeamUserId(rs.getString("t3.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t3.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID"));
            claimHandlerDto.setSpecialTeamUserId(rs.getString("t3.V_SPECIAL_TEAM_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t3.V_SPECIAL_TEAM_ASSIGN_USER_ID"));
            claimHandlerDto.setCalSheetNo(rs.getInt("t3.N_CAL_SHEET_ID"));
            claimHandlerDto.setCloseStatus(rs.getString("t2.V_CLOSE_STATUS"));
            claimHandlerDto.setCalSheetStatus(rs.getString("CALSHEETSTATUS"));
            if (null != rs.getString("t2.V_CLOSE_STATUS") && rs.getString("t2.V_CLOSE_STATUS").equals("CLOSE")) {
                claimHandlerDto.setFinalizeStatus(AppConstant.YES);
            } else {
                claimHandlerDto.setFinalizeStatus(AppConstant.NO);
            }
            claimHandlerDto.setIsNoObjectionUpload(null == rs.getString("t3.V_IS_NO_OBJECTION_UPLOAD") ? AppConstant.STRING_EMPTY : rs.getString("t3.V_IS_NO_OBJECTION_UPLOAD"));
            claimHandlerDto.setNoObjectionDocRefNo(null == rs.getString("t3.N_NO_OBJECTION_DOC_REF_NO") ? AppConstant.ZERO_INT : rs.getInt("t3.N_NO_OBJECTION_DOC_REF_NO"));
            claimHandlerDto.setIsPremiumOutstandingUpload(null == rs.getString("t3.V_IS_PREMIUM_OUTSTANDING_UPLOAD") ? AppConstant.STRING_EMPTY : rs.getString("t3.V_IS_PREMIUM_OUTSTANDING_UPLOAD"));
            claimHandlerDto.setPremiumOutstandingDocRefNo(null == rs.getString("t3.N_PREMIUM_OUTSTANDING_DOC_REF_NO") ? AppConstant.ZERO_INT : rs.getInt("t3.N_PREMIUM_OUTSTANDING_DOC_REF_NO"));

            claimHandlerDto.setRteAssignUserId(null == rs.getString("V_RTE_ASSIGN_USER_ID") ? AppConstant.STRING_EMPTY : rs.getString("V_RTE_ASSIGN_USER_ID"));
            claimHandlerDto.setRteAssignDateTime(null == rs.getString("D_RTE_ASSIGN_DATE_TIME") ? AppConstant.STRING_EMPTY :
                    Utility.getDate(rs.getString("D_RTE_ASSIGN_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return claimHandlerDto;
    }

    private boolean isPendingInspectionFound(Connection conn, int claimNo) {
        try {
            PreparedStatement ps = conn.prepareStatement("SELECT 1 from rte_pending_claim_detail WHERE claim_no = ? limit 1");
            ps.setInt(1, claimNo);
            return ps.executeQuery().next();
        } catch (SQLException e) {
            LOGGER.error(e.getMessage(), e);
        }
        return false;

    }

    @Override
    public BigDecimal getTotalPayableAmount(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        BigDecimal totalPayableAmount = BigDecimal.ZERO;
        try {
            ps = connection.prepareStatement(SELECT_TOT_PAYABLE_AMOUNT);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();

            if (rs.next()) {
                totalPayableAmount = null == rs.getBigDecimal("tot") ? BigDecimal.ZERO : rs.getBigDecimal("tot");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return totalPayableAmount;
    }

    @Override
    public BigDecimal getTotalPayableAmountApprovedCalsheet(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        BigDecimal totalPayableAmount = BigDecimal.ZERO;
        try {
            ps = connection.prepareStatement(SELECT_PAYABLE_AMOUNT_APPROVED_CALSHEET);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();

            if (rs.next()) {
                totalPayableAmount = null == rs.getBigDecimal("tot") ? BigDecimal.ZERO : rs.getBigDecimal("tot");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return totalPayableAmount;
    }

    @Override
    public void updateBillCheckById(Connection connection, Integer id, String isChecked) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_BILLCHECKED_BY_ID);
            ps.setString(1, isChecked);
            ps.setInt(2, id);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }
}
