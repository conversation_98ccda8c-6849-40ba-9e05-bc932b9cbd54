package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.ClaimReferenceTwoCalculationSheetMainTempDao;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetMainTempDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class ClaimReferenceTwoCalculationSheetMainTempDaoImpl implements ClaimReferenceTwoCalculationSheetMainTempDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimReferenceTwoCalculationSheetMainTempDaoImpl.class);

    @Override
    public ClaimCalculationSheetMainTempDto insertMaster(Connection connection, ClaimCalculationSheetMainTempDto claimCalculationSheetMainTempDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_CALCULATION_SHEET_MAIN_TEMP_INSERT);
            ps.setInt(++index, claimCalculationSheetMainTempDto.getClaimNo());
            ps.setInt(++index, claimCalculationSheetMainTempDto.getCalsheetId());
            ps.setInt(++index, claimCalculationSheetMainTempDto.getCalsheetType());
            ps.setBigDecimal(++index, claimCalculationSheetMainTempDto.getTotalApprovedAcr());
            ps.setBigDecimal(++index, claimCalculationSheetMainTempDto.getReserveAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainTempDto.getPrevReserveAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainTempDto.getReserveAmountAfterApproved());
            ps.setBigDecimal(++index, claimCalculationSheetMainTempDto.getPrevReserveAmountAfterApproved());
            ps.setBigDecimal(++index, claimCalculationSheetMainTempDto.getPayableAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainTempDto.getPrevPayableAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainTempDto.getAdvanceAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainTempDto.getPrevAdvanceAmount());


            if (ps.executeUpdate() > 0) {
                return claimCalculationSheetMainTempDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetMainTempDto updateMaster(Connection connection, ClaimCalculationSheetMainTempDto claimCalculationSheetMainTempDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_RESERVED_AMOUNT_BY_CALSHEET);
            ps.setInt(++index, claimCalculationSheetMainTempDto.getCalsheetType());
            ps.setBigDecimal(++index, claimCalculationSheetMainTempDto.getReserveAmount());
            ps.setBigDecimal(++index, claimCalculationSheetMainTempDto.getReserveAmountAfterApproved());
            ps.setBigDecimal(++index, claimCalculationSheetMainTempDto.getPayableAmount());
            ps.setInt(++index, claimCalculationSheetMainTempDto.getCalsheetId());

            if (ps.executeUpdate() > 0) {
                return claimCalculationSheetMainTempDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimCalculationSheetMainTempDto insertTemporary(Connection connection, ClaimCalculationSheetMainTempDto claimCalculationSheetMainTempDto) throws Exception {
        return null;
    }

    @Override
    public ClaimCalculationSheetMainTempDto updateTemporary(Connection connection, ClaimCalculationSheetMainTempDto claimCalculationSheetMainTempDto) throws Exception {
        return null;
    }

    @Override
    public ClaimCalculationSheetMainTempDto insertHistory(Connection connection, ClaimCalculationSheetMainTempDto claimCalculationSheetMainTempDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(DELETE_FROM_CALSHEET_TEMP_BY_CALSHEET_ID);
            ps.setObject(1, id);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimCalculationSheetMainTempDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public ClaimCalculationSheetMainTempDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimCalculationSheetMainTempDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public ClaimCalculationSheetMainTempDto searchByCalsheetId(Connection connection, Integer calSheetId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimCalculationSheetMainTempDto dto;
        try {
            ps = connection.prepareStatement(SELECT_ALL_FROM_CALSHEET_MAIN_TEMP_BY_CALSHEET_ID);
            ps.setInt(1, calSheetId);
            rs = ps.executeQuery();
            if (rs.next()) {
                return getClaimCalculationSheetMainDto(rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return null;
    }

    @Override
    public void updateReserveAmountAfterApproved(Connection connection, BigDecimal reserveAmountAfterApproved, Integer calSheetId) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_RESERVE_AMOUNT_AFTER_APPROVED_BY_CALSHEET_ID);
            ps.setBigDecimal(++index, reserveAmountAfterApproved);
            ps.setInt(++index, calSheetId);

            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public void updateAdvanceAmount(Connection connection, BigDecimal advanceAmount, Integer calSheetId) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_ADVANCE_AMOUNT_BY_CALSHEET_ID);
            ps.setBigDecimal(++index, advanceAmount);
            ps.setInt(++index, calSheetId);

            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public void updatePrevReserveAmountAfterApproved(Connection connection, BigDecimal prevReserveAmountAfterApproved, Integer calSheetId) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_PREV_RESERVE_AMOUNT_AFTER_APPROVED_BY_CALSHEET_ID);
            ps.setBigDecimal(++index, prevReserveAmountAfterApproved);
            ps.setInt(++index, calSheetId);

            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public void updatePrevAdvanceAmount(Connection connection, BigDecimal prevAdvanceAmount, Integer calSheetId) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_PREV_ADVANCE_AMOUNT_BY_CALSHEET_ID);
            ps.setBigDecimal(++index, prevAdvanceAmount);
            ps.setInt(++index, calSheetId);

            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public boolean isAlreadySaved(Connection connection, Integer calsheetId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_CALSHEET_TEMP_DETAILS_BY_CALSHEET_ID);
            ps.setInt(1, calsheetId);
            rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return false;
    }

    @Override
    public List<ClaimCalculationSheetMainTempDto> searchAllByClaimNo(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ClaimCalculationSheetMainTempDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_FROM_CALSHEET_MAIN_TEMP_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                list.add(getClaimCalculationSheetMainDto(rs));
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return list;
    }

    @Override
    public void updateTempDetailsWhenAcrIsUpdated(Connection connection, ClaimCalculationSheetMainTempDto dto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_DETAILS_BY_CALSHEET_ID);
            ps.setBigDecimal(++index, dto.getTotalApprovedAcr());
            ps.setBigDecimal(++index, dto.getReserveAmount());
            ps.setBigDecimal(++index, dto.getPrevReserveAmount());
            ps.setBigDecimal(++index, dto.getReserveAmountAfterApproved());
            ps.setBigDecimal(++index, dto.getPrevReserveAmountAfterApproved());
            ps.setBigDecimal(++index, dto.getAdvanceAmount());
            ps.setBigDecimal(++index, dto.getPrevAdvanceAmount());
            ps.setInt(++index, dto.getCalsheetId());
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    private ClaimCalculationSheetMainTempDto getClaimCalculationSheetMainDto(ResultSet rs) throws Exception {

        ClaimCalculationSheetMainTempDto tempDto = new ClaimCalculationSheetMainTempDto();
        try {
            tempDto.setTxnId(rs.getInt("txn_id"));
            tempDto.setClaimNo(rs.getInt("claim_no"));
            tempDto.setCalsheetId(rs.getInt("calsheet_id"));
            tempDto.setCalsheetType(rs.getInt("calsheet_type"));
            tempDto.setTotalApprovedAcr(rs.getBigDecimal("total_approved_acr"));
            tempDto.setReserveAmount(rs.getBigDecimal("reserve_amount"));
            tempDto.setPrevReserveAmount(rs.getBigDecimal("prev_reserve_amount"));
            tempDto.setReserveAmountAfterApproved(rs.getBigDecimal("reserve_amount_after_approved"));
            tempDto.setPrevReserveAmountAfterApproved(rs.getBigDecimal("prev_reserve_amount_after_approved"));
            tempDto.setPayableAmount(rs.getBigDecimal("payable_amount"));
            tempDto.setPrevPayableAmount(rs.getBigDecimal("prev_payable_amount"));
            tempDto.setAdvanceAmount(rs.getBigDecimal("advance_amount"));
            tempDto.setPrevAdvanceAmount(rs.getBigDecimal("prev_advance_amount"));
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return tempDto;
    }
}
