package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.admin.admin.dao.BranchMstDao;
import com.misyn.mcms.admin.admin.dao.impl.BranchMstDaoImpl;
import com.misyn.mcms.admin.admin.dto.BranchDetailDto;
import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.ClaimReferenceTwoPaymentDispatchDao;
import com.misyn.mcms.claim.dto.ClaimPaymentDispatchDto;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
public class ClaimReferenceTwoPaymentDispatchDaoImpl extends BaseAbstract<ClaimReferenceTwoPaymentDispatchDaoImpl> implements ClaimReferenceTwoPaymentDispatchDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimReferenceTwoPaymentDispatchDaoImpl.class);
    private BranchMstDao branchMstDao = new BranchMstDaoImpl();

    @Override
    public ClaimPaymentDispatchDto getVoucherDetails(Connection connection, String voucherNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_PAYMENT_DISPATCH_DETAILS_BY_VOUCHER_NO);
            ps.setString(1, voucherNo);
            rs = ps.executeQuery();
            while(rs.next()) {
                return getDispatchDto(connection, rs);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return null;
    }

    @Override
    public ClaimPaymentDispatchDto getVoucherDetails(Connection connection, Integer payeeId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_PAYMENT_DISPATCH_DETAILS_BY_PAYEE_ID);
            ps.setInt(1, payeeId);
            rs = ps.executeQuery();
            while(rs.next()) {
                return getDispatchDto(connection, rs);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return null;
    }

    @Override
    public void savePaymentDispatch(Connection connection, ClaimPaymentDispatchDto claimPaymentDispatchDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_INTO_CLAIM_PAYMENT_DISPATCH);
            ps.setInt(++index, claimPaymentDispatchDto.getPayeeId());
            ps.setInt(++index, claimPaymentDispatchDto.getCalSheetId());
            ps.setInt(++index, claimPaymentDispatchDto.getClaimNo());
            ps.setString(++index, claimPaymentDispatchDto.getVoucherNo());
            ps.setString(++index, claimPaymentDispatchDto.getChequeNo());
            ps.setString(++index, null == claimPaymentDispatchDto.getBranchDetailDto().getBranchCode() ? claimPaymentDispatchDto.getDispatchLocation() : claimPaymentDispatchDto.getBranchDetailDto().getBranchCode());
            ps.setString(++index, claimPaymentDispatchDto.getDispatchedLocation().getBranchCode());
            ps.setString(++index, claimPaymentDispatchDto.getDispatchUser());
            ps.setString(++index, claimPaymentDispatchDto.getDispatchDateTime());
            ps.setString(++index, claimPaymentDispatchDto.getChequeDispatchStatus());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void deleteByCalSheetId(Connection connection, Integer calSheetId) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(DELETE_FROM_CLAIM_PAYMENT_DISPATCH);
            ps.setInt(1, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void updatePaymentDispatch(Connection connection, ClaimPaymentDispatchDto claimPaymentDispatchDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_PAYMENT_DISPATCH);
            ps.setString(++index, claimPaymentDispatchDto.getVoucherNo());
            ps.setString(++index, claimPaymentDispatchDto.getChequeNo());
            ps.setString(++index, claimPaymentDispatchDto.getDispatchLocation());
            ps.setString(++index, claimPaymentDispatchDto.getDispatchedLocation().getBranchCode());
            ps.setString(++index, claimPaymentDispatchDto.getDispatchUser());
            ps.setString(++index, claimPaymentDispatchDto.getDispatchDateTime());
            ps.setString(++index, claimPaymentDispatchDto.getChequeDispatchStatus());
            ps.setInt(++index, claimPaymentDispatchDto.getPayeeId());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    private ClaimPaymentDispatchDto getDispatchDto(Connection connection, ResultSet rs) throws Exception {
        ClaimPaymentDispatchDto claimPaymentDispatchDto = new ClaimPaymentDispatchDto();
        claimPaymentDispatchDto.setTxnId(rs.getInt("N_TXN_ID"));
        claimPaymentDispatchDto.setPayeeId(rs.getInt("N_PAYEE_ID"));
        claimPaymentDispatchDto.setClaimNo(rs.getInt("N_CLAIM_NO"));
        claimPaymentDispatchDto.setVoucherNo(rs.getString("V_VOUCHER_NO"));
        claimPaymentDispatchDto.setChequeNo(rs.getString("V_CHEQUE_NO"));
        String branch = null == rs.getString("V_DISPATCH_LOCATION") ? AppConstant.STRING_EMPTY : rs.getString("V_DISPATCH_LOCATION");
        claimPaymentDispatchDto.setDispatchUser(rs.getString("V_DISPATCH_USER"));
        claimPaymentDispatchDto.setDispatchDateTime(rs.getString("D_DISPATCH_DATE_TIME"));
        claimPaymentDispatchDto.setChequeDispatchStatus(rs.getString("V_CHEQUE_DISPATCH_STATUS"));
        if (!branch.isEmpty()) {
            BranchDetailDto branchDetailByBranchCode = branchMstDao.getBranchDetailByBranchCode(connection, branch);
            claimPaymentDispatchDto.setBranchDetailDto(branchDetailByBranchCode);
        }
        if (null != rs.getString("V_DISPATCHED_LOCATION") && !rs.getString("V_DISPATCHED_LOCATION").isEmpty()) {
            BranchDetailDto dispatchedLocation = branchMstDao.getBranchDetailByBranchCode(connection, rs.getString("V_DISPATCHED_LOCATION"));
            claimPaymentDispatchDto.setDispatchedLocation(dispatchedLocation);
        }

        return claimPaymentDispatchDto;
    }
}
