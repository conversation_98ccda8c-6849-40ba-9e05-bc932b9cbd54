package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.ClaimSpecialCaseTypeDao;
import com.misyn.mcms.claim.dto.ClaimSpecialCaseTypeDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.utility.AppConstant;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class ClaimSpecialCaseTypeDaoImpl extends AbstractBaseDao<ClaimSpecialCaseTypeDto> implements ClaimSpecialCaseTypeDao {
    private static final Logger LOGGER = Logger.getLogger(ClaimSpecialCaseTypeDaoImpl.class);


    @Override
    public DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField) {
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List claimSpecialCaseTypeList = new ArrayList<>(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();

        if (SQL_SEARCH.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" WHERE record_status='A'");
        } else {
            SQL_SEARCH = SQL_SEARCH.concat(" AND record_status='A'");
        }

        final String SEL_SQL = "SELECT * FROM claim_special_case_type ".concat(SQL_SEARCH).concat(SQL_ORDER);

        final String COUNT_SQL = "SELECT\n" +
                "count(id) AS cnt\n" +
                "FROM claim_special_case_type ".concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto = new ClaimSpecialCaseTypeDto();
                    claimSpecialCaseTypeDto.setId(rs.getInt("id"));
                    claimSpecialCaseTypeDto.setClaimType(rs.getString("claim_type"));
                    claimSpecialCaseTypeDto.setClaimNo(rs.getString("claim_no"));
                    claimSpecialCaseTypeDto.setRemark(rs.getString("remark"));
                    claimSpecialCaseTypeDto.setInputDateTime(rs.getTimestamp("input_date_time").toLocalDateTime());
                    claimSpecialCaseTypeDto.setInputUser(rs.getString("input_user"));
                    claimSpecialCaseTypeDto.setLastModifiedDateTime(rs.getTimestamp("last_modified_date_time").toLocalDateTime());
                    claimSpecialCaseTypeDto.setLastModifiedUser(rs.getString("last_modified_user"));

                    claimSpecialCaseTypeList.add(claimSpecialCaseTypeDto);
                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(claimSpecialCaseTypeList);

        } catch (Exception e) {
            LOGGER.error(e);
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public ClaimSpecialCaseTypeDto insertClaimSpecialCaseType(Connection connection, ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(CLAIM_SPECIAL_TYPE_INSERT)) {
            ps.setString(++index, claimSpecialCaseTypeDto.getClaimType());
            ps.setString(++index, claimSpecialCaseTypeDto.getRemark());
            ps.setString(++index, claimSpecialCaseTypeDto.getClaimNo());
            ps.setString(++index, claimSpecialCaseTypeDto.getRecordStatus());
            ps.setTimestamp(++index, Timestamp.valueOf(claimSpecialCaseTypeDto.getInputDateTime()));
            ps.setString(++index, claimSpecialCaseTypeDto.getInputUser());
            ps.setTimestamp(++index, Timestamp.valueOf(claimSpecialCaseTypeDto.getLastModifiedDateTime()));
            ps.setString(++index, claimSpecialCaseTypeDto.getLastModifiedUser());

            if (ps.executeUpdate() > 0) {
                return claimSpecialCaseTypeDto;
            }

        } catch (Exception e) {
            LOGGER.error(e);
            throw new Exception("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimSpecialCaseTypeDto updateClaimSpecialCaseType(Connection connection, ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(CLAIM_SPECIAL_TYPE_UPDATE)) {
            ps.setString(++index, claimSpecialCaseTypeDto.getClaimType());
            ps.setString(++index, claimSpecialCaseTypeDto.getRemark());
            ps.setString(++index, claimSpecialCaseTypeDto.getClaimNo());
            ps.setString(++index, claimSpecialCaseTypeDto.getRecordStatus());
            ps.setTimestamp(++index, Timestamp.valueOf(claimSpecialCaseTypeDto.getLastModifiedDateTime()));
            ps.setString(++index, claimSpecialCaseTypeDto.getLastModifiedUser());
            ps.setInt(++index, claimSpecialCaseTypeDto.getId());

            if (ps.executeUpdate() > 0) {
                return claimSpecialCaseTypeDto;
            }

        } catch (Exception e) {
            LOGGER.error(e);
            throw new Exception("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimSpecialCaseTypeDto getClaimSpecialCaseType(Connection connection, Integer holidayTypeId) throws Exception {
        ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto = null;
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(CLAIM_SPECIAL_TYPE);
            ps.setInt(1, holidayTypeId);
            rs = ps.executeQuery();
            while (rs.next()) {
                claimSpecialCaseTypeDto = new ClaimSpecialCaseTypeDto();
                claimSpecialCaseTypeDto.setId(rs.getInt("id"));
                claimSpecialCaseTypeDto.setClaimType(rs.getString("claim_type"));
                claimSpecialCaseTypeDto.setClaimNo(rs.getString("claim_no"));
                claimSpecialCaseTypeDto.setRemark(rs.getString("remark"));
                claimSpecialCaseTypeDto.setRecordStatus(rs.getString("record_status"));
                claimSpecialCaseTypeDto.setInputDateTime(rs.getTimestamp("input_date_time").toLocalDateTime());
                claimSpecialCaseTypeDto.setInputUser(rs.getString("input_user"));
                claimSpecialCaseTypeDto.setLastModifiedDateTime(rs.getTimestamp("last_modified_date_time").toLocalDateTime());
                claimSpecialCaseTypeDto.setLastModifiedUser(rs.getString("last_modified_user"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return claimSpecialCaseTypeDto;
    }

    @Override
    public List<ClaimSpecialCaseTypeDto> getClaimSpecialCaseTypeList(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimSpecialCaseTypeDto> claimSpecialCaseTypeDtoList = new ArrayList();
        try {
            ps = connection.prepareStatement(CLAIM_SPECIAL_TYPE_LIST);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto = new ClaimSpecialCaseTypeDto();
                claimSpecialCaseTypeDto.setId(rs.getInt("id"));
                claimSpecialCaseTypeDto.setClaimType(rs.getString("claim_type"));
                claimSpecialCaseTypeDto.setClaimNo(rs.getString("claim_no"));
                claimSpecialCaseTypeDto.setRemark(rs.getString("remark"));
                claimSpecialCaseTypeDto.setRecordStatus(rs.getString("record_status"));

                claimSpecialCaseTypeDtoList.add(claimSpecialCaseTypeDto);
            }
            ps.close();
            rs.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return !claimSpecialCaseTypeDtoList.isEmpty() ? claimSpecialCaseTypeDtoList : null;
    }

    @Override
    public void deleteClaimSpecialCaseType(Connection connection, Integer id, String userName) throws Exception {
        ClaimSpecialCaseTypeDto claimSpecialCaseTypeDto;
        try {
            claimSpecialCaseTypeDto = getClaimSpecialCaseType(connection, id);
            if (null != claimSpecialCaseTypeDto) {
                claimSpecialCaseTypeDto.setRecordStatus(AppConstant.DELETE);
                claimSpecialCaseTypeDto.setLastModifiedDateTime(LocalDateTime.now());
                claimSpecialCaseTypeDto.setLastModifiedUser(userName);
                updateClaimSpecialCaseType(connection, claimSpecialCaseTypeDto);
            } else {
                throw new Exception();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Boolean isExistClaimNo(Connection connection, String claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        boolean isExistClaimNo = false;
        try {
            ps = connection.prepareStatement(EXIST_CLAIM_CHECK);
            ps.setString(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                isExistClaimNo = rs.getInt("cnt") > 0;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return isExistClaimNo;
    }

}
