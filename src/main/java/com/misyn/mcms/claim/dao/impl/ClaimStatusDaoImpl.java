package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.ClaimStatusDao;
import com.misyn.mcms.claim.dto.ClaimStatusDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
public class ClaimStatusDaoImpl extends BaseAbstract<ClaimStatusDaoImpl> implements ClaimStatusDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimStatusDaoImpl.class);

    @Override
    public ClaimStatusDto insertMaster(Connection connection, ClaimStatusDto claimStatusDto) throws Exception {
        return null;
    }

    @Override
    public ClaimStatusDto insertTemporary(Connection connection, ClaimStatusDto claimStatusDto) throws Exception {
        return null;
    }

    @Override
    public ClaimStatusDto insertHistory(Connection connection, ClaimStatusDto claimStatusDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimStatusDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            ps = connection.prepareStatement(SQL_SELECT_CLAIM_STATUS_BY_REF_ID);
            ps.setObject(1, id);
            rs = ps.executeQuery();
            if (rs.next()) {
                ClaimStatusDto claimStatusDto = new ClaimStatusDto();
                claimStatusDto.setRefId(rs.getInt("n_ref_id"));
                claimStatusDto.setStatusCode(rs.getString("v_status_code"));
                claimStatusDto.setStatusDesc(rs.getString("v_status_desc"));
                claimStatusDto.setType(rs.getString("type"));

                return claimStatusDto;
            }

        } catch (Exception e) {

        }
        return null;

    }

    @Override
    public ClaimStatusDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimStatusDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public ClaimStatusDto updateMaster(Connection connection, ClaimStatusDto claimStatusDto) throws Exception {
        return null;
    }

    @Override
    public ClaimStatusDto updateTemporary(Connection connection, ClaimStatusDto claimStatusDto) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public ClaimStatusDto getClaimStatusByStatusCode(Connection connection, String code) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            ps = connection.prepareStatement(SQL_SELECT_CLAIM_STATUS_BY_STATUS_CODE);
            ps.setString(1, code);
            rs = ps.executeQuery();
            if (rs.next()) {
                ClaimStatusDto claimStatusDto = new ClaimStatusDto();
                claimStatusDto.setRefId(rs.getInt("n_ref_id"));
                claimStatusDto.setStatusCode(rs.getString("v_status_code"));
                claimStatusDto.setStatusDesc(rs.getString("v_status_desc"));
                claimStatusDto.setType(rs.getString("type"));

                return claimStatusDto;
            }

        } catch (Exception e) {

        }
        return null;
    }
}
