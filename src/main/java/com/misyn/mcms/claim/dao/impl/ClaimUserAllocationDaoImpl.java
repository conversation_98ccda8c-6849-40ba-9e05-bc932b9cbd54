/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.ClaimUserAllocationDao;
import com.misyn.mcms.claim.dto.ClaimUserAllocationDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class ClaimUserAllocationDaoImpl extends AbstractBaseDao<ClaimUserAllocationDaoImpl> implements ClaimUserAllocationDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimUserAllocationDaoImpl.class);

    @Override
    public List<ClaimUserAllocationDto> getListNotInLeave(Connection connection, Integer accessLevel, Integer allocationFunctionType, String sysDateTime, String status) throws Exception {
        ResultSet rs;
        List<ClaimUserAllocationDto> claimUserAllocationDtos = new ArrayList<>();
        try (PreparedStatement ps = connection.prepareStatement("SELECT\n"
                + "	*\n"
                + "FROM\n"
                + "	claim_user_allocation cua\n"
                + "LEFT JOIN claim_user_leave cul ON cua.V_ASSIGN_USER_ID = cul.V_USER_ID\n"
                + "WHERE\n"
                + "	((\n"
                + "		? NOT BETWEEN cul.D_FROM_DATE_TIME\n"
                + "		AND cul.D_TO_DATE_TIME\n"
                + "	)\n"
                + "OR (cul.D_FROM_DATE_TIME IS NULL\n"
                + "AND cul.D_TO_DATE_TIME IS NULL))\n"
                + "AND cua.N_ACCESSUSRTYPE = ? AND cua.N_ALLOCATION_FUN_TYPE=? AND cua.V_STATUS = ?")) {
            ps.setString(1, sysDateTime);
            ps.setInt(2, accessLevel);
            ps.setInt(3, allocationFunctionType);
            ps.setString(4, status);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimUserAllocationDto claimUserAllocationDto = new ClaimUserAllocationDto();
                claimUserAllocationDto.setAccessUsrType(rs.getInt("N_ACCESSUSRTYPE"));
                claimUserAllocationDto.setAssignUserId(rs.getString("V_ASSIGN_USER_ID"));
                claimUserAllocationDto.setLastAssignDate(rs.getString("D_LAST_ASSIGN_DATE"));
                claimUserAllocationDto.setStatus(rs.getString("V_STATUS"));
                claimUserAllocationDto.setTotAssignReport(rs.getInt("N_TOT_ASSIGN_REPORT"));
                claimUserAllocationDto.setAssignIndex(rs.getInt("N_ASSIGN_INDEX"));
                claimUserAllocationDto.setTotAssignReportToday(rs.getInt("N_TOT_ASSIGN_REPORT_TODAY"));
                claimUserAllocationDto.setTxnId(rs.getInt("N_TXN_NO"));
                claimUserAllocationDtos.add(claimUserAllocationDto);
            }
            return claimUserAllocationDtos;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public List<ClaimUserAllocationDto> getListInLeave(Connection connection, Integer accessLevel, Integer allocationFunctionType, String sysDateTime, String status) throws Exception {
        ResultSet rs;
        List<ClaimUserAllocationDto> claimUserAllocationDtos = new ArrayList<>();
        try (PreparedStatement ps = connection.prepareStatement("SELECT\n" +
                "	cul.V_USER_ID\n" +
                "FROM\n" +
                "	claim_user_leave AS cul\n" +
                "INNER JOIN claim_user_allocation AS cua ON cul.V_USER_ID = cua.V_ASSIGN_USER_ID\n" +
                "WHERE\n" +
                "	? BETWEEN cul.D_FROM_DATE_TIME\n" +
                "AND cul.D_TO_DATE_TIME\n" +
                "AND cua.N_ACCESSUSRTYPE = ?\n" +
                "AND cua.N_ALLOCATION_FUN_TYPE = ?\n" +
                "AND cua.V_STATUS = ?;")) {
            ps.setString(1, sysDateTime);
            ps.setInt(2, accessLevel);
            ps.setInt(3, allocationFunctionType);
            ps.setString(4, status);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimUserAllocationDto claimUserAllocationDto = new ClaimUserAllocationDto();
                claimUserAllocationDto.setAssignUserId(rs.getString("cul.V_USER_ID"));
                claimUserAllocationDtos.add(claimUserAllocationDto);
            }
            return claimUserAllocationDtos;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public void updateLeaveUserAssignReport(Connection connection, Integer assignReport, Integer assignIndex, String assignUSerId) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement("UPDATE claim_user_allocation SET N_ASSIGN_INDEX=?, N_TOT_ASSIGN_REPORT_TODAY=? WHERE V_ASSIGN_USER_ID=?");
            ps.setInt(++index, assignIndex);
            ps.setInt(++index, assignReport);
            ps.setString(++index, assignUSerId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public List<ClaimUserAllocationDto> getMofaListNotInLeave(Connection connection, Integer accessLevel, Integer allocationFunctionType, String sysDatetime, String status, BigDecimal amount, BigDecimal limit, boolean isMandatory) throws Exception {
        ResultSet rs;
        List<ClaimUserAllocationDto> claimUserAllocationDtos = new ArrayList<>();

        String SQL = isMandatory ?
                "SELECT\n" +
                        "	* \n" +
                        "FROM\n" +
                        "	claim_user_allocation cua\n" +
                        "	INNER JOIN usr_mst um ON cua.V_ASSIGN_USER_ID = um.v_usrid \n" +
                        "	LEFT JOIN claim_user_leave cul ON cua.V_ASSIGN_USER_ID = cul.V_USER_ID\n" +
                        "WHERE\n" +
                        "	cua.N_ACCESSUSRTYPE = ? \n" +
                        "	AND cua.N_ALLOCATION_FUN_TYPE =? \n" +
                        "	AND cua.V_STATUS = ? \n" +
                        "	AND um.N_PAYMENT_AUTH_LIMIT BETWEEN ? AND ?"
                :
                "SELECT\n" +
                        "	* \n" +
                        "FROM\n" +
                        "	claim_user_allocation cua\n" +
                        "	INNER JOIN usr_mst um ON cua.V_ASSIGN_USER_ID = um.v_usrid \n" +
                        "	LEFT JOIN claim_user_leave cul ON cua.V_ASSIGN_USER_ID = cul.V_USER_ID\n" +
                        "WHERE\n" +
                        "	(\n" +
                        "		( ? NOT BETWEEN cul.D_FROM_DATE_TIME AND cul.D_TO_DATE_TIME ) \n" +
                        "		OR ( cul.D_FROM_DATE_TIME IS NULL AND cul.D_TO_DATE_TIME IS NULL ) \n" +
                        "	) \n" +
                        "	AND cua.N_ACCESSUSRTYPE = ? \n" +
                        "	AND cua.N_ALLOCATION_FUN_TYPE =? \n" +
                        "	AND cua.V_STATUS = ? \n" +
                        "	AND um.N_PAYMENT_AUTH_LIMIT BETWEEN ? AND ?";

        try (PreparedStatement ps = connection.prepareStatement(SQL)) {
            if (isMandatory) {
                ps.setInt(1, accessLevel);
                ps.setInt(2, allocationFunctionType);
                ps.setString(3, status);
                ps.setObject(4, amount);
                ps.setObject(5, limit);
            } else {
                ps.setString(1, sysDatetime);
                ps.setInt(2, accessLevel);
                ps.setInt(3, allocationFunctionType);
                ps.setString(4, status);
                ps.setObject(5, amount);
                ps.setObject(6, limit);
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimUserAllocationDto claimUserAllocationDto = new ClaimUserAllocationDto();
                claimUserAllocationDto.setAccessUsrType(rs.getInt("N_ACCESSUSRTYPE"));
                claimUserAllocationDto.setAssignUserId(rs.getString("V_ASSIGN_USER_ID"));
                claimUserAllocationDto.setLastAssignDate(rs.getString("D_LAST_ASSIGN_DATE"));
                claimUserAllocationDto.setStatus(rs.getString("V_STATUS"));
                claimUserAllocationDto.setTotAssignReport(rs.getInt("N_TOT_ASSIGN_REPORT"));
                claimUserAllocationDto.setAssignIndex(rs.getInt("N_ASSIGN_INDEX"));
                claimUserAllocationDto.setTotAssignReportToday(rs.getInt("N_TOT_ASSIGN_REPORT_TODAY"));
                claimUserAllocationDto.setTxnId(rs.getInt("N_TXN_NO"));
                claimUserAllocationDtos.add(claimUserAllocationDto);
            }
            return claimUserAllocationDtos;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public boolean checkIfLeave(Connection connection, Integer accessLevel, Integer allocationFunctionType, String sysDatetime, String assignUserId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int index = 0;
        try {
            ps = connection.prepareStatement("SELECT\n" +
                    "    cua.V_ASSIGN_USER_ID \n" +
                    "FROM\n" +
                    "    claim_user_leave AS cul\n" +
                    "RIGHT JOIN claim_user_allocation AS cua ON cul.V_USER_ID = cua.V_ASSIGN_USER_ID\n" +
                    "WHERE\n" +
                    "((? BETWEEN cul.D_FROM_DATE_TIME\n" +
                    "AND cul.D_TO_DATE_TIME\n" +
                    "AND cua.N_ACCESSUSRTYPE = ?\n" +
                    "AND cua.N_ALLOCATION_FUN_TYPE = ?\n" +
                    "AND cua.V_STATUS = 'A')\n" +
                    "OR cua.V_STATUS IN ('D','C'))\n" +
                    "AND cua.V_ASSIGN_USER_ID = ?");
            ps.setString(++index, sysDatetime);
            ps.setInt(++index, accessLevel);
            ps.setInt(++index, allocationFunctionType);
            ps.setString(++index, assignUserId);
            rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
            if (rs != null) {
                rs.close();
            }
        }
        return false;
    }

    @Override
    public List<ClaimUserAllocationDto> getListNotInLeaveClaimHandlerDepartment(Connection connection, Integer accessLevel, Integer allocationFunctionType, String sysDatetime, String status, Integer claimNo) throws Exception {
        ResultSet rs;
        List<ClaimUserAllocationDto> claimUserAllocationDtos = new ArrayList<>();
        try (PreparedStatement ps = connection.prepareStatement("SELECT\n" +
                " um.N_ACCESSUSRTYPE,\n" +
                " cua.V_ASSIGN_USER_ID,\n" +
                " cua.D_LAST_ASSIGN_DATE,\n" +
                " cua.V_STATUS,\n" +
                " cua.N_TOT_ASSIGN_REPORT,\n" +
                " cua.N_ASSIGN_INDEX,\n" +
                " cua.N_TOT_ASSIGN_REPORT_TODAY,\n" +
                " cua.N_TXN_NO\n" +
                " FROM\n" +
                "    claim_user_allocation cua\n" +
                " LEFT JOIN claim_user_leave cul ON cua.V_ASSIGN_USER_ID = cul.V_USER_ID\n" +
                " INNER JOIN usr_mst um ON um.v_usrid = cua.V_ASSIGN_USER_ID\n" +
                " INNER JOIN claim_channel_team  cct ON cct.N_TEAM_ID = um.N_TEAM_ID\n" +
                " INNER JOIN claim_channel cc ON cc.N_TEAM_ID = cct.N_TEAM_ID\n" +
                " INNER JOIN claim_vehicle_info_main cvim ON cvim.V_CHANNEL = cc.V_DESCRIPTION\n" +
                " INNER JOIN claim_claim_info_main ccim ON ccim.V_POL_NUMBER = cvim.V_POL_NUMBER\n" +
                " WHERE\n" +
                "    (( ? NOT BETWEEN cul.D_FROM_DATE_TIME\n" +
                "        AND cul.D_TO_DATE_TIME\n" +
                "    )\n" +
                " OR (cul.D_FROM_DATE_TIME IS NULL\n" +
                " AND cul.D_TO_DATE_TIME IS NULL))\n" +
                " AND cua.N_ACCESSUSRTYPE = ? AND cua.N_ALLOCATION_FUN_TYPE= ? AND cua.V_STATUS = ? AND ccim.N_CLIM_NO = ? group by V_ASSIGN_USER_ID")) {
            ps.setString(1, sysDatetime);
            ps.setInt(2, accessLevel);
            ps.setInt(3, allocationFunctionType);
            ps.setString(4, status);
            ps.setInt(5, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimUserAllocationDto claimUserAllocationDto = new ClaimUserAllocationDto();
                claimUserAllocationDto.setAccessUsrType(rs.getInt("N_ACCESSUSRTYPE"));
                claimUserAllocationDto.setAssignUserId(rs.getString("V_ASSIGN_USER_ID"));
                claimUserAllocationDto.setLastAssignDate(rs.getString("D_LAST_ASSIGN_DATE"));
                claimUserAllocationDto.setStatus(rs.getString("V_STATUS"));
                claimUserAllocationDto.setTotAssignReport(rs.getInt("N_TOT_ASSIGN_REPORT"));
                claimUserAllocationDto.setAssignIndex(rs.getInt("N_ASSIGN_INDEX"));
                claimUserAllocationDto.setTotAssignReportToday(rs.getInt("N_TOT_ASSIGN_REPORT_TODAY"));
                claimUserAllocationDto.setTxnId(rs.getInt("N_TXN_NO"));
                claimUserAllocationDtos.add(claimUserAllocationDto);
            }
            return claimUserAllocationDtos;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public List<ClaimUserAllocationDto> getListInLeaveClaimHandlerDepartment(Connection connection, Integer accessLevel, Integer allocationFunctionType, String sysDatetime, String status, Integer claimNo) throws Exception {
        ResultSet rs;
        List<ClaimUserAllocationDto> claimUserAllocationDtos = new ArrayList<>();
        try (PreparedStatement ps = connection.prepareStatement("SELECT\n" +
                " cul.V_USER_ID\n" +
                " FROM\n" +
                " claim_user_leave AS cul\n" +
                " INNER JOIN claim_user_allocation AS cua ON cul.V_USER_ID = cua.V_ASSIGN_USER_ID\n" +
                " INNER JOIN usr_mst um ON um.v_usrid = cua.V_ASSIGN_USER_ID\n" +
                " INNER JOIN claim_channel_team  cct ON cct.N_TEAM_ID = um.N_TEAM_ID\n" +
                " INNER JOIN claim_channel cc ON cc.N_TEAM_ID = cct.N_TEAM_ID\n" +
                " INNER JOIN claim_vehicle_info_main cvim ON cvim.V_CHANNEL = cc.V_DESCRIPTION\n" +
                " INNER JOIN claim_claim_info_main ccim ON ccim.V_POL_NUMBER = cvim.V_POL_NUMBER\n" +
                " WHERE\n" +
                " ? BETWEEN cul.D_FROM_DATE_TIME\n" +
                " AND cul.D_TO_DATE_TIME\n" +
                " AND cua.N_ACCESSUSRTYPE = ?" +
                " AND cua.N_ALLOCATION_FUN_TYPE = ?" +
                " AND cua.V_STATUS = ? AND ccim.N_CLIM_NO = ? GROUP BY V_USER_ID;")) {
            ps.setString(1, sysDatetime);
            ps.setInt(2, accessLevel);
            ps.setInt(3, allocationFunctionType);
            ps.setString(4, status);
            ps.setInt(5, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimUserAllocationDto claimUserAllocationDto = new ClaimUserAllocationDto();
                claimUserAllocationDto.setAssignUserId(rs.getString("cul.V_USER_ID"));
                claimUserAllocationDtos.add(claimUserAllocationDto);
            }
            return claimUserAllocationDtos;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public List<ClaimUserAllocationDto> getRteListNotInLeave(Connection connection, Integer allocationFunctionType, String sysDatetime, String status, int limitLevel) throws Exception {
        ResultSet rs;
        List<ClaimUserAllocationDto> claimUserAllocationDtos = new ArrayList<>();

        String SQL = "SELECT\n" +
                "	* \n" +
                "FROM\n" +
                "	claim_user_allocation cua\n" +
                "	INNER JOIN usr_mst um ON cua.V_ASSIGN_USER_ID = um.v_usrid\n" +
                "	LEFT JOIN claim_user_leave cul ON cua.V_ASSIGN_USER_ID = cul.V_USER_ID \n" +
                "WHERE\n" +
                "	(\n" +
                "		( ? NOT BETWEEN cul.D_FROM_DATE_TIME AND cul.D_TO_DATE_TIME ) \n" +
                "		OR ( cul.D_FROM_DATE_TIME IS NULL AND cul.D_TO_DATE_TIME IS NULL ) \n" +
                "	) \n" +
                "	AND cua.N_ACCESSUSRTYPE IN ( 22, 23, 24 ) \n" +
                "	AND cua.N_ALLOCATION_FUN_TYPE =? \n" +
                "	AND cua.V_STATUS = ? \n" +
                "	AND um.n_auth_level = ?";

        try (PreparedStatement ps = connection.prepareStatement(SQL)) {
            ps.setString(1, sysDatetime);
            ps.setInt(2, allocationFunctionType);
            ps.setString(3, status);
            ps.setObject(4, limitLevel);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimUserAllocationDto claimUserAllocationDto = new ClaimUserAllocationDto();
                claimUserAllocationDto.setAccessUsrType(rs.getInt("N_ACCESSUSRTYPE"));
                claimUserAllocationDto.setAssignUserId(rs.getString("V_ASSIGN_USER_ID"));
                claimUserAllocationDto.setLastAssignDate(rs.getString("D_LAST_ASSIGN_DATE"));
                claimUserAllocationDto.setStatus(rs.getString("V_STATUS"));
                claimUserAllocationDto.setTotAssignReport(rs.getInt("N_TOT_ASSIGN_REPORT"));
                claimUserAllocationDto.setAssignIndex(rs.getInt("N_ASSIGN_INDEX"));
                claimUserAllocationDto.setTotAssignReportToday(rs.getInt("N_TOT_ASSIGN_REPORT_TODAY"));
                claimUserAllocationDto.setTxnId(rs.getInt("N_TXN_NO"));
                claimUserAllocationDtos.add(claimUserAllocationDto);
            }
            return claimUserAllocationDtos;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }

    @Override
    public boolean isRteLeave(Connection connection, Integer allocationFunctionType, String sysDateTime, String status, String assignUserId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int index = 0;
        try {
            ps = connection.prepareStatement("SELECT\n"
                    + "    cul.V_USER_ID\n"
                    + "FROM\n"
                    + "    claim_user_leave AS cul\n"
                    + "INNER JOIN claim_user_allocation AS cua ON cul.V_USER_ID = cua.V_ASSIGN_USER_ID\n"
                    + "WHERE\n"
                    + "    ? BETWEEN cul.D_FROM_DATE_TIME\n"
                    + "AND cul.D_TO_DATE_TIME\n"
                    + "AND cua.N_ALLOCATION_FUN_TYPE = ?\n"
                    + "AND cua.V_STATUS = ?\n"
                    + "AND cua.V_ASSIGN_USER_ID = ?");
            ps.setString(++index, sysDateTime);
            ps.setInt(++index, allocationFunctionType);
            ps.setString(++index, status);
            ps.setString(++index, assignUserId);
            rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
            if (rs != null) {
                rs.close();
            }
        }
        return false;
    }

    @Override
    public int checkAssignReportForToday(Connection connection, Integer accessLevel) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        int assignReport = 0;
        try {
            ps = connection.prepareStatement("SELECT\n" +
                    "\tN_TOT_ASSIGN_REPORT_TODAY AS ASSIGN_REPORT\n" +
                    "FROM\n" +
                    "\tclaim_user_allocation\n" +
                    "WHERE\n" +
                    "\tD_LAST_ASSIGN_DATE = CURDATE()\n" +
                    "AND N_ACCESSUSRTYPE = ?\n" +
                    "ORDER BY\n" +
                    "\tN_TOT_ASSIGN_REPORT_TODAY\n" +
                    "LIMIT 1");
            ps.setInt(1, accessLevel);
            rs = ps.executeQuery();
            if (rs.next()) {
                assignReport = rs.getInt("ASSIGN_REPORT");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return assignReport;
    }

    @Override
    public ClaimUserAllocationDto updateMaster(Connection connection, ClaimUserAllocationDto t) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement("UPDATE claim_user_allocation SET\n"
                    + "		V_ASSIGN_USER_ID=?,\n"
                    + "		N_TOT_ASSIGN_REPORT=?,\n"
                    + "     N_ASSIGN_INDEX=?,\n"
                    + "		N_TOT_ASSIGN_REPORT_TODAY=?,\n"
                    + "		D_LAST_ASSIGN_DATE=?,\n"
                    + "		N_ACCESSUSRTYPE=?,\n"
                    + "		V_STATUS=?\n"
                    + "WHERE N_TXN_NO = ?");
            ps.setString(++index, t.getAssignUserId());
            ps.setInt(++index, t.getTotAssignReport());
            ps.setInt(++index, t.getAssignIndex());
            ps.setInt(++index, t.getTotAssignReportToday());
            ps.setString(++index, t.getLastAssignDate());
            ps.setInt(++index, t.getAccessUsrType());
            ps.setString(++index, t.getStatus());
            ps.setInt(++index, t.getTxnId());
            ps.executeUpdate();
            return t;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }

    }


}
