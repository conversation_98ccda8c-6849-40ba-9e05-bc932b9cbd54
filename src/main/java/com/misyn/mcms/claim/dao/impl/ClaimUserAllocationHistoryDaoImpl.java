package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.ClaimUserAllocationHistoryDao;
import com.misyn.mcms.claim.dto.ClaimUserAllocationHistoryDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
public class ClaimUserAllocationHistoryDaoImpl extends AbstractBaseDao<ClaimUserAllocationHistoryDaoImpl> implements ClaimUserAllocationHistoryDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimUserAllocationHistoryDaoImpl.class);

    @Override
    public void insert(Connection connection, ClaimUserAllocationHistoryDto claimUserAllocationHistoryDto) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(INSERT_CLAIM_USER_ALLOCATION_HISTORY);
            ps.setInt(1, claimUserAllocationHistoryDto.getClaimNo());
            ps.setString(2, claimUserAllocationHistoryDto.getAssignUserId());
            ps.setString(3, claimUserAllocationHistoryDto.getAssignDate());
            ps.setString(4, claimUserAllocationHistoryDto.getAssignTime());
            ps.setString(5, claimUserAllocationHistoryDto.getAssignDateTime());
            ps.setString(6, claimUserAllocationHistoryDto.getType());
            ps.setInt(7, claimUserAllocationHistoryDto.getAccessUsrType());
            ps.setString(8, claimUserAllocationHistoryDto.getInputUser());
            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }
}
