package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.ClaimUserLeaveDao;
import com.misyn.mcms.claim.dto.ClaimUserLeaveDto;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class ClaimUserLeaveDaoImpl extends AbstractBaseDao<ClaimUserLeaveDaoImpl> implements ClaimUserLeaveDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimUserLeaveDaoImpl.class);

    @Override
    public ClaimUserLeaveDto insertMaster(Connection connection, ClaimUserLeaveDto claimUserLeaveDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_USER_LEAVE_INSERT);
            ps.setString(++index, claimUserLeaveDto.getUserId());
            ps.setString(++index, claimUserLeaveDto.getFromDateTime());
            ps.setString(++index, claimUserLeaveDto.getToDateTime());
            ps.setString(++index, claimUserLeaveDto.getLeaveType());
            ps.setString(++index, claimUserLeaveDto.getInputUser());
            ps.setString(++index, claimUserLeaveDto.getInputDateTime());

            if (ps.executeUpdate() > 0) {
                return claimUserLeaveDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimUserLeaveDto updateMaster(Connection connection, ClaimUserLeaveDto claimUserLeaveDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_USER_LEAVE_UPDATE);
            ps.setString(++index, claimUserLeaveDto.getFromDateTime());
            ps.setString(++index, claimUserLeaveDto.getToDateTime());
            ps.setString(++index, claimUserLeaveDto.getLeaveType());
            ps.setString(++index, claimUserLeaveDto.getInputUser());
            ps.setString(++index, claimUserLeaveDto.getInputDateTime());
            ps.setString(++index, claimUserLeaveDto.getUserId());

            if (ps.executeUpdate() > 0) {
                return claimUserLeaveDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimUserLeaveDto insertTemporary(Connection connection, ClaimUserLeaveDto claimUserLeaveDto) throws Exception {
        return null;
    }

    @Override
    public ClaimUserLeaveDto updateTemporary(Connection connection, ClaimUserLeaveDto claimUserLeaveDto) throws Exception {
        return null;
    }

    @Override
    public ClaimUserLeaveDto insertHistory(Connection connection, ClaimUserLeaveDto claimUserLeaveDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimUserLeaveDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimUserLeaveDto claimUserLeaveDto = new ClaimUserLeaveDto();
        try {
            ps = connection.prepareStatement(CLAIM_USER_LEAVE_SEARCH);
            ps.setObject(1, id);
            rs = ps.executeQuery();
            if (rs.next()) {

                claimUserLeaveDto.setUserId(rs.getString("t1.v_usrid"));
                claimUserLeaveDto.setFromDateTime(null == rs.getString("D_FROM_DATE_TIME") || "1980-01-01 00:00:00.0".equals(rs.getString("D_FROM_DATE_TIME")) ? "" : Utility.getDate(rs.getString("D_FROM_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimUserLeaveDto.setToDateTime(null == rs.getString("D_TO_DATE_TIME") || "1980-01-01 00:00:00.0".equals(rs.getString("D_TO_DATE_TIME")) ? "" : Utility.getDate(rs.getString("D_TO_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimUserLeaveDto.setLeaveType(rs.getString("V_LEAVE_TYPE"));
                claimUserLeaveDto.setInputUser(rs.getString("V_INPUT_USER"));
                claimUserLeaveDto.setInputDateTime(rs.getString("D_INPUT_DATE_TIME"));
                claimUserLeaveDto.setFirstName(rs.getString("t1.v_firstname"));
                claimUserLeaveDto.setLastName(rs.getString("t1.v_lastname"));

                return claimUserLeaveDto;
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return claimUserLeaveDto;
    }

    @Override
    public ClaimUserLeaveDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimUserLeaveDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ClaimUserLeaveDto> claimUserLeaveDtoList = new ArrayList();

        try {
            ps = connection.prepareStatement(CLAIM_USER_LEAVE_SEARCH_ALL);
            rs = ps.executeQuery();

            while (rs.next()) {
                ClaimUserLeaveDto claimUserLeaveDto = new ClaimUserLeaveDto();

                claimUserLeaveDto.setUserId(rs.getString("V_USER_ID"));
                claimUserLeaveDto.setFromDateTime("1980-01-01 00:00:00".equals(rs.getString("D_FROM_DATE_TIME")) ? "" : rs.getString("D_FROM_DATE_TIME"));
                claimUserLeaveDto.setToDateTime("1980-01-01 00:00:00".equals(rs.getString("D_TO_DATE_TIME")) ? "" : rs.getString("D_TO_DATE_TIME"));
                claimUserLeaveDto.setLeaveType(rs.getString("V_LEAVE_TYPE"));
                claimUserLeaveDto.setInputUser(rs.getString("V_INPUT_USER"));
                claimUserLeaveDto.setInputDateTime(rs.getString("D_INPUT_DATE_TIME"));

                claimUserLeaveDtoList.add(claimUserLeaveDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimUserLeaveDtoList;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, Integer type) {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List claimList = new ArrayList(200);
        String DATE_BETWEEN = "";

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        String SQL_SEARCH1 = formatSQL1(parameterList).toString();
        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            DATE_BETWEEN = " AND D_FROM_DATE_TIME>='" + fromDate + "' AND D_TO_DATE_TIME<='" + toDate + "'";
        }

        final String SEL_SQL = type == 2 ?
                "SELECT\n" +
                        "IFNULL(t2.D_FROM_DATE_TIME,'') AS D_FROM_DATE_TIME,\n" +
                        "IFNULL(t2.D_TO_DATE_TIME,'') AS D_TO_DATE_TIME,\n" +
                        "IFNULL(t2.V_LEAVE_TYPE,'') AS V_LEAVE_TYPE,\n" +
                        "IFNULL(t2.V_INPUT_USER,'') AS V_INPUT_USER,\n" +
                        "IFNULL(t2.D_INPUT_DATE_TIME,'') AS D_INPUT_DATE_TIME,\n" +
                        "t1.v_usrid,\n" +
                        "t1.v_firstname,\n" +
                        "t1.v_lastname\n" +
                        "FROM\n" +
                        "usr_mst AS t1\n" +
                        "LEFT JOIN claim_user_leave AS t2 ON t1.v_usrid = t2.V_USER_ID\n" +
                        "WHERE\n" +
                        "t1.n_accessusrtype AND\n" +
                        "t1.n_accessusrtype IN(21,22,23,24) ".concat(SQL_SEARCH1).concat(DATE_BETWEEN).concat(SQL_ORDER)
                :
                "SELECT\n" +
                        "IFNULL(t2.D_FROM_DATE_TIME,'') AS D_FROM_DATE_TIME,\n" +
                        "IFNULL(t2.D_TO_DATE_TIME,'') AS D_TO_DATE_TIME,\n" +
                        "IFNULL(t2.V_LEAVE_TYPE,'') AS V_LEAVE_TYPE,\n" +
                        "IFNULL(t2.V_INPUT_USER,'') AS V_INPUT_USER,\n" +
                        "IFNULL(t2.D_INPUT_DATE_TIME,'') AS D_INPUT_DATE_TIME,\n" +
                        "t1.v_usrid,\n" +
                        "t1.v_firstname,\n" +
                        "t1.v_lastname\n" +
                        "FROM\n" +
                        "usr_mst AS t1\n" +
                        "LEFT JOIN claim_user_leave AS t2 ON t1.v_usrid = t2.V_USER_ID\n" +
                        "WHERE\n" +
                        "t1.n_accessusrtype AND\n" +
                        "t1.n_accessusrtype IN( 27,28,29,40,41,42,43,44,47,48,46,45,60,61,62,63) ".concat(SQL_SEARCH1).concat(DATE_BETWEEN).concat(SQL_ORDER);


        final String COUNT_SQL = type == 2 ?
                "SELECT\n" +
                        "	COUNT( t1.v_usrid ) AS cnt\n" +
                        "FROM\n" +
                        "	usr_mst AS t1\n" +
                        "LEFT JOIN claim_user_leave AS t2 ON t1.v_usrid = t2.V_USER_ID\n" +
                        "WHERE\n" +
                        "t1.n_accessusrtype AND\n" +
                        "t1.n_accessusrtype IN(21,22,23,24) ".concat(SQL_SEARCH1)
                :
                "SELECT\n" +
                        "	COUNT( t1.v_usrid ) AS cnt\n" +
                        "FROM\n" +
                        "	usr_mst AS t1\n" +
                        "LEFT JOIN claim_user_leave AS t2 ON t1.v_usrid = t2.V_USER_ID\n" +
                        "WHERE\n" +
                        "t1.n_accessusrtype AND\n" +
                        "t1.n_accessusrtype IN( 27,28,29,40,41,42,43,44,47,48,46,45,60,61,62,63) ".concat(SQL_SEARCH1);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ClaimUserLeaveDto claimUserLeaveDto = getClaimUserLeave(rs);
                    claimUserLeaveDto.setIndex(++index);
                    claimList.add(claimUserLeaveDto);
                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(claimList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public boolean isLeaveUser(Connection connection, String assignUserId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimUserLeaveDto claimUserLeaveDto = new ClaimUserLeaveDto();
        try {
            ps = connection.prepareStatement(SQL_TODAY_LEAVE);
            ps.setObject(1, assignUserId);
            ps.setObject(2, Utility.sysDate(AppConstant.DATE_TIME_FORMAT));
            ps.setObject(3, Utility.sysDate(AppConstant.DATE_TIME_FORMAT));
            rs = ps.executeQuery();
            if (rs.next()) {
                return true;

            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return false;
    }

    private ClaimUserLeaveDto getClaimUserLeave(ResultSet rs) {
        ClaimUserLeaveDto claimUserLeaveDto = new ClaimUserLeaveDto();
        try {

            claimUserLeaveDto.setUserId(rs.getString("t1.v_usrid"));
            claimUserLeaveDto.setFromDateTime(null == rs.getString("D_FROM_DATE_TIME") || rs.getString("D_FROM_DATE_TIME").isEmpty() ? Utility.getDate(AppConstant.DEFAULT_DATE_TIME, AppConstant.DATE_TIME_FORMAT) : rs.getString("D_FROM_DATE_TIME"));
            claimUserLeaveDto.setToDateTime(null == rs.getString("D_TO_DATE_TIME") || rs.getString("D_TO_DATE_TIME").isEmpty() ? Utility.getDate(AppConstant.DEFAULT_DATE_TIME, AppConstant.DATE_TIME_FORMAT) : rs.getString("D_TO_DATE_TIME"));
            claimUserLeaveDto.setLeaveType(null == rs.getString("V_LEAVE_TYPE") || rs.getString("V_LEAVE_TYPE").isEmpty() ? "CANCEL" : rs.getString("V_LEAVE_TYPE"));
            claimUserLeaveDto.setInputUser(null == rs.getString("V_INPUT_USER") || rs.getString("V_INPUT_USER").isEmpty() ? "SYSTEM" : rs.getString("V_INPUT_USER"));
            claimUserLeaveDto.setInputDateTime(null == rs.getString("D_INPUT_DATE_TIME") || rs.getString("D_INPUT_DATE_TIME").isEmpty() ? Utility.getDate(AppConstant.DEFAULT_DATE_TIME, AppConstant.DATE_TIME_FORMAT) : rs.getString("D_INPUT_DATE_TIME"));
            claimUserLeaveDto.setFirstName(rs.getString("t1.v_firstname"));
            claimUserLeaveDto.setLastName(rs.getString("t1.v_lastname"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimUserLeaveDto;
    }
}
