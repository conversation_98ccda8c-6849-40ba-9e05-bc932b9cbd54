package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.ClaimUserLeaveHistoryDao;
import com.misyn.mcms.claim.dto.ClaimUserLeaveDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.List;
public class ClaimUserLeaveHistoryDaoImpl extends AbstractBaseDao<ClaimUserLeaveHistoryDaoImpl> implements ClaimUserLeaveHistoryDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimUserLeaveHistoryDaoImpl.class);

    @Override
    public ClaimUserLeaveDto insertMaster(Connection connection, ClaimUserLeaveDto claimUserLeaveDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_USER_LEAVE_HISTORY_INSERT);
            ps.setString(++index, claimUserLeaveDto.getUserId());
            ps.setString(++index, claimUserLeaveDto.getFromDateTime());
            ps.setString(++index, claimUserLeaveDto.getToDateTime());
            ps.setString(++index, claimUserLeaveDto.getLeaveType());
            ps.setString(++index, claimUserLeaveDto.getInputUser());
            ps.setString(++index, claimUserLeaveDto.getInputDateTime());

            if (ps.executeUpdate() > 0) {
                return claimUserLeaveDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimUserLeaveDto updateMaster(Connection connection, ClaimUserLeaveDto claimUserLeaveDto) throws Exception {
        return null;
    }

    @Override
    public ClaimUserLeaveDto insertTemporary(Connection connection, ClaimUserLeaveDto claimUserLeaveDto) throws Exception {
        return null;
    }

    @Override
    public ClaimUserLeaveDto updateTemporary(Connection connection, ClaimUserLeaveDto claimUserLeaveDto) throws Exception {
        return null;
    }

    @Override
    public ClaimUserLeaveDto insertHistory(Connection connection, ClaimUserLeaveDto claimUserLeaveDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimUserLeaveDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public ClaimUserLeaveDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimUserLeaveDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }
}
