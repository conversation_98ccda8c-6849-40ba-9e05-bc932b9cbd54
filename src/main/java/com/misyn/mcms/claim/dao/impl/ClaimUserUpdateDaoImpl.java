package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.ClaimUserUpdateDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class ClaimUserUpdateDaoImpl extends BaseAbstract<ClaimUserUpdateDaoImpl> implements ClaimUserUpdateDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimCalculationSheetMainDaoImpl.class);

    @Override
    public ClaimHandlerDto searchClaimUser(Connection connection, Integer txnId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimHandlerDto claimHandlerDto = new ClaimHandlerDto();
        try {
            ps = connection.prepareStatement(SEARCH_CLAIM_USER_BY_TXNID);
            ps.setObject(1, txnId);
            rs = ps.executeQuery();
            if (rs.next()) {

                claimHandlerDto.setTxnNo(rs.getInt("N_TXN_NO"));
                claimHandlerDto.setAssignUserId(rs.getString("V_ASSIGN_USER_ID"));
                claimHandlerDto.setLiabilityAprvAssignUser(rs.getString("V_LIABILITY_APRV_ASSIGN_USER"));
                claimHandlerDto.setClaimNo(rs.getInt("N_CLAIM_NO"));
                return claimHandlerDto;
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public Boolean updateClaimUserByTxnId(Connection connection, Integer txnId, String assignUserId, String liabilityAssignUser) throws Exception {
//        PreparedStatement ps;
//        int index = 0;
//        try {
//            ps = connection.prepareStatement(UPDATE_CLAIM_USER_BY_TXNID);
//            ps.setString(++index, assignUserId);
//            ps.setString(++index, liabilityAssignUser);
//            ps.setInt(++index, txnId);
//
//            if (ps.executeUpdate() > 0) {
//                return true;
//            }
//            ps.close();
//        } catch (Exception e) {
//            LOGGER.error(e.getMessage());
//            throw new MisynJDBCException("System Error", e);
//        }
//        return false;
//    }

        return null;
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDtoClaimHandler(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus, String assignUserType, UserDto sessionUser) throws Exception {

        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);
        final String SEL_SQL;
        String COUNT_SQL = "";

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER;
        if (null == calsheetStatus || AppConstant.EMPTY_STRING.equalsIgnoreCase(calsheetStatus)) {
            SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
        } else {
            SQL_ORDER = formatOrderSQL(start, orderType, orderField).toString();
        }
        int accessUserType;
        if (assignUserType.equals("9")) {
            accessUserType = 61;
        } else {
            accessUserType = 41;
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.D_ACCID_DATE BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

//        final String SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);
//
//        final String COUNT_SQL = SQL_COUNT_CLAIM_HANDLER.concat(SQL_SEARCH);

        if ("".equals(assignUserName)) {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(" WHERE t4.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = SQL_COUNT_CLAIM_HANDLER.concat(SQL_SEARCH).concat(" WHERE t4.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ");
            } else {
                SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(" AND t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = SQL_COUNT_CLAIM_HANDLER.concat(SQL_SEARCH).concat(" AND t4.n_accessusrtype =").concat(String.valueOf(accessUserType)).concat(" ");
            }
        } else {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(" WHERE V_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(" AND t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = SQL_COUNT_CLAIM_HANDLER.concat(SQL_SEARCH).concat(" WHERE V_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'").concat(" AND t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ");
            } else {
                SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(" AND V_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(" AND t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = SQL_COUNT_CLAIM_HANDLER.concat(SQL_SEARCH).concat(" AND V_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'").concat(" AND t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ");

            }
        }

        if (sessionUser.getAccessUserType() == 49) {
            SEL_SQL.concat("AND t4.N_TEAM_ID= "+sessionUser.getTeamId());
            COUNT_SQL.concat("AND t4.N_TEAM_ID= "+sessionUser.getTeamId());
        }

        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    UpdateClaimFileDto updateClaimFileDto = new UpdateClaimFileDto();
                    updateClaimFileDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    updateClaimFileDto.setRefNo(rs.getInt("t1.N_REF_NO"));
                    updateClaimFileDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    updateClaimFileDto.setPolicyNo(rs.getString("t1.V_POL_NUMBER"));
                    updateClaimFileDto.setAssignDateTime(Utility.getDate(rs.getString("D_ASSIGN_DATE_TIME"), "yyyy-MM-dd hh:mm:ss"));
                    updateClaimFileDto.setClaimStatus(rs.getString("t2.N_CLAIM_STATUS"));
                    updateClaimFileDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                    updateClaimFileDto.setAssignUser(rs.getString("V_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("V_ASSIGN_USER_ID"));
                    updateClaimFileDto.setCloseStatus(rs.getString("t2.V_CLOSE_STATUS"));

                    updateClaimFileDto.setIndex(++index);
                    handlerList.add(updateClaimFileDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public DataGridDto getTotalLossDataGridDtoClaimHandler(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);
        final String SEL_SQL;
        String COUNT_SQL = "";

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER;
        if (null == calsheetStatus || AppConstant.EMPTY_STRING.equalsIgnoreCase(calsheetStatus)) {
            SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
        } else {
            SQL_ORDER = formatOrderSQL(start, orderType, orderField).toString();
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.D_ACCID_DATE BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

//        final String SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);
//
//        final String COUNT_SQL = SQL_COUNT_CLAIM_HANDLER.concat(SQL_SEARCH);

        if ("".equals(assignUserName)) {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(" WHERE t4.n_accessusrtype = 46 ").concat(SQL_ORDER);
                COUNT_SQL = SQL_COUNT_CLAIM_HANDLER.concat(SQL_SEARCH).concat(" WHERE t4.n_accessusrtype = 46 ");
            } else {
                SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(" AND t4.n_accessusrtype = 46 ").concat(SQL_ORDER);
                COUNT_SQL = SQL_COUNT_CLAIM_HANDLER.concat(SQL_SEARCH).concat(" AND t4.n_accessusrtype = 46 ");
            }
        } else {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(" WHERE V_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(" AND t4.n_accessusrtype = 46 ").concat(SQL_ORDER);
                COUNT_SQL = SQL_COUNT_CLAIM_HANDLER.concat(SQL_SEARCH).concat(" WHERE V_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'").concat(" AND t4.n_accessusrtype = 46 ");
            } else {
                SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(" AND V_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(" AND t4.n_accessusrtype = 46 ").concat(SQL_ORDER);
                COUNT_SQL = SQL_COUNT_CLAIM_HANDLER.concat(SQL_SEARCH).concat(" AND V_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'").concat(" AND t4.n_accessusrtype = 46 ");

            }
        }

        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    UpdateClaimFileDto updateClaimFileDto = new UpdateClaimFileDto();
                    updateClaimFileDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    updateClaimFileDto.setRefNo(rs.getInt("t1.N_REF_NO"));
                    updateClaimFileDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    updateClaimFileDto.setPolicyNo(rs.getString("t1.V_POL_NUMBER"));
                    updateClaimFileDto.setAssignDateTime(Utility.getDate(rs.getString("D_ASSIGN_DATE_TIME"), "yyyy-MM-dd hh:mm:ss"));
                    updateClaimFileDto.setClaimStatus(rs.getString("t2.N_CLAIM_STATUS"));
                    updateClaimFileDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                    updateClaimFileDto.setAssignUser(rs.getString("V_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("V_ASSIGN_USER_ID"));
                    updateClaimFileDto.setCloseStatus(rs.getString("t2.V_CLOSE_STATUS"));

                    updateClaimFileDto.setIndex(++index);
                    handlerList.add(updateClaimFileDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public DataGridDto getInitialClaimHandlerDataGridDtoClaimHandler(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus, String assignUserType) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);
        final String SEL_SQL;
        String COUNT_SQL = "";

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER;
        if (null == calsheetStatus || AppConstant.EMPTY_STRING.equalsIgnoreCase(calsheetStatus)) {
            SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
        } else {
            SQL_ORDER = formatOrderSQL(start, orderType, orderField).toString();
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.D_ACCID_DATE BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

//        final String SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);
//
//        final String COUNT_SQL = SQL_COUNT_CLAIM_HANDLER.concat(SQL_SEARCH);
        int accessUserType;
        if (assignUserType.equals("10")) {
            accessUserType = 60;
        } else {
            accessUserType = 40;
        }
        if ("".equals(assignUserName)) {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SQL_SELECT_ALL_INIT_CLAIM_HANDLER_TO_GRID.concat(SQL_SEARCH).concat(" WHERE t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = SQL_COUNT_INIT_CLAIM_HANDLER.concat(SQL_SEARCH).concat(" WHERE t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ");
            } else {
                SEL_SQL = SQL_SELECT_ALL_INIT_CLAIM_HANDLER_TO_GRID.concat(SQL_SEARCH).concat(" AND t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = SQL_COUNT_INIT_CLAIM_HANDLER.concat(SQL_SEARCH).concat(" AND t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ");
            }
        } else {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SQL_SELECT_ALL_INIT_CLAIM_HANDLER_TO_GRID.concat(SQL_SEARCH).concat(" WHERE V_INIT_LIABILITY_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(" AND t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = SQL_COUNT_INIT_CLAIM_HANDLER.concat(SQL_SEARCH).concat(" WHERE V_INIT_LIABILITY_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'").concat(" AND t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ");
            } else {
                SEL_SQL = SQL_SELECT_ALL_INIT_CLAIM_HANDLER_TO_GRID.concat(SQL_SEARCH).concat(" AND V_INIT_LIABILITY_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(" AND t4.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = SQL_COUNT_INIT_CLAIM_HANDLER.concat(SQL_SEARCH).concat(" AND V_INIT_LIABILITY_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'").concat(" AND t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ");

            }
        }

        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    UpdateClaimFileDto updateClaimFileDto = new UpdateClaimFileDto();
                    updateClaimFileDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    updateClaimFileDto.setRefNo(rs.getInt("t1.N_REF_NO"));
                    updateClaimFileDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    updateClaimFileDto.setPolicyNo(rs.getString("t1.V_POL_NUMBER"));
                    updateClaimFileDto.setAssignDateTime(Utility.getDate(rs.getString("V_INIT_LIABILITY_ASSIGN_DATE_TIME"), "yyyy-MM-dd hh:mm:ss"));
                    updateClaimFileDto.setClaimStatus(rs.getString("t2.N_CLAIM_STATUS"));
                    updateClaimFileDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                    updateClaimFileDto.setAssignUser(rs.getString("V_INIT_LIABILITY_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("V_INIT_LIABILITY_ASSIGN_USER_ID"));
                    updateClaimFileDto.setCloseStatus(rs.getString("t2.V_CLOSE_STATUS"));

                    updateClaimFileDto.setIndex(++index);
                    handlerList.add(updateClaimFileDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDtoDecisionMaker(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, boolean isRejectedClaim, String calsheetStatus) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);
        final String SEL_SQL;
        String COUNT_SQL = "";

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER;
        if (null == calsheetStatus || AppConstant.EMPTY_STRING.equalsIgnoreCase(calsheetStatus)) {
            SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
        } else {
            SQL_ORDER = formatOrderSQL(start, orderType, orderField).toString();
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t1.D_ACCID_DATE BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        if (!isRejectedClaim) {
            if ("".equals(assignUserName)) {
                if ("".equals(SQL_SEARCH)) {
                    SEL_SQL = SQL_SELECT_ALL_TO_GRID_DECISION_MAKING.concat(SQL_SEARCH).concat(" WHERE t3.n_ref_id=38 ").concat(SQL_ORDER);
                    COUNT_SQL = SQL_COUNT_DECISION_MAKING.concat(SQL_SEARCH).concat(" WHERE t3.n_ref_id=38 ");
                } else {
                    SEL_SQL = SQL_SELECT_ALL_TO_GRID_DECISION_MAKING.concat(SQL_SEARCH).concat(" AND t3.n_ref_id=38 ").concat(SQL_ORDER);
                    COUNT_SQL = SQL_COUNT_DECISION_MAKING.concat(SQL_SEARCH).concat(" AND t3.n_ref_id=38 ");
                }
            } else {
                if ("".equals(SQL_SEARCH)) {
                    SEL_SQL = SQL_SELECT_ALL_TO_GRID_DECISION_MAKING.concat(SQL_SEARCH).concat(" WHERE t3.n_ref_id=38 ").concat(" AND V_DECISION_MAKING_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(SQL_ORDER);
                    COUNT_SQL = SQL_COUNT_DECISION_MAKING.concat(SQL_SEARCH).concat(" WHERE t3.n_ref_id=38 ").concat(" AND V_DECISION_MAKING_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'");
                } else {
                    SEL_SQL = SQL_SELECT_ALL_TO_GRID_DECISION_MAKING.concat(SQL_SEARCH).concat(" AND t3.n_ref_id=38 ").concat(" AND V_DECISION_MAKING_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(SQL_ORDER);
                    COUNT_SQL = SQL_COUNT_DECISION_MAKING.concat(SQL_SEARCH).concat(" AND t3.n_ref_id=38 ").concat(" AND V_DECISION_MAKING_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'");
                }
            }
        } else {
            if ("".equals(assignUserName)) {
                if ("".equals(SQL_SEARCH)) {
                    SEL_SQL = SQL_SELECT_ALL_TO_GRID_DECISION_MAKING.concat(SQL_SEARCH).concat(SQL_ORDER);
                    COUNT_SQL = SQL_COUNT_DECISION_MAKING.concat(SQL_SEARCH);
                } else {
                    SEL_SQL = SQL_SELECT_ALL_TO_GRID_DECISION_MAKING.concat(SQL_SEARCH).concat(SQL_ORDER);
                    COUNT_SQL = SQL_COUNT_DECISION_MAKING.concat(SQL_SEARCH);
                }
            } else {
                if ("".equals(SQL_SEARCH)) {
                    SEL_SQL = SQL_SELECT_ALL_TO_GRID_DECISION_MAKING.concat(SQL_SEARCH).concat(" AND V_DECISION_MAKING_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(SQL_ORDER);
                    COUNT_SQL = SQL_COUNT_DECISION_MAKING.concat(SQL_SEARCH).concat(" AND V_DECISION_MAKING_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'");
                } else {
                    SEL_SQL = SQL_SELECT_ALL_TO_GRID_DECISION_MAKING.concat(SQL_SEARCH).concat(" AND V_DECISION_MAKING_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(SQL_ORDER);
                    COUNT_SQL = SQL_COUNT_DECISION_MAKING.concat(SQL_SEARCH).concat(" AND V_DECISION_MAKING_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'");
                }
            }
        }


        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    UpdateClaimFileDto updateClaimFileDto = new UpdateClaimFileDto();
                    updateClaimFileDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    updateClaimFileDto.setRefNo(rs.getInt("t1.N_REF_NO"));
                    updateClaimFileDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    updateClaimFileDto.setPolicyNo(rs.getString("t1.V_POL_NUMBER"));
                    updateClaimFileDto.setAssignDateTime(Utility.getDate(rs.getString("D_DECISION_MAKING_ASSIGN_DATE_TIME"), "yyyy-MM-dd hh:mm:ss"));
                    updateClaimFileDto.setClaimStatus(rs.getString("t2.N_CLAIM_STATUS"));
                    updateClaimFileDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                    updateClaimFileDto.setAssignUser(rs.getString("V_DECISION_MAKING_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("V_DECISION_MAKING_ASSIGN_USER_ID"));
                    updateClaimFileDto.setCloseStatus(rs.getString("t2.V_CLOSE_STATUS"));

                    updateClaimFileDto.setIndex(++index);
                    handlerList.add(updateClaimFileDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDtoTwoMember(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);
        final String SEL_SQL;
        String COUNT_SQL = "";

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER;
        if (null == calsheetStatus || AppConstant.EMPTY_STRING.equalsIgnoreCase(calsheetStatus)) {
            SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
        } else {
            SQL_ORDER = formatOrderSQL(start, orderType, orderField).toString();
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t2.D_INPUT_DATETIME BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }


        if ("".equals(assignUserName)) {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SELECT_SQL_TWO_MEMBER.concat(SQL_SEARCH).concat(" WHERE t5.N_PANEL_ID=1 AND t1.N_CLAIM_STATUS = 39 ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_TWO_MEMBER.concat(SQL_SEARCH).concat(" WHERE t5.N_PANEL_ID=1 AND t1.N_CLAIM_STATUS = 39 ");
            } else {
                SEL_SQL = SELECT_SQL_TWO_MEMBER.concat(SQL_SEARCH).concat(" AND t5.N_PANEL_ID=1 AND t1.N_CLAIM_STATUS = 39 ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_TWO_MEMBER.concat(SQL_SEARCH).concat(" AND t5.N_PANEL_ID=1 AND t1.N_CLAIM_STATUS = 39 ");
            }
        } else {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SELECT_SQL_TWO_MEMBER.concat(SQL_SEARCH).concat(" WHERE t5.N_PANEL_ID=1  AND t1.N_CLAIM_STATUS = 39 ").concat(" AND t2.V_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_TWO_MEMBER.concat(SQL_SEARCH).concat(" WHERE t5.N_PANEL_ID=1  AND t1.N_CLAIM_STATUS = 39 ").concat(" AND t2.V_USER_ID=").concat("'").concat(assignUserName).concat("'");
            } else {
                SEL_SQL = SELECT_SQL_TWO_MEMBER.concat(SQL_SEARCH).concat(" AND t5.N_PANEL_ID=1  AND t1.N_CLAIM_STATUS = 39 ").concat(" AND t2.V_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_TWO_MEMBER.concat(SQL_SEARCH).concat(" AND t5.N_PANEL_ID=1  AND t1.N_CLAIM_STATUS = 39 ").concat(" AND t2.V_USER_ID=").concat("'").concat(assignUserName).concat("'");

            }
        }


        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    UpdateClaimFileDto updateClaimFileDto = new UpdateClaimFileDto();
                    updateClaimFileDto.setClaimNo(rs.getInt("t1.N_CLAIM_NO"));
                    updateClaimFileDto.setRefNo(rs.getInt("t2.N_ID"));
                    updateClaimFileDto.setVehicleNo(rs.getString("t3.V_VEHICLE_NO"));
                    updateClaimFileDto.setPolicyNo(rs.getString("t3.V_POL_NUMBER"));
                    updateClaimFileDto.setAssignDateTime(Utility.getDate(rs.getString("t2.D_INPUT_DATETIME"), "yyyy-MM-dd hh:mm:ss"));
                    updateClaimFileDto.setClaimStatus(rs.getString("t1.N_CLAIM_STATUS"));
                    updateClaimFileDto.setClaimStatusDesc(rs.getString("t4.V_STATUS_DESC"));
                    updateClaimFileDto.setAssignUser(rs.getString("t2.V_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_USER_ID"));
                    updateClaimFileDto.setCloseStatus(rs.getString("t1.V_CLOSE_STATUS"));

                    updateClaimFileDto.setIndex(++index);
                    handlerList.add(updateClaimFileDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDtoFourMember(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);
        final String SEL_SQL;
        String COUNT_SQL = "";

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER;
        if (null == calsheetStatus || AppConstant.EMPTY_STRING.equalsIgnoreCase(calsheetStatus)) {
            SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
        } else {
            SQL_ORDER = formatOrderSQL(start, orderType, orderField).toString();
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t2.D_INPUT_DATETIME BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }


        if ("".equals(assignUserName)) {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SELECT_SQL_FOUR_MEMBER.concat(SQL_SEARCH).concat(" WHERE t5.N_PANEL_ID=2 AND t1.N_CLAIM_STATUS = 40 ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_FOUR_MEMBER.concat(SQL_SEARCH).concat(" WHERE t5.N_PANEL_ID= 2 AND t1.N_CLAIM_STATUS = 40 ");
            } else {
                SEL_SQL = SELECT_SQL_FOUR_MEMBER.concat(SQL_SEARCH).concat(" AND t5.N_PANEL_ID=2 AND t1.N_CLAIM_STATUS = 40 ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_FOUR_MEMBER.concat(SQL_SEARCH).concat(" AND t5.N_PANEL_ID= 2 AND t1.N_CLAIM_STATUS = 40 ");
            }
        } else {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SELECT_SQL_FOUR_MEMBER.concat(SQL_SEARCH).concat(" WHERE t5.N_PANEL_ID=2 AND t1.N_CLAIM_STATUS = 40 ").concat(" AND t2.V_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_FOUR_MEMBER.concat(SQL_SEARCH).concat(" WHERE t5.N_PANEL_ID=2 AND t1.N_CLAIM_STATUS = 40 ").concat(" AND t2.V_USER_ID=").concat("'").concat(assignUserName).concat("'");
            } else {
                SEL_SQL = SELECT_SQL_FOUR_MEMBER.concat(SQL_SEARCH).concat(" AND t5.N_PANEL_ID=2 AND t1.N_CLAIM_STATUS = 40 ").concat(" AND t2.V_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_FOUR_MEMBER.concat(SQL_SEARCH).concat(" AND t5.N_PANEL_ID=2 AND t1.N_CLAIM_STATUS = 40 ").concat(" AND t2.V_USER_ID=").concat("'").concat(assignUserName).concat("'");

            }
        }


        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    UpdateClaimFileDto updateClaimFileDto = new UpdateClaimFileDto();
                    updateClaimFileDto.setClaimNo(rs.getInt("t1.N_CLAIM_NO"));
                    updateClaimFileDto.setRefNo(rs.getInt("t2.N_ID"));
                    updateClaimFileDto.setVehicleNo(rs.getString("t3.V_VEHICLE_NO"));
                    updateClaimFileDto.setPolicyNo(rs.getString("t3.V_POL_NUMBER"));
                    updateClaimFileDto.setAssignDateTime(Utility.getDate(rs.getString("t2.D_INPUT_DATETIME"), "yyyy-MM-dd hh:mm:ss"));
                    updateClaimFileDto.setClaimStatus(rs.getString("t1.N_CLAIM_STATUS"));
                    updateClaimFileDto.setClaimStatusDesc(rs.getString("t4.V_STATUS_DESC"));
                    updateClaimFileDto.setAssignUser(rs.getString("t2.V_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_USER_ID"));
                    updateClaimFileDto.setCloseStatus(rs.getString("t1.V_CLOSE_STATUS"));

                    updateClaimFileDto.setIndex(++index);
                    handlerList.add(updateClaimFileDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDtoSpecialTeam(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus, String assignUserType) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);
        final String SEL_SQL;
        String COUNT_SQL = "";

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER;
        if (null == calsheetStatus || AppConstant.EMPTY_STRING.equalsIgnoreCase(calsheetStatus)) {
            SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
        } else {
            SQL_ORDER = formatOrderSQL(start, orderType, orderField).toString();
        }

        int accessUserType;
        if (assignUserType.equals("11")) {
            accessUserType = 63;
        } else {
            accessUserType = 43;
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t2.D_SPECIAL_TEAM_ASSIGN_DATE_TIME BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }


        if ("".equals(assignUserName)) {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SELECT_SQL_SPECIAL_TEAM.concat(SQL_SEARCH).concat(" WHERE t2.V_STATUS IN(63,65) ").concat("AND t5.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SPECIAL_TEAM.concat(SQL_SEARCH).concat(" WHERE t2.V_STATUS IN(63,65) ").concat("AND t5.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ");
            } else {
                SEL_SQL = SELECT_SQL_SPECIAL_TEAM.concat(SQL_SEARCH).concat(" AND t2.V_STATUS IN(63,65) ").concat("AND t5.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SPECIAL_TEAM.concat(SQL_SEARCH).concat(" AND t2.V_STATUS IN(63,65) ").concat("AND t5.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ");
            }
        } else {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SELECT_SQL_SPECIAL_TEAM.concat(SQL_SEARCH).concat(" WHERE t2.V_SPECIAL_TEAM_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat("AND t2.V_STATUS IN(63,65) ").concat("AND t5.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SPECIAL_TEAM.concat(SQL_SEARCH).concat(" WHERE t2.V_SPECIAL_TEAM_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'").concat("AND t2.V_STATUS IN(63,65) ").concat("AND t5.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ");
            } else {
                SEL_SQL = SELECT_SQL_SPECIAL_TEAM.concat(SQL_SEARCH).concat(" AND t2.V_SPECIAL_TEAM_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat("AND t2.V_STATUS IN(63,65) ").concat("AND t5.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SPECIAL_TEAM.concat(SQL_SEARCH).concat(" AND t2.V_SPECIAL_TEAM_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'").concat("AND t2.V_STATUS IN(63,65) ").concat("AND t5.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ");

            }
        }


        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    UpdateClaimFileDto updateClaimFileDto = new UpdateClaimFileDto();
                    updateClaimFileDto.setClaimNo(rs.getInt("t1.N_CLAIM_NO"));
                    updateClaimFileDto.setRefNo(rs.getInt("t2.N_CAL_SHEET_ID"));
                    updateClaimFileDto.setVehicleNo(rs.getString("t3.V_VEHICLE_NO"));
                    updateClaimFileDto.setPolicyNo(rs.getString("t3.V_POL_NUMBER"));
                    updateClaimFileDto.setAssignDateTime(Utility.getDate(rs.getString("t2.D_SPECIAL_TEAM_ASSIGN_DATE_TIME"), "yyyy-MM-dd hh:mm:ss"));
                    updateClaimFileDto.setClaimStatus(rs.getString("t3.N_CLAIM_STATUS"));
                    updateClaimFileDto.setClaimStatusDesc(rs.getString("t4.V_STATUS_DESC"));
                    updateClaimFileDto.setAssignUser(rs.getString("t2.V_SPECIAL_TEAM_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_SPECIAL_TEAM_ASSIGN_USER_ID"));
                    updateClaimFileDto.setCloseStatus(rs.getString("t1.V_CLOSE_STATUS"));

                    updateClaimFileDto.setIndex(++index);
                    handlerList.add(updateClaimFileDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDtoMofa(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus, String assignUserType, String status, String assignUserLevel) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = AppConstant.ZERO_INT;
        List handlerList = new ArrayList(200);
        String SEL_SQL=AppConstant.EMPTY_STRING;
        String COUNT_SQL = AppConstant.EMPTY_STRING;


        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER;
        if (null == calsheetStatus || AppConstant.EMPTY_STRING.equalsIgnoreCase(calsheetStatus)) {
            SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
        } else {
            SQL_ORDER = formatOrderSQL(start, orderType, orderField).toString();
        }
          // status = String.valueOf(AppConstant.CAL_SHEET_FORWARD_FOR_MOFA_APPROVAL);

        String accessUserType = String.valueOf(AppConstant.ACCESS_LEVEL_MOFA_TEAM).concat(",")
                .concat(String.valueOf(AppConstant.ACCESS_LEVEL_SPECIAL_TEAM).concat(",")
                        .concat(String.valueOf(AppConstant.ACCESS_LEVEL_OFFER_TEAM_MOFA_TEAM)).concat(",")
                        .concat(String.valueOf(AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM)));
        if (AppConstant.STRING_LEVEL_MOFA_OFFER_TEAM.equals(assignUserType) && AppConstant.STRING_LEVEL_ONE.equals(assignUserLevel)) {
            accessUserType = String.valueOf(AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM);
        } else if (assignUserType.equals(AppConstant.STRING_LEVEL_MOFA_OFFER_TEAM) && (AppConstant.ZERO.equals(assignUserName) || AppConstant.EMPTY_STRING.equals(assignUserName))) {
            accessUserType = String.valueOf(AppConstant.ACCESS_LEVEL_OFFER_TEAM_MOFA_TEAM)
                    .concat(",")
                    .concat(String.valueOf(AppConstant.ACCESS_LEVEL_OFFER_TEAM_SPECIAL_TEAM));
        } else if (AppConstant.STRING_LEVEL_MOFA_OFFER_TEAM.equals(assignUserType)) {
            accessUserType = String.valueOf(AppConstant.ACCESS_LEVEL_OFFER_TEAM_MOFA_TEAM);
        } else if (AppConstant.STRING_LEVEL_MOFA_TEAM.equals(assignUserType) && AppConstant.STRING_LEVEL_ONE.equals(assignUserLevel)) {
            accessUserType = String.valueOf(AppConstant.ACCESS_LEVEL_SPECIAL_TEAM);
        } else if (AppConstant.STRING_LEVEL_MOFA_TEAM.equals(assignUserType) && (AppConstant.ZERO.equals(assignUserName) || AppConstant.EMPTY_STRING.equals(assignUserName))) {
            accessUserType = String.valueOf(AppConstant.ACCESS_LEVEL_SPECIAL_TEAM)
                    .concat(",")
                    .concat(String.valueOf(AppConstant.ACCESS_LEVEL_MOFA_TEAM));
        } else if (AppConstant.STRING_LEVEL_MOFA_TEAM.equals(assignUserType)) {
            accessUserType = String.valueOf(AppConstant.ACCESS_LEVEL_MOFA_TEAM);
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t2.D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        boolean isSpecialComment = status.equals(String.valueOf(AppConstant.CLAIM_STATUS_CLAIM_HANDLER_SPECIAL_COMMENT));

        if ("".equals(assignUserName) || AppConstant.ZERO.equals(assignUserName)) {
            if (isSpecialComment) {
                SEL_SQL = SELECT_SQL_MOFA_SPECIAL_COMMENT.concat(SQL_SEARCH).concat(AppConstant.STRING_EMPTY.equals(SQL_SEARCH) ? " WHERE" : "AND").concat(" AND t5.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_MOFA_SPECIAL_COMMENT.concat(SQL_SEARCH).concat(AppConstant.STRING_EMPTY.equals(SQL_SEARCH) ? " WHERE" : "AND").concat(" AND t5.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ");
            } else if (AppConstant.STRING_LEVEL_ONE.equals(assignUserLevel)) {
                SEL_SQL = SELECT_SQL_SPECIAL2_TEAM.concat(SQL_SEARCH).concat(AppConstant.STRING_EMPTY.equals(SQL_SEARCH) ? " WHERE" : "AND").concat(" t2.V_STATUS = 63").concat(" AND t5.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SPECIAL2_TEAM.concat(SQL_SEARCH).concat(AppConstant.STRING_EMPTY.equals(SQL_SEARCH) ? " WHERE" : "AND").concat(" t2.V_STATUS = 63").concat(" AND t5.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ");
            } else if ((AppConstant.STRING_LEVEL_MOFA_TEAM.equals(assignUserType) || AppConstant.STRING_LEVEL_MOFA_OFFER_TEAM.equals(assignUserType) || AppConstant.EMPTY_STRING.equals(assignUserType)) && (AppConstant.EMPTY_STRING.equals(assignUserLevel) || AppConstant.ZERO.equals(assignUserLevel))) {
                SEL_SQL = SELECT_SQL_MOFA_SPECIAL_COMMENT1.concat(SQL_SEARCH).concat(" WHERE t2.V_STATUS IN (63, 64) ").concat(" AND t5.n_accessusrtype IN ").concat("(").concat(accessUserType).concat(")").concat(SQL_ORDER).concat(" ");
                COUNT_SQL = COUNT_SQL_MOFA_SPECIAL_COMMENT1.concat(SQL_SEARCH).concat(" WHERE t2.V_STATUS IN (63, 64) ").concat(" AND t5.n_accessusrtype IN ").concat("(").concat(accessUserType).concat(")").concat(" ");
            } else if(AppConstant.STRING_LEVEL_MOFA_TEAM.equals(assignUserType) || AppConstant.STRING_LEVEL_MOFA_OFFER_TEAM.equals(assignUserType)){
                SEL_SQL = SELECT_SQL_MOFA_TEAM.concat(SQL_SEARCH).concat(AppConstant.STRING_EMPTY.equals(SQL_SEARCH) ? " WHERE" : "AND").concat(" t2.V_STATUS = 64").concat(" AND t5.n_accessusrtype =  ").concat(accessUserType).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_MOFA_TEAM.concat(SQL_SEARCH).concat(AppConstant.STRING_EMPTY.equals(SQL_SEARCH) ? " WHERE" : "AND").concat(" t2.V_STATUS = 64").concat(" AND t5.n_accessusrtype =  ").concat(accessUserType).concat(" ");
            }
        } else {
            if (isSpecialComment) {
                SEL_SQL = SELECT_SQL_MOFA_SPECIAL_COMMENT.concat(SQL_SEARCH).concat(AppConstant.STRING_EMPTY.equals(SQL_SEARCH) ? " WHERE" : "AND").concat(" t1.V_SPECIAL_APPROVAL_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(" AND t5.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_MOFA_SPECIAL_COMMENT.concat(SQL_SEARCH).concat(AppConstant.STRING_EMPTY.equals(SQL_SEARCH) ? " WHERE" : "AND").concat(" t1.V_SPECIAL_APPROVAL_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(" AND t5.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ");
            } else if (assignUserLevel.equals(AppConstant.STRING_LEVEL_ONE)) {
                SEL_SQL = SELECT_SQL_SPECIAL1_TEAM.concat(SQL_SEARCH).concat(" AND t2.V_SPECIAL_TEAM_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat("AND t2.V_STATUS =63 ").concat("AND t5.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SPECIAL1_TEAM.concat(SQL_SEARCH).concat(" AND t2.V_SPECIAL_TEAM_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'").concat(" AND t2.V_STATUS =63 ").concat("AND t5.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ");
            } else if(AppConstant.STRING_LEVEL_MOFA_TEAM.equals(assignUserType) || AppConstant.STRING_LEVEL_MOFA_OFFER_TEAM.equals(assignUserType) ) {
                SEL_SQL = SELECT_SQL_MOFA_TEAM.concat(SQL_SEARCH).concat(AppConstant.STRING_EMPTY.equals(SQL_SEARCH) ? " WHERE" : "AND").concat(" t2.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat(" AND t2.V_STATUS =64 ").concat("AND t5.n_accessusrtype =").concat(String.valueOf(accessUserType)).concat(" ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_MOFA_TEAM.concat(SQL_SEARCH).concat(AppConstant.STRING_EMPTY.equals(SQL_SEARCH) ? " WHERE" : "AND").concat(" t2.V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'").concat(" AND t2.V_STATUS =64 ").concat("AND t5.n_accessusrtype =").concat(String.valueOf(accessUserType)).concat(" ");
            }
        } try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    UpdateClaimFileDto updateClaimFileDto = new UpdateClaimFileDto();
                    updateClaimFileDto.setClaimNo(rs.getInt("t1.N_CLAIM_NO"));
                    updateClaimFileDto.setRefNo(isSpecialComment ? AppConstant.ZERO_INT : rs.getInt("t2.N_CAL_SHEET_ID"));
                    updateClaimFileDto.setVehicleNo(rs.getString("V_VEHICLE_NO"));
                    updateClaimFileDto.setPolicyNo(rs.getString("V_POL_NUMBER"));
                    updateClaimFileDto.setAssignDateTime(Utility.getDate(isSpecialComment ? rs.getString("ASSIGN_DATE_TIME")
                            : (AppConstant.STRING_LEVEL_MOFA_TEAM.equals(String.valueOf(accessUserType)) && AppConstant.STRING_LEVEL_ONE.equals(assignUserLevel)
                            || AppConstant.STRING_LEVEL_MOFA_OFFER_TEAM.equals(String.valueOf(accessUserType))) && AppConstant.STRING_LEVEL_ONE.equals(assignUserLevel)
                            ? rs.getString("ASSIGN_DATE_TIME")
                            : rs.getString("ASSIGN_USER_ID") == null
                            ? rs.getString("t2.D_SPECIAL_TEAM_ASSIGN_DATE_TIME")
                            : rs.getString("ASSIGN_DATE_TIME"), "yyyy-MM-dd hh:mm:ss"));
                    updateClaimFileDto.setClaimStatus(rs.getString("N_CLAIM_STATUS"));
                    updateClaimFileDto.setClaimStatusDesc(rs.getString("t4.V_STATUS_DESC"));
                    updateClaimFileDto.setAssignUser(isSpecialComment ? rs.getString("ASSIGN_USER_ID") : rs.getString("ASSIGN_USER_ID") == null ? rs.getString("t2.V_SPECIAL_TEAM_ASSIGN_USER_ID") : rs.getString("ASSIGN_USER_ID"));
                    updateClaimFileDto.setCloseStatus(rs.getString("t1.V_CLOSE_STATUS"));

                    updateClaimFileDto.setIndex(++index);
                    handlerList.add(updateClaimFileDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public Boolean updateClaimUHandlerByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_HANDLER_ASSIGN_USER);
            ps.setString(++index, assignUserId);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, txnId);

            if (ps.executeUpdate() > 0) {
                return true;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return false;
    }

    @Override
    public Boolean updateTotalLossTeamByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception {
        PreparedStatement ps;
        PreparedStatement ps2;
        PreparedStatement ps3;
        int index = 0;
        int index2 = 0;
        int index3 = 0;
        try {
            ps = connection.prepareStatement(SELECT_ONE_WHERE_LIABILITY_APPR_STATUS_PENDING);
            ps.setInt(++index, txnId);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                ps2 = connection.prepareStatement(UPDATE_CLAIM_HANDLER_ASSIGN_USER_ANDINIT_LIABILITI_ASSIGN_USER);
                ps2.setString(++index2, assignUserId);
                ps2.setString(++index2, assignUserId);
                ps2.setString(++index2, Utility.sysDateTime());
                ps2.setInt(++index2, txnId);
                if (ps2.executeUpdate() > 0) {
                    return true;
                }
                ps2.close();
            } else {
                ps3 = connection.prepareStatement(UPDATE_CLAIM_HANDLER_ASSIGN_USER);
                ps3.setString(++index3, assignUserId);
                ps3.setString(++index3, Utility.sysDateTime());
                ps3.setInt(++index3, txnId);

                if (ps3.executeUpdate() > 0) {
                    return true;
                }
                ps3.close();
            }
            ps.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return false;
    }

    @Override
    public Boolean updateInitClaimHandlerByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_INIT_CLAIM_HANDLER_ASSIGN_USER);
            ps.setString(++index, assignUserId);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, txnId);

            if (ps.executeUpdate() > 0) {
                return true;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return false;
    }


    @Override
    public Boolean updateDecisionMakerByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_DECISION_MAKER_ASSIGN_USER);
            ps.setString(++index, assignUserId);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, txnId);

            if (ps.executeUpdate() > 0) {
                return true;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return false;
    }

    @Override
    public Boolean updateSpecialTeamByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_SPECIAL_TEAM_ASSIGN_USER);
            ps.setString(++index, assignUserId);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, txnId);

            if (ps.executeUpdate() > 0) {
                return true;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return false;
    }

    @Override
    public Boolean updateMofaByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_MOFA_ASSIGN_USER);
            ps.setString(++index, assignUserId);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, txnId);

            if (ps.executeUpdate() > 0) {
                return true;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return false;
    }

    @Override
    public Boolean updateTwoMemberByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_TWO_MEMBER_PANEL_ASSIGN_USER);
            ps.setString(++index, assignUserId);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, txnId);

            if (ps.executeUpdate() > 0) {
                return true;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return false;
    }

    @Override
    public Boolean updateFourMemberByTxnId(Connection connection, Integer txnId, String assignUserId) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_FOUR_MEMBER_PANEL_ASSIGN_USER);
            ps.setString(++index, assignUserId);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, txnId);

            if (ps.executeUpdate() > 0) {
                return true;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return false;
    }

    @Override
    public Boolean updateCalsheetAssignUser(Connection connection, String assignUserId, Integer claimNo) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CAL_SHEET_ASSIGN_USER);
            ps.setString(++index, assignUserId);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, claimNo);

            if (ps.executeUpdate() > 0) {
                return true;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return false;
    }

    @Override
    public Boolean selectCalsheetAssignUser(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(SELECT_CAL_SHEET_ASSIGN_USER);
            ps.setInt(++index, claimNo);

            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return true;
                }
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return false;
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDtoSparePartsCoordinatorCheckCalsheet(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus, String assignUserType) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);
        final String SEL_SQL;
        String COUNT_SQL = "";

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER;
        if (null == calsheetStatus || AppConstant.EMPTY_STRING.equalsIgnoreCase(calsheetStatus)) {
            SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
        } else {
            SQL_ORDER = formatOrderSQL(start, orderType, orderField).toString();
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t2.D_SPARE_PART_CORDINATOR_ASSIGN_DATE_TIME BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }


        if ("".equals(assignUserName)) {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SELECT_SQL_SPAREPARTS_COORDINATOR.concat(SQL_SEARCH).concat(" WHERE t2.V_STATUS IN(59) ").concat("AND t5.n_accessusrtype = 27 ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SPAREPARTS_COORDINATOR.concat(SQL_SEARCH).concat(" WHERE t2.V_STATUS IN(59) ").concat("AND t5.n_accessusrtype = 27 ");
            } else {
                SEL_SQL = SELECT_SQL_SPAREPARTS_COORDINATOR.concat(SQL_SEARCH).concat(" AND t2.V_STATUS IN(59) ").concat("AND t5.n_accessusrtype = 27 ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SPAREPARTS_COORDINATOR.concat(SQL_SEARCH).concat(" AND t2.V_STATUS IN(59) ").concat("AND t5.n_accessusrtype = 27 ");
            }
        } else {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SELECT_SQL_SPAREPARTS_COORDINATOR.concat(SQL_SEARCH).concat(" WHERE t2.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat("AND t2.V_STATUS IN(59) ").concat("AND t5.n_accessusrtype = 27 ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SPAREPARTS_COORDINATOR.concat(SQL_SEARCH).concat(" WHERE t2.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'").concat("AND t2.V_STATUS IN(59) ").concat("AND t5.n_accessusrtype = 27 ");
            } else {
                SEL_SQL = SELECT_SQL_SPAREPARTS_COORDINATOR.concat(SQL_SEARCH).concat(" AND t2.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat("AND t2.V_STATUS IN(59) ").concat("AND t5.n_accessusrtype = 27 ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SPAREPARTS_COORDINATOR.concat(SQL_SEARCH).concat(" AND t2.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("'").concat("AND t2.V_STATUS IN(59) ").concat("AND t5.n_accessusrtype = 27 ");

            }
        }


        try {

            ps = connection.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = connection.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    UpdateClaimFileDto updateClaimFileDto = new UpdateClaimFileDto();
                    updateClaimFileDto.setClaimNo(rs.getInt("t1.N_CLAIM_NO"));
                    updateClaimFileDto.setRefNo(rs.getInt("t2.N_CAL_SHEET_ID"));
                    updateClaimFileDto.setVehicleNo(rs.getString("t3.V_VEHICLE_NO"));
                    updateClaimFileDto.setPolicyNo(rs.getString("t3.V_POL_NUMBER"));
                    updateClaimFileDto.setAssignDateTime(Utility.getDate(rs.getString("t2.D_SPARE_PART_CORDINATOR_ASSIGN_DATE_TIME"), "yyyy-MM-dd hh:mm:ss"));
                    updateClaimFileDto.setClaimStatus(rs.getString("t3.N_CLAIM_STATUS"));
                    updateClaimFileDto.setClaimStatusDesc(rs.getString("t4.V_STATUS_DESC"));
                    updateClaimFileDto.setAssignUser(rs.getString("t2.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_SPARE_PART_CORDINATOR_ASSIGN_USER_ID"));
                    updateClaimFileDto.setCloseStatus(rs.getString("t1.V_CLOSE_STATUS"));

                    updateClaimFileDto.setIndex(++index);
                    handlerList.add(updateClaimFileDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDtoBillCheckingTeamCheckCalsheet(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus, String assignUserType) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);
        final String SEL_SQL;
        String COUNT_SQL = "";

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER;
        if (null == calsheetStatus || AppConstant.EMPTY_STRING.equalsIgnoreCase(calsheetStatus)) {
            SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
        } else {
            SQL_ORDER = formatOrderSQL(start, orderType, orderField).toString();
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t2.D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }


        if ("".equals(assignUserName)) {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SELECT_SQL_SCURTINIZING_TEAM.concat(SQL_SEARCH).concat(" WHERE t2.V_STATUS IN(61) ").concat("AND t5.n_accessusrtype = 28 ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SCURTINIZING_TEAM.concat(SQL_SEARCH).concat(" WHERE t2.V_STATUS IN(61) ").concat("AND t5.n_accessusrtype = 28 ");
            } else {
                SEL_SQL = SELECT_SQL_SCURTINIZING_TEAM.concat(SQL_SEARCH).concat(" AND t2.V_STATUS IN(61) ").concat("AND t5.n_accessusrtype = 28 ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SCURTINIZING_TEAM.concat(SQL_SEARCH).concat(" AND t2.V_STATUS IN(61) ").concat("AND t5.n_accessusrtype = 28 ");
            }
        } else {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SELECT_SQL_SCURTINIZING_TEAM.concat(SQL_SEARCH).concat(" WHERE t2.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat("AND t2.V_STATUS IN(61) ").concat("AND t5.n_accessusrtype = 28 ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SCURTINIZING_TEAM.concat(SQL_SEARCH).concat(" WHERE t2.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat("AND t2.V_STATUS IN(61) ").concat("AND t5.n_accessusrtype = 28 ");
            } else {
                SEL_SQL = SELECT_SQL_SCURTINIZING_TEAM.concat(SQL_SEARCH).concat(" AND t2.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat("AND t2.V_STATUS IN(61) ").concat("AND t5.n_accessusrtype = 28 ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SCURTINIZING_TEAM.concat(SQL_SEARCH).concat(" AND t2.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID=").concat("'").concat(assignUserName).concat("' ").concat("AND t2.V_STATUS IN(61) ").concat("AND t5.n_accessusrtype = 28 ");

            }
        }


        try {

            ps = connection.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = connection.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    UpdateClaimFileDto updateClaimFileDto = new UpdateClaimFileDto();
                    updateClaimFileDto.setClaimNo(rs.getInt("t1.N_CLAIM_NO"));
                    updateClaimFileDto.setRefNo(rs.getInt("t2.N_CAL_SHEET_ID"));
                    updateClaimFileDto.setVehicleNo(rs.getString("t3.V_VEHICLE_NO"));
                    updateClaimFileDto.setPolicyNo(rs.getString("t3.V_POL_NUMBER"));
                    updateClaimFileDto.setAssignDateTime(Utility.getDate(rs.getString("t2.D_SCRUTINIZE_TEAM_ASSIGN_DATE_TIME"), "yyyy-MM-dd hh:mm:ss"));
                    updateClaimFileDto.setClaimStatus(rs.getString("t3.N_CLAIM_STATUS"));
                    updateClaimFileDto.setClaimStatusDesc(rs.getString("t4.V_STATUS_DESC"));
                    updateClaimFileDto.setAssignUser(rs.getString("t2.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_SCRUTINIZE_TEAM_ASSIGN_USER_ID"));
                    updateClaimFileDto.setCloseStatus(rs.getString("t1.V_CLOSE_STATUS"));

                    updateClaimFileDto.setIndex(++index);
                    handlerList.add(updateClaimFileDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDtoSparePartsCoordinatorSupplyOrder(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String calsheetStatus, String assignUserType) throws Exception {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);
        final String SEL_SQL;
        String COUNT_SQL = "";

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER;
        if (null == calsheetStatus || AppConstant.EMPTY_STRING.equalsIgnoreCase(calsheetStatus)) {
            SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
        } else {
            SQL_ORDER = formatOrderSQL(start, orderType, orderField).toString();
        }

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t2.D_SUPPLY_ORDER_ASSIGN_DATE_TIME BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

//        final String SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);
//
//        final String COUNT_SQL = SQL_COUNT_CLAIM_HANDLER.concat(SQL_SEARCH);
        int accessUserType = 27;
        if ("".equals(assignUserName)) {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SELECT_SQL_SPAREPARTS_COORDINATOR_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" WHERE t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SPAREPARTS_COORDINATOR_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" WHERE t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ").concat(" ");
            } else {
                SEL_SQL = SELECT_SQL_SPAREPARTS_COORDINATOR_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" AND t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SPAREPARTS_COORDINATOR_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" AND t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ");
            }
        } else {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SELECT_SQL_SPAREPARTS_COORDINATOR_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" WHERE t2.V_SUPPLY_ORDER_ASSIGN_USER=").concat("'").concat(assignUserName).concat("' ").concat(" AND t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SPAREPARTS_COORDINATOR_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" WHERE t2.V_SUPPLY_ORDER_ASSIGN_USER=").concat("'").concat(assignUserName).concat("' ").concat(" AND t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ");
            } else {
                SEL_SQL = SELECT_SQL_SPAREPARTS_COORDINATOR_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_USER=").concat("'").concat(assignUserName).concat("' ").concat(" AND t4.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ").concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SPAREPARTS_COORDINATOR_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_USER=").concat("'").concat(assignUserName).concat("' ").concat(" AND t4.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ");

            }
        }

        try {

            ps = connection.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = connection.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    UpdateClaimFileDto updateClaimFileDto = new UpdateClaimFileDto();
                    updateClaimFileDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    updateClaimFileDto.setRefNo(rs.getInt("t1.N_REF_NO"));
                    updateClaimFileDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    updateClaimFileDto.setPolicyNo(rs.getString("t1.V_POL_NUMBER"));
                    updateClaimFileDto.setAssignDateTime(Utility.getDate(rs.getString("t2.D_SUPPLY_ORDER_ASSIGN_DATE_TIME"), "yyyy-MM-dd hh:mm:ss"));
                    updateClaimFileDto.setClaimStatus(rs.getString("t2.N_CLAIM_STATUS"));
                    updateClaimFileDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                    updateClaimFileDto.setAssignUser(rs.getString("t2.V_SUPPLY_ORDER_ASSIGN_USER") == null ? AppConstant.STRING_EMPTY : rs.getString("t2.V_SUPPLY_ORDER_ASSIGN_USER"));
                    updateClaimFileDto.setCloseStatus(rs.getString("t2.V_CLOSE_STATUS"));

                    updateClaimFileDto.setIndex(++index);
                    handlerList.add(updateClaimFileDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public boolean updateSupplyOrderByClaimNo(Connection connection, Integer claimNo, String assignUser) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_SUPPLY_ORDER_ASSIGN_USER);
            ps.setString(++index, assignUser);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, claimNo);

            if (ps.executeUpdate() > 0) {
                return true;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return false;
    }

    @Override
    public boolean updateSparePartsCoordinatorByTxnId(Connection connection, Integer txnId, String assignUser) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_SPARE_PARTS_COORDINATOR);
            ps.setString(++index, assignUser);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, txnId);

            if (ps.executeUpdate() > 0) {
                return true;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return false;
    }

    @Override
    public boolean updateScurtinizingTeamByTxnId(Connection connection, Integer txnId, String assignUser) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_SCURTINIZING_TEAM);
            ps.setString(++index, assignUser);
            ps.setString(++index, Utility.sysDateTime());
            ps.setInt(++index, txnId);

            if (ps.executeUpdate() > 0) {
                return true;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return false;
    }

    @Override
    public void updateCalsheetAssignMofaUser(Connection connection, String assignUser, Integer txnId) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_CALSHEET_MOFA_ASSIGN_USER);
            ps.setString(1, assignUser);
            ps.setString(2, Utility.sysDateTime());
            ps.setInt(3, txnId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public DataGridDto getClaimHandlerDataGridDtoBillCheckingTeamSupplyOrder(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String assignUserName, String assignUserType) {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List handlerList = new ArrayList(200);
        final String SEL_SQL;
        String COUNT_SQL;

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER;
        SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t2.D_SUPPLY_ORDER_ASSIGN_DATE_TIME BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        int accessUserType = 28;
        if ("".equals(assignUserName)) {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SELECT_SQL_SCRUTINIZING_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" WHERE t5.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SCRUTINIZING_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" WHERE t5.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ").concat(" ");
            } else {
                SEL_SQL = SELECT_SQL_SCRUTINIZING_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" AND t5.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SCRUTINIZING_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" AND t5.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ");
            }
        } else {
            if ("".equals(SQL_SEARCH)) {
                SEL_SQL = SELECT_SQL_SCRUTINIZING_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" WHERE t4.v_apprv_assign_scrutinizing_user_id=").concat("'").concat(assignUserName).concat("' ").concat(" AND t5.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SCRUTINIZING_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" WHERE t4.v_apprv_assign_scrutinizing_user_id=").concat("'").concat(assignUserName).concat("' ").concat(" AND t5.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ");
            } else {
                SEL_SQL = SELECT_SQL_SCRUTINIZING_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" AND t4.v_apprv_assign_scrutinizing_user_id=").concat("'").concat(assignUserName).concat("' ").concat(" AND t5.n_accessusrtype =  ").concat(String.valueOf(accessUserType)).concat(" ").concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ").concat(SQL_ORDER);
                COUNT_SQL = COUNT_SQL_SCRUTINIZING_SUPPLY_ORDER.concat(SQL_SEARCH).concat(" AND t4.v_apprv_assign_scrutinizing_user_id=").concat("'").concat(assignUserName).concat("' ").concat(" AND t5.n_accessusrtype = ").concat(String.valueOf(accessUserType)).concat(" ").concat(" AND t2.V_SUPPLY_ORDER_ASSIGN_STATUS = 'Y' ");

            }
        }

        try {

            ps = connection.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = connection.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    UpdateClaimFileDto updateClaimFileDto = new UpdateClaimFileDto();
                    updateClaimFileDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    updateClaimFileDto.setRefNo(rs.getInt("t4.n_supply_order_ref_no"));
                    updateClaimFileDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    updateClaimFileDto.setPolicyNo(rs.getString("t1.V_POL_NUMBER"));
                    updateClaimFileDto.setAssignDateTime(Utility.getDate(rs.getString("t4.d_apprv_assign_scrutinizing_date_time"), "yyyy-MM-dd hh:mm:ss"));
                    updateClaimFileDto.setClaimStatus(rs.getString("t2.N_CLAIM_STATUS"));
                    updateClaimFileDto.setClaimStatusDesc(rs.getString("t3.v_status_desc"));
                    updateClaimFileDto.setAssignUser(rs.getString("t4.v_apprv_assign_scrutinizing_user_id") == null ? AppConstant.STRING_EMPTY : rs.getString("t4.v_apprv_assign_scrutinizing_user_id"));
                    updateClaimFileDto.setCloseStatus(rs.getString("t2.V_CLOSE_STATUS"));

                    updateClaimFileDto.setIndex(++index);
                    handlerList.add(updateClaimFileDto);

                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(handlerList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public boolean updateSupplyOrderScurtinizingTeamByTxnId(Connection connection, Integer txnId, String assignUser) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_SUPPLY_ORDER_SCRUTINIZING_USER);
            ps.setString(1, assignUser);
            ps.setString(2, Utility.sysDateTime());
            ps.setInt(3, txnId);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean updateSpecialApprovalUser(Connection connection, Integer claimNo, String assignUser, String user) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(UPDATE_SPECIAL_APPROVAL_USER);
            ps.setString(1, assignUser);
            ps.setString(2, Utility.sysDateTime());
            ps.setString(3, Utility.sysDateTime());
            ps.setInt(4, claimNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

}
