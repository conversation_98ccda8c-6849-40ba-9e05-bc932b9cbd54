package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.ClaimWiseDocumentDao;
import com.misyn.mcms.claim.dto.ClaimDocumentDto;
import com.misyn.mcms.claim.dto.ClaimDocumentTypeDto;
import com.misyn.mcms.claim.dto.ClaimUploadViewDto;
import com.misyn.mcms.claim.dto.ClaimWiseDocumentDto;
import com.misyn.mcms.claim.enums.YesNoWantDecideEnum;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class ClaimWiseDocumentDaoImpl extends AbstractBaseDao<ClaimDocumentDaoImpl> implements ClaimWiseDocumentDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimWiseDocumentDao.class);

    @Override
    public ClaimWiseDocumentDto insertMaster(Connection connection, ClaimWiseDocumentDto claimWiseDocumentDto) throws MisynJDBCException {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(INSERT_CLAIM_WISE_DOCUMENT);
            ps.setInt(++index, claimWiseDocumentDto.getDocumentTypeId());
            ps.setInt(++index, claimWiseDocumentDto.getClaimNo());
            ps.setInt(++index, claimWiseDocumentDto.getDepartmentId());
            ps.setString(++index, claimWiseDocumentDto.getIsMandatory());
            ps.setString(++index, claimWiseDocumentDto.getIsPartialLoss());
            ps.setString(++index, claimWiseDocumentDto.getIsTotLoss());
            ps.setString(++index, claimWiseDocumentDto.getIsLumpSum());
            ps.setString(++index, claimWiseDocumentDto.getIsThirdPartyPropVehicle());
            ps.setString(++index, claimWiseDocumentDto.getIsThirdPartyDeath());
            ps.setString(++index, claimWiseDocumentDto.getIsThirdPartyInjuries());
            ps.setInt(++index, claimWiseDocumentDto.getDocReqFrom());
            ps.setString(++index, claimWiseDocumentDto.getIsDocUpload());
            ps.setString(++index, claimWiseDocumentDto.getRecStatus());
            ps.setString(++index, claimWiseDocumentDto.getInpUserId());
            ps.setString(++index, claimWiseDocumentDto.getInpDateTime());
            if (ps.executeUpdate() > 0) {
                return claimWiseDocumentDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimWiseDocumentDto updateMaster(Connection connection, ClaimWiseDocumentDto claimWiseDocumentDto) throws MisynJDBCException {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_WISE_DOCUMENT);
            ps.setString(++index, claimWiseDocumentDto.getIsMandatory());
            ps.setString(++index, claimWiseDocumentDto.getInpUserId());
            ps.setString(++index, claimWiseDocumentDto.getInpDateTime());
            ps.setInt(++index, claimWiseDocumentDto.getClaimNo());
            ps.setInt(++index, claimWiseDocumentDto.getDocumentTypeId());

            if (ps.executeUpdate() > 0) {
                return claimWiseDocumentDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public ClaimWiseDocumentDto insertTemporary(Connection connection, ClaimWiseDocumentDto claimWiseDocumentDto) throws MisynJDBCException {
        return null;
    }

    @Override
    public ClaimWiseDocumentDto updateTemporary(Connection connection, ClaimWiseDocumentDto claimWiseDocumentDto) throws MisynJDBCException {
        return null;
    }

    @Override
    public ClaimWiseDocumentDto insertHistory(Connection connection, ClaimWiseDocumentDto claimWiseDocumentDto) throws MisynJDBCException {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ClaimWiseDocumentDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public ClaimWiseDocumentDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ClaimWiseDocumentDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public void updateByClaimNoAndDocumentTypeId(Connection connection, ClaimWiseDocumentDto claimWiseDocumentDto) throws MisynJDBCException {

        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_WISE_DOCUMENT_BY_CLAIM_NO_AND_DOC_TYPE_ID);
            ps.setString(++index, claimWiseDocumentDto.getIsMandatory());
            ps.setString(++index, claimWiseDocumentDto.getInpUserId());
            ps.setString(++index, claimWiseDocumentDto.getInpDateTime());
            ps.setInt(++index, claimWiseDocumentDto.getClaimNo());
            ps.setInt(++index, claimWiseDocumentDto.getDocumentTypeId());
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public void updateByClaimNoAndDocumentTypeIdInPartialLoss(Connection connection, ClaimWiseDocumentDto claimWiseDocumentDto) throws MisynJDBCException {

        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_WISE_DOCUMENT_BY_CLAIM_NO_AND_DOC_TYPE_ID_IN);
            ps.setString(++index, claimWiseDocumentDto.getIsMandatory());
            ps.setString(++index, claimWiseDocumentDto.getInpUserId());
            ps.setString(++index, claimWiseDocumentDto.getInpDateTime());
            ps.setInt(++index, claimWiseDocumentDto.getClaimNo());
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public void updateByClaimNoAndDocumentTypeIdInPartialLossOld(Connection connection, ClaimWiseDocumentDto claimWiseDocumentDto) throws MisynJDBCException {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_WISE_DOCUMENT_BY_CLAIM_NO_AND_DOC_TYPE_ID_IN_LOSS_TYPE_OLD);
            ps.setString(++index, claimWiseDocumentDto.getIsMandatory());
            ps.setString(++index, claimWiseDocumentDto.getInpUserId());
            ps.setString(++index, claimWiseDocumentDto.getInpDateTime());
            ps.setString(++index, claimWiseDocumentDto.getIsPartialLoss());
            ps.setInt(++index, claimWiseDocumentDto.getClaimNo());
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public void updateDefineDocumentByRefId(Connection connection, ClaimWiseDocumentDto claimWiseDocumentDto) throws MisynJDBCException {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_DEFINE_CLAIM_WISE_DOCUMENT_BY_N_TXN_NO);
            ps.setString(++index, claimWiseDocumentDto.getIsMandatory());
            ps.setInt(++index, claimWiseDocumentDto.getDocReqFrom());
            ps.setString(++index, claimWiseDocumentDto.getInpUserId());
            ps.setString(++index, claimWiseDocumentDto.getInpDateTime());
            ps.setInt(++index, claimWiseDocumentDto.getRefNo());
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public void updateDefineDocumentByDocumentTypeId(Connection connection, ClaimWiseDocumentDto claimWiseDocumentDto) throws MisynJDBCException {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_DEFINE_CLAIM_WISE_DOCUMENT_BY_N_DOC_TYPE_ID);
            ps.setString(++index, claimWiseDocumentDto.getIsMandatory());
            ps.setInt(++index, claimWiseDocumentDto.getDocReqFrom());
            ps.setString(++index, claimWiseDocumentDto.getInpUserId());
            ps.setString(++index, claimWiseDocumentDto.getInpDateTime());
            ps.setInt(++index, claimWiseDocumentDto.getClaimNo());
            ps.setInt(++index, claimWiseDocumentDto.getDocumentTypeId());
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public void updateDocumentUploadStatusByClaimNoAndDocumentTypeId(Connection connection, Integer claimNo,
                                                                     Integer documentTypeId,
                                                                     YesNoWantDecideEnum documentUploadStatus) throws MisynJDBCException {

        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_WISE_DOCUMENT_DOC_UPLOAD_STATUS_BY_CLAIM_NO_AND_DOC_TYPE_ID);
            ps.setString(++index, documentUploadStatus.getYesNoWantDecide());
            ps.setInt(++index, claimNo);
            ps.setInt(++index, documentTypeId);
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public void saveAll(Connection connection, int claimNo, String userId, String inpDateTime) throws MisynJDBCException {
        try (PreparedStatement ps = connection.prepareStatement(INSERT_CLAIM_WISE_DOCUMENT_All)) {
            ps.setInt(1, claimNo);
            ps.setString(2, userId);
            ps.setString(3, inpDateTime);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e.getMessage());
        }
    }

    @Override
    public List<ClaimWiseDocumentDto> searchAllByClaimNo(Connection connection, Integer claimNo) {
        List<ClaimWiseDocumentDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_WISE_DOCUMENT_BY_N_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimWiseDocumentDto claimWiseDocumentDto = this.getClaimWiseDocumentDto(rs);
                claimWiseDocumentDto.setClaimNo(claimNo);
                list.add(claimWiseDocumentDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }


    @Override
    public List<ClaimWiseDocumentDto> searchAllByClaimNoAndIsMandatory(Connection connection, Integer claimNo, String isMandatory) {
        List<ClaimWiseDocumentDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_WISE_DOCUMENT_BY_N_CLAIM_NO_AND_V_IS_MANDATORY);
            ps.setInt(1, claimNo);
            ps.setString(2, isMandatory);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimWiseDocumentDto claimWiseDocumentDto = this.getClaimWiseDocumentDto(rs);
                claimWiseDocumentDto.setClaimNo(claimNo);
                list.add(claimWiseDocumentDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    private ClaimWiseDocumentDto getClaimWiseDocumentDto(ResultSet rs) {
        ClaimWiseDocumentDto claimWiseDocumentDto = new ClaimWiseDocumentDto();
        try {
            claimWiseDocumentDto.setRefNo(rs.getInt("t1.N_TXN_NO"));
            claimWiseDocumentDto.setDocumentTypeId(rs.getInt("t1.N_DOC_TYPE_ID"));
            claimWiseDocumentDto.setIsMandatory(rs.getString("t1.V_IS_MANDATORY"));
            claimWiseDocumentDto.setIsPartialLoss(rs.getString("t1.V_IS_PARTIAL_LOSS"));
            claimWiseDocumentDto.setIsTotLoss(rs.getString("t1.V_IS_TOT_LOSS"));
            claimWiseDocumentDto.setIsLumpSum(rs.getString("t1.V_IS_LUMP_SUM"));
            claimWiseDocumentDto.setIsThirdPartyPropVehicle(rs.getString("t1.V_IS_THIRD_PARTY_PROP_VEHICLE"));
            claimWiseDocumentDto.setIsThirdPartyDeath(rs.getString("t1.V_IS_THIRD_PARTY_DEATH"));
            claimWiseDocumentDto.setIsThirdPartyInjuries(rs.getString("t1.V_IS_THIRD_PARTY_INJURIES"));
            claimWiseDocumentDto.setDocReqFrom(rs.getInt("t1.N_DOC_REQ_FROM"));
            claimWiseDocumentDto.setIsDocUpload(rs.getString("t1.V_IS_DOC_UPLOAD"));
            claimWiseDocumentDto.setRecStatus(rs.getString("t1.V_REC_STATUS"));
            claimWiseDocumentDto.setInpUserId(rs.getString("t1.V_INP_USER_ID"));
            claimWiseDocumentDto.setInpDateTime(rs.getString("t1.D_INP_DATE_TIME"));
            claimWiseDocumentDto.setDocumentTypeName(rs.getString("t3.V_DOC_TYPE_NAME"));
            claimWiseDocumentDto.setReminderDocDisplayName(rs.getString("t3.V_REMIN_DOC_DISPLAY_NAME"));
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        }
        return claimWiseDocumentDto;
    }

    @Override
    public List<ClaimUploadViewDto> getClaimUploadViewDtoList(Connection connection, Integer claimNo) {
        List<ClaimUploadViewDto> claimUploadViewDtoList = new ArrayList<>();
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        ResultSet rs1 = null;
        ResultSet rs2;
        try {
            ps1 = connection.prepareStatement(SELECT_CLAIM_WISE_DOCUMENT_BY_N_CLAIM_NO);
            ps2 = connection.prepareStatement(SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_N_JOB_REF_NO_AND_CLAIM_NO_AND_DOC_TYPE_ID);
            ps1.setInt(1, claimNo);
            rs1 = ps1.executeQuery();
            while (rs1.next()) {
                ClaimUploadViewDto claimUploadViewDto = new ClaimUploadViewDto();
                claimUploadViewDto.setClaimNo(claimNo);
                ClaimDocumentTypeDto claimDocumentTypeDto = new ClaimDocumentTypeDto();
                claimDocumentTypeDto.setDocumentTypeId(rs1.getInt("t1.N_DOC_TYPE_ID"));
                claimDocumentTypeDto.setIsMandatory(rs1.getString("t1.V_IS_MANDATORY"));
                claimDocumentTypeDto.setIsPartialLoss(rs1.getString("t1.V_IS_PARTIAL_LOSS"));
                claimDocumentTypeDto.setIsTotLoss(rs1.getString("t1.V_IS_TOT_LOSS"));
                claimDocumentTypeDto.setIsLumpSum(rs1.getString("t1.V_IS_LUMP_SUM"));
                claimDocumentTypeDto.setIsThirdPartyPropVehicle(rs1.getString("t1.V_IS_THIRD_PARTY_PROP_VEHICLE"));
                claimDocumentTypeDto.setIsThirdPartyDeath(rs1.getString("t1.V_IS_THIRD_PARTY_DEATH"));
                claimDocumentTypeDto.setIsThirdPartyInjuries(rs1.getString("t1.V_IS_THIRD_PARTY_INJURIES"));
                claimDocumentTypeDto.setDocReqFrom(rs1.getInt("t1.N_DOC_REQ_FROM"));
                claimDocumentTypeDto.setDocumentTypeName(rs1.getString("t3.V_DOC_TYPE_NAME"));
                claimUploadViewDto.setClaimDocumentTypeDto(claimDocumentTypeDto);

                ps2.setInt(1, claimNo);
                ps2.setInt(2, claimDocumentTypeDto.getDocumentTypeId());
                rs2 = ps2.executeQuery();

                while (rs2.next()) {
                    ClaimDocumentDto claimDocumentDto = this.getClaimDocumentDto(rs2);
                    claimDocumentDto.setClaimDocumentTypeDto(claimDocumentTypeDto);
                    claimUploadViewDto.getClaimDocumentDtoList().add(claimDocumentDto);
                }
                rs2.close();
                claimUploadViewDtoList.add(claimUploadViewDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs1 != null) {
                    rs1.close();
                }
                if (ps1 != null) {
                    ps1.close();
                }
                if (ps2 != null) {
                    ps2.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return claimUploadViewDtoList;
    }

    @Override
    public List<ClaimUploadViewDto> getClaimUploadViewDtoList(Connection connection, Integer claimNo, String documentType) {
        String SELECT_CLAIM_WISE_DOCUMENT_BY_N_CLAIM_NO_AND_BILL_TYPE = "SELECT\n" +
                "t1.*,\n" +
                "t3.V_DOC_TYPE_NAME," +
                "t3.V_REMIN_DOC_DISPLAY_NAME\n" +
                "FROM\n" +
                "claim_documents_claim_wise AS t1\n" +
                "INNER JOIN claim_document_type AS t3 ON t1.N_DOC_TYPE_ID = t3.N_DOC_TYPE_ID\n" +
                "INNER JOIN claim_bill_check_documet AS t4 ON t1.N_DOC_TYPE_ID = t4.N_DOC_TYPE_ID\n" +
                "WHERE\n" +
                "t1.N_CLAIM_NO = ? AND t4.v_doc_type IN(".concat(documentType).concat(") ") +
                " ORDER BY t1.V_IS_MANDATORY DESC,t4.N_SEQUENCE_ORDER,t3.V_DOC_TYPE_NAME";
        List<ClaimUploadViewDto> claimUploadViewDtoList = new ArrayList<>();
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        ResultSet rs1 = null;
        ResultSet rs2;
        try {
            ps1 = connection.prepareStatement(SELECT_CLAIM_WISE_DOCUMENT_BY_N_CLAIM_NO_AND_BILL_TYPE);
            ps2 = connection.prepareStatement(SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_N_JOB_REF_NO_AND_CLAIM_NO_AND_DOC_TYPE_ID);
            ps1.setInt(1, claimNo);
            rs1 = ps1.executeQuery();
            while (rs1.next()) {
                ClaimUploadViewDto claimUploadViewDto = new ClaimUploadViewDto();
                claimUploadViewDto.setClaimNo(claimNo);
                ClaimDocumentTypeDto claimDocumentTypeDto = new ClaimDocumentTypeDto();
                claimDocumentTypeDto.setDocumentTypeId(rs1.getInt("t1.N_DOC_TYPE_ID"));
                claimDocumentTypeDto.setIsMandatory(rs1.getString("t1.V_IS_MANDATORY"));
                claimDocumentTypeDto.setIsPartialLoss(rs1.getString("t1.V_IS_PARTIAL_LOSS"));
                claimDocumentTypeDto.setIsTotLoss(rs1.getString("t1.V_IS_TOT_LOSS"));
                claimDocumentTypeDto.setIsLumpSum(rs1.getString("t1.V_IS_LUMP_SUM"));
                claimDocumentTypeDto.setIsThirdPartyPropVehicle(rs1.getString("t1.V_IS_THIRD_PARTY_PROP_VEHICLE"));
                claimDocumentTypeDto.setIsThirdPartyDeath(rs1.getString("t1.V_IS_THIRD_PARTY_DEATH"));
                claimDocumentTypeDto.setIsThirdPartyInjuries(rs1.getString("t1.V_IS_THIRD_PARTY_INJURIES"));
                claimDocumentTypeDto.setDocReqFrom(rs1.getInt("t1.N_DOC_REQ_FROM"));
                claimDocumentTypeDto.setDocumentTypeName(rs1.getString("t3.V_DOC_TYPE_NAME"));
                claimUploadViewDto.setClaimDocumentTypeDto(claimDocumentTypeDto);

                ps2.setInt(1, claimNo);
                ps2.setInt(2, claimDocumentTypeDto.getDocumentTypeId());
                rs2 = ps2.executeQuery();

                while (rs2.next()) {
                    ClaimDocumentDto claimDocumentDto = this.getClaimDocumentDto(rs2);
                    claimDocumentDto.setClaimDocumentTypeDto(claimDocumentTypeDto);
                    claimUploadViewDto.getClaimDocumentDtoList().add(claimDocumentDto);
                }
                rs2.close();
                claimUploadViewDtoList.add(claimUploadViewDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs1 != null) {
                    rs1.close();
                }
                if (ps1 != null) {
                    ps1.close();
                }
                if (ps2 != null) {
                    ps2.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return claimUploadViewDtoList;
    }

    @Override
    public List<ClaimUploadViewDto> getClaimUploadViewForDesktopAssesment(Connection connection, Integer claimNo) {
        String SELECT_CLAIM_WISE_DOCUMENT_FOR_DESKTOP_ASSESMENT = "SELECT\n" +
                "t1.*,\n" +
                "t3.V_DOC_TYPE_NAME,\n" +
                "t3.V_REMIN_DOC_DISPLAY_NAME\n" +
                "FROM\n" +
                "claim_documents_claim_wise AS t1\n" +
                "INNER JOIN \n" +
                "claim_document_type AS t3 ON t1.N_DOC_TYPE_ID = t3.N_DOC_TYPE_ID\n" +
                "WHERE \n" +
                "t1.N_CLAIM_NO = ? AND t3.N_DOC_TYPE_ID='3'";
        List<ClaimUploadViewDto> claimUploadViewDtoList = new ArrayList<>();
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        ResultSet rs1 = null;
        ResultSet rs2;
        try {
            ps1 = connection.prepareStatement(SELECT_CLAIM_WISE_DOCUMENT_FOR_DESKTOP_ASSESMENT);
            ps2 = connection.prepareStatement(SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_N_JOB_REF_NO_AND_CLAIM_NO_AND_DOC_TYPE_ID);
            ps1.setInt(1, claimNo);
            rs1 = ps1.executeQuery();
            while (rs1.next()) {
                ClaimUploadViewDto claimUploadViewDto = new ClaimUploadViewDto();
                claimUploadViewDto.setClaimNo(claimNo);
                ClaimDocumentTypeDto claimDocumentTypeDto = new ClaimDocumentTypeDto();
                claimDocumentTypeDto.setDocumentTypeId(rs1.getInt("t1.N_DOC_TYPE_ID"));
                claimDocumentTypeDto.setIsMandatory(rs1.getString("t1.V_IS_MANDATORY"));
                claimDocumentTypeDto.setIsPartialLoss(rs1.getString("t1.V_IS_PARTIAL_LOSS"));
                claimDocumentTypeDto.setIsTotLoss(rs1.getString("t1.V_IS_TOT_LOSS"));
                claimDocumentTypeDto.setIsLumpSum(rs1.getString("t1.V_IS_LUMP_SUM"));
                claimDocumentTypeDto.setIsThirdPartyPropVehicle(rs1.getString("t1.V_IS_THIRD_PARTY_PROP_VEHICLE"));
                claimDocumentTypeDto.setIsThirdPartyDeath(rs1.getString("t1.V_IS_THIRD_PARTY_DEATH"));
                claimDocumentTypeDto.setIsThirdPartyInjuries(rs1.getString("t1.V_IS_THIRD_PARTY_INJURIES"));
                claimDocumentTypeDto.setDocReqFrom(rs1.getInt("t1.N_DOC_REQ_FROM"));
                claimDocumentTypeDto.setDocumentTypeName(rs1.getString("t3.V_DOC_TYPE_NAME"));
                claimUploadViewDto.setClaimDocumentTypeDto(claimDocumentTypeDto);

                ps2.setInt(1, claimNo);
                ps2.setInt(2, claimDocumentTypeDto.getDocumentTypeId());
                rs2 = ps2.executeQuery();

                while (rs2.next()) {
                    ClaimDocumentDto claimDocumentDto = this.getClaimDocumentDto(rs2);
                    claimDocumentDto.setClaimDocumentTypeDto(claimDocumentTypeDto);
                    claimUploadViewDto.getClaimDocumentDtoList().add(claimDocumentDto);
                }
                rs2.close();
                claimUploadViewDtoList.add(claimUploadViewDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs1 != null) {
                    rs1.close();
                }
                if (ps1 != null) {
                    ps1.close();
                }
                if (ps2 != null) {
                    ps2.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return claimUploadViewDtoList;
    }

    @Override
    public List<ClaimWiseDocumentDto> searchAllByClaimNoAndName(Connection connection, Integer claimNo, String docName) {
        List<ClaimWiseDocumentDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_WISE_DOCUMENT_BY_N_CLAIM_NO_CLAIM_NAME);
            ps.setInt(1, claimNo);
            ps.setString(2, "%" + docName + "%");
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimWiseDocumentDto claimWiseDocumentDto = this.getClaimWiseDocumentDto(rs);
                claimWiseDocumentDto.setClaimNo(claimNo);
                list.add(claimWiseDocumentDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<ClaimUploadViewDto> getClaimUploadBranchViewDtoList(Connection connection, Integer claimNo, String searchDoc) {
        List<ClaimUploadViewDto> claimUploadViewDtoList = new ArrayList<>();
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        ResultSet rs1 = null;
        ResultSet rs2;
        try {
            ps1 = connection.prepareStatement(SELECT_CLAIM_WISE_DOCUMENT_AND_BRANCH_WISE_AND_DOC_NAME_BY_N_CLAIM_NO);
            ps2 = connection.prepareStatement(SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_N_JOB_REF_NO_AND_CLAIM_NO_AND_DOC_TYPE_ID);
            ps1.setInt(1, claimNo);
            ps1.setString(2, "%" + searchDoc + "%");
            rs1 = ps1.executeQuery();
            while (rs1.next()) {
                ClaimUploadViewDto claimUploadViewDto = new ClaimUploadViewDto();
                claimUploadViewDto.setClaimNo(claimNo);
                ClaimDocumentTypeDto claimDocumentTypeDto = new ClaimDocumentTypeDto();
                claimDocumentTypeDto.setDocumentTypeId(rs1.getInt("t1.N_DOC_TYPE_ID"));
                claimDocumentTypeDto.setIsMandatory(rs1.getString("t1.V_IS_MANDATORY"));
                claimDocumentTypeDto.setIsPartialLoss(rs1.getString("t1.V_IS_PARTIAL_LOSS"));
                claimDocumentTypeDto.setIsTotLoss(rs1.getString("t1.V_IS_TOT_LOSS"));
                claimDocumentTypeDto.setIsLumpSum(rs1.getString("t1.V_IS_LUMP_SUM"));
                claimDocumentTypeDto.setIsThirdPartyPropVehicle(rs1.getString("t1.V_IS_THIRD_PARTY_PROP_VEHICLE"));
                claimDocumentTypeDto.setIsThirdPartyDeath(rs1.getString("t1.V_IS_THIRD_PARTY_DEATH"));
                claimDocumentTypeDto.setIsThirdPartyInjuries(rs1.getString("t1.V_IS_THIRD_PARTY_INJURIES"));
                claimDocumentTypeDto.setDocReqFrom(rs1.getInt("t1.N_DOC_REQ_FROM"));
                claimDocumentTypeDto.setDocumentTypeName(rs1.getString("t3.V_DOC_TYPE_NAME"));
                claimUploadViewDto.setClaimDocumentTypeDto(claimDocumentTypeDto);

                ps2.setInt(1, claimNo);
                ps2.setInt(2, claimDocumentTypeDto.getDocumentTypeId());
                rs2 = ps2.executeQuery();

                while (rs2.next()) {
                    ClaimDocumentDto claimDocumentDto = this.getClaimDocumentDto(rs2);
                    claimDocumentDto.setClaimDocumentTypeDto(claimDocumentTypeDto);
                    claimUploadViewDto.getClaimDocumentDtoList().add(claimDocumentDto);
                }
                rs2.close();
                claimUploadViewDtoList.add(claimUploadViewDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs1 != null) {
                    rs1.close();
                }
                if (ps1 != null) {
                    ps1.close();
                }
                if (ps2 != null) {
                    ps2.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return claimUploadViewDtoList;
    }

    @Override
    public List<ClaimUploadViewDto> getClaimUploadBranchViewDtoList(Connection connection, Integer claimNo) {
        List<ClaimUploadViewDto> claimUploadViewDtoList = new ArrayList<>();
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        ResultSet rs1 = null;
        ResultSet rs2;
        try {
            ps1 = connection.prepareStatement(SELECT_CLAIM_WISE_DOCUMENT_AND_BRANCH_WISE_BY_N_CLAIM_NO);
            ps2 = connection.prepareStatement(SELECT_CLAIM_UPLOAD_DOCUMENTS_BY_N_JOB_REF_NO_AND_CLAIM_NO_AND_DOC_TYPE_ID);
            ps1.setInt(1, claimNo);
            rs1 = ps1.executeQuery();
            while (rs1.next()) {
                ClaimUploadViewDto claimUploadViewDto = new ClaimUploadViewDto();
                claimUploadViewDto.setClaimNo(claimNo);
                ClaimDocumentTypeDto claimDocumentTypeDto = new ClaimDocumentTypeDto();
                claimDocumentTypeDto.setDocumentTypeId(rs1.getInt("t1.N_DOC_TYPE_ID"));
                claimDocumentTypeDto.setIsMandatory(rs1.getString("t1.V_IS_MANDATORY"));
                claimDocumentTypeDto.setIsPartialLoss(rs1.getString("t1.V_IS_PARTIAL_LOSS"));
                claimDocumentTypeDto.setIsTotLoss(rs1.getString("t1.V_IS_TOT_LOSS"));
                claimDocumentTypeDto.setIsLumpSum(rs1.getString("t1.V_IS_LUMP_SUM"));
                claimDocumentTypeDto.setIsThirdPartyPropVehicle(rs1.getString("t1.V_IS_THIRD_PARTY_PROP_VEHICLE"));
                claimDocumentTypeDto.setIsThirdPartyDeath(rs1.getString("t1.V_IS_THIRD_PARTY_DEATH"));
                claimDocumentTypeDto.setIsThirdPartyInjuries(rs1.getString("t1.V_IS_THIRD_PARTY_INJURIES"));
                claimDocumentTypeDto.setDocReqFrom(rs1.getInt("t1.N_DOC_REQ_FROM"));
                claimDocumentTypeDto.setDocumentTypeName(rs1.getString("t3.V_DOC_TYPE_NAME"));
                claimUploadViewDto.setClaimDocumentTypeDto(claimDocumentTypeDto);

                ps2.setInt(1, claimNo);
                ps2.setInt(2, claimDocumentTypeDto.getDocumentTypeId());
                rs2 = ps2.executeQuery();

                while (rs2.next()) {
                    ClaimDocumentDto claimDocumentDto = this.getClaimDocumentDto(rs2);
                    claimDocumentDto.setClaimDocumentTypeDto(claimDocumentTypeDto);
                    claimUploadViewDto.getClaimDocumentDtoList().add(claimDocumentDto);
                }
                rs2.close();
                claimUploadViewDtoList.add(claimUploadViewDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs1 != null) {
                    rs1.close();
                }
                if (ps1 != null) {
                    ps1.close();
                }
                if (ps2 != null) {
                    ps2.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return claimUploadViewDtoList;
    }

    @Override
    public void updateTotalLossDocumentList(Connection connection, Integer claimNo, String isMandatory) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_WISE_DOCUMENT_TOTAL_LOSS_DOC_LIST);
            ps.setString(++index, isMandatory);
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public void updateDocumentListTypeN(Connection connection, Integer claimNo, String isMandatory) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_WISE_DOCUMENT_TYPE_N_);
            ps.setString(++index, isMandatory);
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public void updateDocumentListTypeNLossTypeOld(Connection connection, Integer claimNo, String isMandatory) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_WISE_DOCUMENT_TYPE_N_LOSS_TYPE_OLD);
            ps.setString(++index, isMandatory);
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public void updatePartialLossDocumentList(Connection connection, Integer claimNo, String isMandatory) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_WISE_DOCUMENT_PARTIAL_LOSS_DOC_LIST);
            ps.setString(++index, isMandatory);
            ps.setInt(++index, claimNo);
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
    }

    @Override
    public boolean isUploadDocumentByClaimNoAndDocTypeId(Connection connection, Integer claimNo, Integer documentTypeId) {
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_SQL_FROM_CLAIM_UPLOAD_DOCUMENT_BY_CLAIM_NO_AND_DOC_TYPE_ID);
            ps.setInt(1, claimNo);
            ps.setInt(2, documentTypeId);
            rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }


    private ClaimDocumentDto getClaimDocumentDto(ResultSet rs) {
        ClaimDocumentDto claimDocumentDto = new ClaimDocumentDto();
        try {
            if (claimDocumentDto.getJobRefNo() != null) {
                claimDocumentDto.setRefNo(rs.getInt("t1.N_REF_NO"));
                claimDocumentDto.setJobRefNo(rs.getInt("t1.N_JOB_REF_NO"));
                claimDocumentDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                claimDocumentDto.setDocumentTypeId(rs.getInt("t1.N_DOC_TYPE_ID"));
                claimDocumentDto.setDepartmentId(rs.getInt("t1.N_DEPARTMENT_ID"));
                claimDocumentDto.setIsMandatory(rs.getString("t1.V_IS_MANDATORY"));
                claimDocumentDto.setDocumentStatus(rs.getString("t1.V_DOC_STATUS"));
                claimDocumentDto.setDocumentPath(rs.getString("t1.V_DOC_PATH"));
                claimDocumentDto.setDocumentName(rs.getString("t1.V_DOC_NAME"));
                claimDocumentDto.setIsDocumentUpload(rs.getString("t1.V_IS_DOC_UPLOAD"));
                claimDocumentDto.setIsCheck(rs.getString("t1.V_IS_CHECK"));
                claimDocumentDto.setCheckUser(rs.getString("t1.V_CHECK_USER"));
                claimDocumentDto.setCheckDateTime(rs.getString("t1.D_CHECK_DATE_TIME"));
                claimDocumentDto.setHoldUser(rs.getString("t1.V_HOLD_USER"));
                claimDocumentDto.setHoldDateTime(rs.getString("t1.D_HOLD_DATE_TIME"));
                claimDocumentDto.setRejectUser(rs.getString("t1.V_REJECT_USER"));
                claimDocumentDto.setRejectDateTime(rs.getString("t1.D_REJECT_DATE_TIME"));
                claimDocumentDto.setBillSummaryRemark(rs.getString("t1.V_BILL_SUMMARY_REMARK"));
                claimDocumentDto.setBillSummaryCheckUser(rs.getString("t1.V_BILL_SUMMARY_CHECK_USER"));
                claimDocumentDto.setBillSummaryCheckDateTime(rs.getString("t1.D_BILL_SUMMARY_CHECK_DATE_TIME"));
                claimDocumentDto.setRemark(rs.getString("t1.V_REMARK"));
                claimDocumentDto.setCancelUser(rs.getString("V_CANCEL_USER"));
                claimDocumentDto.setCancelDateTime(rs.getString("D_CANCEL_DATE_TIME"));
                claimDocumentDto.setCancelRemark(rs.getString("V_CANCEL_REMARK"));
                claimDocumentDto.setInpStat(rs.getString("t1.V_INP_STAT"));
                claimDocumentDto.setInpUser(rs.getString("t1.V_INP_USER"));
                claimDocumentDto.setInpDateTime(Utility.getCustomDateFormat(rs.getString("t1.D_INP_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                claimDocumentDto.setToolTip(this.getToolTip(claimDocumentDto.getDocumentName(), claimDocumentDto.getInpUser(), claimDocumentDto.getInpDateTime()));
                claimDocumentDto.setFileTypeEnum(this.getFileType(claimDocumentDto.getDocumentTypeId()));

            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return claimDocumentDto;
    }

}
