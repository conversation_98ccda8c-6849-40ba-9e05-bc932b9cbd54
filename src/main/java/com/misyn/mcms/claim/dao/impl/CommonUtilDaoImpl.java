package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.CommonUtilDao;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
public class CommonUtilDaoImpl implements CommonUtilDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(CommonUtilDaoImpl.class);

    @Override
    public String findOne(Connection conn, String tblName, String outField, String searchKey) {
        String value = null;
        PreparedStatement ps;
        ResultSet rs;
        String strSQL = "SELECT distinct(" + outField.trim() + ") FROM " + tblName;
        if (!searchKey.trim().equals(AppConstant.STRING_EMPTY)) {
            strSQL = "SELECT distinct(" + outField.trim() + ") FROM " + tblName + " WHERE " + searchKey;
        }
        try {
            ps = conn.prepareStatement(strSQL, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            rs = ps.executeQuery();
            if (rs.next()) {
                value = rs.getString(outField);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return value;
    }
}
