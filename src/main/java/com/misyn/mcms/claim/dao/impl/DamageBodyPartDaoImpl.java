package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.DamageBodyPartDao;
import com.misyn.mcms.claim.dto.ClaimsDto;
import com.misyn.mcms.claim.dto.DamageBodyPartDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.LinkedList;
import java.util.List;
public class DamageBodyPartDaoImpl implements DamageBodyPartDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(DamageBodyPartDaoImpl.class);

    @Override
    public int insertDamageBodyPartsList(Connection connection, List<DamageBodyPartDto> damageBodyPartsList, ClaimsDto claimsDto) throws Exception {
        int result = 0;
        PreparedStatement ps = null;
        try {
            for (DamageBodyPartDto damageBodyPartDto : damageBodyPartsList) {
                ps = connection.prepareStatement(INSERT_CLAIM_DAMAGE_PART_MAIN);
                if (damageBodyPartDto.isBodyPartsCheck()) {
                    damageBodyPartDto.setClaimNo(claimsDto.getClaimNo());
                    ps.setInt(1, damageBodyPartDto.getClaimNo());
                    ps.setInt(2, damageBodyPartDto.getVehClsId());
                    ps.setString(3, damageBodyPartDto.getPartCode());
                    ps.setString(4, damageBodyPartDto.getDamegType());
                    ps.setString(5, damageBodyPartDto.getOtherText());
                    result = result + ps.executeUpdate();
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return result;
    }

    @Override
    public void deleteDamageBodyPartsList(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(DELETE_CLAIM_DAMAGE_PART_MAIN);
            ps.setInt(1, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public List<DamageBodyPartDto> getDamageBodyPartDtoList(Connection connection, Integer claimNo, Integer vehClsId) throws Exception {
        List<DamageBodyPartDto> damageBodyPartList = new LinkedList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_DAMAGE_PART_MAIN);
            ps.setInt(1, claimNo);
            ps.setInt(2, vehClsId);
            rs = ps.executeQuery();
            while (rs.next()) {
                DamageBodyPartDto damageBodyPartDto = new DamageBodyPartDto();
                damageBodyPartDto.setTxnId(rs.getInt("n_txn_id"));
                damageBodyPartDto.setVehClsId(rs.getInt("n_veh_cls_id"));
                damageBodyPartDto.setPartCode(rs.getString("v_part_code"));
                damageBodyPartDto.setPartName(rs.getString("v_part_name"));
                damageBodyPartDto.setPartDesc(rs.getString("v_part_desc"));

                damageBodyPartDto.setDamegType(rs.getString("v_dameg_type"));
                damageBodyPartDto.setOtherText(rs.getString("V_OTHER_TEXT"));
                damageBodyPartDto.setClaimNo(rs.getInt("n_clim_no"));

                if (damageBodyPartDto.getClaimNo() > 0) {
                    damageBodyPartDto.setRecFoundMainTable(true);
                }
                damageBodyPartList.add(damageBodyPartDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
        }
        return damageBodyPartList;
    }
}
