package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.DistrictDao;
import com.misyn.mcms.claim.dto.DistrictDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class DistrictDaoImpl extends BaseAbstract<DistrictDaoImpl> implements DistrictDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(DistrictDaoImpl.class);

    @Override
    public DistrictDto insertMaster(Connection connection, DistrictDto districtDto) throws Exception {
        return null;
    }

    @Override
    public DistrictDto insertTemporary(Connection connection, DistrictDto districtDto) throws Exception {
        return null;
    }

    @Override
    public DistrictDto insertHistory(Connection connection, DistrictDto districtDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public DistrictDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement(SELECT_ALL_FROM_DISTRICT_MASTER_BY_DITRICT_CODE);
            ps.setObject(1, id);

            rs = ps.executeQuery();
            if (rs.next()) {
                DistrictDto districtDto = getDistrictDto(rs);
                return districtDto;

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            ps.close();
        }
        return null;
    }

    @Override
    public DistrictDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<DistrictDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<DistrictDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_FROM_DISTRICT_MASTER);

            rs = ps.executeQuery();
            while (rs.next()) {
                DistrictDto districtDto = getDistrictDto(rs);
                list.add(districtDto);

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return list;
    }

    @Override
    public DistrictDto updateMaster(Connection connection, DistrictDto districtDto) throws Exception {
        return null;
    }

    @Override
    public DistrictDto updateTemporary(Connection connection, DistrictDto districtDto) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    private DistrictDto getDistrictDto(ResultSet rst) {
        DistrictDto districtDto = new DistrictDto();
        try {
            districtDto.setProvinceCode(rst.getString("V_PROVINCE_ID"));
            districtDto.setDistrictCode(rst.getString("V_DISTRICT_CODE"));
            districtDto.setDistrictName(rst.getString("V_DISTRICT_NAME"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return districtDto;
    }

    @Override
    public DistrictDto searchMasterId(Connection connection, Object id) throws Exception {
        try {
            PreparedStatement ps = null;
            ResultSet rs = null;


            ps = connection.prepareStatement(SELECT_ALL_FROM_DISTRICT_MASTER_BY_DITRICT_CODE_BY_ID);
            ps.setObject(1, id);

            rs = ps.executeQuery();
            if (rs.next()) {
                DistrictDto districtDto = getDistrictDto(rs);
                return districtDto;

            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }
}
