package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.EmailDao;
import com.misyn.mcms.claim.dto.MessageContentDetails;
import com.misyn.mcms.utility.Email;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
public class EmailDaoImpl implements EmailDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(EmailDaoImpl.class);

    @Override
    public Email insertEmail(Connection connection, Email email) throws Exception {

        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_INTO_EMAIL, Statement.RETURN_GENERATED_KEYS);
            ps.setString(++index, email.getEmailMassege());
            ps.setString(++index, email.getCcAddresses());
            ps.setString(++index, email.getFromAddress());
            ps.setString(++index, email.getJmsAttachment());
            ps.setInt(++index, email.getJmsRetryAttempts());
            ps.setString(++index, email.getJmsSentTime());
            ps.setString(++index, email.getMailAttachment());
            ps.setInt(++index, email.getMailRetryAttempts());
            ps.setString(++index, email.getMailSentTime());
            ps.setString(++index, email.getStatus());
            ps.setString(++index, email.getSubject());
            ps.setString(++index, email.getToAddresses());
            ps.setInt(++index, email.getUpId());

            if (ps.executeUpdate() > 0) {

                ResultSet rsKeys = ps.getGeneratedKeys();
                if (rsKeys.next()) {
                    int autoGeneratedId = rsKeys.getInt(1);
                    email.setSaveId(autoGeneratedId);
                    return email;
                }
                rsKeys.close();


            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public void updateEmail(Connection connection, Email email) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_MAIL_STAUS, Statement.RETURN_GENERATED_KEYS);
            ps.setString(++index, email.getStatus());
            ps.setInt(++index, email.getSaveId());
            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }

    }

    @Override
    public MessageContentDetails searchMessageContentDetail(Connection connection, Integer id) {
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            ps = connection.prepareStatement(SQL_SELECT_ALL_FROM_MESSAGE_CONTENT_DETAILS);
            ps.setInt(1, id);

            rs = ps.executeQuery();
            if (rs.next()) {
                MessageContentDetails messageContentDetails = new MessageContentDetails();
                messageContentDetails.setMessageBody(rs.getString("message_body"));
                messageContentDetails.setStatus(rs.getString("status"));
                messageContentDetails.setSubject(rs.getString("subject"));
                return messageContentDetails;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public List<String> getFinanceDepartmentMailList(Connection connection) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<>();

        try {
            ps = connection.prepareStatement(SQL_SELECT_ALL_FROM_FINANCE_DEPARTMENT);


            rs = ps.executeQuery();
            while (rs.next()) {
                list.add(rs.getString("email_address"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }
}
