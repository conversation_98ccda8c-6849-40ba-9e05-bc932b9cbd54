package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.FormFieldDao;
import com.misyn.mcms.claim.dto.FormFieldDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class FormFieldDaoImpl extends AbstractBaseDao<FormFieldDaoImpl> implements FormFieldDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(FormFieldDaoImpl.class);

    @Override
    public FormFieldDto insertMaster(Connection connection, FormFieldDto formFieldDto) throws Exception {
        return null;
    }

    @Override
    public FormFieldDto updateMaster(Connection connection, FormFieldDto formFieldDto) throws Exception {
        return null;
    }

    @Override
    public FormFieldDto insertTemporary(Connection connection, FormFieldDto formFieldDto) throws Exception {
        return null;
    }

    @Override
    public FormFieldDto updateTemporary(Connection connection, FormFieldDto formFieldDto) throws Exception {
        return null;
    }

    @Override
    public FormFieldDto insertHistory(Connection connection, FormFieldDto formFieldDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public FormFieldDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public FormFieldDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<FormFieldDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public List<FormFieldDto> getFormRelatedFields(Connection connection, FormFieldDto formFieldDto) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<FormFieldDto> list = new ArrayList<>();

        try {
            ps = connection.prepareStatement(SQL_SEARCH_ALL_FORM_RELATED_FIELDS);
            ps.setInt(1, formFieldDto.getFormNameId());
            ps.setString(2, formFieldDto.getFieldRequired());

            rs = ps.executeQuery();
            while (rs.next()) {
                FormFieldDto dto = new FormFieldDto();
                dto.setId(rs.getInt("id"));
                dto.setFormNameId(rs.getInt("form_name_id"));
                dto.setDtoFieldName(rs.getString("dto_field_name"));
                dto.setFormFieldName(rs.getString("form_field_name"));
                dto.setFieldRequired(rs.getString("field_required"));
                list.add(dto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
        }
        return list;

    }

    @Override
    public FormFieldDto getDtoFieldRelatedField(Connection connection, FormFieldDto formFieldDto) throws Exception {
        FormFieldDto dto = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SQL_SEARCH_FIELD_USING_DTO_FIELD);
            ps.setInt(1, formFieldDto.getFormNameId());
            ps.setString(2, formFieldDto.getDtoFieldName());
            rs = ps.executeQuery();
            if (rs.next()) {
                dto = new FormFieldDto();
                dto.setId(rs.getInt("id"));
                dto.setFormNameId(rs.getInt("form_name_id"));
                dto.setDtoFieldName(rs.getString("dto_field_name"));
                dto.setFormFieldName(rs.getString("form_field_name"));
                dto.setFieldRequired(rs.getString("field_required"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
        }
        return dto;
    }
}
