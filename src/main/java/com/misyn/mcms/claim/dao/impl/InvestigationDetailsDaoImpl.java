package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.InvestigationDetailsDao;
import com.misyn.mcms.claim.dto.InvestigationDetailsDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
public class InvestigationDetailsDaoImpl implements InvestigationDetailsDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(InvestigationDetailsDaoImpl.class);

    @Override
    public InvestigationDetailsDto insertMaster(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(INSERT_INVESTIGATION_DETAILS, Statement.RETURN_GENERATED_KEYS)) {
            Integer investigationJobNo = getSequenceInvestigationJobNo(connection);
            investigationDetailsDto.setInvestJobNo("INV-" + Utility.addZeroRJ(String.valueOf(investigationJobNo), 7));
            ps.setString(++index, investigationDetailsDto.getInvestJobNo());
            ps.setInt(++index, investigationDetailsDto.getClaimNo());
            ps.setString(++index, investigationDetailsDto.getInvestigationStatus());
            ps.setString(++index, investigationDetailsDto.getIsAccident());
            ps.setString(++index, investigationDetailsDto.getIsTheft());
            ps.setString(++index, investigationDetailsDto.getIsFire());
            ps.setInt(++index, investigationDetailsDto.getAssignInvestigatorUserRefId());
            ps.setString(++index, investigationDetailsDto.getInvestArrangeUser());
            ps.setString(++index, investigationDetailsDto.getInvestArrangeDateTime());
            ps.setString(++index, investigationDetailsDto.getInvestCompletedUser());
            ps.setString(++index, investigationDetailsDto.getInvestCompletedDateTime());
            ps.setString(++index, investigationDetailsDto.getIsFirstStatement());
            ps.setString(++index, investigationDetailsDto.getIsDl());
            ps.setString(++index, investigationDetailsDto.getIsClaimForm());
            ps.setString(++index, investigationDetailsDto.getIsPhotos());
            ps.setString(++index, investigationDetailsDto.getReason());
            ps.setBigDecimal(++index, investigationDetailsDto.getProfFee());
            ps.setBigDecimal(++index, investigationDetailsDto.getTravelFee());
            ps.setBigDecimal(++index, investigationDetailsDto.getOtherFee());
            ps.setBigDecimal(++index, investigationDetailsDto.getTotalFee());
            ps.setString(++index, investigationDetailsDto.getPaymentStatus());
            ps.setString(++index, investigationDetailsDto.getPaymentInputUser());
            ps.setString(++index, investigationDetailsDto.getPaymentInputDateTime());
            ps.setString(++index, investigationDetailsDto.getPaymentAprvUser());
            ps.setString(++index, investigationDetailsDto.getPaymentInputDateTime());

            ps.setString(++index, investigationDetailsDto.getInvestReqUser());
            ps.setString(++index, investigationDetailsDto.getInvestReqDateTime());
            ps.setString(++index, investigationDetailsDto.getInvestReqAprvdUser());
            ps.setString(++index, investigationDetailsDto.getInvestReqAprvdDateTime());
            if (ps.executeUpdate() > 0) {
                ResultSet rsKeys = ps.getGeneratedKeys();
                if (rsKeys.next()) {
                    int autoGeneratedId = rsKeys.getInt(1);
                    investigationDetailsDto.setInvestTxnNo(autoGeneratedId);
                    this.updateSequenceInvestigationJobNo(connection, investigationJobNo);
                    return investigationDetailsDto;
                }
                rsKeys.close();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return null;
    }

    @Override
    public InvestigationDetailsDto updateMaster(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_INVESTIGATION_DETAILS)) {


            ps.setString(++index, investigationDetailsDto.getInvestigationStatus());
            ps.setString(++index, investigationDetailsDto.getIsAccident());
            ps.setString(++index, investigationDetailsDto.getIsTheft());
            ps.setString(++index, investigationDetailsDto.getIsFire());
            ps.setInt(++index, investigationDetailsDto.getAssignInvestigatorUserRefId());
            ps.setString(++index, investigationDetailsDto.getInvestArrangeUser());
            ps.setString(++index, investigationDetailsDto.getInvestArrangeDateTime());
            ps.setString(++index, investigationDetailsDto.getIsFirstStatement());
            ps.setString(++index, investigationDetailsDto.getIsDl());
            ps.setString(++index, investigationDetailsDto.getIsClaimForm());
            ps.setString(++index, investigationDetailsDto.getIsPhotos());
            ps.setString(++index, investigationDetailsDto.getReason());
            ps.setInt(++index, investigationDetailsDto.getInvestTxnNo());

            if (ps.executeUpdate() > 0) {
                return investigationDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        }
        return null;

    }

    @Override
    public InvestigationDetailsDto insertTemporary(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception {
        return null;
    }

    @Override
    public InvestigationDetailsDto updateTemporary(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception {
        return null;
    }

    @Override
    public InvestigationDetailsDto insertHistory(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public InvestigationDetailsDto searchMaster(Connection connection, Object id) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(SEARCH_INVESTIGATION_DETAILS)) {
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return getInvestigationDetails(rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public InvestigationDetailsDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<InvestigationDetailsDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public List<InvestigationDetailsDto> searchAll(Connection connection, Integer claimNo) throws Exception {
        List<InvestigationDetailsDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SEARCH_INVESTIGATION_DETAILS_USING_CLAIM_NUMBER);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                InvestigationDetailsDto investigationDetailsDto = new InvestigationDetailsDto();
                investigationDetailsDto.setInvestTxnNo(rs.getInt("t1.N_INVEST_TXN_NO"));
                investigationDetailsDto.setInvestJobNo(rs.getString("t1.V_INVEST_JOB_NO"));
                investigationDetailsDto.setClaimNo(rs.getInt("t1.N_CLAIM_NO"));
                investigationDetailsDto.setInvestigationStatus(rs.getString("t1.V_INVESTIGATION_STATUS"));
                investigationDetailsDto.setIsAccident(rs.getString("t1.V_IS_ACCIDENT"));
                investigationDetailsDto.setIsTheft(rs.getString("t1.V_IS_THEFT"));
                investigationDetailsDto.setIsFire(rs.getString("t1.V_IS_FIRE"));
                investigationDetailsDto.setAssignInvestigatorUserRefId(rs.getInt("t1.N_ASSIGN_INVESTIGATOR_USER_REF_ID"));
                investigationDetailsDto.setAssignInvestigatorName(rs.getString("t2.V_NAME"));
                investigationDetailsDto.setInvestArrangeUser(rs.getString("t1.V_INVEST_ARRANGE_USER"));
                investigationDetailsDto.setInvestCompletedUser(rs.getString("t1.V_INVEST_COMPLETED_USER"));
                investigationDetailsDto.setIsFirstStatement(rs.getString("t1.V_IS_FIRST_STATEMENT"));
                investigationDetailsDto.setIsDl(rs.getString("t1.V_IS_DL"));
                investigationDetailsDto.setIsClaimForm(rs.getString("t1.V_IS_CLAIM_FORM"));
                investigationDetailsDto.setIsPhotos(rs.getString("t1.V_IS_PHOTOS"));
                investigationDetailsDto.setPaymentStatus(rs.getString("t1.V_PAYMENT_STATUS"));

                investigationDetailsDto.setInvestArrangeDateTime(Utility.getCustomDateFormat(rs.getString("t1.D_INVEST_ARRANGE_DATE_TIME"), AppConstant.DATE_TIME_FORMAT, AppConstant.DATE_TIME_FORMAT));
                investigationDetailsDto.setInvestCompletedDateTime(Utility.getCustomDateFormat(rs.getString("t1.D_INVEST_COMPLETED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT, AppConstant.DATE_TIME_FORMAT));
                list.add(investigationDetailsDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public Integer getMaxInvestigationTxnIdByClaimNo(Connection connection, Integer claimNo) {
        Integer maxId = 0;
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(GET_MAX_INVESTIGATION_TXN_ID_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                maxId = rs.getInt("maxTxnId");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return maxId;
    }

    @Override
    public InvestigationDetailsDto updateInvestigationCompleteDetails(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_INVESTIGATION_COMPLETE)) {


            ps.setString(++index, investigationDetailsDto.getInvestigationStatus());
            ps.setString(++index, investigationDetailsDto.getInvestCompletedUser());
            ps.setString(++index, investigationDetailsDto.getInvestCompletedDateTime());
            ps.setBigDecimal(++index, investigationDetailsDto.getProfFee());
            ps.setBigDecimal(++index, investigationDetailsDto.getTravelFee());
            ps.setBigDecimal(++index, investigationDetailsDto.getOtherFee());
            ps.setBigDecimal(++index, investigationDetailsDto.getTotalFee());
            ps.setString(++index, investigationDetailsDto.getPaymentStatus());
            ps.setString(++index, investigationDetailsDto.getPaymentInputUser());
            ps.setString(++index, investigationDetailsDto.getPaymentInputDateTime());
            ps.setString(++index, investigationDetailsDto.getPaymentAprvUser());
            ps.setString(++index, investigationDetailsDto.getPaymentAprvDateTime());
            ps.setInt(++index, investigationDetailsDto.getInvestTxnNo());

            if (ps.executeUpdate() > 0) {
                return investigationDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        }
        return null;
    }

    @Override
    public InvestigationDetailsDto updateInvestigationPaymentDetails(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_INVESTIGATION_PAYMENT)) {


            ps.setBigDecimal(++index, investigationDetailsDto.getProfFee());
            ps.setBigDecimal(++index, investigationDetailsDto.getTravelFee());
            ps.setBigDecimal(++index, investigationDetailsDto.getOtherFee());
            ps.setBigDecimal(++index, investigationDetailsDto.getTotalFee());
            ps.setString(++index, investigationDetailsDto.getPaymentStatus());
            ps.setString(++index, investigationDetailsDto.getPaymentInputUser());
            ps.setString(++index, investigationDetailsDto.getPaymentInputDateTime());
            ps.setString(++index, investigationDetailsDto.getPaymentAprvUser());
            ps.setString(++index, investigationDetailsDto.getPaymentAprvDateTime());
            ps.setInt(++index, investigationDetailsDto.getInvestTxnNo());

            if (ps.executeUpdate() > 0) {
                return investigationDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        }
        return null;
    }

    @Override
    public InvestigationDetailsDto updateInvestigationArrangedDetails(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_INVESTIGATION_STATUS)) {
            ps.setString(++index, investigationDetailsDto.getInvestigationStatus());
            ps.setInt(++index, investigationDetailsDto.getInvestTxnNo());
            if (ps.executeUpdate() > 0) {
                return investigationDetailsDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return null;
    }

    @Override
    public InvestigationDetailsDto updateInvestigationStatus(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_INVESTIGATION_STATUS)) {
            ps.setString(++index, investigationDetailsDto.getInvestigationStatus());
            ps.setInt(++index, investigationDetailsDto.getInvestTxnNo());
            if (ps.executeUpdate() > 0) {
                return investigationDetailsDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return null;
    }

    @Override
    public InvestigationDetailsDto updateInvestigationPaymentStatus(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_INVESTIGATION_PAYMENT_STATUS)) {
            ps.setString(++index, investigationDetailsDto.getPaymentStatus());
            ps.setInt(++index, investigationDetailsDto.getInvestTxnNo());
            if (ps.executeUpdate() > 0) {
                return investigationDetailsDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return null;
    }


    @Override
    public InvestigationDetailsDto updateInvestigationRequestApprove(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_INVESTIGATION_REQUEST_APPROVED)) {
            ps.setString(++index, investigationDetailsDto.getInvestigationStatus());
            ps.setString(++index, investigationDetailsDto.getInvestReqAprvdUser());
            ps.setString(++index, investigationDetailsDto.getInvestReqAprvdDateTime());
            ps.setInt(++index, investigationDetailsDto.getInvestTxnNo());
            if (ps.executeUpdate() > 0) {
                return investigationDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        }
        return null;
    }

    @Override
    public InvestigationDetailsDto updateInvestigationCancelDetails(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_INVESTIGATION_CANCEL)) {

            ps.setString(++index, investigationDetailsDto.getInvestigationStatus());
            ps.setString(++index, investigationDetailsDto.getInvestArrangeUser());
            ps.setString(++index, investigationDetailsDto.getInvestArrangeDateTime());
            ps.setInt(++index, investigationDetailsDto.getInvestTxnNo());
            if (ps.executeUpdate() > 0) {
                return investigationDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        }
        return null;
    }

    @Override
    public InvestigationDetailsDto updateInvestigationAssignUser(Connection connection, InvestigationDetailsDto investigationDetailsDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_INVESTIGATION_ASSIGN_USER)) {

            ps.setString(++index, investigationDetailsDto.getInvestReqUser());
            ps.setString(++index, investigationDetailsDto.getInvestReqDateTime());
            ps.setInt(++index, investigationDetailsDto.getInvestTxnNo());
            if (ps.executeUpdate() > 0) {
                return investigationDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        }
        return null;
    }

    @Override
    public List<InvestigationDetailsDto> getInvestigationListStillPendingAfterFiveDays(Connection connection, String date) throws Exception {
        List<InvestigationDetailsDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_INVESTIGATION_DETAILS_NOT_IN_COMPLETE_OR_CANCEL_BY_ARRANGE_DATE);
            ps.setString(1, date);
            rs = ps.executeQuery();
            while (rs.next()) {
                InvestigationDetailsDto investigationDetailsDto = new InvestigationDetailsDto();
                investigationDetailsDto.setInvestJobNo(rs.getString("V_INVEST_JOB_NO"));
                investigationDetailsDto.setClaimNo(rs.getInt("N_CLAIM_NO"));
                investigationDetailsDto.setInvestigationStatus(rs.getString("V_INVESTIGATION_STATUS"));
                investigationDetailsDto.setInvestArrangeUser(rs.getString("V_INVEST_ARRANGE_USER"));
                investigationDetailsDto.setInvestArrangeDateTime(Utility.getCustomDateFormat(rs.getString("D_INVEST_ARRANGE_DATE_TIME"), AppConstant.DATE_TIME_FORMAT, AppConstant.DATE_TIME_FORMAT));
                list.add(investigationDetailsDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    private InvestigationDetailsDto getInvestigationDetails(ResultSet rs) {
        InvestigationDetailsDto investigationDetailsDto = new InvestigationDetailsDto();
        try {
            investigationDetailsDto.setInvestTxnNo(rs.getInt("t1.N_INVEST_TXN_NO"));
            investigationDetailsDto.setInvestJobNo(rs.getString("t1.V_INVEST_JOB_NO"));
            investigationDetailsDto.setClaimNo(rs.getInt("t1.N_CLAIM_NO"));
            investigationDetailsDto.setInvestigationStatus(rs.getString("t1.V_INVESTIGATION_STATUS"));
            investigationDetailsDto.setIsAccident(rs.getString("t1.V_IS_ACCIDENT"));
            investigationDetailsDto.setIsTheft(rs.getString("t1.V_IS_THEFT"));
            investigationDetailsDto.setIsFire(rs.getString("t1.V_IS_FIRE"));
            investigationDetailsDto.setAssignInvestigatorUserRefId(rs.getInt("t1.N_ASSIGN_INVESTIGATOR_USER_REF_ID"));
            investigationDetailsDto.setAssignInvestigatorName(rs.getString("t2.V_NAME"));
            investigationDetailsDto.setInvestArrangeUser(rs.getString("t1.V_INVEST_ARRANGE_USER"));
            investigationDetailsDto.setInvestArrangeDateTime(Utility.getCustomDateFormat(rs.getString("t1.D_INVEST_ARRANGE_DATE_TIME"), AppConstant.DATE_TIME_FORMAT, AppConstant.DATE_TIME_FORMAT));
            investigationDetailsDto.setInvestCompletedUser(rs.getString("t1.V_INVEST_COMPLETED_USER"));
            investigationDetailsDto.setInvestCompletedDateTime(Utility.getCustomDateFormat(rs.getString("t1.D_INVEST_COMPLETED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT, AppConstant.DATE_TIME_FORMAT));
            investigationDetailsDto.setIsFirstStatement(rs.getString("t1.V_IS_FIRST_STATEMENT"));
            investigationDetailsDto.setIsDl(rs.getString("t1.V_IS_DL"));
            investigationDetailsDto.setIsClaimForm(rs.getString("t1.V_IS_CLAIM_FORM"));
            investigationDetailsDto.setIsPhotos(rs.getString("t1.V_IS_PHOTOS"));
            investigationDetailsDto.setProfFee(rs.getBigDecimal("t1.N_PROF_FEE"));
            investigationDetailsDto.setTravelFee(rs.getBigDecimal("t1.N_TRAVEL_FEE"));
            investigationDetailsDto.setOtherFee(rs.getBigDecimal("t1.N_OTHER_FEE"));
            investigationDetailsDto.setTotalFee(rs.getBigDecimal("t1.N_TOTAL_FEE"));
            investigationDetailsDto.setPaymentStatus(rs.getString("t1.V_PAYMENT_STATUS"));
            investigationDetailsDto.setPaymentInputUser(rs.getString("t1.V_PAYMENT_INPUT_USER"));
            investigationDetailsDto.setPaymentInputDateTime(rs.getString("t1.D_PAYMENT_INPUT_DATE_TIME"));
            investigationDetailsDto.setPaymentAprvUser(rs.getString("t1.V_PAYMENT_APRV_USER"));
            investigationDetailsDto.setPaymentAprvDateTime(rs.getString("t1.D_PAYMENT_APRV_DATE_TIME"));
            investigationDetailsDto.setReason(rs.getString("t1.V_REASON"));

            investigationDetailsDto.setInvestReqUser(rs.getString("t1.V_INVEST_REQ_USER"));
            investigationDetailsDto.setInvestReqDateTime(rs.getString("t1.D_INVEST_REQ_DATE_TIME"));
            investigationDetailsDto.setInvestReqAprvdUser(rs.getString("t1.V_INVEST_REQ_APRVD_USER"));
            investigationDetailsDto.setInvestReqAprvdDateTime(rs.getString("t1.D_INVEST_REQ_APRVD_DATE_TIME"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return investigationDetailsDto;
    }

    private synchronized Integer getSequenceInvestigationJobNo(Connection conn) {
        PreparedStatement ps = null;
        Integer sequenceId = 0;
        try {
            ps = conn.prepareStatement(SELECT_CLAIM_INVESTIGATION_SEQUENCE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    sequenceId = rs.getInt("invest_job_no");
                    sequenceId++;
                }
                rs.close();
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return sequenceId;
    }

    private synchronized void updateSequenceInvestigationJobNo(Connection connection, Integer sequenceId) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_INVESTIGATION_SEQUENCE);
            ps.setInt(1, sequenceId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
    }
}
