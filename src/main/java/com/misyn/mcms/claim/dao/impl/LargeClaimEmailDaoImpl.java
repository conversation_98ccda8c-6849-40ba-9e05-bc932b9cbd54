package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.LargeClaimEmailDao;
import com.misyn.mcms.claim.dto.LargeClaimEmailHistoryDto;
import com.misyn.mcms.claim.dto.LargeClaimEmailReceiversDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class LargeClaimEmailDaoImpl extends AbstractBaseDao<LargeClaimEmailDaoImpl> implements LargeClaimEmailDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(LargeClaimEmailDaoImpl.class);

    @Override
    public BigDecimal getAuthorityLimits(Connection connection, Integer accessUserType) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(GET_AUTHORITY_LIMITS);
            ps.setInt(1, accessUserType);
            rs = ps.executeQuery();
            while (rs.next()) {
                return BigDecimal.valueOf(rs.getDouble("N_LIMIT"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return null;
    }

    @Override
    public List<LargeClaimEmailReceiversDto> getAutomatedMailReceivers(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<LargeClaimEmailReceiversDto> emails = new ArrayList<>();
        try {
            ps = connection.prepareStatement(GET_MAIL_ADDRESSESS);
            rs = ps.executeQuery();
            while (rs.next()) {
                LargeClaimEmailReceiversDto receiversDto = new LargeClaimEmailReceiversDto();
                receiversDto.setId(rs.getInt("N_ID"));
                receiversDto.setEmail(rs.getString("V_EMAIL_ADDRESS"));
                emails.add(receiversDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return emails;
    }

    @Override
    public boolean isMailAlreadySent(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        boolean isMailSent = false;
        try {
            ps = connection.prepareStatement(IS_MAIL_SENT);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                isMailSent = true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return isMailSent;
    }

    @Override
    public List<LargeClaimEmailHistoryDto> getHistoryRecords(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<LargeClaimEmailHistoryDto> historyDtos = new ArrayList<>();
        try {
            ps = connection.prepareStatement(GET_HISTORY_RECORDS);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                LargeClaimEmailHistoryDto emailHistoryDto = new LargeClaimEmailHistoryDto();
                emailHistoryDto.setId(rs.getInt("N_ID"));
                emailHistoryDto.setClaimNo(rs.getInt("N_CLAIM_NO"));
                emailHistoryDto.setAcr(rs.getBigDecimal("N_TOT_APRV_ACR"));
                emailHistoryDto.setApprovedUser(rs.getString("V_APRV_USER"));
                emailHistoryDto.setApprovedDate(Utility.getDate(rs.getString("V_APRV_DATE_TIME"), AppConstant.DATE_FORMAT));
                emailHistoryDto.setTotalPayable(rs.getBigDecimal("N_PAYABLE_AMOUNT"));
                emailHistoryDto.setCalsheetType(rs.getString("V_CALSHEET_TYPE"));
                historyDtos.add(emailHistoryDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return historyDtos.size() > 0 ? historyDtos : null;
    }

    @Override
    public void saveHistory(Connection connection, LargeClaimEmailHistoryDto emailHistoryDto) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SAVE_HISTORY);
            ps.setInt(1, emailHistoryDto.getClaimNo());
            ps.setBigDecimal(2, emailHistoryDto.getAcr());
            ps.setString(3, emailHistoryDto.getApprovedUser());
            ps.setString(4, emailHistoryDto.getApprovedDate());
            ps.setBigDecimal(5, emailHistoryDto.getTotalPayable());
            ps.setString(6, emailHistoryDto.getCalsheetType());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }
}
