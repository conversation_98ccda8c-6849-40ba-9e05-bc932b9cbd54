package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.MainPanelAssignHstDao;
import com.misyn.mcms.claim.dto.PanelDecisionDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class MainPanelAssignHstDaoImpl implements MainPanelAssignHstDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(MainPanelAssignHstDaoImpl.class);
    @Override
    public void shiftFromPanel(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(SHIFT_HISTORY_RECORD);
            ps.setInt(1, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<PanelDecisionDto> getPanelDecisionHistory(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        PreparedStatement ps1;
        ResultSet rs;
        ResultSet rs1;
        List<PanelDecisionDto> panelDecisionDtos = new ArrayList<>();
        int index = 0;
        try {
            ps = connection.prepareStatement(GET_PANEL_DECISION_HISTORY);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                PanelDecisionDto panelDecisionDto = new PanelDecisionDto();
                panelDecisionDto.setUserName(rs.getString("V_USER_ID"));
                panelDecisionDto.setAssignDateTime(Utility.getDate(rs.getString("D_INPUT_DATETIME"), AppConstant.DATE_TIME_FORMAT));
                panelDecisionDto.setDecision(rs.getString("V_STATUS"));
                panelDecisionDto.setDecisionDateTime(null == rs.getString("D_UPDATED_DATE_TIME") ? "N/A" : Utility.getDate(rs.getString("D_UPDATED_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                panelDecisionDto.setComment(null == rs.getString("V_REMARK") ? AppConstant.STRING_EMPTY : rs.getString("V_REMARK"));
                panelDecisionDto.setHighlightColor(rs.getInt("N_FORWARD_INDEX") % 2 == 0 ? "lightgray" : "aliceblue");
                if (index != rs.getInt("N_FORWARD_INDEX")) {
                    ++index;
                    panelDecisionDto.setDmRemark(null == rs.getString("V_DM_REMARK") ? "N/A": rs.getString("V_DM_REMARK"));
                    ps1 = connection.prepareStatement(GET_PANEL_DATE);
                    ps1.setInt(1, claimNo);
                    ps1.setInt(2, index);
                    rs1 = ps1.executeQuery();
                    if (rs1.next()) {
                        panelDecisionDto.setPanelDate(rs1.getString("PANEL_DATE"));
                    }
                }
                panelDecisionDtos.add(panelDecisionDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return panelDecisionDtos;
    }

}
