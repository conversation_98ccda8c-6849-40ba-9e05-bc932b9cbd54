package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.McmsClaimOfflineReserveClaimDao;
import com.misyn.mcms.claim.dto.McmsClaimOfflineReserveClaimDto;
import com.misyn.mcms.claim.enums.PolicyChannelType;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
public class McmsClaimOfflineReserveClaimDaoImpl implements McmsClaimOfflineReserveClaimDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(McmsClaimOfflineReserveClaimDaoImpl.class);

    @Override
    public McmsClaimOfflineReserveClaimDto insertMaster(Connection connection, McmsClaimOfflineReserveClaimDto mcmsClaimOfflineReserveClaimDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(MCMS_CLAIM_OFFLINE_RESERVE_CLAIM_INSERT);
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOvClaimNo());
            ps.setBigDecimal(++index, mcmsClaimOfflineReserveClaimDto.getOnBillAmount());
            ps.setBigDecimal(++index, mcmsClaimOfflineReserveClaimDto.getOnAllowedAmount());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOnDepPer());
            ps.setBigDecimal(++index, mcmsClaimOfflineReserveClaimDto.getOnPaEstimateAmount());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOvReportType());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOvIdentificationNo());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOdDateOfAccssesSub());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOdDateOfAppointment());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOvType());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOdDateOfAccssessment());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOvPanelCategory());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOvInstitutionBranch());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOvInstitutionCode());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOvIdentificationCode());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOvPanelType());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOdReceivedDate());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOvLossType());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getCvRequired());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getOvRiskNo());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getDInsertDateTime());
            ps.setInt(++index, mcmsClaimOfflineReserveClaimDto.getNRetryAttempt());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getVIsfsUpdateStat());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getDIsfsUpdateDateTime());
            ps.setInt(++index, mcmsClaimOfflineReserveClaimDto.getClaimNo());
            ps.setString(++index, mcmsClaimOfflineReserveClaimDto.getPolicyChannelType());

            if (ps.executeUpdate() > 0) {
                return mcmsClaimOfflineReserveClaimDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public McmsClaimOfflineReserveClaimDto updateMaster(Connection connection, McmsClaimOfflineReserveClaimDto mcmsClaimOfflineReserveClaimDto) throws Exception {
        return null;
    }

    @Override
    public McmsClaimOfflineReserveClaimDto insertTemporary(Connection connection, McmsClaimOfflineReserveClaimDto mcmsClaimOfflineReserveClaimDto) throws Exception {
        return null;
    }

    @Override
    public McmsClaimOfflineReserveClaimDto updateTemporary(Connection connection, McmsClaimOfflineReserveClaimDto mcmsClaimOfflineReserveClaimDto) throws Exception {
        return null;
    }

    @Override
    public McmsClaimOfflineReserveClaimDto insertHistory(Connection connection, McmsClaimOfflineReserveClaimDto mcmsClaimOfflineReserveClaimDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public McmsClaimOfflineReserveClaimDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        McmsClaimOfflineReserveClaimDto mcmsClaimOfflineReserveClaimDto = new McmsClaimOfflineReserveClaimDto();
        try {
            ps = connection.prepareStatement(MCMS_CLAIM_OFFLINE_RESERVE_CLAIM_UPDATE);
            ps.setObject(1, id);
            rs = ps.executeQuery();
            if (rs.next()) {

                mcmsClaimOfflineReserveClaimDto.setNReserveClaimId(rs.getLong("N_RESERVE_CLAIM_ID"));
                mcmsClaimOfflineReserveClaimDto.setOvClaimNo(rs.getString("OV_CLAIM_NO"));
                mcmsClaimOfflineReserveClaimDto.setOnBillAmount(rs.getBigDecimal("ON_BILL_AMOUNT"));
                mcmsClaimOfflineReserveClaimDto.setOnAllowedAmount(rs.getBigDecimal("ON_ALLOWED_AMOUNT"));
                mcmsClaimOfflineReserveClaimDto.setOnDepPer(rs.getString("ON_DEP_PER"));
                mcmsClaimOfflineReserveClaimDto.setOnPaEstimateAmount(rs.getBigDecimal("ON_PA_ESTIMATE_AMOUNT"));
                mcmsClaimOfflineReserveClaimDto.setOvReportType(rs.getString("OV_REPORT_TYPE"));
                mcmsClaimOfflineReserveClaimDto.setOvIdentificationNo(rs.getString("OV_IDENTIFICATION_NO"));
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAccssesSub(rs.getString("OD_DATE_OF_ACCSSES_SUB"));
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAppointment(rs.getString("OD_DATE_OF_APPOINTMENT"));
                mcmsClaimOfflineReserveClaimDto.setOvType(rs.getString("OV_TYPE"));
                mcmsClaimOfflineReserveClaimDto.setOdDateOfAccssessment(rs.getString("OD_DATE_OF_ACCSSESSMENT"));
                mcmsClaimOfflineReserveClaimDto.setOvPanelCategory(rs.getString("OV_PANEL_CATEGORY"));
                mcmsClaimOfflineReserveClaimDto.setOvInstitutionBranch(rs.getString("OV_INSTITUTION_BRANCH"));
                mcmsClaimOfflineReserveClaimDto.setOvInstitutionCode(rs.getString("OV_INSTITUTION_CODE"));
                mcmsClaimOfflineReserveClaimDto.setDIsfsUpdateDateTime(rs.getString("OV_IDENTIFICATION_CODE"));
                mcmsClaimOfflineReserveClaimDto.setOvPanelType(rs.getString("OV_PANEL_TYPE"));
                mcmsClaimOfflineReserveClaimDto.setOdReceivedDate(rs.getString("OD_RECEIVED_DATE"));
                mcmsClaimOfflineReserveClaimDto.setOvLossType(rs.getString("OV_LOSS_TYPE"));
                mcmsClaimOfflineReserveClaimDto.setCvRequired(rs.getString("CV_REQUIRED"));
                mcmsClaimOfflineReserveClaimDto.setOvRiskNo(rs.getString("OV_RISK_NO"));
                mcmsClaimOfflineReserveClaimDto.setDInsertDateTime(rs.getString("D_INSERT_DATE_TIME"));
                mcmsClaimOfflineReserveClaimDto.setNRetryAttempt(rs.getInt("N_RETRY_ATTEMPT"));
                mcmsClaimOfflineReserveClaimDto.setVIsfsUpdateStat(rs.getString("V_ISFS_UPDATE_STAT"));
                mcmsClaimOfflineReserveClaimDto.setDIsfsUpdateDateTime(rs.getString("D_ISFS_UPDATE_DATE_TIME"));
                mcmsClaimOfflineReserveClaimDto.setPolicyChannelType(null == rs.getString("t1.V_POLICY_CHANNEL_TYPE") || rs.getString("t1.V_POLICY_CHANNEL_TYPE").trim().isEmpty() ? PolicyChannelType.CONVENTIONAL.name() : rs.getString("t1.V_POLICY_CHANNEL_TYPE"));

                return mcmsClaimOfflineReserveClaimDto;
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public McmsClaimOfflineReserveClaimDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<McmsClaimOfflineReserveClaimDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public void updateReserveClaimPolicyChannelType(Connection connection, Integer claimNo, String policyChannelType) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(UPDATE_OFFLINE_RESERVE_CLAIM_POLICY_CHANNEL_TYPE);
            ps.setString(1, policyChannelType);
            ps.setInt(2, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }
}
