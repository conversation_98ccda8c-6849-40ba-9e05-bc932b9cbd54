package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.McmsClaimRefTwoOfflinePaymentDao;
import com.misyn.mcms.claim.dto.McmsClaimOfflinePaymentDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
public class McmsClaimRefTwoOfflinePaymentDaoImpl implements McmsClaimRefTwoOfflinePaymentDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(McmsClaimRefTwoOfflinePaymentDaoImpl.class);

    @Override
    public McmsClaimOfflinePaymentDto insertMaster(Connection connection, McmsClaimOfflinePaymentDto mcmsClaimOfflinePaymentDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(MCMS_CLAIM_OFFLINE_PAYMENT_INSERT);
            ps.setInt(++index, mcmsClaimOfflinePaymentDto.getReferenceId());
            ps.setString(++index, mcmsClaimOfflinePaymentDto.getOvPayeeType());
            ps.setString(++index, mcmsClaimOfflinePaymentDto.getOvIdentificationNo());
            ps.setString(++index, mcmsClaimOfflinePaymentDto.getOvClaimNo());
            ps.setBigDecimal(++index, mcmsClaimOfflinePaymentDto.getOnPaidAmount());
            ps.setString(++index, mcmsClaimOfflinePaymentDto.getOvInstitutionBranch());
            ps.setString(++index, mcmsClaimOfflinePaymentDto.getOvInstitutionCode());
            ps.setBigDecimal(++index, mcmsClaimOfflinePaymentDto.getOnTotalPayable());
            ps.setString(++index, mcmsClaimOfflinePaymentDto.getOvIdentificationCode());
            ps.setString(++index, mcmsClaimOfflinePaymentDto.getOvVoucherFlag());
            ps.setString(++index, mcmsClaimOfflinePaymentDto.getdInsertDateTime());
            ps.setInt(++index, mcmsClaimOfflinePaymentDto.getNRetryAttempt());
            ps.setString(++index, mcmsClaimOfflinePaymentDto.getVIsfsUpdateStat());
            ps.setString(++index, mcmsClaimOfflinePaymentDto.getdIsfsUpdateDateTime());
            ps.setInt(++index, mcmsClaimOfflinePaymentDto.getClaimNo());
            ps.setString(++index, mcmsClaimOfflinePaymentDto.getPolicyChannelType());

            if (ps.executeUpdate() > 0) {
                return mcmsClaimOfflinePaymentDto;
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public McmsClaimOfflinePaymentDto updateMaster(Connection connection, McmsClaimOfflinePaymentDto mcmsClaimOfflinePaymentDto) throws Exception {
        return null;
    }

    @Override
    public McmsClaimOfflinePaymentDto insertTemporary(Connection connection, McmsClaimOfflinePaymentDto mcmsClaimOfflinePaymentDto) throws Exception {
        return null;
    }

    @Override
    public McmsClaimOfflinePaymentDto updateTemporary(Connection connection, McmsClaimOfflinePaymentDto mcmsClaimOfflinePaymentDto) throws Exception {
        return null;
    }

    @Override
    public McmsClaimOfflinePaymentDto insertHistory(Connection connection, McmsClaimOfflinePaymentDto mcmsClaimOfflinePaymentDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public McmsClaimOfflinePaymentDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        McmsClaimOfflinePaymentDto mcmsClaimOfflinePaymentDto = new McmsClaimOfflinePaymentDto();
        try {
            ps = connection.prepareStatement(MCMS_CLAIM_OFFLINE_PAYMENT_UPDATE);
            ps.setObject(1, id);
            rs = ps.executeQuery();
            if (rs.next()) {

                mcmsClaimOfflinePaymentDto.setNPaymentId(rs.getLong("N_PAYMENT_ID"));
                mcmsClaimOfflinePaymentDto.setOvPayeeType(rs.getString("OV_PAYEE_TYPE"));
                mcmsClaimOfflinePaymentDto.setOvIdentificationNo(rs.getString("OV_IDENTIFICATION_NO"));
                mcmsClaimOfflinePaymentDto.setOvClaimNo(rs.getString("OV_CLAIM_NO"));
                mcmsClaimOfflinePaymentDto.setOnPaidAmount(rs.getBigDecimal("ON_PAID_AMOUNT"));
                mcmsClaimOfflinePaymentDto.setOvInstitutionBranch(rs.getString("OV_INSTITUTION_BRANCH"));
                mcmsClaimOfflinePaymentDto.setOvInstitutionCode(rs.getString("OV_INSTITUTION_CODE"));
                mcmsClaimOfflinePaymentDto.setOnTotalPayable(rs.getBigDecimal("ON_TOTAL_PAYABLE"));
                mcmsClaimOfflinePaymentDto.setOvInstitutionCode(rs.getString("OV_IDENTIFICATION_CODE"));
                mcmsClaimOfflinePaymentDto.setOvVoucherFlag(rs.getString("OV_VOUCHER_FLAG"));
                mcmsClaimOfflinePaymentDto.setdInsertDateTime(rs.getString("D_INSERT_DATE_TIME"));
                mcmsClaimOfflinePaymentDto.setNRetryAttempt(rs.getInt("N_RETRY_ATTEMPT"));
                mcmsClaimOfflinePaymentDto.setVIsfsUpdateStat(rs.getString("V_ISFS_UPDATE_STAT"));
                mcmsClaimOfflinePaymentDto.setdIsfsUpdateDateTime(rs.getString("D_ISFS_UPDATE_DATE_TIME"));

                return mcmsClaimOfflinePaymentDto;
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public McmsClaimOfflinePaymentDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<McmsClaimOfflinePaymentDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public void updatePaymentPolicyChannelType(Connection connection, Integer claimNo, String policyChannelType) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(UPDATE_OFFLINE_PAYMENT_POLICY_CHANNEL_TYPE);
            ps.setString(1, policyChannelType);
            ps.setInt(2, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }
}
