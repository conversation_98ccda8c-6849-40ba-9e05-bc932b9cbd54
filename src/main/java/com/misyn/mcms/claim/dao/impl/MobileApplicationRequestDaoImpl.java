package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.MobileApplicationRequestDao;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.MobileApplicationRequestDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class MobileApplicationRequestDaoImpl extends AbstractBaseDao<MobileApplicationRequestDaoImpl> implements MobileApplicationRequestDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(MobileApplicationRequestDaoImpl.class);

//    @Override
//    public DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String filteredBy) {
//        int index = start;
//        DataGridDto dataGridDTO = new DataGridDto();
//        int count = 0;
//        final String SEL_SQL;
//        final String COUNT_SQL;
//        List claimList = new ArrayList(200);
//
//        PreparedStatement ps = null;
//        if ("0".equals(filteredBy)) {
//            String SQL_SEARCH = formatSQL(parameterList).toString();
//            final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
//
//            if (!fromDate.isEmpty() && !toDate.isEmpty()) {
//                SQL_SEARCH = SQL_SEARCH.concat(" WHERE DATE_FORMAT(t1.input_date_time,'%Y-%m-%d %H:%i') BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
//            }
//
//            SEL_SQL = "SELECT\n" +
//                    "t1.txn_id,\n" +
//                    "t1.assessor_user_name,\n" +
//                    "t1.pushed_job_ref_no,\n" +
//                    "t1.input_date_time,\n" +
//                    "t1.request_status,\n" +
//                    "t1.claim_no\n" +
//                    "FROM\n" +
//                    "mobile_application_connection_status AS t1 ".concat(SQL_SEARCH).concat(SQL_ORDER);
//
//            COUNT_SQL = "SELECT\n" +
//                    "COUNT(*) AS cnt\n" +
//                    "FROM\n" +
//                    "mobile_application_connection_status AS t1 ".concat(SQL_SEARCH);
//
//        } else {
//            String SQL_SEARCH = formatSQL(parameterList).toString();
//            final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
//
//            if (!fromDate.isEmpty() && !toDate.isEmpty()) {
//                SQL_SEARCH = SQL_SEARCH.concat(" AND DATE_FORMAT(t2.assign_datetime,'%Y-%m-%d %H:%i') BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
//            }
//
//            SEL_SQL = "SELECT\n" +
//                    "t1.txn_id,\n" +
//                    "t1.assessor_user_name,\n" +
//                    "t1.pushed_job_ref_no,\n" +
//                    "t1.input_date_time,\n" +
//                    "t1.request_status,\n" +
//                    "t1.claim_no,\n" +
//                    "t2.assign_datetime,\n" +
//                    "t2.inp_datetime,\n" +
//                    "t2.job_id,\n" +
//                    "t2.record_status,\n" +
//                    "t4.v_status_desc,\n" +
//                    "t3.inspection_type_desc\n" +
//                    "FROM\n" +
//                    "claim_assign_assesor AS t2\n" +
//                    "INNER JOIN mobile_application_connection_status AS t1 ON t1.pushed_job_ref_no = t2.ref_no\n" +
//                    "INNER JOIN claim_status_para AS t4 ON t2.record_status = t4.n_ref_id\n" +
//                    "INNER JOIN claim_inspection_type AS t3 ON t2.insepction_id = t3.inspection_type_id ".concat(SQL_SEARCH).concat(SQL_ORDER);
//            ;
//
//            COUNT_SQL = "SELECT\n" +
//                    "COUNT(*) AS cnt\n" +
//                    "FROM\n" +
//                    "claim_assign_assesor AS t2\n" +
//                    "INNER JOIN mobile_application_connection_status AS t1 ON t1.pushed_job_ref_no = t2.ref_no ".concat(SQL_SEARCH);
//
//        }
//        try {
//
//            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
//            try (ResultSet rs = ps.executeQuery()) {
//                if (rs.next()) {
//                    count = rs.getInt("cnt");
//                }
//            }
//            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
//            try (ResultSet rs = ps.executeQuery()) {
//                while (rs.next()) {
//                    MobileApplicationRequestDto mobileApplicationRequestDto = getMobileApplicationRequest(rs, filteredBy);
//                    mobileApplicationRequestDto.setIndex(++index);
//                    claimList.add(mobileApplicationRequestDto);
//                }
//            }
//            dataGridDTO.setDraw(drawRandomId);
//            dataGridDTO.setRecordsTotal(count);
//            dataGridDTO.setRecordsFiltered(count);
//            dataGridDTO.setData(claimList);
//
//        } catch (Exception e) {
//            LOGGER.error(e.getMessage());
//        } finally {
//            try {
//                if (ps != null) {
//                    ps.close();
//                }
//            } catch (Exception ex) {
//            }
//        }
//        return dataGridDTO;
//    }

//    @Override
//    public DataGridDto getInspectionDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) {
//        int index = start;
//        DataGridDto dataGridDTO = new DataGridDto();
//        int count = 0;
//        final String SEL_SQL;
//        final String COUNT_SQL;
//        List claimList = new ArrayList(200);
//
//        PreparedStatement ps = null;
//        String SQL_SEARCH = formatSQL(parameterList).toString();
//        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
//
//        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
//            SQL_SEARCH = SQL_SEARCH.concat(" AND DATE_FORMAT(t1.D_NOTIFY_DATE_TIME,'%Y-%m-%d %H:%i') BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
//        }
//
//        SEL_SQL = "SELECT\n" +
//                "	t1.N_TXN_ID,\n" +
//                "	t1.N_CLAIM_NO,\n" +
//                "	t1.V_READ_DATE_TIME,\n" +
//                "	t1.D_NOTIFY_DATE_TIME,\n" +
//                "	t1.V_VEHICLE_NO,\n" +
//                "	t1.D_ACCIDENT_DATE,\n" +
//                "	t1.V_IS_MOBILE_READ,\n" +
//                "	t3.inspection_type_desc,\n" +
//                "	t2.job_id,\n" +
//                "	t1.V_ASSIGN_USER_ID,\n" +
//                "	t2.insepction_id\n" +
//                "FROM\n" +
//                "	claim_notification AS t1\n" +
//                "INNER JOIN claim_assign_assesor AS t2 ON t1.N_REF_NO = t2.ref_no\n" +
//                "INNER JOIN claim_inspection_type AS t3 ON t2.insepction_id = t3.inspection_type_id\n" +
//                "INNER JOIN usr_mst AS t4 ON t1.V_ASSIGN_USER_ID = t4.v_usrid ".concat(SQL_SEARCH).concat(SQL_ORDER);
//        ;
//
//        COUNT_SQL = "SELECT\n" +
//                "	COUNT(*) AS cnt\n" +
//                "FROM\n" +
//                "	claim_notification AS t1\n" +
//                "INNER JOIN claim_assign_assesor AS t2 ON t1.N_REF_NO = t2.ref_no\n" +
//                "INNER JOIN claim_inspection_type AS t3 ON t2.insepction_id = t3.inspection_type_id\n" +
//                "INNER JOIN usr_mst AS t4 ON t1.V_ASSIGN_USER_ID = t4.v_usrid ".concat(SQL_SEARCH);
//
//        try {
//
//            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
//            try (ResultSet rs = ps.executeQuery()) {
//                if (rs.next()) {
//                    count = rs.getInt("cnt");
//                }
//            }
//            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
//            try (ResultSet rs = ps.executeQuery()) {
//                while (rs.next()) {
//                    MobileAppInspectionViewDto mobileAppInspectionViewDto = getMobileAppInspectionView(rs);
//                    mobileAppInspectionViewDto.setIndex(++index);
//                    claimList.add(mobileAppInspectionViewDto);
//                }
//            }
//            dataGridDTO.setDraw(drawRandomId);
//            dataGridDTO.setRecordsTotal(count);
//            dataGridDTO.setRecordsFiltered(count);
//            dataGridDTO.setData(claimList);
//
//        } catch (Exception e) {
//            LOGGER.error(e.getMessage());
//        } finally {
//            try {
//                if (ps != null) {
//                    ps.close();
//                }
//            } catch (Exception ex) {
//            }
//        }
//        return dataGridDTO;
//    }

    @Override
    public DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String filteredBy) {
        return null;
    }

    @Override
    public DataGridDto getInspectionDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) {
        return null;
    }

    @Override
    public DataGridDto searchNotification(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate, String notificationStatus) {
        int index = start;
        int count = 0;
        final String SEL_SQL;
        final String COUNT_SQL;
        DataGridDto dataGridDto = new DataGridDto();
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();
        List datalist = new ArrayList(200);
        String DATATABLE = AppConstant.STRING_EMPTY;
        final String DATE_DIFF = " AND DATE_FORMAT(t1.D_NOTIFY_DATE_TIME,'%Y-%m-%d %H:%i') BETWEEN " + "'" + fromDate + "'" + " AND " + "'" + toDate + "'";
        final String SQL_JOIN = " AS t1 INNER JOIN usr_mst AS t2 ON t1.V_ASSIGN_USER_ID=t2.v_usrid ";
        PreparedStatement preparedStatement = null;
        try {
            switch (notificationStatus) {
                case "0":
                    DATATABLE = "claim_notification";
                    break;
                case "1":
                    DATATABLE = "claim_notification_history";
                    break;

            }
            SEL_SQL = MobileApplicationRequestDao.SEARCH_NOTIFICATIONS
                    .concat(DATATABLE)
                    .concat(SQL_JOIN)
                    .concat(SQL_SEARCH)
                    .concat(DATE_DIFF)
                    .concat(SQL_ORDER);

            COUNT_SQL = MobileApplicationRequestDao.NOTIFICATIONS_COUNT
                    .concat(DATATABLE)
                    .concat(SQL_JOIN)
                    .concat(SQL_SEARCH)
                    .concat(DATE_DIFF);

            preparedStatement = connection.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    count = resultSet.getInt("cnt");
                }
            }
            preparedStatement = connection.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                datalist = getNotificationList(resultSet, index);
            }
            dataGridDto.setDraw(drawRandomId);
            dataGridDto.setData(datalist);
            dataGridDto.setRecordsTotal(count);
            dataGridDto.setRecordsFiltered(count);

        } catch (Exception e) {
            LOGGER.error(e.getMessage(),e);
        } finally {
            try {
                if (preparedStatement != null) {
                    preparedStatement.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage(),ex);
            }
        }
        return dataGridDto;
    }

    private List getNotificationList(ResultSet resultSet, int index) {
        List datalist = new ArrayList();
        MobileApplicationRequestDto dto;
        try {
            while (resultSet.next()) {
                dto = new MobileApplicationRequestDto();
                dto.setIndex(++index);
                dto.setClaimNo(resultSet.getString("N_CLAIM_NO"));
                dto.setVehicleNo(resultSet.getString("V_VEHICLE_NO"));
                dto.setAssignUserId(resultSet.getString("V_ASSIGN_USER_ID"));
                dto.setNotifyDate(Utility.getDate(resultSet.getString("t1.D_NOTIFY_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
                dto.setReadStatus(resultSet.getString("V_READ_STATUS"));
                dto.setMobileReadStatus(resultSet.getString("V_IS_MOBILE_READ"));
                datalist.add(dto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return datalist;
    }

//    private MobileAppInspectionViewDto getMobileAppInspectionView(ResultSet rs) {
//        MobileAppInspectionViewDto dto = new MobileAppInspectionViewDto();
//        try {
//            dto.setTxnId(rs.getInt("t1.N_TXN_ID"));
//            dto.setClaimNo(rs.getInt("t1.N_CLAIM_NO"));
//            dto.setReadDatetime(Utility.getDate(rs.getString("t1.V_READ_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
//            dto.setNotifyDatetime(Utility.getDate(rs.getString("t1.D_NOTIFY_DATE_TIME"), AppConstant.DATE_TIME_FORMAT));
//            dto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
//            dto.setAccidentDate(Utility.getDate(rs.getString("t1.D_ACCIDENT_DATE"), AppConstant.DATE_FORMAT));
//            dto.setIsMobileRead(rs.getString("t1.V_IS_MOBILE_READ"));
//            dto.setInspectionType(rs.getString("t3.inspection_type_desc"));
//            dto.setJobId(rs.getString("t2.job_id"));
//            dto.setAssignUserId(rs.getString("t1.V_ASSIGN_USER_ID"));
//        } catch (SQLException e) {
//            LOGGER.error(e.getMessage());
//        }
//        return dto;
//    }

//    private MobileApplicationRequestDto getMobileApplicationRequest(ResultSet rs, String filteredBy) {
//
//        MobileApplicationRequestDto dto = new MobileApplicationRequestDto();
//        try {
//            if ("1".equals(filteredBy)) {
//                dto.setInspectionId(null == rs.getString("t3.inspection_type_desc") ? AppConstant.DASH : rs.getString("t3.inspection_type_desc"));
//                dto.setNotifyDateTime(null == rs.getString("t2.inp_datetime") ? AppConstant.DASH : Utility.getDate(rs.getString("t2.inp_datetime"), AppConstant.DATE_TIME_FORMAT));
//                dto.setRecordStatus(null == rs.getString("t4.v_status_desc") ? AppConstant.DASH : rs.getString("t4.v_status_desc"));
//                dto.setJobId(null == rs.getString("t2.job_id") ? AppConstant.DASH : rs.getString("t2.job_id"));
//            }
//            dto.setRefNo(rs.getInt("t1.txn_id"));
//            dto.setAssignUserId(null == rs.getString("t1.assessor_user_name") ? AppConstant.DASH : rs.getString("t1.assessor_user_name"));
//            dto.setJobRefNo(null == rs.getString("t1.pushed_job_ref_no") ? AppConstant.DASH : rs.getString("t1.pushed_job_ref_no"));
//            dto.setRequestDateTime(null == rs.getString("t1.input_date_time") ? AppConstant.DASH : Utility.getDate(rs.getString("t1.input_date_time"), AppConstant.DATE_TIME_FORMAT));
//            dto.setClaimNo(null == rs.getString("t1.claim_no") ? AppConstant.DASH : rs.getString("t1.claim_no"));
//            dto.setRequestStatus(null == rs.getString("t1.request_status") ? AppConstant.DASH : rs.getString("t1.request_status"));
//
//        } catch (SQLException e) {
//            LOGGER.error(e.getMessage());
//        }
//        return dto;
//    }

    @Override
    public MobileApplicationRequestDao insertMaster(Connection connection, MobileApplicationRequestDao mobileApplicationRequestDao) throws Exception {
        return null;
    }

    @Override
    public MobileApplicationRequestDao updateMaster(Connection connection, MobileApplicationRequestDao mobileApplicationRequestDao) throws Exception {
        return null;
    }

    @Override
    public MobileApplicationRequestDao insertTemporary(Connection connection, MobileApplicationRequestDao mobileApplicationRequestDao) throws Exception {
        return null;
    }

    @Override
    public MobileApplicationRequestDao updateTemporary(Connection connection, MobileApplicationRequestDao mobileApplicationRequestDao) throws Exception {
        return null;
    }

    @Override
    public MobileApplicationRequestDao insertHistory(Connection connection, MobileApplicationRequestDao mobileApplicationRequestDao) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public MobileApplicationRequestDao searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public MobileApplicationRequestDao searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<MobileApplicationRequestDao> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }
}
