package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.MofaLevelHierarchyDao;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class MofaLevelHierarchyDaoImpl implements MofaLevelHierarchyDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(MofaLevelHierarchyDaoImpl.class);

    @Override
    public List<Integer> getAllMandatoryMofaLevel(Connection connection) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Integer> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_MANDATORY_LEVEL);
            rs = ps.executeQuery();
            while (rs.next()) {
                list.add(rs.getInt("mofa_level"));
            }
            return list;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return list;
    }

    @Override
    public Integer getMofaMaxLevel(Connection connection) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_MAX_MOFA_LEVEL);
            rs = ps.executeQuery();
            while (rs.next()) {
                return rs.getInt("mofa_level");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return AppConstant.ZERO_INT;
    }
}
