package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.OfflineIntimationDao;
import com.misyn.mcms.claim.dto.OfflineIntimationDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
public class OfflineIntimationDaoImpl implements OfflineIntimationDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(OfflineIntimationDaoImpl.class);

    @Override
    public void saveOfflineIntimation(Connection connection, OfflineIntimationDto offlineIntimationDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(INSERT_CLAIM_OFFLINE_INTIMATION);
            ps.setString(++index, offlineIntimationDto.getOvPolicyNo());
            ps.setInt(++index, offlineIntimationDto.getOnRenCount());
            ps.setInt(++index, offlineIntimationDto.getOnEndCount());
            ps.setString(++index, offlineIntimationDto.getOvRiskNo());
            ps.setString(++index, offlineIntimationDto.getOvIntNo());
            ps.setString(++index, offlineIntimationDto.getOdIntimation());
            ps.setString(++index, offlineIntimationDto.getOvIntimationTime());
            ps.setString(++index, offlineIntimationDto.getOvIntimatedSource());
            ps.setString(++index, offlineIntimationDto.getOvIntimatedMeans());
            ps.setString(++index, offlineIntimationDto.getOvIntType());
            ps.setString(++index, offlineIntimationDto.getOvIntimatorName());
            ps.setString(++index, offlineIntimationDto.getOdLoss());
            ps.setString(++index, offlineIntimationDto.getOdLossTime());
            ps.setString(++index, offlineIntimationDto.getOdTravelStartDt());
            ps.setInt(++index, offlineIntimationDto.getOnNoOfDays());
            ps.setString(++index, offlineIntimationDto.getOvIdenNoDriver());
            ps.setString(++index, offlineIntimationDto.getOvIdenCodeDriver());
            ps.setString(++index, offlineIntimationDto.getOvCauseOfLoss());
            ps.setString(++index, offlineIntimationDto.getOvDescOfLoss());
            ps.setString(++index, offlineIntimationDto.getOvLossCode());
            ps.setString(++index, offlineIntimationDto.getOvReportType());
            ps.setString(++index, offlineIntimationDto.getOvPanelCategory());
            ps.setString(++index, offlineIntimationDto.getOvSurvIndComp());
            ps.setString(++index, offlineIntimationDto.getOvSurvIdenCode());
            ps.setString(++index, offlineIntimationDto.getOdAppointment());
            ps.setString(++index, offlineIntimationDto.getOdAssessment());
            ps.setString(++index, offlineIntimationDto.getOdSubmit());
            ps.setString(++index, offlineIntimationDto.getOvPoliceStation());
            ps.setString(++index, offlineIntimationDto.getOdReportDate());
            ps.setString(++index, offlineIntimationDto.getOdServiceRequestDate());
            ps.setString(++index, offlineIntimationDto.getOvUserId());
            ps.setString(++index, offlineIntimationDto.getOvDriverName());
            ps.setString(++index, Utility.sysDate(AppConstant.DATE_TIME_FORMAT));
            ps.setInt(++index, 0);
            ps.setString(++index, offlineIntimationDto.getIsfsUpdateStat());
            ps.setString(++index, AppConstant.DEFAULT_DATE_TIME);
            ps.setString(++index, offlineIntimationDto.getPolicyChannelType());
            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateOfflineIntimation(Connection connection, OfflineIntimationDto offlineIntimationDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_OFFLINE_INTIMATION);
            ps.setString(++index, offlineIntimationDto.getOvPolicyNo());
            ps.setString(++index, offlineIntimationDto.getIsfsUpdateStat());
            ps.setInt(++index, 0);
            ps.setString(++index, offlineIntimationDto.getOvRiskNo());
            ps.setString(++index, offlineIntimationDto.getPolicyChannelType());
            ps.setString(++index, offlineIntimationDto.getOvIntNo());
            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateIsfUpdateStatus(Connection connection, String status, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_ISF_STATUS);
            ps.setString(++index, status);
            ps.setInt(++index, claimNo);
            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }

    }
}
