package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.OnSiteInspectionDetailsDao;
import com.misyn.mcms.claim.dto.OnSiteInspectionDetailsDto;
import com.misyn.mcms.claim.enums.ConditionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
public class OnSiteInspectionDetailsDaoImpl extends BaseAbstract<OnSiteInspectionDetailsDaoImpl> implements OnSiteInspectionDetailsDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(OnSiteInspectionDetailsDaoImpl.class);

    @Override
    public OnSiteInspectionDetailsDto insertMaster(Connection connection, OnSiteInspectionDetailsDto onSiteInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_INTO_ONSITE_INSPECTION_DETAILS);
            ps.setInt(++index, onSiteInspectionDetailsDto.getInspectionId());
            ps.setInt(++index, onSiteInspectionDetailsDto.getRefNo());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getCostPart());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getCostLabour());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getExcess());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getAcr());
            ps.setString(++index, onSiteInspectionDetailsDto.getBoldTyrePenalty().getCondtionType());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getBoldPercent());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getBoldTirePenaltyAmount());
            ps.setString(++index, onSiteInspectionDetailsDto.getUnderInsuradPenalty().getCondtionType());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getUnderPenaltyPercent());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getUnderPenaltyAmount());
            ps.setString(++index, onSiteInspectionDetailsDto.getProvideOffer().getCondtionType());
            ps.setInt(++index, onSiteInspectionDetailsDto.getOfferType());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getPayableAmount());
            ps.setString(++index, onSiteInspectionDetailsDto.getRequestAri().getCondtionType());
            if (ps.executeUpdate() > 0) {
                return onSiteInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public OnSiteInspectionDetailsDto updateMaster(Connection connection, OnSiteInspectionDetailsDto onSiteInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_ONSITE_INSPECTION_DETAILS);
            ps.setInt(++index, onSiteInspectionDetailsDto.getInspectionId());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getCostPart());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getCostLabour());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getExcess());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getAcr());
            ps.setString(++index, onSiteInspectionDetailsDto.getBoldTyrePenalty().getCondtionType());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getBoldPercent());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getBoldTirePenaltyAmount());
            ps.setString(++index, onSiteInspectionDetailsDto.getUnderInsuradPenalty().getCondtionType());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getUnderPenaltyPercent());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getUnderPenaltyAmount());
            ps.setString(++index, onSiteInspectionDetailsDto.getProvideOffer().getCondtionType());
            ps.setInt(++index, onSiteInspectionDetailsDto.getOfferType());
            ps.setBigDecimal(++index, onSiteInspectionDetailsDto.getPayableAmount());
            ps.setString(++index, onSiteInspectionDetailsDto.getRequestAri().getCondtionType());
            ps.setInt(++index, onSiteInspectionDetailsDto.getRefNo());
            if (ps.executeUpdate() > 0) {
                return onSiteInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public OnSiteInspectionDetailsDto insertTemporary(Connection connection, OnSiteInspectionDetailsDto onSiteInspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public OnSiteInspectionDetailsDto updateTemporary(Connection connection, OnSiteInspectionDetailsDto onSiteInspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public OnSiteInspectionDetailsDto insertHistory(Connection connection, OnSiteInspectionDetailsDto onSiteInspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public OnSiteInspectionDetailsDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement(SQL_SELECT_ONSITE_INSPECTION_DETAILS);
            ps.setObject(1, id);

            rs = ps.executeQuery();
            if (rs.next()) {
                return getOnSiteInspectionDetails(rs);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            ps.close();
        }
        return null;

    }

    @Override
    public OnSiteInspectionDetailsDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<OnSiteInspectionDetailsDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    private OnSiteInspectionDetailsDto getOnSiteInspectionDetails(ResultSet rst) {
        OnSiteInspectionDetailsDto onSiteInspectionDetailsDto = new OnSiteInspectionDetailsDto();
        try {
            onSiteInspectionDetailsDto.setInspectionId(rst.getInt("inspection_id"));
            onSiteInspectionDetailsDto.setRefNo(rst.getInt("n_ref_no"));
            onSiteInspectionDetailsDto.setCostPart(rst.getBigDecimal("cost_part"));
            onSiteInspectionDetailsDto.setCostLabour(rst.getBigDecimal("cost_labour"));
            onSiteInspectionDetailsDto.setExcess(rst.getBigDecimal("excess"));
            onSiteInspectionDetailsDto.setAcr(rst.getBigDecimal("acr"));
            onSiteInspectionDetailsDto.setBoldTyrePenalty(rst.getString("bold_tyre_penalty").equals("Y") ? ConditionType.Yes : ConditionType.No);
            onSiteInspectionDetailsDto.setBoldPercent(rst.getBigDecimal("bold_percent"));
            onSiteInspectionDetailsDto.setBoldTirePenaltyAmount(rst.getBigDecimal("bold_tire_penalty_amount"));
            onSiteInspectionDetailsDto.setUnderInsuradPenalty(rst.getString("under_insurad_penalty").equals("Y") ? ConditionType.Yes : ConditionType.No);
            onSiteInspectionDetailsDto.setUnderPenaltyPercent(rst.getBigDecimal("under_penalty_percent"));
            onSiteInspectionDetailsDto.setUnderPenaltyAmount(rst.getBigDecimal("under_penalty_amount"));
            onSiteInspectionDetailsDto.setProvideOffer(rst.getString("provide_offer").equals("Y") ? ConditionType.Yes : ConditionType.No);
            onSiteInspectionDetailsDto.setOfferType(rst.getInt("offer_type"));
            onSiteInspectionDetailsDto.setPayableAmount(rst.getBigDecimal("payable_amount"));
            onSiteInspectionDetailsDto.setRequestAri(rst.getString("request_ari").equals("Y") ? ConditionType.Yes : ConditionType.No);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

        return onSiteInspectionDetailsDto;

    }


    @Override
    public BigDecimal getAcr(Connection connection, Integer refNo) {
        PreparedStatement ps = null;
        BigDecimal acr = BigDecimal.ZERO;
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement(SQL_SELECT_ACR_BY_REF_NO);
            ps.setObject(1, refNo);

            rs = ps.executeQuery();
            if (rs.next()) {
                acr = rs.getBigDecimal("acr");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return acr;
    }
}
