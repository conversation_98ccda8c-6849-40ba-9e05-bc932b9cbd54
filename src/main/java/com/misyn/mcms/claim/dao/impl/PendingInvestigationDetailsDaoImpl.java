package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.PendingInvestigationDetailsDao;
import com.misyn.mcms.claim.dto.PendingInvestigationDetailsDto;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
public class PendingInvestigationDetailsDaoImpl implements PendingInvestigationDetailsDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(PendingInvestigationDetailsDaoImpl.class);

    @Override
    public PendingInvestigationDetailsDto insertMaster(Connection connection, PendingInvestigationDetailsDto pendingInvestigationDetailsDto) throws Exception {

        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_MASTER);
            ps.setInt(++index, pendingInvestigationDetailsDto.getClaimNo());
            ps.setString(++index, pendingInvestigationDetailsDto.getInvestigationJobNo());
            ps.setString(++index, pendingInvestigationDetailsDto.getNotificationSendDate());

            if (ps.executeUpdate() > 0) {
                return pendingInvestigationDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public PendingInvestigationDetailsDto updateMaster(Connection connection, PendingInvestigationDetailsDto pendingInvestigationDetailsDto) throws Exception {
        return null;
    }

    @Override
    public PendingInvestigationDetailsDto insertTemporary(Connection connection, PendingInvestigationDetailsDto pendingInvestigationDetailsDto) throws Exception {
        return null;
    }

    @Override
    public PendingInvestigationDetailsDto updateTemporary(Connection connection, PendingInvestigationDetailsDto pendingInvestigationDetailsDto) throws Exception {
        return null;
    }

    @Override
    public PendingInvestigationDetailsDto insertHistory(Connection connection, PendingInvestigationDetailsDto pendingInvestigationDetailsDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public PendingInvestigationDetailsDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public PendingInvestigationDetailsDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<PendingInvestigationDetailsDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public boolean isNotificationSend(Connection connection, Integer claimNo) throws SQLException {
        PreparedStatement ps = null;
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement(SQL_SELECT_ONE_BY_CLAIM_NO_AND_SEND_DATE);
            ps.setObject(1, claimNo);
            ps.setObject(2, Utility.sysDate());

            rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            ps.close();
        }
        return false;
    }
}
