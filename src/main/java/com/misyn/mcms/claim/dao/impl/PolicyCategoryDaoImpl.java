package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.PolicyCategoryDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.LinkedHashMap;
import java.util.Map;
public class PolicyCategoryDaoImpl implements PolicyCategoryDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(PolicyCategoryDaoImpl.class);

    @Override
    public Map<String, String> getServiceFactorCategoryDtoList(Connection connection) {
        Map<String, String> map = new LinkedHashMap<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_SERVICE_FACTOR_CATEGORY);
            rs = ps.executeQuery();
            while (rs.next()) {
                String code = rs.getString("V_CODE");
                String name = rs.getString("V_SERVICE_FACTOR_NAME");
                map.put(code, name);

            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return map;

    }

    @Override
    public Map<String, String> getCoverCategoryDtoList(Connection connection) {
        Map<String, String> map = new LinkedHashMap<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_COVER_CATEGORY);
            rs = ps.executeQuery();
            while (rs.next()) {
                String code = rs.getString("V_CODE");
                String name = rs.getString("V_COVER_NAME");
                map.put(code, name);

            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return map;

    }

    @Override
    public Map<String, String> getBenefitCategoryDtoList(Connection connection) {
        Map<String, String> map = new LinkedHashMap<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_BENEFIT_CATEGORY);
            rs = ps.executeQuery();
            while (rs.next()) {
                String code = rs.getString("V_CODE");
                String name = rs.getString("V_BENEFIT_NAME");
                map.put(code, name);

            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return map;

    }

    @Override
    public Map<String, String> getConditionCategoryDtoList(Connection connection) {
        Map<String, String> map = new LinkedHashMap<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_CONDITION_CATEGORY);
            rs = ps.executeQuery();
            while (rs.next()) {
                String code = rs.getString("V_CODE");
                String name = rs.getString("V_C_E_NAME");
                map.put(code, name);

            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return map;

    }

    @Override
    public Map<String, String> getSpecialCategoryDtoList(Connection connection) {
        Map<String, String> map = new LinkedHashMap<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_SPECIAL_CATEGORY);
            rs = ps.executeQuery();
            while (rs.next()) {
                String code = rs.getString("V_CODE");
                String name = rs.getString("V_NAME");
                map.put(code, name);

            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return map;

    }
}
