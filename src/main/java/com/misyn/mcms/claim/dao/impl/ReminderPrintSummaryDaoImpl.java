package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.ReminderPrintSummaryDao;
import com.misyn.mcms.claim.dto.ReminderPrintSummaryDto;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
public class ReminderPrintSummaryDaoImpl implements ReminderPrintSummaryDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReminderPrintSummaryDaoImpl.class);

    @Override
    public ReminderPrintSummaryDto insertMaster(Connection connection, ReminderPrintSummaryDto reminderPrintSummaryDto) throws MisynJDBCException {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(INSERT_INTO_CLAIM_REMINDER_LETTER_PRINT_SUMMARY, Statement.RETURN_GENERATED_KEYS);
            ps.setInt(++index, reminderPrintSummaryDto.getClaimNo());
            ps.setString(++index, reminderPrintSummaryDto.getPrintUserId());
            ps.setString(++index, reminderPrintSummaryDto.getPrintDateTime());
            ps.setString(++index, reminderPrintSummaryDto.getGeneratedUserId());
            ps.setString(++index, reminderPrintSummaryDto.getGeneratedDateTime());
            ps.setString(++index, reminderPrintSummaryDto.getRecordStatus());
            if (ps.executeUpdate() > 0) {
                ResultSet rsKeys = ps.getGeneratedKeys();
                if (rsKeys.next()) {
                    int autoGeneratedId = rsKeys.getInt(1);
                    reminderPrintSummaryDto.setReminderSummaryRefId(autoGeneratedId);
                    return reminderPrintSummaryDto;
                }
                rsKeys.close();
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException(e);
        }
        return null;
    }

    @Override
    public ReminderPrintSummaryDto getReminderPrintSummaryDto(Connection connection, Integer reminderSummaryRefId) {
        ReminderPrintSummaryDto reminderPrintSummaryDto = null;
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_REMINDER_LETTER_PRINT_SUMMARY_BY_N_REMIN_SUMMARY_REF_ID);
            ps.setInt(1, reminderSummaryRefId);
            rs = ps.executeQuery();
            if (rs.next()) {
                reminderPrintSummaryDto = new ReminderPrintSummaryDto();
                reminderPrintSummaryDto.setReminderSummaryRefId(rs.getInt("N_REMIN_SUMMARY_REF_ID"));
                reminderPrintSummaryDto.setClaimNo(rs.getInt("N_CLAIM_NO"));
                reminderPrintSummaryDto.setPrintUserId(rs.getString("V_PRINT_USER_ID"));
                reminderPrintSummaryDto.setPrintDateTime(rs.getString("D_PRINT_DATE_TIME"));
                reminderPrintSummaryDto.setGeneratedUserId(rs.getString("V_GENERATE_USER_ID"));
                reminderPrintSummaryDto.setGeneratedDateTime(rs.getString("D_GENERATE_DATE_TIME"));
                reminderPrintSummaryDto.setRecordStatus(rs.getString("V_RECORD_STATUS"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return reminderPrintSummaryDto;
    }

    @Override
    public Integer getMaxReminderSummaryRefIdByClaimNo(Connection connection, Integer claimNo) {
        Integer maxId = 0;
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_MAX_CLAIM_REMINDER_LETTER_PRINT_SUMMARY);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                maxId = rs.getInt("maxId");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return maxId;
    }

    @Override
    public Integer getMaxReminderIdByClaimNo(Connection connection, Integer claimNo) {
        Integer maxId = 0;
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_MAX_CLAIM_REMINDER_ID_PRINT_SUMMARY);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                maxId = rs.getInt("maxIds");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return maxId;
    }

    @Override
    public List<ReminderPrintSummaryDto> searchAllByClaimNo(Connection connection, Integer claimNo) {
        List<ReminderPrintSummaryDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_ALL_CLAIM_REMINDER_LETTER_PRINT_SUMMARY_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ReminderPrintSummaryDto reminderPrintSummaryDto = new ReminderPrintSummaryDto();
                reminderPrintSummaryDto.setReminderSummaryRefId(rs.getInt("N_REMIN_SUMMARY_REF_ID"));
                reminderPrintSummaryDto.setClaimNo(rs.getInt("N_CLAIM_NO"));
                reminderPrintSummaryDto.setPrintUserId(rs.getString("V_PRINT_USER_ID"));
                reminderPrintSummaryDto.setPrintDateTime(rs.getString("D_PRINT_DATE_TIME"));
                reminderPrintSummaryDto.setGeneratedUserId(rs.getString("V_GENERATE_USER_ID"));
                reminderPrintSummaryDto.setGeneratedDateTime(Utility.getCustomDateFormat(rs.getString("D_GENERATE_DATE_TIME"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT, AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
                reminderPrintSummaryDto.setRecordStatus(rs.getString("V_RECORD_STATUS"));
                list.add(reminderPrintSummaryDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }
}
