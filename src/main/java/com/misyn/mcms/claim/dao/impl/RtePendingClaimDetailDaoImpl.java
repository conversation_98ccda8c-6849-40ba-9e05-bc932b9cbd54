package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.RtePendingClaimDetailDao;
import com.misyn.mcms.claim.dao.impl.motorengineer.MotorEngineerDetailsDaoImpl;
import com.misyn.mcms.claim.dto.RtePendingClaimsDto;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class RtePendingClaimDetailDaoImpl implements RtePendingClaimDetailDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(MotorEngineerDetailsDaoImpl.class);

    @Override
    public void savePendingJobs(Connection connection, Integer claimNo, Integer accessUserType, String userId) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SAVE_PENDING_JOBS_FOR_RTE);
            ps.setInt(1, claimNo);
            ps.setInt(2, accessUserType);
            ps.setString(3, userId);
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @Override
    public void removePendingJobs(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(REMOVE_PENDING_JOBS_FOR_RTE);
            ps.setInt(1, claimNo);
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @Override
    public List<RtePendingClaimsDto> getAllPendingClaimsFromSparePartsCoord(Connection connection) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<RtePendingClaimsDto> pendingClaims = new ArrayList<>();
        try {
            ps = connection.prepareStatement(GET_ALL_PENDING_CLAIMS);
            ps.setInt(1, AppConstant.ACCESS_LEVEL_SPARE_PARTS_COORDINATOR);
            rs = ps.executeQuery();
            while (rs.next()) {
                RtePendingClaimsDto rtePendingClaimsDto = new RtePendingClaimsDto();
                rtePendingClaimsDto.setClaimNo(rs.getInt("claim_no"));
                rtePendingClaimsDto.setAccessUserType(rs.getInt("access_user_type"));
                pendingClaims.add(rtePendingClaimsDto);
            }
            ps.close();
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return pendingClaims;
    }

    @Override
    public List<RtePendingClaimsDto> getAllPendingClaimsFromScrutinizingTeam(Connection connection) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<RtePendingClaimsDto> pendingClaims = new ArrayList<>();
        try {
            ps = connection.prepareStatement(GET_ALL_PENDING_CLAIMS);
            ps.setInt(1, AppConstant.ACCESS_LEVEL_SCRUTINIZING_TEAM);
            rs = ps.executeQuery();
            while (rs.next()) {
                RtePendingClaimsDto rtePendingClaimsDto = new RtePendingClaimsDto();
                rtePendingClaimsDto.setClaimNo(rs.getInt("claim_no"));
                rtePendingClaimsDto.setAccessUserType(rs.getInt("access_user_type"));
                pendingClaims.add(rtePendingClaimsDto);
            }
            ps.close();
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return pendingClaims;
    }

    @Override
    public RtePendingClaimsDto checkRtePendingJobs(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        RtePendingClaimsDto rtePendingClaimsDto = null;
        try {
            ps = connection.prepareStatement(CHECK_RTE_PENDING_JOBS);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                rtePendingClaimsDto = new RtePendingClaimsDto();
                rtePendingClaimsDto.setTxnId(rs.getInt("txn_id"));
                rtePendingClaimsDto.setClaimNo(rs.getInt("claim_no"));
                rtePendingClaimsDto.setAccessUserType(rs.getInt("access_user_type"));
                rtePendingClaimsDto.setUserId(rs.getString("user_id"));
            }
            ps.close();
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return rtePendingClaimsDto;
    }

    @Override
    public boolean checkIfRteJobsPending(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(CHECK_IF_RTE_JOBS_PENDING);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
            ps.close();
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return false;
    }

    @Override
    public void updateAssignUser(Connection connection, Integer claimNo, String assignUser, Integer accessUserType) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(UPDATE_RTE_PENDING_CLAIM);
            ps.setString(1, assignUser);
            ps.setInt(2, accessUserType);
            ps.setInt(3, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }
}
