package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.SparePartDatabaseDao;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.PopupItemDto;
import com.misyn.mcms.claim.dto.SparePartDatabaseDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class SparePartDatabaseDaoImpl extends BaseAbstract<SparePartDatabaseDaoImpl> implements SparePartDatabaseDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(SparePartDatabaseDaoImpl.class);

    @Override
    public DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List sparePartsDatabase = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat("  WHERE t1.input_date_time BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        final String SEL_SQL = "SELECT\n" +
                "t1.txn_id,\n" +
                "t1.vehicle_make,\n" +
                "t1.vehicle_model,\n" +
                "t1.manufacture_year,\n" +
                "t1.spare_part_ref_no,\n" +
                "t1.supplier_id,\n" +
                "t1.price,\n" +
                "t1.proceed_date,\n" +
                "t1.input_date_time,\n" +
                "t1.input_user_id,\n" +
                "t2.v_spare_part_name,\n" +
                "t3.V_SUPPLER_NAME,\n" +
                "t3.V_SUPPLIER_ADDRESS_LINE1,\n" +
                "t3.V_SUPPLIER_ADDRESS_LINE2,\n" +
                "t3.V_SUPPLIER_ADDRESS_LINE3,\n" +
                "t3.V_CONTACT_NO,\n" +
                "t3.V_EMAIL,\n" +
                "t3.V_CONTACT_PERSON\n" +
                "FROM\n" +
                "spare_part_item_mst AS t2\n" +
                "INNER JOIN claim_spare_part_database AS t1 ON t1.spare_part_ref_no = t2.n_spare_part_ref_no\n" +
                "INNER JOIN supplier_details_mst AS t3 ON t1.supplier_id = t3.N_SUPPLIER_ID ".concat(SQL_SEARCH).concat(SQL_ORDER);
        ;

        final String COUNT_SQL = "SELECT\n" +
                "count(t1.txn_id) AS cnt\n" +
                "FROM\n" +
                "spare_part_item_mst AS t2\n" +
                "INNER JOIN claim_spare_part_database AS t1 ON t1.spare_part_ref_no = t2.n_spare_part_ref_no\n" +
                "INNER JOIN supplier_details_mst AS t3 ON t1.supplier_id = t3.N_SUPPLIER_ID ".concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    SparePartDatabaseDto sparePartDatabaseDto = getSparePartDatabase(rs);
                    sparePartDatabaseDto.setIndex(++index);
                    sparePartsDatabase.add(sparePartDatabaseDto);
                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(sparePartsDatabase);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    @Override
    public List<PopupItemDto> getVehickeMake(Connection connection) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<PopupItemDto> list = new ArrayList<>();

        try {
            ps = connection.prepareStatement(GET_VEHICLE_MAKE);
            rs = ps.executeQuery();
            while (rs.next()) {
                PopupItemDto popupItemDto = new PopupItemDto();
//                popupItemDto.setValue(String.valueOf(rs.getInt("N_POL_REF_NO")));
                popupItemDto.setLabel(rs.getString("V_VEHICLE_MAKE"));
                list.add(popupItemDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (SQLException e) {
                LOGGER.error(e.getMessage());
            }
        }
        return list;
    }

    @Override
    public List<PopupItemDto> getVehickeModel(Connection connection, String vehicleMake) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<PopupItemDto> list = new ArrayList<>();

        try {
            ps = connection.prepareStatement(GET_VEHICLE_MODEL);
            ps.setString(1, vehicleMake);
            rs = ps.executeQuery();
            while (rs.next()) {
                PopupItemDto popupItemDto = new PopupItemDto();
//                popupItemDto.setValue(String.valueOf(rs.getInt("N_POL_REF_NO")));
                popupItemDto.setLabel(rs.getString("V_VEHICLE_MODEL"));
                list.add(popupItemDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs != null) {
                    rs.close();
                }
                if (ps != null) {
                    ps.close();
                }
            } catch (SQLException e) {
                LOGGER.error(e.getMessage());
            }
        }
        return list;
    }


    @Override
    public SparePartDatabaseDto insertMaster(Connection connection, SparePartDatabaseDto sparePartDatabaseDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_SPARE_PART_DATABASE_INSERT);
            ps.setString(++index, sparePartDatabaseDto.getVehicleMake());
            ps.setString(++index, sparePartDatabaseDto.getVehicleModel());
            ps.setInt(++index, sparePartDatabaseDto.getManufactureYear());
            ps.setInt(++index, sparePartDatabaseDto.getSparePartRefNo());
            ps.setInt(++index, sparePartDatabaseDto.getSupplierId());
            ps.setBigDecimal(++index, sparePartDatabaseDto.getPrice());
            ps.setString(++index, sparePartDatabaseDto.getProceedDate());
            ps.setString(++index, sparePartDatabaseDto.getInputDateTime());
            ps.setString(++index, sparePartDatabaseDto.getInputUserId());

            if (ps.executeUpdate() > 0) {
                return sparePartDatabaseDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public SparePartDatabaseDto updateMaster(Connection connection, SparePartDatabaseDto sparePartDatabaseDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(CLAIM_SPARE_PART_DATABASE_INSERT);
            ps.setString(++index, sparePartDatabaseDto.getVehicleMake());
            ps.setString(++index, sparePartDatabaseDto.getVehicleModel());
            ps.setInt(++index, sparePartDatabaseDto.getManufactureYear());
            ps.setInt(++index, sparePartDatabaseDto.getSparePartRefNo());
            ps.setInt(++index, sparePartDatabaseDto.getSupplierId());
            ps.setBigDecimal(++index, sparePartDatabaseDto.getPrice());
            ps.setString(++index, sparePartDatabaseDto.getProceedDate());
            ps.setString(++index, sparePartDatabaseDto.getInputDateTime());
            ps.setString(++index, sparePartDatabaseDto.getInputUserId());
            ps.setInt(++index, sparePartDatabaseDto.getTxnId());

            if (ps.executeUpdate() > 0) {
                return sparePartDatabaseDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public SparePartDatabaseDto insertTemporary(Connection connection, SparePartDatabaseDto sparePartDatabaseDto) throws Exception {
        return null;
    }

    @Override
    public SparePartDatabaseDto updateTemporary(Connection connection, SparePartDatabaseDto sparePartDatabaseDto) throws Exception {
        return null;
    }

    @Override
    public SparePartDatabaseDto insertHistory(Connection connection, SparePartDatabaseDto sparePartDatabaseDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public SparePartDatabaseDto searchMaster(Connection connection, Object id) throws Exception {
        SparePartDatabaseDto sparePartDatabaseDto = null;
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(CLAIM_SPARE_PART_DATABASE_SEARCH);
            ps.setInt(1, (Integer) id);
            rs = ps.executeQuery();
            if (rs.next()) {
                sparePartDatabaseDto = getSparePartDatabase(rs);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return sparePartDatabaseDto;
    }

    @Override
    public SparePartDatabaseDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<SparePartDatabaseDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    private SparePartDatabaseDto getSparePartDatabase(ResultSet rs) {
        SparePartDatabaseDto sparePartDatabaseDto = new SparePartDatabaseDto();
        try {
            sparePartDatabaseDto.setTxnId(rs.getInt("t1.txn_id"));
            sparePartDatabaseDto.setVehicleMake(rs.getString("t1.vehicle_make"));
            sparePartDatabaseDto.setVehicleModel(rs.getString("t1.vehicle_model"));
            sparePartDatabaseDto.setManufactureYear(rs.getInt("t1.manufacture_year"));
            sparePartDatabaseDto.setSparePartRefNo(rs.getInt("t1.spare_part_ref_no"));
            sparePartDatabaseDto.setSupplierId(rs.getInt("t1.supplier_id"));
            sparePartDatabaseDto.setPrice(rs.getBigDecimal("t1.price"));
            sparePartDatabaseDto.setProceedDate(rs.getString("t1.proceed_date"));
            sparePartDatabaseDto.setInputDateTime(rs.getString("t1.input_date_time"));
            sparePartDatabaseDto.setInputUserId(rs.getString("t1.input_user_id"));

            sparePartDatabaseDto.setSparePartName(rs.getString("t2.v_spare_part_name"));

            sparePartDatabaseDto.getSupplierDetailsMasterDto().setSupplerName(rs.getString("t3.V_SUPPLER_NAME"));
            sparePartDatabaseDto.getSupplierDetailsMasterDto().setSupplierAddressLine1(rs.getString("t3.V_SUPPLIER_ADDRESS_LINE1"));
            sparePartDatabaseDto.getSupplierDetailsMasterDto().setSupplierAddressLine2(rs.getString("t3.V_SUPPLIER_ADDRESS_LINE2"));
            sparePartDatabaseDto.getSupplierDetailsMasterDto().setSupplierAddressLine3(rs.getString("t3.V_SUPPLIER_ADDRESS_LINE3"));
            sparePartDatabaseDto.getSupplierDetailsMasterDto().setContactNo(rs.getString("t3.V_CONTACT_NO"));
            sparePartDatabaseDto.getSupplierDetailsMasterDto().setEmail(rs.getString("t3.V_EMAIL"));
            sparePartDatabaseDto.getSupplierDetailsMasterDto().setContactPerson(rs.getString("t3.V_CONTACT_PERSON"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return sparePartDatabaseDto;
    }
}
