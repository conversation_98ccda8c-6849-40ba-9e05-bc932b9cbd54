package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.SupplierDetailsMasterDao;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.SupplierDetailsMasterDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class SupplierDetailsMasterDaoImpl extends BaseAbstract<SupplierDetailsMasterDaoImpl> implements SupplierDetailsMasterDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(SupplierDetailsMasterDaoImpl.class);

    @Override
    public SupplierDetailsMasterDto insertMaster(Connection connection, SupplierDetailsMasterDto supplierDetailsMasterDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(SUPPLIER_DETAILS_MASTER_INSERT)) {
            ps.setString(++index, supplierDetailsMasterDto.getSupplerName());
            ps.setString(++index, supplierDetailsMasterDto.getSupplierAddressLine1());
            ps.setString(++index, supplierDetailsMasterDto.getSupplierAddressLine2());
            ps.setString(++index, supplierDetailsMasterDto.getSupplierAddressLine3());
            ps.setString(++index, supplierDetailsMasterDto.getContactNo());
            ps.setString(++index, supplierDetailsMasterDto.getContactPerson());
            ps.setString(++index, supplierDetailsMasterDto.getEmail());
            ps.setString(++index, supplierDetailsMasterDto.getRecordStatus());
            ps.setString(++index, supplierDetailsMasterDto.getInputUserId());

            if (ps.executeUpdate() > 0) {
                return supplierDetailsMasterDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return null;
    }

    @Override
    public SupplierDetailsMasterDto updateMaster(Connection connection, SupplierDetailsMasterDto supplierDetailsMasterDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(SUPPLIER_DETAILS_MASTER_UPDATE)) {
            ps.setString(++index, supplierDetailsMasterDto.getSupplerName());
            ps.setString(++index, supplierDetailsMasterDto.getSupplierAddressLine1());
            ps.setString(++index, supplierDetailsMasterDto.getSupplierAddressLine2());
            ps.setString(++index, supplierDetailsMasterDto.getSupplierAddressLine3());
            ps.setString(++index, supplierDetailsMasterDto.getContactNo());
            ps.setString(++index, supplierDetailsMasterDto.getContactPerson());
            ps.setString(++index, supplierDetailsMasterDto.getEmail());
            ps.setString(++index, supplierDetailsMasterDto.getRecordStatus());
            ps.setString(++index, supplierDetailsMasterDto.getInputUserId());
            ps.setInt(++index, supplierDetailsMasterDto.getSupplierId());

            if (ps.executeUpdate() > 0) {
                return supplierDetailsMasterDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return null;
    }

    @Override
    public SupplierDetailsMasterDto insertTemporary(Connection connection, SupplierDetailsMasterDto supplierDetailsMasterDto) throws Exception {
        return null;
    }

    @Override
    public SupplierDetailsMasterDto updateTemporary(Connection connection, SupplierDetailsMasterDto supplierDetailsMasterDto) throws Exception {
        return null;
    }

    @Override
    public SupplierDetailsMasterDto insertHistory(Connection connection, SupplierDetailsMasterDto supplierDetailsMasterDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public SupplierDetailsMasterDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        SupplierDetailsMasterDto supplierDetailsMasterDto = new SupplierDetailsMasterDto();
        try {
            ps = connection.prepareStatement(SUPPLIER_DETAILS_MASTER_SEARCH);
            ps.setObject(1, id);
            rs = ps.executeQuery();
            if (rs.next()) {

                supplierDetailsMasterDto.setSupplierId(rs.getInt("N_SUPPLIER_ID"));
                supplierDetailsMasterDto.setSupplerName(rs.getString("V_SUPPLER_NAME"));
                supplierDetailsMasterDto.setSupplierAddressLine1(rs.getString("V_SUPPLIER_ADDRESS_LINE1"));
                supplierDetailsMasterDto.setSupplierAddressLine2(rs.getString("V_SUPPLIER_ADDRESS_LINE2"));
                supplierDetailsMasterDto.setSupplierAddressLine3(rs.getString("V_SUPPLIER_ADDRESS_LINE3"));
                supplierDetailsMasterDto.setContactNo(rs.getString("V_CONTACT_NO"));
                supplierDetailsMasterDto.setContactPerson(rs.getString("V_CONTACT_PERSON"));
                supplierDetailsMasterDto.setEmail(rs.getString("V_EMAIL"));
                supplierDetailsMasterDto.setRecordStatus(rs.getString("V_RECORD_STATUS"));
                supplierDetailsMasterDto.setInputUserId(rs.getString("V_INPUT_USER_ID"));
                return supplierDetailsMasterDto;
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return supplierDetailsMasterDto;
    }

    @Override
    public SupplierDetailsMasterDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<SupplierDetailsMasterDto> searchAll(Connection connection) throws Exception {
        List<SupplierDetailsMasterDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SUPPLIER_DETAILS_MASTER_SEARCH_ALL);
            rs = ps.executeQuery();
            while (rs.next()) {
                SupplierDetailsMasterDto supplierDetailsMasterDto = new SupplierDetailsMasterDto();
                supplierDetailsMasterDto.setSupplierId(rs.getInt("N_SUPPLIER_ID"));
                supplierDetailsMasterDto.setSupplerName(rs.getString("V_SUPPLER_NAME"));
                supplierDetailsMasterDto.setSupplierAddressLine1(rs.getString("V_SUPPLIER_ADDRESS_LINE1"));
                supplierDetailsMasterDto.setSupplierAddressLine2(rs.getString("V_SUPPLIER_ADDRESS_LINE2"));
                supplierDetailsMasterDto.setSupplierAddressLine3(rs.getString("V_SUPPLIER_ADDRESS_LINE3"));
                supplierDetailsMasterDto.setContactNo(rs.getString("V_CONTACT_NO"));
                supplierDetailsMasterDto.setContactPerson(rs.getString("V_CONTACT_PERSON"));
                supplierDetailsMasterDto.setEmail(rs.getString("V_EMAIL"));
                supplierDetailsMasterDto.setRecordStatus(rs.getString("V_RECORD_STATUS"));
                supplierDetailsMasterDto.setInputUserId(rs.getString("V_INPUT_USER_ID"));

                list.add(supplierDetailsMasterDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public DataGridDto getDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField) {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List sparePartsDatabase = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();

        final String SEL_SQL = "SELECT * FROM supplier_details_mst ".concat(SQL_SEARCH).concat(SQL_ORDER);

        final String COUNT_SQL = "SELECT\n" +
                "count(N_SUPPLIER_ID) AS cnt\n" +
                "FROM supplier_details_mst ".concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    SupplierDetailsMasterDto supplierDetailsMasterDto = getsupplierDetailsDatabase(rs);
                    supplierDetailsMasterDto.setIndex(++index);
                    sparePartsDatabase.add(supplierDetailsMasterDto);
                }
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(sparePartsDatabase);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;
    }

    private SupplierDetailsMasterDto getsupplierDetailsDatabase(ResultSet rs) {
        SupplierDetailsMasterDto supplierDetailsMasterDto = new SupplierDetailsMasterDto();
        try {
            supplierDetailsMasterDto.setSupplierId(rs.getInt("N_SUPPLIER_ID"));
            supplierDetailsMasterDto.setSupplerName(rs.getString("V_SUPPLER_NAME"));
            supplierDetailsMasterDto.setSupplierAddressLine1(rs.getString("V_SUPPLIER_ADDRESS_LINE1"));
            supplierDetailsMasterDto.setSupplierAddressLine2(rs.getString("V_SUPPLIER_ADDRESS_LINE2"));
            supplierDetailsMasterDto.setSupplierAddressLine3(rs.getString("V_SUPPLIER_ADDRESS_LINE3"));
            supplierDetailsMasterDto.setContactNo(rs.getString("V_CONTACT_NO"));
            supplierDetailsMasterDto.setContactPerson(rs.getString("V_CONTACT_PERSON"));
            supplierDetailsMasterDto.setEmail(rs.getString("V_EMAIL"));
            supplierDetailsMasterDto.setRecordStatus(rs.getString("V_RECORD_STATUS"));
            supplierDetailsMasterDto.setInputUserId(rs.getString("V_INPUT_USER_ID"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return supplierDetailsMasterDto;
    }
}
