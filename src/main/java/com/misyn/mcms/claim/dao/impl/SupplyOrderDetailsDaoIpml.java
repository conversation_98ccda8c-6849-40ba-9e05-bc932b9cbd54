package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.SupplyOrderDetailsDao;
import com.misyn.mcms.claim.dto.SupplyOrderDetailsDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class SupplyOrderDetailsDaoIpml implements SupplyOrderDetailsDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(SupplyOrderDetailsDaoIpml.class);

    @Override
    public SupplyOrderDetailsDto insertMaster(Connection connection, SupplyOrderDetailsDto supplyOrderDetailsDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(INSERT_CLAIM_SUPPLY_ORDER_DETAILS)) {
            ps.setInt(++index, supplyOrderDetailsDto.getSupplyOrderRefNo());
            ps.setInt(++index, supplyOrderDetailsDto.getSparePartRefNo());
            ps.setInt(++index, supplyOrderDetailsDto.getQuantity());
            ps.setBigDecimal(++index, supplyOrderDetailsDto.getIndividualPrice());
            ps.setBigDecimal(++index, supplyOrderDetailsDto.getOaRate());
            ps.setBigDecimal(++index, supplyOrderDetailsDto.getTotalAmount());
            ps.setInt(++index, supplyOrderDetailsDto.getIndex());
            ps.setBoolean(++index, Boolean.TRUE.equals(supplyOrderDetailsDto.getIsPendingIndividualPrice()));

            if (ps.executeUpdate() > 0) {
                return supplyOrderDetailsDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public SupplyOrderDetailsDto updateMaster(Connection connection, SupplyOrderDetailsDto supplyOrderDetailsDto) throws Exception {
        return null;
    }

    @Override
    public SupplyOrderDetailsDto insertTemporary(Connection connection, SupplyOrderDetailsDto supplyOrderDetailsDto) throws Exception {
        return null;
    }

    @Override
    public SupplyOrderDetailsDto updateTemporary(Connection connection, SupplyOrderDetailsDto supplyOrderDetailsDto) throws Exception {
        return null;
    }

    @Override
    public SupplyOrderDetailsDto insertHistory(Connection connection, SupplyOrderDetailsDto supplyOrderDetailsDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public SupplyOrderDetailsDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public SupplyOrderDetailsDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<SupplyOrderDetailsDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public List<SupplyOrderDetailsDto> searchAllBySupplyOrderDetailsRefNo(Connection connection, Integer SupplyOrderDetailsRefNo) throws Exception {
        int index = 1;
        List<SupplyOrderDetailsDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SEARCH_CLAIM_SUPPLY_ORDER_DETAILS);
            ps.setInt(1, SupplyOrderDetailsRefNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                SupplyOrderDetailsDto supplyOrderDetailsDto = new SupplyOrderDetailsDto();
                supplyOrderDetailsDto.setIndex(index++);
                supplyOrderDetailsDto.setRefNo(rs.getInt("t1.n_ref_no"));
                supplyOrderDetailsDto.setSupplyOrderRefNo(rs.getInt("t1.n_supply_order_ref_no"));
                supplyOrderDetailsDto.setSparePartRefNo(rs.getInt("t1.n_spare_part_ref_no"));
                supplyOrderDetailsDto.setSparePartName(rs.getString("t2.v_spare_part_name"));
                supplyOrderDetailsDto.setQuantity(rs.getInt("t1.n_qunatity"));
                supplyOrderDetailsDto.setIndividualPrice(rs.getBigDecimal("t1.n_individual_price"));
                supplyOrderDetailsDto.setOaRate(rs.getBigDecimal("t1.n_oa_rate"));
                supplyOrderDetailsDto.setTotalAmount(rs.getBigDecimal("t1.n_total_amount"));
                supplyOrderDetailsDto.setIsPendingIndividualPrice(rs.getBoolean("t1.is_pending_individual_price"));

                list.add(supplyOrderDetailsDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public void deleteSupplyOrderDetailsRefNo(Connection connection, Integer SupplyOrderDetailsRefNo) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(DELETE_CLAIM_SUPPLY_ORDER_DETAILS)) {
            ps.setInt(1, SupplyOrderDetailsRefNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
    }
}
