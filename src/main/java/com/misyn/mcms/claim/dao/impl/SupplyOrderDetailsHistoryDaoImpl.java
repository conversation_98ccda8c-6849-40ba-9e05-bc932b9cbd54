package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.SupplyOrderDetailsHistoryDao;
import com.misyn.mcms.claim.dto.SupplyOrderDetailsDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class SupplyOrderDetailsHistoryDaoImpl implements SupplyOrderDetailsHistoryDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(SupplyOrderHistoryDaoImpl.class);

    @Override
    public List<SupplyOrderDetailsDto> searchAllByRefNo(Connection connection, Integer supplyOrderRefNo) throws Exception {
        int index = 1;
        List<SupplyOrderDetailsDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SEARCH_CLAIM_SUPPLY_ORDER_DETAILS_HISTORY);
            ps.setInt(1, supplyOrderRefNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                SupplyOrderDetailsDto supplyOrderDetailsDto = new SupplyOrderDetailsDto();
                supplyOrderDetailsDto.setIndex(index++);
                supplyOrderDetailsDto.setRefNo(rs.getInt("t1.n_ref_no"));
                supplyOrderDetailsDto.setSupplyOrderRefNo(rs.getInt("t1.n_supply_order_ref_no"));
                supplyOrderDetailsDto.setSparePartRefNo(rs.getInt("t1.n_spare_part_ref_no"));
                supplyOrderDetailsDto.setSparePartName(rs.getString("t2.v_spare_part_name"));
                supplyOrderDetailsDto.setQuantity(rs.getInt("t1.n_qunatity"));
                supplyOrderDetailsDto.setIndividualPrice(rs.getBigDecimal("t1.n_individual_price"));
                supplyOrderDetailsDto.setOaRate(rs.getBigDecimal("t1.n_oa_rate"));
                supplyOrderDetailsDto.setTotalAmount(rs.getBigDecimal("t1.n_total_amount"));

                list.add(supplyOrderDetailsDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }
}
