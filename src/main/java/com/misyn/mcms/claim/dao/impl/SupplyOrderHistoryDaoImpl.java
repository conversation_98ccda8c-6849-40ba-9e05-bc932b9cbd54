package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.SupplyOrderHistoryDao;
import com.misyn.mcms.claim.dto.SupplyOrderSummaryDto;
import com.misyn.mcms.claim.enums.PolicyChannelType;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
public class SupplyOrderHistoryDaoImpl implements SupplyOrderHistoryDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(SupplyOrderHistoryDaoImpl.class);

    @Override
    public SupplyOrderSummaryDto searchByRefNo(Connection connection, Integer supplyOrderRefNo, String policyChannelType) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? SEARCH_TAKAFUL_CLAIM_SUPPLY_ORDER_HISTORY_BY_SUPPLY_ORDER_REF_NO : SEARCH_CLAIM_SUPPLY_ORDER_HISTORY_BY_SUPPLY_ORDER_REF_NO);
            ps.setInt(1, supplyOrderRefNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                SupplyOrderSummaryDto supplyOrderSummaryDto = getSupplyOrderSummary(rs, "t1.", connection);
                supplyOrderSummaryDto.setSupplierName(rs.getString("t.V_COMPANY_NAME"));
                return supplyOrderSummaryDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return null;
    }

    @Override
    public void shiftDoToHistory(Connection connection, Integer refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SHIFT_MASTER_TO_HISTORY);
            ps.setInt(1, refNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void shiftDoDetailsToHistory(Connection connection, Integer supplyOrderRefNo) throws Exception {
        PreparedStatement preparedStatement = null;
        try {
            preparedStatement = connection.prepareStatement(SHIFT_DO_DETAILS_TO_HISTORY);
            preparedStatement.setInt(1, supplyOrderRefNo);
            preparedStatement.executeUpdate();
            preparedStatement.close();
            preparedStatement = connection.prepareStatement(UPDATE_MAX_DETAIL_ID);
            preparedStatement.executeUpdate();
            preparedStatement.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    private SupplyOrderSummaryDto getSupplyOrderSummary(ResultSet rs, String tbl, Connection connection) {
        SupplyOrderSummaryDto supplyOrderSummaryDto = new SupplyOrderSummaryDto();
        try {
            supplyOrderSummaryDto.setSupplyOrderRefNo(rs.getInt(tbl.concat("n_supply_order_ref_no")));
            supplyOrderSummaryDto.setClaimNo(rs.getInt(tbl.concat("n_claim_no")));
            supplyOrderSummaryDto.setSupplierId(rs.getInt(tbl.concat("n_supplier_id")));
            supplyOrderSummaryDto.setSupplyOrderSerialNo(rs.getString(tbl.concat("v_supply_order_serial_no")));
            supplyOrderSummaryDto.setSupplyOrderStatus(rs.getString(tbl.concat("v_supply_order_status")));
            supplyOrderSummaryDto.setSupplierEmail(rs.getString(tbl.concat("v_supplier_email")));
            supplyOrderSummaryDto.setSupplierContactNo(rs.getString(tbl.concat("v_supplier_contact_no")));
            supplyOrderSummaryDto.setTotalAmount(rs.getBigDecimal(tbl.concat("n_total_amount")));
            supplyOrderSummaryDto.setTotalOwnersAccountAmount(rs.getBigDecimal(tbl.concat("n_total_owners_account_amount")));
            supplyOrderSummaryDto.setOthertDeductionAmount(rs.getBigDecimal(tbl.concat("n_other_deduction")));
            supplyOrderSummaryDto.setPolicyExcess(rs.getBigDecimal(tbl.concat("n_policy_excess")));
            supplyOrderSummaryDto.setFinalAmount(rs.getBigDecimal(tbl.concat("n_final_amount")));
            supplyOrderSummaryDto.setSupplyOrderRemark(rs.getString(tbl.concat("v_supply_order_remark")));
            supplyOrderSummaryDto.setOtherRemark(rs.getString(tbl.concat("v_other_remark")));
            supplyOrderSummaryDto.setWorkShopName(rs.getString(tbl.concat("v_work_shop_name")));
            supplyOrderSummaryDto.setWorkShopAddress1(rs.getString(tbl.concat("v_work_shop_address1")));
            supplyOrderSummaryDto.setWorkShopAddress2(rs.getString(tbl.concat("v_work_shop_address2")));
            supplyOrderSummaryDto.setWorkShopAddress3(rs.getString(tbl.concat("v_work_shop_address3")));
            supplyOrderSummaryDto.setWorkShopContactNo(rs.getString(tbl.concat("v_work_shop_contact_no")));
            supplyOrderSummaryDto.setApproveAssignSparePartCoordinator(rs.getString(tbl.concat("v_apprv_assign_spare_part_coordinator")));
            supplyOrderSummaryDto.setApproveAssignSparePartCoordinatorDateTime(rs.getString(tbl.concat("d_apprv_assign_spare_part_coordinator_date_time")));
            supplyOrderSummaryDto.setInputUserId(rs.getString(tbl.concat("v_input_user_id")));
            supplyOrderSummaryDto.setInputDateTime(rs.getString(tbl.concat("d_input_date_time")));
            supplyOrderSummaryDto.setApprvAssignScrutinizingUserId(rs.getString(tbl.concat("v_apprv_assign_scrutinizing_user_id")));
            supplyOrderSummaryDto.setApprvAssignScrutinizingDateTime(rs.getString(tbl.concat("d_apprv_assign_scrutinizing_date_time")));
            supplyOrderSummaryDto.setApprvScrutinizingUserId(rs.getString(tbl.concat("v_apprv_scrutinizing_user_id")));
            supplyOrderSummaryDto.setApprvScrutinizingDateTime(rs.getString(tbl.concat("v_apprv_scrutinizing_date_time")));
            supplyOrderSummaryDto.setApprvAssignSpecialTeamUserId(rs.getString(tbl.concat("v_apprv_assign_special_team_user_id")));
            supplyOrderSummaryDto.setApprvAssignSpecialTeamDateTime(rs.getString(tbl.concat("d_apprv_assign_special_team_date_time")));
            supplyOrderSummaryDto.setApprvSpecialTeamUserId(rs.getString(tbl.concat("v_apprv_special_team_user_id")));
            supplyOrderSummaryDto.setApprvSpecialTeamDateTime(rs.getString(tbl.concat("v_apprv_special_team_date_time")));
            supplyOrderSummaryDto.setApprvClaimHandlerUserId(rs.getString(tbl.concat("v_apprv_claim_handler_user_id")));
            supplyOrderSummaryDto.setApprvClaimHandlerDateTime(rs.getString(tbl.concat("d_apprv_claim_handler_date_time")));
            supplyOrderSummaryDto.setGenerateUserId(rs.getString(tbl.concat("v_generate_user_id")));
            supplyOrderSummaryDto.setGenerateDateTime(rs.getString(tbl.concat("d_generate_date_time")));
            supplyOrderSummaryDto.setIsGenerate(rs.getString(tbl.concat("v_is_generate")));
            supplyOrderSummaryDto.setSupplyOrderDetailRefNo(rs.getInt(tbl.concat("n_supply_order_detail_ref_no")));

            supplyOrderSummaryDto.setVatAmount(null == rs.getBigDecimal(tbl.concat("n_vat_amount")) ? BigDecimal.ZERO : rs.getBigDecimal(tbl.concat("n_vat_amount")));
            supplyOrderSummaryDto.setVatStatus(null == rs.getString(tbl.concat("v_vat_status")) ? AppConstant.STRING_EMPTY : rs.getString(tbl.concat("v_vat_status")));
            if (supplyOrderSummaryDto.getInputUserId() != null) {
                supplyOrderSummaryDto.setInputUserFullName(getFullNameDetails(connection, supplyOrderSummaryDto.getInputUserId()));
            }
            if (supplyOrderSummaryDto.getApprvScrutinizingUserId() != null) {
                supplyOrderSummaryDto.setApprvScrutinizingUserFullName(getFullNameDetails(connection, supplyOrderSummaryDto.getApprvScrutinizingUserId()));
            }
            if (supplyOrderSummaryDto.getApprvClaimHandlerUserId() != null) {
                supplyOrderSummaryDto.setApprvClaimHandlerUserFullName(getFullNameDetails(connection, supplyOrderSummaryDto.getApprvClaimHandlerUserId()));
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return supplyOrderSummaryDto;
    }

    private String getFullNameDetails(Connection connection, String userId) {
        PreparedStatement ps;
        ResultSet rs;
        String fullName = AppConstant.STRING_EMPTY;
        try {
            ps = connection.prepareStatement("SELECT CONCAT(v_title,' ',v_firstname,' ',v_lastname) AS full_name FROM usr_mst WHERE v_usrid=?");
            ps.setString(1, userId);
            rs = ps.executeQuery();
            if (rs.next()) {
                fullName = rs.getString("full_name");
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return fullName;
    }
}
