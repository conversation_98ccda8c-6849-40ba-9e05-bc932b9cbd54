package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.SupplyOrderRequestDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
public class SupplyOrderRequestDaoImpl implements SupplyOrderRequestDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(SupplyOrderRequestDaoImpl.class);

    @Override
    public Integer getPendingDO(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        Integer refId = null;
        try {
            ps = connection.prepareStatement(IS_AVAILABLE_PENDING_DO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                refId = rs.getInt("N_REF_NO");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return refId;
    }

    @Override
    public void newDoRequest(Connection connection, Integer claimNo, Integer refNo) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(INSERT_DO_REQUEST);
            ps.setInt(1, claimNo);
            ps.setInt(2, refNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void updateDoRequest(Connection connection, Integer claimNo, Integer supplyOrderRefNo) throws Exception {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(UPDATE_DO_REQUEST);
            ps.setInt(1, supplyOrderRefNo);
            ps.setInt(2, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }
}
