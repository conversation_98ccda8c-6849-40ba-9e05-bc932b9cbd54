package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.SupplyOrderSummaryDao;
import com.misyn.mcms.claim.dto.ClaimCalculationSheetMainDto;
import com.misyn.mcms.claim.dto.SupplyOrderSummaryDto;
import com.misyn.mcms.claim.dto.VatRateDto;
import com.misyn.mcms.claim.enums.PolicyChannelType;
import com.misyn.mcms.claim.enums.SupplyOrderStatusEnum;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
public class SupplyOrderSummaryDaoImpl implements SupplyOrderSummaryDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(SupplyOrderSummaryDaoImpl.class);

    @Override
    public SupplyOrderSummaryDto insertMaster(Connection connection, SupplyOrderSummaryDto supplyOrderSummaryDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(INSERT_CLAIM_SUPPLY_ORDER_SUMMARY, Statement.RETURN_GENERATED_KEYS)) {
//            Integer supplyOrderSerial = getSequenceSupplyOrderSerialNo(connection);
//            supplyOrderSummaryDto.setSupplyOrderSerialNo(Utility.getCustomDateFormat(supplyOrderSummaryDto.getInputDateTime(), "yyyy-MM-dd", "yyyy/MM/dd") + "/" + Utility.addZeroRJ(String.valueOf(supplyOrderSerial), 5));

            ps.setInt(++index, supplyOrderSummaryDto.getClaimNo());
            ps.setInt(++index, supplyOrderSummaryDto.getSupplierId());
            ps.setString(++index, supplyOrderSummaryDto.getSupplyOrderSerialNo());
            ps.setString(++index, supplyOrderSummaryDto.getSupplyOrderStatus());
            ps.setString(++index, supplyOrderSummaryDto.getSupplierEmail());
            ps.setString(++index, supplyOrderSummaryDto.getSupplierContactNo());
            ps.setBigDecimal(++index, supplyOrderSummaryDto.getTotalAmount());
            ps.setBigDecimal(++index, supplyOrderSummaryDto.getTotalOwnersAccountAmount());
            ps.setBigDecimal(++index, supplyOrderSummaryDto.getOthertDeductionAmount());
            ps.setBigDecimal(++index, supplyOrderSummaryDto.getPolicyExcess());
            ps.setBigDecimal(++index, supplyOrderSummaryDto.getFinalAmount());
            ps.setString(++index, supplyOrderSummaryDto.getSupplyOrderRemark());
            ps.setString(++index, supplyOrderSummaryDto.getOtherRemark());
            ps.setString(++index, supplyOrderSummaryDto.getWorkShopName());
            ps.setString(++index, supplyOrderSummaryDto.getWorkShopAddress1());
            ps.setString(++index, supplyOrderSummaryDto.getWorkShopAddress2());
            ps.setString(++index, supplyOrderSummaryDto.getWorkShopAddress3());
            ps.setString(++index, supplyOrderSummaryDto.getWorkShopContactNo());
            ps.setString(++index, supplyOrderSummaryDto.getApproveAssignSparePartCoordinator());
            ps.setString(++index, supplyOrderSummaryDto.getApproveAssignSparePartCoordinatorDateTime());
            ps.setString(++index, supplyOrderSummaryDto.getInputUserId());
            ps.setString(++index, supplyOrderSummaryDto.getInputDateTime());
            ps.setString(++index, supplyOrderSummaryDto.getApprvAssignScrutinizingUserId());
            ps.setString(++index, supplyOrderSummaryDto.getApprvAssignScrutinizingDateTime());
            ps.setString(++index, supplyOrderSummaryDto.getApprvScrutinizingUserId());
            ps.setString(++index, supplyOrderSummaryDto.getApprvScrutinizingDateTime());
            ps.setString(++index, supplyOrderSummaryDto.getApprvAssignSpecialTeamUserId());
            ps.setString(++index, supplyOrderSummaryDto.getApprvAssignSpecialTeamDateTime());
            ps.setString(++index, supplyOrderSummaryDto.getApprvSpecialTeamUserId());
            ps.setString(++index, supplyOrderSummaryDto.getApprvSpecialTeamDateTime());
            ps.setString(++index, supplyOrderSummaryDto.getApprvClaimHandlerUserId());
            ps.setString(++index, supplyOrderSummaryDto.getApprvClaimHandlerDateTime());
            ps.setString(++index, supplyOrderSummaryDto.getGenerateUserId());
            ps.setString(++index, supplyOrderSummaryDto.getGenerateDateTime());
            ps.setString(++index, supplyOrderSummaryDto.getIsGenerate());
            ps.setBigDecimal(++index, supplyOrderSummaryDto.getVatAmount());
            ps.setString(++index, supplyOrderSummaryDto.getVatStatus());
            ps.setString(++index, supplyOrderSummaryDto.getIsUpdated());
//            TODO
            ps.setString(++index, "N");
            ps.setString(++index, "N");
            ps.setString(++index, "N");
            ps.setString(++index, "N");

            if (ps.executeUpdate() > 0) {
                ResultSet rsKeys = ps.getGeneratedKeys();
                if (rsKeys.next()) {
                    int autoGeneratedId = rsKeys.getInt(1);
                    supplyOrderSummaryDto.setSupplyOrderRefNo(autoGeneratedId);
//                    this.updateSequenceSupplyOrderSerialNo(connection, supplyOrderSerial);
                    return supplyOrderSummaryDto;
                }
                rsKeys.close();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return null;
    }

    @Override
    public SupplyOrderSummaryDto updateMaster(Connection connection, SupplyOrderSummaryDto supplyOrderSummaryDto) throws Exception {
        int index = 0;
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_CLAIM_SUPPLY_ORDER_SUMMARY)) {
            ps.setInt(++index, supplyOrderSummaryDto.getClaimNo());
            ps.setInt(++index, supplyOrderSummaryDto.getSupplierId());
            ps.setString(++index, supplyOrderSummaryDto.getSupplyOrderStatus());
            ps.setString(++index, supplyOrderSummaryDto.getSupplierEmail());
            ps.setString(++index, supplyOrderSummaryDto.getSupplierContactNo());
            ps.setBigDecimal(++index, supplyOrderSummaryDto.getTotalAmount());
            ps.setBigDecimal(++index, supplyOrderSummaryDto.getTotalOwnersAccountAmount());
            ps.setBigDecimal(++index, supplyOrderSummaryDto.getOthertDeductionAmount());
            ps.setBigDecimal(++index, supplyOrderSummaryDto.getPolicyExcess());
            ps.setBigDecimal(++index, supplyOrderSummaryDto.getFinalAmount());
            ps.setString(++index, supplyOrderSummaryDto.getSupplyOrderRemark());
            ps.setString(++index, supplyOrderSummaryDto.getOtherRemark());
            ps.setString(++index, supplyOrderSummaryDto.getWorkShopName());
            ps.setString(++index, supplyOrderSummaryDto.getWorkShopAddress1());
            ps.setString(++index, supplyOrderSummaryDto.getWorkShopAddress2());
            ps.setString(++index, supplyOrderSummaryDto.getWorkShopAddress3());
            ps.setString(++index, supplyOrderSummaryDto.getWorkShopContactNo());
            ps.setString(++index, supplyOrderSummaryDto.getInputUserId());
            ps.setString(++index, supplyOrderSummaryDto.getInputDateTime());
            ps.setString(++index, supplyOrderSummaryDto.getApprvScrutinizingUserId());
            ps.setString(++index, supplyOrderSummaryDto.getApprvScrutinizingDateTime());
            ps.setBigDecimal(++index, supplyOrderSummaryDto.getVatAmount());
            ps.setString(++index, supplyOrderSummaryDto.getVatStatus());
            ps.setString(++index, supplyOrderSummaryDto.getApprvClaimHandlerUserId());
            ps.setString(++index, supplyOrderSummaryDto.getApprvClaimHandlerDateTime());
            ps.setString(++index, supplyOrderSummaryDto.getGenerateUserId());
            ps.setString(++index, supplyOrderSummaryDto.getGenerateDateTime());
            ps.setString(++index, supplyOrderSummaryDto.getIsGenerate());
            ps.setString(++index, supplyOrderSummaryDto.getIsUpdated());
            ps.setInt(++index, supplyOrderSummaryDto.getSupplyOrderRefNo());

            if (ps.executeUpdate() > 0) {
                return supplyOrderSummaryDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        }
        return null;
    }


    @Override
    public SupplyOrderSummaryDto searchMaster(Connection connection, Object id) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(SEARCH_CLAIM_SUPPLY_ORDER_SUMMARY)) {
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return getSupplyOrderSummary(rs, AppConstant.STRING_EMPTY, connection);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public SupplyOrderSummaryDto searchMasterBySupplyOrderRefNo(Connection connection, Object id) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(SEARCH_CLAIM_SUPPLY_ORDER_SUMMARY_BY_N_SUPPLY_ORDER_REF_NO)) {
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return getSupplyOrderSummary(rs, AppConstant.STRING_EMPTY, connection);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }


    @Override
    public SupplyOrderSummaryDto searchBysupplyOrderRefNo(Connection connection, Integer supplyOrderRefNo) throws Exception {
        try {
            String policyChannelType = getChannelTypeByClaimOrRefNo(connection, null, supplyOrderRefNo);
            PreparedStatement ps = connection.prepareStatement(policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? SEARCH_TAKAFUL_CLAIM_SUPPLY_ORDER_SUMMARY_BY_SUPPLY_ORDER_REF_NO : SEARCH_CLAIM_SUPPLY_ORDER_SUMMARY_BY_SUPPLY_ORDER_REF_NO);
            ps.setObject(1, supplyOrderRefNo);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                SupplyOrderSummaryDto supplyOrderSummaryDto = getSupplyOrderSummary(rs, "t1.", connection);
                supplyOrderSummaryDto.setSupplierName(rs.getString("t.V_COMPANY_NAME"));
                return supplyOrderSummaryDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    private String getFullNameDetails(Connection connection, String userId) {
        PreparedStatement ps;
        ResultSet rs;
        String fullName = AppConstant.STRING_EMPTY;
        try {
            ps = connection.prepareStatement("SELECT CONCAT(v_title,' ',v_firstname,' ',v_lastname) AS full_name FROM usr_mst WHERE v_usrid=?");
            ps.setString(1, userId);
            rs = ps.executeQuery();
            if (rs.next()) {
                fullName = rs.getString("full_name");
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return fullName;
    }


    private SupplyOrderSummaryDto getSupplyOrderSummary(ResultSet rs, String tbl, Connection connection) {
        SupplyOrderSummaryDto supplyOrderSummaryDto = new SupplyOrderSummaryDto();
        try {
            supplyOrderSummaryDto.setSupplyOrderRefNo(rs.getInt(tbl.concat("n_supply_order_ref_no")));
            supplyOrderSummaryDto.setClaimNo(rs.getInt(tbl.concat("n_claim_no")));
            supplyOrderSummaryDto.setSupplierId(rs.getInt(tbl.concat("n_supplier_id")));
            supplyOrderSummaryDto.setSupplyOrderSerialNo(rs.getString(tbl.concat("v_supply_order_serial_no")));
            supplyOrderSummaryDto.setSupplyOrderStatus(rs.getString(tbl.concat("v_supply_order_status")));
            supplyOrderSummaryDto.setSupplierEmail(rs.getString(tbl.concat("v_supplier_email")));
            supplyOrderSummaryDto.setSupplierContactNo(rs.getString(tbl.concat("v_supplier_contact_no")));
            supplyOrderSummaryDto.setTotalAmount(rs.getBigDecimal(tbl.concat("n_total_amount")));
            supplyOrderSummaryDto.setTotalOwnersAccountAmount(rs.getBigDecimal(tbl.concat("n_total_owners_account_amount")));
            supplyOrderSummaryDto.setOthertDeductionAmount(rs.getBigDecimal(tbl.concat("n_other_deduction")));
            supplyOrderSummaryDto.setPolicyExcess(rs.getBigDecimal(tbl.concat("n_policy_excess")));
            supplyOrderSummaryDto.setFinalAmount(rs.getBigDecimal(tbl.concat("n_final_amount")));
            supplyOrderSummaryDto.setSupplyOrderRemark(rs.getString(tbl.concat("v_supply_order_remark")));
            supplyOrderSummaryDto.setOtherRemark(rs.getString(tbl.concat("v_other_remark")));
            supplyOrderSummaryDto.setWorkShopName(rs.getString(tbl.concat("v_work_shop_name")));
            supplyOrderSummaryDto.setWorkShopAddress1(rs.getString(tbl.concat("v_work_shop_address1")));
            supplyOrderSummaryDto.setWorkShopAddress2(rs.getString(tbl.concat("v_work_shop_address2")));
            supplyOrderSummaryDto.setWorkShopAddress3(rs.getString(tbl.concat("v_work_shop_address3")));
            supplyOrderSummaryDto.setWorkShopContactNo(rs.getString(tbl.concat("v_work_shop_contact_no")));
            supplyOrderSummaryDto.setApproveAssignSparePartCoordinator(rs.getString(tbl.concat("v_apprv_assign_spare_part_coordinator")));
            supplyOrderSummaryDto.setApproveAssignSparePartCoordinatorDateTime(rs.getString(tbl.concat("d_apprv_assign_spare_part_coordinator_date_time")));
            supplyOrderSummaryDto.setInputUserId(rs.getString(tbl.concat("v_input_user_id")));
            supplyOrderSummaryDto.setInputDateTime(rs.getString(tbl.concat("d_input_date_time")));
            supplyOrderSummaryDto.setApprvAssignScrutinizingUserId(rs.getString(tbl.concat("v_apprv_assign_scrutinizing_user_id")));
            supplyOrderSummaryDto.setApprvAssignScrutinizingDateTime(rs.getString(tbl.concat("d_apprv_assign_scrutinizing_date_time")));
            supplyOrderSummaryDto.setApprvScrutinizingUserId(rs.getString(tbl.concat("v_apprv_scrutinizing_user_id")));
            supplyOrderSummaryDto.setApprvScrutinizingDateTime(rs.getString(tbl.concat("v_apprv_scrutinizing_date_time")));
            supplyOrderSummaryDto.setApprvAssignSpecialTeamUserId(rs.getString(tbl.concat("v_apprv_assign_special_team_user_id")));
            supplyOrderSummaryDto.setApprvAssignSpecialTeamDateTime(rs.getString(tbl.concat("d_apprv_assign_special_team_date_time")));
            supplyOrderSummaryDto.setApprvSpecialTeamUserId(rs.getString(tbl.concat("v_apprv_special_team_user_id")));
            supplyOrderSummaryDto.setApprvSpecialTeamDateTime(rs.getString(tbl.concat("v_apprv_special_team_date_time")));
            supplyOrderSummaryDto.setApprvClaimHandlerUserId(rs.getString(tbl.concat("v_apprv_claim_handler_user_id")));
            supplyOrderSummaryDto.setApprvClaimHandlerDateTime(rs.getString(tbl.concat("d_apprv_claim_handler_date_time")));
            supplyOrderSummaryDto.setGenerateUserId(rs.getString(tbl.concat("v_generate_user_id")));
            supplyOrderSummaryDto.setGenerateDateTime(rs.getString(tbl.concat("d_generate_date_time")));
            supplyOrderSummaryDto.setIsGenerate(rs.getString(tbl.concat("v_is_generate")));
            supplyOrderSummaryDto.setIsUpdated(rs.getString(tbl.concat("v_is_updated")));
//            supplyOrderSummaryDto.setVatAmount(rs.getBigDecimal(tbl.concat("n_vat_amount")));
//            supplyOrderSummaryDto.setVatStatus(rs.getString(tbl.concat("v_vat_status")));

            supplyOrderSummaryDto.setVatAmount(null == rs.getBigDecimal(tbl.concat("n_vat_amount")) ? BigDecimal.ZERO : rs.getBigDecimal(tbl.concat("n_vat_amount")));
            supplyOrderSummaryDto.setVatStatus(null == rs.getString(tbl.concat("v_vat_status")) ? AppConstant.STRING_EMPTY : rs.getString(tbl.concat("v_vat_status")));
            if (supplyOrderSummaryDto.getInputUserId() != null) {
                supplyOrderSummaryDto.setInputUserFullName(getFullNameDetails(connection, supplyOrderSummaryDto.getInputUserId()));
            }
            if (supplyOrderSummaryDto.getApprvScrutinizingUserId() != null) {
                supplyOrderSummaryDto.setApprvScrutinizingUserFullName(getFullNameDetails(connection, supplyOrderSummaryDto.getApprvScrutinizingUserId()));
            }
            if (supplyOrderSummaryDto.getApprvClaimHandlerUserId() != null) {
                supplyOrderSummaryDto.setApprvClaimHandlerUserFullName(getFullNameDetails(connection, supplyOrderSummaryDto.getApprvClaimHandlerUserId()));
            }


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return supplyOrderSummaryDto;
    }

    @Override
    public void deleteClaimSupplyOrderSummary(Connection connection, Integer supplyOrderRefNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(DELETE_CLAIM_SUPPLY_ORDER_SUMMARY);
            ps.setInt(1, supplyOrderRefNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public List<SupplyOrderSummaryDto> searchClaimSupplyOrderSummary(Connection connection, Integer claimNo, Integer refNo) throws Exception {
        List<SupplyOrderSummaryDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        boolean isGen;
        try {
            ps = connection.prepareStatement(IS_GENERATED);
            ps.setInt(1, refNo);
            rs = ps.executeQuery();
            isGen = rs.next();
            ps.close();
            rs.close();
            String policyChannelType = getChannelTypeByClaimOrRefNo(connection, claimNo, null);
            ps = connection.prepareStatement(policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? SEARCH_TAKAFUL_CLAIM_SUPPLY_ORDER_SUMMARY_CLAIM_NO_AND_IN_SUPPLY_STATUS : SEARCH_CLAIM_SUPPLY_ORDER_SUMMARY_CLAIM_NO_AND_IN_SUPPLY_STATUS);
            ps.setInt(1, claimNo);
            ps.setInt(2, isGen ? AppConstant.ZERO_INT : refNo);
            rs = ps.executeQuery();
            int index = 0;
            while (rs.next()) {
                ++index;
                populateSummaryDto(rs, index, list, true);
            }
            ps.close();
            rs.close();
            ps = connection.prepareStatement(policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? SEARCH_TAKAFUL_SUPPLY_ORDER_HISTORY : SEARCH_SUPPLY_ORDER_HISTORY);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ++index;
                populateSummaryDto(rs, index, list, false);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    private List<SupplyOrderSummaryDto> populateSummaryDto(ResultSet rs, Integer index, List<SupplyOrderSummaryDto> list, boolean isMasterRecord) {
        try {
            SupplyOrderSummaryDto supplyOrderSummaryDto = new SupplyOrderSummaryDto();
            supplyOrderSummaryDto.setNo(index);
            supplyOrderSummaryDto.setSupplyOrderRefNo(isMasterRecord ? rs.getInt("t1.n_supply_order_ref_no") : rs.getInt("t1.n_ref_no"));
            supplyOrderSummaryDto.setClaimNo(rs.getInt("t1.n_claim_no"));
            supplyOrderSummaryDto.setSupplyOrderSerialNo(rs.getString("t1.v_supply_order_serial_no"));
            supplyOrderSummaryDto.setSupplierName(rs.getString("t2.V_COMPANY_NAME"));
            supplyOrderSummaryDto.setGenerateUserId(rs.getString("t1.v_generate_user_id"));
            supplyOrderSummaryDto.setGenerateDateTime(Utility.getDate(rs.getString("t1.d_generate_date_time"), AppConstant.DATE_TIME_FORMAT));
            supplyOrderSummaryDto.setInputDateTime(Utility.getDate(rs.getString("t1.d_input_date_time"), AppConstant.DATE_TIME_FORMAT));
            supplyOrderSummaryDto.setMasterRecord(isMasterRecord ? AppConstant.YES : AppConstant.NO);
            list.add(supplyOrderSummaryDto);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return list;
    }

    @Override
    public Integer getMaxSupplyOrderRefNo(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        Integer maxId = 0;
        try {
            ps = connection.prepareStatement(SELECT_MAX_ID);
            ps.setInt(1, claimNo);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    maxId = rs.getInt("max_id");
                }
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return maxId;
    }

    @Override
    public void updateSupplyOrderScrTeamDetails(Connection connection, Integer supplyOrderRefNo, SupplyOrderStatusEnum supplyOrderStatusEnum, String apprvAssignScrutinizingUserId, String apprvAssignScrutinizingDateTime) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_SUPPLY_ORDER_SUMMARY_SCRUTINIZING_DETAILS_ON_DO_UPDATE);
            ps.setString(1, supplyOrderStatusEnum.getSupplyOrderStatusEnum());
            ps.setString(2, apprvAssignScrutinizingUserId);
            ps.setString(3, apprvAssignScrutinizingDateTime);
            ps.setString(4, AppConstant.STRING_EMPTY);
            ps.setString(5, AppConstant.DEFAULT_DATE_TIME);
            ps.setInt(6, supplyOrderRefNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateSupplyOrderReturnToSupCoord(Connection connection, Integer supplyOrderRefNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_SUPPLY_ORDER_SUMMARY_SCRUTINIZING_DETAILS);
            ps.setString(1, SupplyOrderStatusEnum.PENDING.getSupplyOrderStatusEnum());
            ps.setString(2, AppConstant.STRING_EMPTY);
            ps.setString(3, AppConstant.DEFAULT_DATE_TIME);
            ps.setInt(4, supplyOrderRefNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateSupplyOrderReturnToScrTeam(Connection connection, SupplyOrderStatusEnum supplyOrderStatusEnum, Integer supplyOrderRefNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_SUPPLY_ORDER_SUMMARY_SCRUTINIZING_DETAILS_BY_CLAIM_HANDLER_RETURN);
            ps.setString(1, supplyOrderStatusEnum.getSupplyOrderStatusEnum());
            ps.setString(2, AppConstant.DEFAULT_DATE_TIME);
            ps.setInt(3, supplyOrderRefNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateSupplyOrderScrTeamApprovedAndForwardClaimHandler(Connection connection, Integer supplyOrderRefNo,
                                                                       String apprvScrutinizingUserId,
                                                                       String apprvScrutinizingDateTime) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_SUPPLY_ORDER_SUMMARY_SCRUTINIZING_APPROVAL);
            ps.setString(1, SupplyOrderStatusEnum.APPROVED_SCRUTINIZING_TEAM.getSupplyOrderStatusEnum());
            ps.setString(2, apprvScrutinizingUserId);
            ps.setString(3, apprvScrutinizingDateTime);
            ps.setInt(4, supplyOrderRefNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateSupplyOrderForwardAndGenerate(Connection connection, Integer supplyOrderRefNo, String inputUser) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_SUPPLY_ORDER_SUMMARY_FORWARD_AND_GENERATE);
            ps.setString(1, SupplyOrderStatusEnum.APPROVED_CLAIM_HANDLER.getSupplyOrderStatusEnum());
            ps.setString(2, inputUser);
            ps.setString(3, Utility.sysDateTime());
            ps.setInt(4, supplyOrderRefNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void updateSupplyOrderGenerate(Connection connection, SupplyOrderSummaryDto supplyOrderSummaryDto) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_SUPPLY_ORDER_GENERATE);
            ps.setString(1, SupplyOrderStatusEnum.GENERATE.getSupplyOrderStatusEnum());
            ps.setString(2, supplyOrderSummaryDto.getGenerateUserId());
            ps.setString(3, supplyOrderSummaryDto.getGenerateDateTime());
            ps.setString(4, supplyOrderSummaryDto.getIsGenerate());
            ps.setInt(5, supplyOrderSummaryDto.getSupplyOrderRefNo());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }


    @Override
    public List<VatRateDto> getVateRateList(Connection connection) {
        List<VatRateDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_ALL_FROM_VATE_RATE);
            rs = ps.executeQuery();
            int index = 0;
            while (rs.next()) {
                VatRateDto vatRateDto = new VatRateDto();
                vatRateDto.setTxnId(rs.getInt("n_txn_id"));
                vatRateDto.setVatRate(rs.getBigDecimal("n_vat_rate"));
                vatRateDto.setStatus(rs.getString("v_status"));
                vatRateDto.setVatRateChangeDate(rs.getString("d_vat_rate_change_date"));
                vatRateDto.setVatRateEffectiveDate(rs.getString("d_vat_rate_effictive_date"));
                list.add(vatRateDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public String getSupplyOrderInputUser(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        String inputUser = null;
        try {
            ps = connection.prepareStatement(SELECT_INPUT_USER_ID_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    inputUser = rs.getString("v_input_user_id");
                    return inputUser;
                }
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
        return inputUser;
    }

    @Override
    public boolean isPendingSupplyOrder(Connection connection, Integer claimNo) throws Exception {

        boolean isPending = false;
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        ResultSet rs1 = null;
        ResultSet rs2;
        try {
            ps1 = connection.prepareStatement(SELECT_ALL_BY_CLAIM_NO);
            ps2 = connection.prepareStatement(SELECT_IS_PENDING_ORDER);
            ps1.setInt(1, claimNo);
            ps2.setInt(1, claimNo);
            rs1 = ps1.executeQuery();
            if (rs1.next()) {
                rs2 = ps2.executeQuery();
                if (rs2.next()) {
                    isPending = true;
                } else {
                    isPending = false;
                }
                rs2.close();
            } else {
                isPending = true;
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (rs1 != null) {
                    rs1.close();
                }
                if (ps1 != null) {
                    ps1.close();
                }
                if (ps2 != null) {
                    ps2.close();
                }
            } catch (Exception e) {
                LOGGER.error(e.getMessage());
            }
        }
        return isPending;
    }

    @Override
    public ClaimCalculationSheetMainDto getAvailableCalSheet(Connection connection, Integer supplyOrderRefNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ClaimCalculationSheetMainDto claimCalculationSheetMainDto = null;
        try {
            ps = connection.prepareStatement(SELECT_PENDING_CAL_SHEET);
            ps.setInt(1, supplyOrderRefNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                claimCalculationSheetMainDto = new ClaimCalculationSheetMainDto();
                claimCalculationSheetMainDto.setCalSheetId(rs.getInt("N_CAL_SHEET_ID"));
                claimCalculationSheetMainDto.setAssignUserId(rs.getString("V_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setStatus(rs.getInt("t2.V_STATUS"));
                claimCalculationSheetMainDto.setSpecialTeamAssignUserId(rs.getString("t2.V_SPECIAL_TEAM_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSpecialTeamAssignDateTime(rs.getString("D_SPECIAL_TEAM_ASSIGN_DATE_TIME"));
                claimCalculationSheetMainDto.setSpecialTeamMofaAssignUserId(rs.getString("V_SPECIAL_TEAM_MOFA_ASSIGN_USER_ID"));
                claimCalculationSheetMainDto.setSpecialTeamMofaAssignDateTime(rs.getString("D_SPECIAL_TEAM_MOFA_ASSIGN_DATE_TIME"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return claimCalculationSheetMainDto;
    }

    @Override
    public boolean updateSupplyOrderStatus(Connection connection, int refNo, String status) {
        PreparedStatement ps = null;
        boolean isUpdate = false;
        try {
            ps = connection.prepareStatement(UPDATE_SUPPLY_ORDER_SUMMARY_STATUS);
            ps.setString(1, status);
            ps.setInt(2, refNo);
            isUpdate = ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return isUpdate;
    }

    @Override
    public SupplyOrderSummaryDto getOngoingSupplyOrder(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        SupplyOrderSummaryDto summaryDto = null;
        try {
            ps = connection.prepareStatement(SELECT_ONGOING_SUPPLY_ORDER);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                summaryDto = new SupplyOrderSummaryDto();
                summaryDto.setSupplyOrderRefNo(rs.getInt("n_supply_order_ref_no"));
                summaryDto.setSupplyOrderStatus(rs.getString("v_supply_order_status"));
                summaryDto.setInputUserId(rs.getString("v_input_user_id"));
                summaryDto.setApprvAssignScrutinizingUserId(rs.getString("v_apprv_assign_scrutinizing_user_id"));
                summaryDto.setApproveAssignSparePartCoordinator(rs.getString("v_apprv_assign_spare_part_coordinator"));
                summaryDto.setApprvScrutinizingUserId(rs.getString("v_apprv_scrutinizing_user_id"));
                summaryDto.setApprvClaimHandlerUserId(rs.getString("v_apprv_claim_handler_user_id"));
            }
            ps.close();
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return summaryDto;
    }

    @Override
    public void updateSupplyOrderSpcoodDetails(Connection connection, Integer refNo, SupplyOrderStatusEnum forwardSparePartsCoordinator, String approveAssignSparePartCoordinator) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_SUPPLY_ORDER_SUMMARY_SUPPLY_ORDER_DETAILS);
            ps.setString(1, forwardSparePartsCoordinator.getSupplyOrderStatusEnum());
            ps.setString(2, approveAssignSparePartCoordinator);
            ps.setString(3, Utility.sysDate());
            ps.setString(4, AppConstant.STRING_EMPTY);
            ps.setString(5, AppConstant.DEFAULT_DATE_TIME);
            ps.setInt(6, refNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void updateApproveAndForwardBySPC(Connection connection, Integer refNo, String supplyOrderStatusEnum, String apprvAssignScrutinizingUserId, String approveAssignSparePartCoordinator) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_SUPPLY_ORDER_SUMMARY_SCRUTINIZING_FORWARD_DETAILS_BY_SPC);
            ps.setString(1, supplyOrderStatusEnum);
            ps.setString(2, apprvAssignScrutinizingUserId);
            ps.setString(3, Utility.sysDateTime());
            ps.setString(4, approveAssignSparePartCoordinator);
            ps.setString(5, Utility.sysDateTime());
            ps.setInt(6, refNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void supplyOrderUpprovedAndForwardToClaimHandler(Connection connection, Integer refNo, String apprvAssignScrutinizingUserId, String approveAssignSparePartCoordinator) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_SUPPLY_ORDER_APPROVE_AND_FORWARD_TO_CLAIM_HANDLER);
            ps.setString(1, SupplyOrderStatusEnum.APPROVED_SCRUTINIZING_TEAM.getSupplyOrderStatusEnum());
            ps.setString(2, approveAssignSparePartCoordinator);
            ps.setString(3, Utility.sysDateTime());
            ps.setString(4, apprvAssignScrutinizingUserId);
            ps.setString(5, Utility.sysDateTime());
            ps.setInt(6, refNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public SupplyOrderSummaryDto searchMasterUpdate(Connection connection, Integer supplyOrderRefNo) {
        try (PreparedStatement ps = connection.prepareStatement(SEARCH_CLAIM_SUPPLY_ORDER_SUMMARY_ALL)) {
            ps.setObject(1, supplyOrderRefNo);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return getSupplyOrderSummary(rs, AppConstant.STRING_EMPTY, connection);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public List<SupplyOrderSummaryDto> searchClaimSupplyOrderSummaryPending(Connection connection, Integer claimNo, Integer refNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<SupplyOrderSummaryDto> list = new ArrayList<>();
        int index = 0;
        try {
            String policyChannelType = getChannelTypeByClaimOrRefNo(connection, claimNo, null);
            ps = connection.prepareStatement(policyChannelType.equalsIgnoreCase(String.valueOf(PolicyChannelType.TAKAFUL)) ? SELECT_TAKAFUL_SUPPLY_ORDER_SUMMARY_PENDING : SELECT_SUPPLY_ORDER_SUMMARY_PENDING);
            ps.setInt(1, claimNo);
            ps.setInt(2, refNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ++index;
                SupplyOrderSummaryDto supplyOrderSummaryDto = new SupplyOrderSummaryDto();
                supplyOrderSummaryDto.setNo(index);
                supplyOrderSummaryDto.setSupplyOrderRefNo(rs.getInt("t1.n_supply_order_ref_no"));
                supplyOrderSummaryDto.setClaimNo(rs.getInt("t1.n_claim_no"));
                supplyOrderSummaryDto.setSupplyOrderSerialNo(rs.getString("t1.v_supply_order_serial_no"));
                supplyOrderSummaryDto.setSupplierName(rs.getString("t2.V_COMPANY_NAME"));
                supplyOrderSummaryDto.setInputUserId(rs.getString("t1.v_input_user_id"));
                supplyOrderSummaryDto.setInputDateTime(Utility.getDate(rs.getString("t1.d_input_date_time"), AppConstant.DATE_TIME_FORMAT));
                list.add(supplyOrderSummaryDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return list;
    }

    @Override
    public boolean isGenerated(Connection connection, Integer refNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(IS_GENERATED);
            ps.setInt(1, refNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
            ps.close();
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return false;
    }

    @Override
    public void updateDOUpdatedStatus(Connection connection, Integer calSheetId, String updateStatus) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_DO_UPDATE_STATUS);
            ps.setString(1, updateStatus);
            ps.setInt(2, calSheetId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Integer isUpdatedDo(Connection connection, Integer calSheetId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        Integer refNo = AppConstant.ZERO_INT;
        try {
            ps = connection.prepareStatement(IS_DO_UPDATED);
            ps.setInt(1, calSheetId);
            rs = ps.executeQuery();
            while (rs.next()) {
                refNo = rs.getInt("csos.n_supply_order_ref_no");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return refNo;
    }

    @Override
    public String getPolicyChannelType(Connection connection, Integer supplyOrderRefNo, Integer claimNo) throws Exception {
        try {
            return getChannelTypeByClaimOrRefNo(connection, claimNo, supplyOrderRefNo);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
    }

    @Override
    public Boolean isPendingBillForDeliveryOrderApprovedDocument(Connection connection, Integer claimNo, Integer documentTypeId) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        boolean isPendingBillDocument = false;
        try {
            ps = connection.prepareStatement(IS_CHECK_PENDING_BILL_DOCUMENT);
            ps.setInt(1, claimNo);
            ps.setInt(2, documentTypeId);
            rs = ps.executeQuery();
            while (rs.next()) {
                isPendingBillDocument = rs.getInt("cnt") == 0;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return isPendingBillDocument;
    }

    private synchronized Integer getSequenceSupplyOrderSerialNo(Connection conn) {
        PreparedStatement ps = null;
        Integer sequenceId = 0;
        try {
            ps = conn.prepareStatement(SELECT_CLAIM_SUPPLY_ORDER_SUMMARY_SEQUENCE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    sequenceId = rs.getInt("n_supply_order_serial_no");
                    sequenceId++;
                }
            }
        } catch (SQLException e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
        return sequenceId;
    }

    private synchronized void updateSequenceSupplyOrderSerialNo(Connection connection, Integer sequenceId) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_SUPPLY_ORDER_SUMMARY_SEQUENCE);
            ps.setInt(1, sequenceId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
                LOGGER.error(ex.getMessage());;
            }
        }
    }

    private String getChannelTypeByClaimOrRefNo (Connection connection, Integer claimNo, Integer refNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        String policyChannelType = AppConstant.STRING_EMPTY;
        try {
            ps = connection.prepareStatement(null == claimNo || claimNo.equals(AppConstant.ZERO_INT) ? GET_POLICY_CHANNEL_TYPE_BY_DO_REF_NO : GET_POLICY_CHANNEL_TYPE_BY_CLAIM_NO);
            ps.setInt(1, null == claimNo || claimNo.equals(AppConstant.ZERO_INT) ? refNo : claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                policyChannelType = rs.getString("V_POLICY_CHANNEL_TYPE");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return policyChannelType;
    }
}
