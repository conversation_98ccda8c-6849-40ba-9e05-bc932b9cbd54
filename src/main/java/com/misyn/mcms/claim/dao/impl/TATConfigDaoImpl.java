package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.TATConfigDao;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.TATDetailDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
public class TATConfigDaoImpl extends AbstractBaseDao<TATConfigDaoImpl> implements TATConfigDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(TATConfigDaoImpl.class);

    public Integer saveByID(Connection connection, TATDetailDto tatDetailDto) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int generatedId = -1;
        try {
            ps = connection.prepareStatement(INSERT_TAT_DETAILS, Statement.RETURN_GENERATED_KEYS);
            int index = 0;
            ps.setString(++index, tatDetailDto.getTaskName());
            ps.setString(++index, tatDetailDto.getMinTime());
            ps.setString(++index, tatDetailDto.getMaxTime());
            ps.setString(++index, tatDetailDto.getVisibility());
            ps.setString(++index, "ACTIVE");

            ps.executeUpdate();

            // Retrieve the auto increment keys
            rs = ps.getGeneratedKeys();
            if (rs.next()) {
                generatedId = rs.getInt(1);
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while saving TAT details", e);
            throw new Exception("Error occurred while saving TAT details", e);
        } finally {
            secureResources(ps, rs);
        }
        return generatedId;
    }


    @Override
    public void updateByID(Connection connection, TATDetailDto tatDetailDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_TAT_DETAILS);
            ps.setString(++index, tatDetailDto.getTaskName());
            ps.setString(++index, tatDetailDto.getMinTime());
            ps.setString(++index, tatDetailDto.getMaxTime());
            ps.setString(++index, tatDetailDto.getVisibility());
            ps.setInt(++index, tatDetailDto.getTatId());

            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating TAT details", e);
            throw new Exception("Error occurred while updating TAT details", e);
        } finally {
            if (ps != null) {
                try {
                    ps.close();
                } catch (SQLException e) {
                    LOGGER.error("Error closing PreparedStatement", e);
                }
            }
        }
    }

    @Override
    public boolean isAvailable(Connection connection, TATDetailDto tatDetailDto) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        int index = 0;
        try {
            ps = connection.prepareStatement(IS_TAT_DETAILS_AVAILABLE);
            ps.setInt(++index, tatDetailDto.getTatId());

            rs = ps.executeQuery();

            if (rs.next() && rs.getInt("count") > 0) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while checking TAT details availability", e);
            throw new Exception("Error occurred while checking TAT details availability", e);
        }
        return false;
    }

    @Override
    public TATDetailDto searchTATData(int tatId, Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            ps = connection.prepareStatement(SEARCH_TAT_DETAILS);
            ps.setInt(1, tatId);
            rs = ps.executeQuery();

            if (rs.next()) {
                TATDetailDto tatDetailDto = new TATDetailDto();
                tatDetailDto.setTatId(rs.getInt("tatId"));
                tatDetailDto.setTaskName(rs.getString("taskName"));
                tatDetailDto.setMinTime(rs.getString("minTime"));
                tatDetailDto.setMaxTime(rs.getString("maxTime"));
                tatDetailDto.setVisibility(rs.getString("visibility"));
                return tatDetailDto;
            } else {
                LOGGER.warn("No TAT details found for tatId: " + tatId);
                throw new Exception("Error occurred while finding all TAT details for tatId: " + tatId);
            }

        } catch (Exception e) {
            LOGGER.error("Error occurred while finding TAT details for tatId:" + tatId, e);
            throw new Exception("Error occurred while finding TAT details for tatId:" + tatId, e);
        } finally {
            secureResources(ps, rs);
        }
    }

    @Override
    public void deleteTATData(int tatId, String deleteReason, Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int index = 0;
        try {

            ps = connection.prepareStatement(DELETE_TAT_DETAILS);
            ps.setString(++index, "INACTIVE");
            ps.setString(++index, deleteReason);
            ps.setInt(++index, tatId);

            ps.executeUpdate();

        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting TAT details", e);
            throw new Exception("Error occurred while deleting TAT details", e);
        } finally {
            secureResources(ps, rs);
        }
    }

    @Override
    public DataGridDto findAllTATData(Connection connection, int start, int length, String sortColumn, String sortDirection, String searchValue) throws Exception {
        List<TATDetailDto> tatDetailsList = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        int recordsTotal = 0;
        int recordsFiltered = 0;

        try {
            // Get total record count
            ps = connection.prepareStatement(GET_ALL_RECORD_COUNT);
            rs = ps.executeQuery();
            if (rs.next()) {
                recordsTotal = rs.getInt(1);
            }

            // Build the query with pagination, sorting, and filtering
            String query = "SELECT * FROM tat_details WHERE status = 'ACTIVE'";
            if (searchValue != null && !searchValue.isEmpty()) {
                query += " AND (taskName LIKE ? OR minTime LIKE ? OR maxTime LIKE ? OR visibility LIKE ?)";
            }
            query += " ORDER BY " + sortColumn + " " + sortDirection;
            query += " LIMIT ? OFFSET ?";

            ps = connection.prepareStatement(query);

            int paramIndex = 1;
            if (searchValue != null && !searchValue.isEmpty()) {
                for (int i = 0; i < 4; i++) {
                    ps.setString(paramIndex++, "%" + searchValue + "%");
                }
            }
            ps.setInt(paramIndex++, length);
            ps.setInt(paramIndex, start);

            rs = ps.executeQuery();
            while (rs.next()) {
                TATDetailDto tatDetailDto = new TATDetailDto();
                tatDetailDto.setTatId(rs.getInt("tatId"));
                tatDetailDto.setTaskName(rs.getString("taskName"));
                tatDetailDto.setMinTime(rs.getString("minTime"));
                tatDetailDto.setMaxTime(rs.getString("maxTime"));
                tatDetailDto.setVisibility(rs.getString("visibility"));
                tatDetailsList.add(tatDetailDto);
            }

            // Get filtered record count
            if (searchValue != null && !searchValue.isEmpty()) {
                ps = connection.prepareStatement(FILTERED_RECORD_COUNT);
                paramIndex = 1;
                for (int i = 0; i < 4; i++) {
                    ps.setString(paramIndex++, "%" + searchValue + "%");
                }
                rs = ps.executeQuery();
                if (rs.next()) {
                    recordsFiltered = rs.getInt(1);
                }
            } else {
                recordsFiltered = recordsTotal;
            }

        } catch (Exception e) {
            LOGGER.error("Error occurred while finding all TAT details", e);
            throw new Exception("Error occurred while finding all TAT details", e);
        } finally {
            secureResources(ps, rs);
        }

        DataGridDto dataGridDto = new DataGridDto();
        dataGridDto.setData(new ArrayList<>(tatDetailsList));
        dataGridDto.setRecordsTotal(recordsTotal);
        dataGridDto.setRecordsFiltered(recordsFiltered);

        return dataGridDto;
    }

    @Override
    public List<TATDetailDto> filterTATDetails(int tatId, String taskName, Connection connection) throws Exception {
        List<TATDetailDto> tatDetailsList = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            String query = FILTER_TAT_DETAILS;
            if (tatId > 0) {
                query += APPEND_TAT_ID;
            }
            if (taskName != null && !taskName.isEmpty()) {
                query += APPEND_TASK_NAME;
            }
            ps = connection.prepareStatement(query);

            int paramIndex = 1;
            if (tatId > 0) {
                ps.setInt(paramIndex++, tatId);
            }
            if (taskName != null && !taskName.isEmpty()) {
                ps.setString(paramIndex++, "%" + taskName + "%");
            }

            rs = ps.executeQuery();
            while (rs.next()) {
                TATDetailDto tatDetailDto = new TATDetailDto();
                tatDetailDto.setTatId(rs.getInt("tatId"));
                tatDetailDto.setTaskName(rs.getString("taskName"));
                tatDetailDto.setMinTime(rs.getString("minTime"));
                tatDetailDto.setMaxTime(rs.getString("maxTime"));
                tatDetailDto.setVisibility(rs.getString("visibility"));
                tatDetailsList.add(tatDetailDto);
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while filtering TAT details", e);
            throw new Exception("Error occurred while filtering TAT details", e);
        } finally {
            secureResources(ps, rs);
        }
        return tatDetailsList;
    }


    private void secureResources(PreparedStatement ps, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                LOGGER.error("Error closing ResultSet", e);
            }
        }
        if (ps != null) {
            try {
                ps.close();
            } catch (SQLException e) {
                LOGGER.error("Error closing PreparedStatement", e);
            }
        }
    }
}

