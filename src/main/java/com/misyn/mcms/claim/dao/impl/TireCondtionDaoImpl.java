package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.TireCondtionDao;
import com.misyn.mcms.claim.dto.TireCondtionDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class TireCondtionDaoImpl extends BaseAbstract<TireCondtionDaoImpl> implements TireCondtionDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(TireCondtionDaoImpl.class);

    @Override
    public TireCondtionDto insertMaster(Connection connection, TireCondtionDto tireCondtionDto) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_INSERT_TIRE_CONDTION_VALUES);
            ps.setInt(1, tireCondtionDto.getRefNo());
            ps.setInt(2, tireCondtionDto.getClaimsDto().getClaimNo());
            ps.setInt(3, tireCondtionDto.getPosition());
            ps.setString(4, tireCondtionDto.getRf());
            ps.setString(5, tireCondtionDto.getLf());
            ps.setString(6, tireCondtionDto.getRr());
            ps.setString(7, tireCondtionDto.getRl());
            ps.setString(8, tireCondtionDto.getRri());
            ps.setString(9, tireCondtionDto.getLri());
            ps.setString(10, tireCondtionDto.getOther());

            if (ps.executeUpdate() > 0) {
                return tireCondtionDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public TireCondtionDto updateMaster(Connection connection, TireCondtionDto tireCondtionDto) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_TIRE_CONDTION_VALUES);
            ps.setString(1, tireCondtionDto.getRf());
            ps.setString(2, tireCondtionDto.getLf());
            ps.setString(3, tireCondtionDto.getRr());
            ps.setString(4, tireCondtionDto.getRl());
            ps.setString(5, tireCondtionDto.getRri());
            ps.setString(6, tireCondtionDto.getLri());
            ps.setString(7, tireCondtionDto.getOther());
            ps.setInt(8, tireCondtionDto.getClaimsDto().getClaimNo());
            ps.setInt(9, tireCondtionDto.getPosition());
            ps.setInt(10, tireCondtionDto.getRefNo());

            if (ps.executeUpdate() > 0) {
                return tireCondtionDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public TireCondtionDto insertTemporary(Connection connection, TireCondtionDto tireCondtionDto) throws Exception {
        return null;
    }

    @Override
    public TireCondtionDto updateTemporary(Connection connection, TireCondtionDto tireCondtionDto) throws Exception {
        return null;
    }

    @Override
    public TireCondtionDto insertHistory(Connection connection, TireCondtionDto tireCondtionDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public TireCondtionDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public TireCondtionDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<TireCondtionDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public TireCondtionDto insertMotorEngMaster(Connection connection, TireCondtionDto tireCondtionDto) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_INSERT_TIRE_CONDTION_MOTOR_ENG_VALUES);
            ps.setInt(1, tireCondtionDto.getClaimsDto().getClaimNo());
            ps.setInt(2, tireCondtionDto.getPosition());
            ps.setString(3, tireCondtionDto.getRf());
            ps.setString(4, tireCondtionDto.getLf());
            ps.setString(5, tireCondtionDto.getRr());
            ps.setString(6, tireCondtionDto.getRl());
            ps.setString(7, tireCondtionDto.getRri());
            ps.setString(8, tireCondtionDto.getLri());
            ps.setString(9, tireCondtionDto.getOther());

            if (ps.executeUpdate() > 0) {
                return tireCondtionDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public TireCondtionDto searchTireCondtionByClaimNoAndPosition(Connection connection, TireCondtionDto tireCondtionDto) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SQL_SQL_TIRE_CONDTION_MOTOR_ENG_VALUES);
            ps.setInt(1, tireCondtionDto.getClaimsDto().getClaimNo());
            ps.setInt(2, tireCondtionDto.getPosition());
            ps.setInt(3, tireCondtionDto.getRefNo());
            rs = ps.executeQuery();

            if (rs.next()) {
                return getTireCondtion(rs);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    private TireCondtionDto getTireCondtion(ResultSet rs) {
        TireCondtionDto tireCondtionDto = new TireCondtionDto();
        try {
            tireCondtionDto.setRefNo(rs.getInt("N_REF_NO"));
            tireCondtionDto.getClaimsDto().setClaimNo(rs.getInt("N_CLIM_NO"));
            tireCondtionDto.setPosition(rs.getInt("N_POSITION"));
            tireCondtionDto.setRf(rs.getString("V_RF"));
            tireCondtionDto.setLf(rs.getString("V_LF"));
            tireCondtionDto.setRr(rs.getString("V_RR"));
            tireCondtionDto.setRl(rs.getString("V_RL"));
            tireCondtionDto.setRri(rs.getString("V_RRI"));
            tireCondtionDto.setLri(rs.getString("V_LRI"));
            tireCondtionDto.setOther(rs.getString("V_OTHER"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }

        return tireCondtionDto;
    }

    @Override
    public List<TireCondtionDto> searchByClaimNoAndRefNo(Connection connection, int claimNo, int refNo) throws Exception {
        String sql = "SELECT N_POSITION, V_RF, V_LF, V_RR, V_RL, V_RRI, V_LRI, V_OTHER\n"
                + "	FROM claim_tire_condition WHERE N_CLIM_NO = ? AND N_REF_NO = ? ORDER BY N_POSITION";
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<TireCondtionDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(sql);
            ps.setInt(1, claimNo);
            ps.setInt(2, refNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                TireCondtionDto tireCondtionDto = new TireCondtionDto();
                tireCondtionDto.setPosition(rs.getInt("N_POSITION"));
                tireCondtionDto.setRf(rs.getString("V_RF"));
                tireCondtionDto.setLf(rs.getString("V_LF"));
                tireCondtionDto.setRr(rs.getString("V_RR"));
                tireCondtionDto.setRl(rs.getString("V_RL"));
                tireCondtionDto.setRri(rs.getString("V_RRI"));
                tireCondtionDto.setLri(rs.getString("V_LRI"));
                tireCondtionDto.setOther(rs.getString("V_OTHER"));
                list.add(tireCondtionDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }
}
