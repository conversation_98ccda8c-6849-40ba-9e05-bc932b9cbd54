package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.UserAuthorityLimitDao;
import com.misyn.mcms.claim.dto.PopupItemDto;
import com.misyn.mcms.claim.dto.UserAuthorityLimitDto;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class UserAuthorityLimitDaoImpl implements UserAuthorityLimitDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserAuthorityLimitDaoImpl.class);

    @Override
    public UserAuthorityLimitDto insertMaster(Connection connection, UserAuthorityLimitDto userAuthorityLimitDto) throws Exception {
        return null;
    }

    @Override
    public UserAuthorityLimitDto updateMaster(Connection connection, UserAuthorityLimitDto userAuthorityLimitDto) throws Exception {
        return null;
    }

    @Override
    public UserAuthorityLimitDto insertTemporary(Connection connection, UserAuthorityLimitDto userAuthorityLimitDto) throws Exception {
        return null;
    }

    @Override
    public UserAuthorityLimitDto updateTemporary(Connection connection, UserAuthorityLimitDto userAuthorityLimitDto) throws Exception {
        return null;
    }

    @Override
    public UserAuthorityLimitDto insertHistory(Connection connection, UserAuthorityLimitDto userAuthorityLimitDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public UserAuthorityLimitDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public UserAuthorityLimitDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<UserAuthorityLimitDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public UserAuthorityLimitDto searchByLimitAndDepartmentId(Connection connection, BigDecimal limit, int mofaTeamDepartmentId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        UserAuthorityLimitDto userAuthorityLimitDto = new UserAuthorityLimitDto();
        try {
            ps = connection.prepareStatement(SELECT_BY_LIMIT_AND_DEPARTMENT_ID);
            ps.setObject(1, limit);
            ps.setObject(2, limit);
            ps.setObject(3, mofaTeamDepartmentId);
            rs = ps.executeQuery();
            if (rs.next()) {

                userAuthorityLimitDto.setLevelId(rs.getInt("level_id"));
                userAuthorityLimitDto.setLevelCode(rs.getInt("level_code"));
                userAuthorityLimitDto.setLevelName(rs.getString("level_name"));
                userAuthorityLimitDto.setDepartmentId(rs.getInt("department_id"));
                userAuthorityLimitDto.setFromLimit(rs.getBigDecimal("from_limit"));
                userAuthorityLimitDto.setToLimit(rs.getBigDecimal("to_limit"));

                return userAuthorityLimitDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return userAuthorityLimitDto;
    }

    @Override
    public UserAuthorityLimitDto searchByLevelCodeAndDepartmentId(Connection connection, int levelCode, int mofaTeamDepartmentId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        UserAuthorityLimitDto userAuthorityLimitDto = new UserAuthorityLimitDto();
        try {
            ps = connection.prepareStatement(SELECT_BY_LEVEL_CODE_AND_DEPARTMENT_ID);
            ps.setObject(1, levelCode);
            ps.setObject(2, mofaTeamDepartmentId);
            rs = ps.executeQuery();
            if (rs.next()) {

                userAuthorityLimitDto.setLevelId(rs.getInt("level_id"));
                userAuthorityLimitDto.setLevelCode(rs.getInt("level_code"));
                userAuthorityLimitDto.setLevelName(rs.getString("level_name"));
                userAuthorityLimitDto.setDepartmentId(rs.getInt("department_id"));
                userAuthorityLimitDto.setFromLimit(rs.getBigDecimal("from_limit"));
                userAuthorityLimitDto.setToLimit(rs.getBigDecimal("to_limit"));

                return userAuthorityLimitDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return userAuthorityLimitDto;
    }

    @Override
    public int getMofaLevel(Connection connection, BigDecimal limit, int mofaTeamDepartmentId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_LEVEL_CODE_BY_LIMIT_AND_DEPARTMENT_ID);
            ps.setObject(1, limit);
            ps.setObject(2, limit);
            ps.setObject(3, mofaTeamDepartmentId);
            rs = ps.executeQuery();
            if (rs.next()) {
                return rs.getInt("level_code");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return AppConstant.ZERO_INT;
    }

    @Override
    public UserAuthorityLimitDto getUserAuthorityDetailsByMofaDetails(Connection connection, Integer calSheetId, String assignUserId, int departmentId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        UserAuthorityLimitDto userAuthorityLimitDto = new UserAuthorityLimitDto();
        try {
            ps = connection.prepareStatement(SELECT_ALL_FROM_USER_AUTHORITY_LIMIT_AND_ASSIGN_MOFA_LEVEL_MST);
            ps.setObject(1, calSheetId);
            ps.setObject(2, assignUserId);
            ps.setObject(3, departmentId);
            rs = ps.executeQuery();
            if (rs.next()) {

                userAuthorityLimitDto.setLevelId(rs.getInt("t2.level_id"));
                userAuthorityLimitDto.setLevelCode(rs.getInt("t2.level_code"));
                userAuthorityLimitDto.setLevelName(rs.getString("t2.level_name"));
                userAuthorityLimitDto.setDepartmentId(rs.getInt("t2.department_id"));
                userAuthorityLimitDto.setFromLimit(rs.getBigDecimal("t2.from_limit"));
                userAuthorityLimitDto.setToLimit(rs.getBigDecimal("t2.to_limit"));

                return userAuthorityLimitDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != rs) {
                rs.close();
            }
            if (null != ps) {
                ps.close();
            }
        }
        return userAuthorityLimitDto;
    }

    @Override
    public List<PopupItemDto> getAllMofaLevels(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<PopupItemDto> listItems = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_AUTHORITY_LEVELS);
            ps.setInt(1, AppConstant.MOFA_TEAM_DEPARTMENT_ID);
            rs = ps.executeQuery();
            while (rs.next()) {
                PopupItemDto popupItemDto = new PopupItemDto();
                popupItemDto.setLabel(rs.getString("level_name"));
                popupItemDto.setValue(rs.getString("level_code"));
                listItems.add(popupItemDto);
            }
            return listItems;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }
}
