package com.misyn.mcms.claim.dao.impl.motorengineer;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.motorengineer.ARIInspectionDetailsMeDao;
import com.misyn.mcms.claim.dto.ARIInspectionDetailsDto;
import com.misyn.mcms.claim.enums.ConditionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
public class ARIInspectionDetailsMeDaoImpl extends BaseAbstract<ARIInspectionDetailsMeDaoImpl> implements ARIInspectionDetailsMeDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(ARIInspectionDetailsMeDaoImpl.class);

    @Override
    public ARIInspectionDetailsDto insertMaster(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_ARI_INSPECTION_DETAILS);
            ps.setInt(++index, ariInspectionDetailsDto.getInspectionId());
            ps.setInt(++index, ariInspectionDetailsDto.getRefNo());
            ps.setString(++index, ariInspectionDetailsDto.getIsAri().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getAriOrder().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getIsSalvage().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getSalvageOrder().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getAssessorSpecialRemark());
            ps.setString(++index, ariInspectionDetailsDto.getInspectionRemark());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getMiles());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getTelephoneCharge());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getOtherCharge());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getSpecialDeduction());
            ps.setString(++index, ariInspectionDetailsDto.getReason());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getTotalCharge());

            if (ps.executeUpdate() > 0) {
                return ariInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ARIInspectionDetailsDto updateMaster(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_ARI_INSPECTION);
            ps.setInt(++index, ariInspectionDetailsDto.getInspectionId());
            ps.setString(++index, ariInspectionDetailsDto.getAriOrder().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getSalvageOrder().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getAssessorSpecialRemark());
            ps.setString(++index, ariInspectionDetailsDto.getInspectionRemark());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getMiles());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getTelephoneCharge());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getOtherCharge());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getSpecialDeduction());
            ps.setString(++index, ariInspectionDetailsDto.getReason());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getTotalCharge());
            ps.setInt(++index, ariInspectionDetailsDto.getRefNo());

            if (ps.executeUpdate() > 0) {
                return ariInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;

    }

    @Override
    public ARIInspectionDetailsDto insertTemporary(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public ARIInspectionDetailsDto updateTemporary(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public ARIInspectionDetailsDto insertHistory(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ARIInspectionDetailsDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_ARI_DETAILS);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getAriInspectionDto(rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public ARIInspectionDetailsDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ARIInspectionDetailsDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    private ARIInspectionDetailsDto getAriInspectionDto(ResultSet rst) {
        ARIInspectionDetailsDto ariInspectionDetailsDto = new ARIInspectionDetailsDto();

        try {
            ariInspectionDetailsDto.setInspectionId(rst.getInt("inspection_id"));
            ariInspectionDetailsDto.setRefNo(rst.getInt("n_ref_no"));
            ariInspectionDetailsDto.setIsAri(rst.getString("is_ari").equals("Y") ? ConditionType.Yes : ConditionType.No);
            ariInspectionDetailsDto.setAriOrder(rst.getString("ari_order").equals("Y") ? ConditionType.Yes : ConditionType.No);
            ariInspectionDetailsDto.setIsSalvage(rst.getString("is_salvage").equals("Y") ? ConditionType.Yes : ConditionType.No);
            ariInspectionDetailsDto.setSalvageOrder(rst.getString("salvage_order").equals("Y") ? ConditionType.Yes : ConditionType.No);
            ariInspectionDetailsDto.setAssessorSpecialRemark(rst.getString("assessor_remark"));
            ariInspectionDetailsDto.setInspectionRemark(rst.getString("inspection_remark"));
            ariInspectionDetailsDto.setProfessionalFee(rst.getBigDecimal("professional_fee"));
            ariInspectionDetailsDto.setMiles(rst.getBigDecimal("miles"));
            ariInspectionDetailsDto.setTelephoneCharge(rst.getBigDecimal("telephone_charge"));
            ariInspectionDetailsDto.setOtherCharge(rst.getBigDecimal("other_charge"));
            ariInspectionDetailsDto.setSpecialDeduction(rst.getBigDecimal("special_deduction"));
            ariInspectionDetailsDto.setReason(rst.getString("reason"));
            ariInspectionDetailsDto.setTotalCharge(rst.getBigDecimal("total_charge"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return ariInspectionDetailsDto;
    }

    @Override
    public ARIInspectionDetailsDto updateAriInspectionDetailMaster(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_ARI_INSPECTION_DETAILS);
            ps.setInt(++index, ariInspectionDetailsDto.getInspectionId());
            ps.setString(++index, ariInspectionDetailsDto.getIsAri().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getAriOrder().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getIsSalvage().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getSalvageOrder().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getAssessorSpecialRemark());
            ps.setString(++index, ariInspectionDetailsDto.getInspectionRemark());
            ps.setInt(++index, ariInspectionDetailsDto.getRefNo());

            if (ps.executeUpdate() > 0) {
                return ariInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ARIInspectionDetailsDto updateAriAssessorFeeDetailMaster(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_ARI_ASSESSOR_FEE_DETAILS);
            ps.setString(++index, ariInspectionDetailsDto.getInspectionRemark());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getMiles());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getTelephoneCharge());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getOtherCharge());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getSpecialDeduction());
            ps.setString(++index, ariInspectionDetailsDto.getReason());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getTotalCharge());
            ps.setInt(++index, ariInspectionDetailsDto.getRefNo());

            if (ps.executeUpdate() > 0) {
                return ariInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }


    @Override
    public ARIInspectionDetailsDto insertAriInspectionDetailMaster(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_ARI_INSPECTION_DETAIL_MASTER);
            ps.setInt(++index, ariInspectionDetailsDto.getInspectionId());
            ps.setInt(++index, ariInspectionDetailsDto.getRefNo());
            ps.setString(++index, ariInspectionDetailsDto.getIsAri().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getAriOrder().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getIsSalvage().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getSalvageOrder().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getAssessorSpecialRemark());
            ps.setString(++index, ariInspectionDetailsDto.getInspectionRemark());
            if (ps.executeUpdate() > 0) {
                return ariInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ARIInspectionDetailsDto insertAriAssessorFeeDetailMaster(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_ARI_INSPECTION_ASSESSOR_FEE_DETAIL_MASTER);
            ps.setInt(++index, ariInspectionDetailsDto.getInspectionId());
            ps.setInt(++index, ariInspectionDetailsDto.getRefNo());
            ps.setString(++index, ariInspectionDetailsDto.getIsAri().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getAriOrder().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getIsSalvage().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getSalvageOrder().getCondtionType());
            ps.setString(++index, ariInspectionDetailsDto.getAssessorSpecialRemark());
            ps.setString(++index, ariInspectionDetailsDto.getInspectionRemark());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getMiles());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getTelephoneCharge());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getOtherCharge());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getSpecialDeduction());
            ps.setString(++index, ariInspectionDetailsDto.getReason());
            ps.setBigDecimal(++index, ariInspectionDetailsDto.getTotalCharge());

            if (ps.executeUpdate() > 0) {
                return ariInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ARIInspectionDetailsDto getARIInspectionDetails(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto, Object id) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_ARI_INSPECTION_DETAILS);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getAriInspectionDetailsDto(ariInspectionDetailsDto, rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public ARIInspectionDetailsDto getARIInspectionAssessorFeeDetails(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto, Object id) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_ARI_ASSESSOR_FEE_DETAILS);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getAriInspectionAssessorFeeDetailsDto(ariInspectionDetailsDto, rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    private ARIInspectionDetailsDto getAriInspectionAssessorFeeDetailsDto(ARIInspectionDetailsDto ariInspectionDetailsDto, ResultSet rst) {
        try {
            ariInspectionDetailsDto.setInspectionId(rst.getInt("inspection_id"));
            ariInspectionDetailsDto.setRefNo(rst.getInt("n_ref_no"));
            ariInspectionDetailsDto.setIsAri(rst.getString("is_ari").equals("Y") ? ConditionType.Yes : ConditionType.No);
            ariInspectionDetailsDto.setAriOrder(rst.getString("ari_order").equals("Y") ? ConditionType.Yes : ConditionType.No);
            ariInspectionDetailsDto.setIsSalvage(rst.getString("is_salvage").equals("Y") ? ConditionType.Yes : ConditionType.No);
            ariInspectionDetailsDto.setSalvageOrder(rst.getString("salvage_order").equals("Y") ? ConditionType.Yes : ConditionType.No);
            ariInspectionDetailsDto.setAssessorSpecialRemark(rst.getString("assessor_remark"));
            ariInspectionDetailsDto.setInspectionRemark(rst.getString("inspection_remark"));
            ariInspectionDetailsDto.setProfessionalFee(rst.getBigDecimal("professional_fee"));
            ariInspectionDetailsDto.setMiles(rst.getBigDecimal("miles"));
            ariInspectionDetailsDto.setTelephoneCharge(rst.getBigDecimal("telephone_charge"));
            ariInspectionDetailsDto.setOtherCharge(rst.getBigDecimal("other_charge"));
            ariInspectionDetailsDto.setSpecialDeduction(rst.getBigDecimal("special_deduction"));
            ariInspectionDetailsDto.setReason(rst.getString("reason"));
            ariInspectionDetailsDto.setTotalCharge(rst.getBigDecimal("total_charge"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return ariInspectionDetailsDto;
    }

    private ARIInspectionDetailsDto getAriInspectionDetailsDto(ARIInspectionDetailsDto ariInspectionDetailsDto, ResultSet rst) {
        try {
            ariInspectionDetailsDto.setInspectionId(rst.getInt("inspection_id"));
            ariInspectionDetailsDto.setRefNo(rst.getInt("n_ref_no"));
            ariInspectionDetailsDto.setIsAri(rst.getString("is_ari").equals("Y") ? ConditionType.Yes : ConditionType.No);
            ariInspectionDetailsDto.setAriOrder(rst.getString("ari_order").equals("Y") ? ConditionType.Yes : ConditionType.No);
            ariInspectionDetailsDto.setIsSalvage(rst.getString("is_salvage").equals("Y") ? ConditionType.Yes : ConditionType.No);
            ariInspectionDetailsDto.setSalvageOrder(rst.getString("salvage_order").equals("Y") ? ConditionType.Yes : ConditionType.No);
            ariInspectionDetailsDto.setAssessorSpecialRemark(rst.getString("assessor_remark"));
            ariInspectionDetailsDto.setInspectionRemark(rst.getString("inspection_remark"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return ariInspectionDetailsDto;
    }

}
