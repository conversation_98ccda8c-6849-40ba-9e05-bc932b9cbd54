package com.misyn.mcms.claim.dao.impl.motorengineer;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.motorengineer.DesktopInspectionDetailsMeDao;
import com.misyn.mcms.claim.dto.DesktopInspectionDetailsDto;
import com.misyn.mcms.claim.enums.ConditionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
public class DesktopInspectionDetailsMeDaoImpl extends BaseAbstract<DesktopInspectionDetailsMeDaoImpl> implements DesktopInspectionDetailsMeDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(DesktopInspectionDetailsMeDaoImpl.class);

    @Override
    public DesktopInspectionDetailsDto insertMaster(Connection connection, DesktopInspectionDetailsDto desktopInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_INTO_DESKTIOP_INSPECTION);
            ps.setInt(++index, desktopInspectionDetailsDto.getInspectionId());
            ps.setInt(++index, desktopInspectionDetailsDto.getRefNo());
            ps.setString(++index, desktopInspectionDetailsDto.getProvideOffer().getCondtionType());
            ps.setInt(++index, desktopInspectionDetailsDto.getOfferType());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getAppCostReport());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getPreAccidentValue());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getExcess());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getAcr());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getOldAcr());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getBoldTyrePenaltyAmount());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getUnderInsurancePenaltyAmount());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getPayableAmount());
            ps.setString(++index, desktopInspectionDetailsDto.getDesktopOffer().getCondtionType());
            ps.setString(++index, desktopInspectionDetailsDto.getAriSalvage().getCondtionType());
            ps.setString(++index, desktopInspectionDetailsDto.getSettlementMethod());
            ps.setString(++index, desktopInspectionDetailsDto.getInspectionRemark());
            ps.setString(++index, desktopInspectionDetailsDto.getPoliceReportRequested().getCondtionType());
            ps.setString(++index, desktopInspectionDetailsDto.getSpecialRemark());
            ps.setString(++index, desktopInspectionDetailsDto.getInvestigaedClaim().getCondtionType());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getMiles());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getTelephoneCharge());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getOtherCharge());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getSpecialDeduction());
            ps.setString(++index, desktopInspectionDetailsDto.getReason());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getTotalCharge());
            ps.setString(++index, desktopInspectionDetailsDto.getInformToGarageName());
            ps.setString(++index, desktopInspectionDetailsDto.getInformToGarageContact());
            ps.setString(++index, desktopInspectionDetailsDto.getIsAgreeGarage());
            ps.setString(++index, desktopInspectionDetailsDto.getInformToCustomerName());
            ps.setString(++index, desktopInspectionDetailsDto.getInformToCustomerContact());
            ps.setString(++index, desktopInspectionDetailsDto.getIsAgreeCustomer());
            ps.setString(++index, desktopInspectionDetailsDto.getReasonForDisagree());
            ps.setString(++index, desktopInspectionDetailsDto.getDesktopComment());
            ps.setString(++index, desktopInspectionDetailsDto.getIsInformed());
            ps.setString(++index, desktopInspectionDetailsDto.getInformedUser());
            ps.setString(++index, desktopInspectionDetailsDto.getInformedDateTime().isEmpty() ? null : desktopInspectionDetailsDto.getInformedDateTime());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getAdvancedAmount());
            ps.setString(++index, desktopInspectionDetailsDto.getAdvanceChange());

            if (ps.executeUpdate() > 0) {
                return desktopInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public DesktopInspectionDetailsDto updateMaster(Connection connection, DesktopInspectionDetailsDto desktopInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_DESKTIOP_INSPECTION);
            ps.setInt(++index, desktopInspectionDetailsDto.getInspectionId());
            ps.setString(++index, desktopInspectionDetailsDto.getProvideOffer().getCondtionType());
            ps.setInt(++index, desktopInspectionDetailsDto.getOfferType());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getAppCostReport());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getPreAccidentValue());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getExcess());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getAcr());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getOldAcr());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getBoldTyrePenaltyAmount());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getUnderInsurancePenaltyAmount());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getPayableAmount());
            ps.setString(++index, desktopInspectionDetailsDto.getDesktopOffer().getCondtionType());
            ps.setString(++index, desktopInspectionDetailsDto.getAriSalvage().getCondtionType());
            ps.setString(++index, desktopInspectionDetailsDto.getSettlementMethod());
            ps.setString(++index, desktopInspectionDetailsDto.getInspectionRemark());
            ps.setString(++index, desktopInspectionDetailsDto.getPoliceReportRequested().getCondtionType());
            ps.setString(++index, desktopInspectionDetailsDto.getSpecialRemark());
            ps.setString(++index, desktopInspectionDetailsDto.getInvestigaedClaim().getCondtionType());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getMiles());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getTelephoneCharge());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getOtherCharge());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getSpecialDeduction());
            ps.setString(++index, desktopInspectionDetailsDto.getReason());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getTotalCharge());
            ps.setBigDecimal(++index, desktopInspectionDetailsDto.getAdvancedAmount());
            ps.setString(++index, desktopInspectionDetailsDto.getAdvanceChange());
            ps.setInt(++index, desktopInspectionDetailsDto.getRefNo());

            if (ps.executeUpdate() > 0) {
                return desktopInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public DesktopInspectionDetailsDto insertTemporary(Connection connection, DesktopInspectionDetailsDto desktopInspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public DesktopInspectionDetailsDto updateTemporary(Connection connection, DesktopInspectionDetailsDto desktopInspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public DesktopInspectionDetailsDto insertHistory(Connection connection, DesktopInspectionDetailsDto desktopInspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public DesktopInspectionDetailsDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_DESKTIOP_INSPECTION);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getDesktopInspectionDto(rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public DesktopInspectionDetailsDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<DesktopInspectionDetailsDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    private DesktopInspectionDetailsDto getDesktopInspectionDto(ResultSet rst) {
        DesktopInspectionDetailsDto desktopInspectionDetailsDto = new DesktopInspectionDetailsDto();
        try {
            desktopInspectionDetailsDto.setRefNo(rst.getInt("n_ref_no"));
            desktopInspectionDetailsDto.setAppCostReport(rst.getBigDecimal("app_cost_report"));
            desktopInspectionDetailsDto.setPreAccidentValue(rst.getBigDecimal("pre_accident_value"));
            desktopInspectionDetailsDto.setExcess(rst.getBigDecimal("excess"));
            desktopInspectionDetailsDto.setAcr(rst.getBigDecimal("acr"));
            desktopInspectionDetailsDto.setOldAcr(rst.getBigDecimal("previous_acr"));
            desktopInspectionDetailsDto.setBoldTyrePenaltyAmount(rst.getBigDecimal("bold_tyre_penalty_amount"));
            desktopInspectionDetailsDto.setUnderInsurancePenaltyAmount(rst.getBigDecimal("under_insurance_penalty_amount"));
            desktopInspectionDetailsDto.setPayableAmount(rst.getBigDecimal("payable_amount"));
            desktopInspectionDetailsDto.setDesktopOffer(rst.getString("desktop_offer").equals("Y") ? ConditionType.Yes : ConditionType.No);
            desktopInspectionDetailsDto.setAriSalvage(rst.getString("ari_salvage").equals("Y") ? ConditionType.Yes : ConditionType.No);
            desktopInspectionDetailsDto.setSettlementMethod(rst.getString("settlement_method"));
            desktopInspectionDetailsDto.setInspectionRemark(rst.getString("inspection_remark"));
            desktopInspectionDetailsDto.setPoliceReportRequested(rst.getString("police_report_requested").equals("Y") ? ConditionType.Yes : ConditionType.No);
            desktopInspectionDetailsDto.setInvestigaedClaim(rst.getString("investigaed_claim").equals("Y") ? ConditionType.Yes : ConditionType.No);
            desktopInspectionDetailsDto.setProfessionalFee(rst.getBigDecimal("professional_fee"));
            desktopInspectionDetailsDto.setMiles(rst.getBigDecimal("miles"));
            desktopInspectionDetailsDto.setTelephoneCharge(rst.getBigDecimal("telephone_charge"));
            desktopInspectionDetailsDto.setOtherCharge(rst.getBigDecimal("other_charge"));
            desktopInspectionDetailsDto.setSpecialDeduction(rst.getBigDecimal("special_deduction"));
            desktopInspectionDetailsDto.setReason(rst.getString("reason"));
            desktopInspectionDetailsDto.setTotalCharge(rst.getBigDecimal("total_charge"));
            desktopInspectionDetailsDto.setInformToGarageName(rst.getString("inform_to_garage_name"));
            desktopInspectionDetailsDto.setInformToGarageContact(rst.getString("inform_to_garage_contact"));
            desktopInspectionDetailsDto.setInformToCustomerName(rst.getString("inform_to_customer_name"));
            desktopInspectionDetailsDto.setInformToCustomerContact(rst.getString("inform_to_customer_contact"));
            desktopInspectionDetailsDto.setIsAgreeGarage(rst.getString("is_agree_garage"));
            desktopInspectionDetailsDto.setIsAgreeCustomer(rst.getString("is_agree_customer"));
            desktopInspectionDetailsDto.setReasonForDisagree(rst.getString("reason_for_disagree"));
            desktopInspectionDetailsDto.setDesktopComment(rst.getString("desktop_comment"));
            desktopInspectionDetailsDto.setIsInformed(rst.getString("is_informed"));
            desktopInspectionDetailsDto.setInformedUser(rst.getString("v_informed_user"));
            desktopInspectionDetailsDto.setInformedDateTime(rst.getString("d_informed_datetime"));
            desktopInspectionDetailsDto.setAdvancedAmount(rst.getBigDecimal("advanced_amount"));
            desktopInspectionDetailsDto.setAdvanceChange(rst.getString("advance_amount_action"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return desktopInspectionDetailsDto;
    }

    @Override
    public BigDecimal getAcr(Connection connection, Integer refNo) {
        PreparedStatement ps = null;
        BigDecimal acr = BigDecimal.ZERO;
        try {
            ps = connection.prepareStatement(SQL_SELECT_ACR_BY_REF_NO);
            ps.setObject(1, refNo);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                acr = rs.getBigDecimal("acr");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return acr;
    }

    @Override
    public boolean isInformed(Connection connection, int refNo) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SQL_IS_INFORMED_DESKTOP);
            ps.setInt(1, refNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                return rs.getString("is_informed").equals("YES");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
        return false;
    }

    @Override
    public DesktopInspectionDetailsDto getDesktopOfferDetails(Connection connection, Integer claimNo) {
        PreparedStatement ps;
        ResultSet rs;
        DesktopInspectionDetailsDto desktopInspectionDetailsDto = null;
        try {
            ps = connection.prepareStatement(SQL_GET_DESKTOP_OFFER_DETAILS);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            if (rs.next()) {
                desktopInspectionDetailsDto = getDesktopInspectionDto(rs);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return desktopInspectionDetailsDto;
    }
}
