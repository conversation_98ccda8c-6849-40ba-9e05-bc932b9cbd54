package com.misyn.mcms.claim.dao.impl.motorengineer;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.motorengineer.DrSupplementaryInspectionDetailsMeDao;
import com.misyn.mcms.claim.dto.DrSupplementaryInspectionDetailsDto;
import com.misyn.mcms.claim.enums.ConditionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
public class DrSupplementaryInspectionDetailsMeDaoImpl extends BaseAbstract<DrSupplementaryInspectionDetailsMeDaoImpl> implements DrSupplementaryInspectionDetailsMeDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(DrSupplementaryInspectionDetailsMeDaoImpl.class);

    @Override
    public DrSupplementaryInspectionDetailsDto insertMaster(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_DR_SUPPLEMENTARY);
            ps.setInt(++index, drSupplementaryInspectionDetailsDto.getInspectionId());
            ps.setInt(++index, drSupplementaryInspectionDetailsDto.getRefNo());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getSumInsured());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getPreAccidentValue());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getExcess());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getAcr());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getBoldTyrePenaltyAmount());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getUnderInsurancePenaltyAmount());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getPayableAmount());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getAssessorRemark());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getInspectionRemark());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getAriAndSalvage().getCondtionType());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getMiles());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getTelephoneCharge());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getOtherCharge());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getSpecialDeduction());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getReason());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getTotalCharge());

            if (ps.executeUpdate() > 0) {
                return drSupplementaryInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public DrSupplementaryInspectionDetailsDto updateMaster(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_DR_SUPPLEMENTARY);
            ps.setInt(++index, drSupplementaryInspectionDetailsDto.getInspectionId());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getSumInsured());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getPreAccidentValue());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getExcess());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getAcr());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getBoldTyrePenaltyAmount());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getUnderInsurancePenaltyAmount());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getPayableAmount());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getAssessorRemark());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getInspectionRemark());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getAriAndSalvage().getCondtionType());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getMiles());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getTelephoneCharge());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getOtherCharge());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getSpecialDeduction());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getReason());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getTotalCharge());
            ps.setInt(++index, drSupplementaryInspectionDetailsDto.getRefNo());

            if (ps.executeUpdate() > 0) {
                return drSupplementaryInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public DrSupplementaryInspectionDetailsDto insertTemporary(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public DrSupplementaryInspectionDetailsDto updateTemporary(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public DrSupplementaryInspectionDetailsDto insertHistory(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public DrSupplementaryInspectionDetailsDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_DR_SUPPLEMENTARY);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getDrSupplementaryDetailsDto(rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public DrSupplementaryInspectionDetailsDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<DrSupplementaryInspectionDetailsDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    private DrSupplementaryInspectionDetailsDto getDrSupplementaryDetailsDto(ResultSet rst) {
        DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto = new DrSupplementaryInspectionDetailsDto();
        try {
            drSupplementaryInspectionDetailsDto.setInspectionId(rst.getInt("inspection_id"));
            drSupplementaryInspectionDetailsDto.setRefNo(rst.getInt("n_ref_no"));
            drSupplementaryInspectionDetailsDto.setPreAccidentValue(rst.getBigDecimal("pre_accident_value"));
            drSupplementaryInspectionDetailsDto.setSumInsured(rst.getBigDecimal("sum_insured"));
            drSupplementaryInspectionDetailsDto.setExcess(rst.getBigDecimal("excess"));
            drSupplementaryInspectionDetailsDto.setAcr(rst.getBigDecimal("acr"));
            drSupplementaryInspectionDetailsDto.setOldAcr(rst.getBigDecimal("previous_acr"));
            drSupplementaryInspectionDetailsDto.setBoldTyrePenaltyAmount(rst.getBigDecimal("bold_tyre_penalty_amount"));
            drSupplementaryInspectionDetailsDto.setUnderInsurancePenaltyAmount(rst.getBigDecimal("under_insurance_penalty_amount"));
            drSupplementaryInspectionDetailsDto.setPayableAmount(rst.getBigDecimal("payable_amount"));
            drSupplementaryInspectionDetailsDto.setAssessorRemark(rst.getString("assessor_remark"));
            drSupplementaryInspectionDetailsDto.setInspectionRemark(rst.getString("inspection_remark"));
            drSupplementaryInspectionDetailsDto.setAriAndSalvage(rst.getString("ari_and_salvage").equals("Y") ? ConditionType.Yes : ConditionType.No);
            drSupplementaryInspectionDetailsDto.setProfessionalFee(rst.getBigDecimal("professional_fee"));
            drSupplementaryInspectionDetailsDto.setMiles(rst.getBigDecimal("miles"));
            drSupplementaryInspectionDetailsDto.setTelephoneCharge(rst.getBigDecimal("telephone_charge"));
            drSupplementaryInspectionDetailsDto.setOtherCharge(rst.getBigDecimal("other_charge"));
            drSupplementaryInspectionDetailsDto.setSpecialDeduction(rst.getBigDecimal("special_deduction"));
            drSupplementaryInspectionDetailsDto.setReason(rst.getString("reason"));
            drSupplementaryInspectionDetailsDto.setTotalCharge(rst.getBigDecimal("total_charge"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return drSupplementaryInspectionDetailsDto;
    }

    @Override
    public DrSupplementaryInspectionDetailsDto updateDrInspectionDetailMaster(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_DR_SUPPLEMENTARY_INSPECTION_DETAIL);
            ps.setInt(++index, drSupplementaryInspectionDetailsDto.getInspectionId());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getSumInsured());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getPreAccidentValue());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getExcess());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getAcr());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getOldAcr());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getBoldTyrePenaltyAmount());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getUnderInsurancePenaltyAmount());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getPayableAmount());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getAssessorRemark());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getInspectionRemark());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getAriAndSalvage().getCondtionType());
            ps.setInt(++index, drSupplementaryInspectionDetailsDto.getRefNo());

            if (ps.executeUpdate() > 0) {
                return drSupplementaryInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public DrSupplementaryInspectionDetailsDto updateDrAssessorFeeDetailMaster(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_DR_SUPPLEMENTARY_ASSESSOR_FEE_DETAIL);
            ps = connection.prepareStatement(SQL_UPDATE_DR_SUPPLEMENTARY);
            ps.setInt(++index, drSupplementaryInspectionDetailsDto.getInspectionId());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getMiles());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getTelephoneCharge());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getOtherCharge());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getSpecialDeduction());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getReason());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getTotalCharge());
            ps.setInt(++index, drSupplementaryInspectionDetailsDto.getRefNo());
            if (ps.executeUpdate() > 0) {
                return drSupplementaryInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }


    @Override
    public DrSupplementaryInspectionDetailsDto insertDrInspectionDetailMaster(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_DR_SUPPLEMENTARY_INSPECTION_DETAIL);
            ps.setInt(++index, drSupplementaryInspectionDetailsDto.getInspectionId());
            ps.setInt(++index, drSupplementaryInspectionDetailsDto.getRefNo());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getSumInsured());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getPreAccidentValue());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getExcess());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getAcr());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getOldAcr());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getBoldTyrePenaltyAmount());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getUnderInsurancePenaltyAmount());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getPayableAmount());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getAssessorRemark());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getInspectionRemark());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getAriAndSalvage().getCondtionType());

            if (ps.executeUpdate() > 0) {
                return drSupplementaryInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public DrSupplementaryInspectionDetailsDto insertDrAssessorFeeDetailMaster(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_DR_SUPPLEMENTARY_ASSESSOR_FEE_DETAIL);
            ps.setInt(++index, drSupplementaryInspectionDetailsDto.getInspectionId());
            ps.setInt(++index, drSupplementaryInspectionDetailsDto.getRefNo());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getMiles());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getTelephoneCharge());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getOtherCharge());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getSpecialDeduction());
            ps.setString(++index, drSupplementaryInspectionDetailsDto.getReason());
            ps.setBigDecimal(++index, drSupplementaryInspectionDetailsDto.getTotalCharge());

            if (ps.executeUpdate() > 0) {
                return drSupplementaryInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public DrSupplementaryInspectionDetailsDto getDrSupplementaryInspectionDetails(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto, Object id) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_DR_SUPPLEMENTARY_DETAILS);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getDrSupplementaryInspectionDetailsDto(drSupplementaryInspectionDetailsDto, rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public DrSupplementaryInspectionDetailsDto getDrSupplementaryAssessorFeeDetails(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto, Object id) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_DR_SUPPLEMENTARY_ASSESSOR_FEE_DETAILS);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getDrSupplementaryAssessorFeeDetailsDto(drSupplementaryInspectionDetailsDto, rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public BigDecimal getAcr(Connection connection, Integer refNo) {
        PreparedStatement ps = null;
        BigDecimal acr = BigDecimal.ZERO;
        try {
            ps = connection.prepareStatement(SQL_SELECT_ACR_BY_REF_NO);
            ps.setObject(1, refNo);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                acr = rs.getBigDecimal("acr");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return acr;
    }

    private DrSupplementaryInspectionDetailsDto getDrSupplementaryAssessorFeeDetailsDto(DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto, ResultSet rst) {
        try {
            drSupplementaryInspectionDetailsDto.setProfessionalFee(rst.getBigDecimal("professional_fee"));
            drSupplementaryInspectionDetailsDto.setMiles(rst.getBigDecimal("miles"));
            drSupplementaryInspectionDetailsDto.setTelephoneCharge(rst.getBigDecimal("telephone_charge"));
            drSupplementaryInspectionDetailsDto.setOtherCharge(rst.getBigDecimal("other_charge"));
            drSupplementaryInspectionDetailsDto.setSpecialDeduction(rst.getBigDecimal("special_deduction"));
            drSupplementaryInspectionDetailsDto.setReason(rst.getString("reason"));
            drSupplementaryInspectionDetailsDto.setTotalCharge(rst.getBigDecimal("total_charge"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return drSupplementaryInspectionDetailsDto;
    }

    private DrSupplementaryInspectionDetailsDto getDrSupplementaryInspectionDetailsDto(DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto, ResultSet rst) {
        try {
            drSupplementaryInspectionDetailsDto.setInspectionId(rst.getInt("inspection_id"));
            drSupplementaryInspectionDetailsDto.setRefNo(rst.getInt("n_ref_no"));
            drSupplementaryInspectionDetailsDto.setPreAccidentValue(rst.getBigDecimal("pre_accident_value"));
            drSupplementaryInspectionDetailsDto.setSumInsured(rst.getBigDecimal("sum_insured"));
            drSupplementaryInspectionDetailsDto.setExcess(rst.getBigDecimal("excess"));
            drSupplementaryInspectionDetailsDto.setAcr(rst.getBigDecimal("acr"));
            drSupplementaryInspectionDetailsDto.setBoldTyrePenaltyAmount(rst.getBigDecimal("bold_tyre_penalty_amount"));
            drSupplementaryInspectionDetailsDto.setUnderInsurancePenaltyAmount(rst.getBigDecimal("under_insurance_penalty_amount"));
            drSupplementaryInspectionDetailsDto.setPayableAmount(rst.getBigDecimal("payable_amount"));
            drSupplementaryInspectionDetailsDto.setAssessorRemark(rst.getString("assessor_remark"));
            drSupplementaryInspectionDetailsDto.setInspectionRemark(rst.getString("inspection_remark"));
            drSupplementaryInspectionDetailsDto.setAriAndSalvage(rst.getString("ari_and_salvage").equals("Y") ? ConditionType.Yes : ConditionType.No);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return drSupplementaryInspectionDetailsDto;
    }


}
