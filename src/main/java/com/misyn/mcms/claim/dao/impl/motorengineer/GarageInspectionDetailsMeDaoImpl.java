package com.misyn.mcms.claim.dao.impl.motorengineer;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.motorengineer.GarageInspectionDetailsMeDao;
import com.misyn.mcms.claim.dto.GarageInspectionDetailsDto;
import com.misyn.mcms.claim.enums.ConditionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
public class GarageInspectionDetailsMeDaoImpl extends BaseAbstract<GarageInspectionDetailsMeDaoImpl> implements GarageInspectionDetailsMeDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(DrSupplementaryInspectionDetailsMeDaoImpl.class);

    @Override
    public GarageInspectionDetailsDto insertMaster(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_INTO_GRAGE_INSPECTION_DETAILS);
            ps.setInt(++index, garageInspectionDetailsDto.getInspectionId());
            ps.setInt(++index, garageInspectionDetailsDto.getRefNo());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getExcess());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getAcr());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getBoldTyrePenaltyAmount());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getUnderInsurancePenaltyAmount());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getPayableAmount());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getAdvancedAmount());
            ps.setString(++index, garageInspectionDetailsDto.getSettlementMethod());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getOfferAmount());
            ps.setString(++index, garageInspectionDetailsDto.getInspectionRemark());
            ps.setString(++index, garageInspectionDetailsDto.getPoliceReportRequested().getCondtionType());
            ps.setString(++index, garageInspectionDetailsDto.getSpecialRemark());
            ps.setString(++index, garageInspectionDetailsDto.getInvestigateClaim().getCondtionType());
            ps.setString(++index, garageInspectionDetailsDto.getAriAndSalvage().getCondtionType());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getPreAccidentValue());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getMiles());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getTelephoneCharge());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getOtherCharge());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getSpecialDeduction());
            ps.setString(++index, garageInspectionDetailsDto.getReason());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getTotalCharge());

            if (ps.executeUpdate() > 0) {
                return garageInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public GarageInspectionDetailsDto updateMaster(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_GARAGE_INSPECTION);
            ps.setInt(++index, garageInspectionDetailsDto.getInspectionId());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getExcess());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getAcr());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getBoldTyrePenaltyAmount());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getUnderInsurancePenaltyAmount());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getPayableAmount());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getAdvancedAmount());
            ps.setString(++index, garageInspectionDetailsDto.getSettlementMethod());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getOfferAmount());
            ps.setString(++index, garageInspectionDetailsDto.getInspectionRemark());
            ps.setString(++index, garageInspectionDetailsDto.getPoliceReportRequested().getCondtionType());
            ps.setString(++index, garageInspectionDetailsDto.getSpecialRemark());
            ps.setString(++index, garageInspectionDetailsDto.getInvestigateClaim().getCondtionType());
            ps.setString(++index, garageInspectionDetailsDto.getAriAndSalvage().getCondtionType());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getPreAccidentValue());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getMiles());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getTelephoneCharge());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getOtherCharge());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getSpecialDeduction());
            ps.setString(++index, garageInspectionDetailsDto.getReason());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getTotalCharge());
            ps.setInt(++index, garageInspectionDetailsDto.getRefNo());

            if (ps.executeUpdate() > 0) {
                return garageInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public GarageInspectionDetailsDto insertTemporary(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public GarageInspectionDetailsDto updateTemporary(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public GarageInspectionDetailsDto insertHistory(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public GarageInspectionDetailsDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_GARAGE_INSPECTION);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getGarageInspection(rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public GarageInspectionDetailsDto searchTemporary(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_GARAGE_INSPECTION);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getGarageInspection(rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public List<GarageInspectionDetailsDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    private GarageInspectionDetailsDto getGarageInspection(ResultSet rst) {
        GarageInspectionDetailsDto garageInspectionDetailsDto = new GarageInspectionDetailsDto();
        try {

            garageInspectionDetailsDto.setInspectionId(rst.getInt("inspection_id"));
            garageInspectionDetailsDto.setRefNo(rst.getInt("n_ref_no"));
            garageInspectionDetailsDto.setExcess(rst.getBigDecimal("excess"));
            garageInspectionDetailsDto.setAcr(rst.getBigDecimal("acr"));
            garageInspectionDetailsDto.setOldAcr(rst.getBigDecimal("previous_acr"));
            garageInspectionDetailsDto.setBoldTyrePenaltyAmount(rst.getBigDecimal("bold_tyre_penalty_amount"));
            garageInspectionDetailsDto.setUnderInsurancePenaltyAmount(rst.getBigDecimal("under_insurance_penalty_amount"));
            garageInspectionDetailsDto.setPayableAmount(rst.getBigDecimal("payable_amount"));
            garageInspectionDetailsDto.setAdvancedAmount(rst.getBigDecimal("advanced_amount"));
            garageInspectionDetailsDto.setSettlementMethod(rst.getString("settlement_method"));
            garageInspectionDetailsDto.setOfferAmount(rst.getBigDecimal("offer_amount"));
            garageInspectionDetailsDto.setInspectionRemark(rst.getString("inspection_remark"));
            garageInspectionDetailsDto.setPoliceReportRequested(rst.getString("police_report_requested").equals("Y") ? ConditionType.Yes : ConditionType.No);
            garageInspectionDetailsDto.setInvestigateClaim(rst.getString("investigaed_claim").equals("Y") ? ConditionType.Yes : ConditionType.No);
            garageInspectionDetailsDto.setAriAndSalvage(rst.getString("ari_and_salvage").equals("Y") ? ConditionType.Yes : ConditionType.No);
            garageInspectionDetailsDto.setPreAccidentValue(rst.getBigDecimal("pre_accident_value"));
            garageInspectionDetailsDto.setProfessionalFee(rst.getBigDecimal("professional_fee"));
            garageInspectionDetailsDto.setMiles(rst.getBigDecimal("miles"));
            garageInspectionDetailsDto.setTelephoneCharge(rst.getBigDecimal("telephone_charge"));
            garageInspectionDetailsDto.setOtherCharge(rst.getBigDecimal("other_charge"));
            garageInspectionDetailsDto.setSpecialDeduction(rst.getBigDecimal("special_deduction"));
            garageInspectionDetailsDto.setReason(rst.getString("reason"));
            garageInspectionDetailsDto.setTotalCharge(rst.getBigDecimal("total_charge"));
            garageInspectionDetailsDto.setAdvanceChange(rst.getString("advance_amount_action"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return garageInspectionDetailsDto;
    }

    @Override
    public GarageInspectionDetailsDto updateGarageInspectionDetailMaster(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto) throws Exception {

        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_GARAGE_INSPECTION_DETAIL_MASTER);
            ps.setInt(++index, garageInspectionDetailsDto.getInspectionId());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getExcess());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getAcr());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getOldAcr());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getBoldTyrePenaltyAmount());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getUnderInsurancePenaltyAmount());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getPayableAmount());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getAdvancedAmount());
            ps.setString(++index, garageInspectionDetailsDto.getSettlementMethod());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getOfferAmount());
            ps.setString(++index, garageInspectionDetailsDto.getInspectionRemark());
            ps.setString(++index, garageInspectionDetailsDto.getPoliceReportRequested().getCondtionType());
            ps.setString(++index, garageInspectionDetailsDto.getSpecialRemark());
            ps.setString(++index, garageInspectionDetailsDto.getInvestigateClaim().getCondtionType());
            ps.setString(++index, garageInspectionDetailsDto.getAriAndSalvage().getCondtionType());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getPreAccidentValue());
            ps.setString(++index, garageInspectionDetailsDto.getAdvanceChange());
            ps.setInt(++index, garageInspectionDetailsDto.getRefNo());

            if (ps.executeUpdate() > 0) {
                return garageInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;

    }

    @Override
    public GarageInspectionDetailsDto updateGarageAssessorFeeDetailMaster(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_GARAGE_ASSESSOR_FEE_DETAIL_MASTER);
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getMiles());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getTelephoneCharge());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getOtherCharge());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getSpecialDeduction());
            ps.setString(++index, garageInspectionDetailsDto.getReason());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getTotalCharge());
            ps.setInt(++index, garageInspectionDetailsDto.getRefNo());

            if (ps.executeUpdate() > 0) {
                return garageInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }


    @Override
    public GarageInspectionDetailsDto insertGarageInspectionDetailMaster(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_GARAGE_INSPECTION_DETAIL_MASTER);
            ps.setInt(++index, garageInspectionDetailsDto.getInspectionId());
            ps.setInt(++index, garageInspectionDetailsDto.getRefNo());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getExcess());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getAcr());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getOldAcr());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getBoldTyrePenaltyAmount());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getUnderInsurancePenaltyAmount());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getPayableAmount());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getAdvancedAmount());
            ps.setString(++index, garageInspectionDetailsDto.getSettlementMethod());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getOfferAmount());
            ps.setString(++index, garageInspectionDetailsDto.getInspectionRemark());
            ps.setString(++index, garageInspectionDetailsDto.getPoliceReportRequested().getCondtionType());
            ps.setString(++index, garageInspectionDetailsDto.getSpecialRemark());
            ps.setString(++index, garageInspectionDetailsDto.getInvestigateClaim().getCondtionType());
            ps.setString(++index, garageInspectionDetailsDto.getAriAndSalvage().getCondtionType());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getPreAccidentValue());
            ps.setString(++index, garageInspectionDetailsDto.getAdvanceChange());

            if (ps.executeUpdate() > 0) {
                return garageInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public GarageInspectionDetailsDto insertGarageAssessorFeeDetailMaster(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_GARAGE_ASSESSOR_FEE_DETAIL_MASTER);
            ps.setInt(++index, garageInspectionDetailsDto.getInspectionId());
            ps.setInt(++index, garageInspectionDetailsDto.getRefNo());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getProfessionalFee());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getMiles());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getTelephoneCharge());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getOtherCharge());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getSpecialDeduction());
            ps.setString(++index, garageInspectionDetailsDto.getReason());
            ps.setBigDecimal(++index, garageInspectionDetailsDto.getTotalCharge());

            if (ps.executeUpdate() > 0) {
                return garageInspectionDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public GarageInspectionDetailsDto getGarageInspectionDetails(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto, Object id) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_GARAGE_INSPECTION_DETAILS);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getGarageInspectionDetailsDto(garageInspectionDetailsDto, rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public GarageInspectionDetailsDto getGarageAssessorFeeDetails(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto, Object id) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_GARAGE_ASSESSOR_FEE_DETAILS);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getGarageInspectionAssessorFeeDetailsDto(garageInspectionDetailsDto, rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public BigDecimal getAcr(Connection connection, Integer refNo) {
        PreparedStatement ps = null;
        BigDecimal acr = BigDecimal.ZERO;
        try {
            ps = connection.prepareStatement(SQL_SELECT_ACR_BY_REF_NO);
            ps.setObject(1, refNo);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                acr = rs.getBigDecimal("acr");
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return acr;
    }

    private GarageInspectionDetailsDto getGarageInspectionAssessorFeeDetailsDto(GarageInspectionDetailsDto garageInspectionDetailsDto, ResultSet rst) {
        try {

            garageInspectionDetailsDto.setInspectionId(rst.getInt("inspection_id"));
            garageInspectionDetailsDto.setRefNo(rst.getInt("n_ref_no"));
            garageInspectionDetailsDto.setProfessionalFee(rst.getBigDecimal("professional_fee"));
            garageInspectionDetailsDto.setMiles(rst.getBigDecimal("miles"));
            garageInspectionDetailsDto.setTelephoneCharge(rst.getBigDecimal("telephone_charge"));
            garageInspectionDetailsDto.setOtherCharge(rst.getBigDecimal("other_charge"));
            garageInspectionDetailsDto.setSpecialDeduction(rst.getBigDecimal("special_deduction"));
            garageInspectionDetailsDto.setReason(rst.getString("reason"));
            garageInspectionDetailsDto.setTotalCharge(rst.getBigDecimal("total_charge"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return garageInspectionDetailsDto;
    }

    private GarageInspectionDetailsDto getGarageInspectionDetailsDto(GarageInspectionDetailsDto garageInspectionDetailsDto, ResultSet rst) {
        try {
            garageInspectionDetailsDto.setInspectionId(rst.getInt("inspection_id"));
            garageInspectionDetailsDto.setRefNo(rst.getInt("n_ref_no"));
            garageInspectionDetailsDto.setExcess(rst.getBigDecimal("excess"));
            garageInspectionDetailsDto.setAcr(rst.getBigDecimal("acr"));
            garageInspectionDetailsDto.setBoldTyrePenaltyAmount(rst.getBigDecimal("bold_tyre_penalty_amount"));
            garageInspectionDetailsDto.setUnderInsurancePenaltyAmount(rst.getBigDecimal("under_insurance_penalty_amount"));
            garageInspectionDetailsDto.setPayableAmount(rst.getBigDecimal("payable_amount"));
            garageInspectionDetailsDto.setAdvancedAmount(rst.getBigDecimal("advanced_amount"));
            garageInspectionDetailsDto.setSettlementMethod(rst.getString("settlement_method"));
            garageInspectionDetailsDto.setOfferAmount(rst.getBigDecimal("offer_amount"));
            garageInspectionDetailsDto.setInspectionRemark(rst.getString("inspection_remark"));
            garageInspectionDetailsDto.setPoliceReportRequested(rst.getString("police_report_requested").equals("Y") ? ConditionType.Yes : ConditionType.No);
            garageInspectionDetailsDto.setInvestigateClaim(rst.getString("investigaed_claim").equals("Y") ? ConditionType.Yes : ConditionType.No);
            garageInspectionDetailsDto.setAriAndSalvage(rst.getString("ari_and_salvage").equals("Y") ? ConditionType.Yes : ConditionType.No);
            garageInspectionDetailsDto.setPreAccidentValue(rst.getBigDecimal("pre_accident_value"));
            garageInspectionDetailsDto.setAdvanceChange(rst.getString("advance_amount_action"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return garageInspectionDetailsDto;
    }

}
