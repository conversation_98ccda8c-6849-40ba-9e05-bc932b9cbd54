package com.misyn.mcms.claim.dao.impl.motorengineer;

import com.misyn.mcms.claim.dao.BaseAbstract;
import com.misyn.mcms.claim.dao.motorengineer.MotorEngineerDetailsDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.motorengineer.MotorEngineerDetailsDto;
import com.misyn.mcms.claim.enums.AccidentStatus;
import com.misyn.mcms.claim.enums.ConditionType;
import com.misyn.mcms.claim.enums.SelectionType;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
public class MotorEngineerDetailsDaoImpl extends BaseAbstract<MotorEngineerDetailsDaoImpl> implements MotorEngineerDetailsDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(MotorEngineerDetailsDaoImpl.class);

    @Override
    public MotorEngineerDetailsDto insertMaster(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_CLAIM_INSPECTION_INFO_MAIN);
            ps.setInt(++index, motorEngineerDetailsDto.getRefNo());
            ps.setString(++index, motorEngineerDetailsDto.getJobId());
            ps.setInt(++index, motorEngineerDetailsDto.getClaimNo());
            ps.setString(++index, motorEngineerDetailsDto.getMakeConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getModelConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getEngNoConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getChassisNo());
            ps.setString(++index, motorEngineerDetailsDto.getYearMakeConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getInspectDateTime());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getPav());
            ps.setString(++index, motorEngineerDetailsDto.getDamageDetails());
            ps.setString(++index, motorEngineerDetailsDto.getPad());
            ps.setString(++index, motorEngineerDetailsDto.getGenuineOfAccident().getAccidentStatus());
            ps.setString(++index, motorEngineerDetailsDto.getFirstStatementReq().getCondtionType());
            ps.setString(++index, motorEngineerDetailsDto.getFirstStatementReqReason());
            ps.setString(++index, motorEngineerDetailsDto.getInvestReq().getCondtionType());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorSpecialRemark());
            ps.setInt(++index, motorEngineerDetailsDto.getInspectionDto().getInspectionId());
            ps.setInt(++index, motorEngineerDetailsDto.getJobType());
            ps.setString(++index, motorEngineerDetailsDto.getAssignedLocation());
            ps.setString(++index, motorEngineerDetailsDto.getCurrentLocation());
            ps.setString(++index, motorEngineerDetailsDto.getPlaceOfInspection());
            ps.setInt(++index, motorEngineerDetailsDto.getMileage());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getCostOfCall());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getOtherFee());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getDeductions());
            ps.setString(++index, motorEngineerDetailsDto.getReasonOfDeduction());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getTotalAssessorFee());
            ps.setString(++index, motorEngineerDetailsDto.getFeeDesc());
            ps.setInt(++index, motorEngineerDetailsDto.getRecordStatus());
            ps.setString(++index, motorEngineerDetailsDto.getInputUserId());
            ps.setString(++index, motorEngineerDetailsDto.getInputDatetime());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorFeeAuthStatus());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorFeeAuthUserId());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorFeeAuthDatetime());
            ps.setString(++index, motorEngineerDetailsDto.getInspectionDetailsAuthStatus());
            ps.setString(++index, motorEngineerDetailsDto.getInspectionDetailsAuthUserId());
            ps.setString(++index, motorEngineerDetailsDto.getInspectionDetailsAuthDatetime());
            ps.setString(++index, null == motorEngineerDetailsDto.getChassisNoConfirm() || null == motorEngineerDetailsDto.getChassisNoConfirm().getSelectionType() ? AppConstant.STRING_PENDING : motorEngineerDetailsDto.getChassisNoConfirm().getSelectionType());
            ps.setInt(++index, motorEngineerDetailsDto.getNotCheckedReason());

            if (ps.executeUpdate() > 0) {
                return motorEngineerDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public MotorEngineerDetailsDto updateMaster(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_INSPECTION_DEATIL_MASTER);
            ps.setString(++index, motorEngineerDetailsDto.getAssessorAllocationDto().getJobId());
            ps.setInt(++index, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo());
            ps.setString(++index, motorEngineerDetailsDto.getMakeConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getModelConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getEngNoConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getChassisNo());
            ps.setString(++index, motorEngineerDetailsDto.getYearMakeConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getInspectDateTime());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getPav());
            ps.setString(++index, motorEngineerDetailsDto.getDamageDetails());
            ps.setString(++index, motorEngineerDetailsDto.getPad());
            ps.setString(++index, motorEngineerDetailsDto.getGenuineOfAccident().getAccidentStatus());
            ps.setString(++index, motorEngineerDetailsDto.getFirstStatementReq().getCondtionType());
            ps.setString(++index, motorEngineerDetailsDto.getFirstStatementReqReason());
            ps.setString(++index, motorEngineerDetailsDto.getInvestReq().getCondtionType());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorSpecialRemark());
            ps.setInt(++index, motorEngineerDetailsDto.getInspectionDto().getInspectionId());
            ps.setInt(++index, motorEngineerDetailsDto.getJobType());
            ps.setString(++index, motorEngineerDetailsDto.getAssignedLocation());
            ps.setString(++index, motorEngineerDetailsDto.getCurrentLocation());
            ps.setString(++index, motorEngineerDetailsDto.getPlaceOfInspection());
            ps.setInt(++index, motorEngineerDetailsDto.getMileage());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getCostOfCall());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getOtherFee());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getDeductions());
            ps.setString(++index, motorEngineerDetailsDto.getReasonOfDeduction());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getTotalAssessorFee());
            ps.setString(++index, motorEngineerDetailsDto.getFeeDesc());
            ps.setInt(++index, motorEngineerDetailsDto.getRecordStatus());
            ps.setString(++index, motorEngineerDetailsDto.getInputUserId());
            ps.setString(++index, motorEngineerDetailsDto.getInputDatetime());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorFeeAuthStatus());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorFeeAuthUserId());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorFeeAuthDatetime());
            ps.setString(++index, motorEngineerDetailsDto.getInspectionDetailsAuthStatus());
            ps.setString(++index, motorEngineerDetailsDto.getInspectionDetailsAuthUserId());
            ps.setString(++index, motorEngineerDetailsDto.getInspectionDetailsAuthDatetime());
            ps.setString(++index, motorEngineerDetailsDto.getChassisNoConfirm().getSelectionType());
            ps.setInt(++index, motorEngineerDetailsDto.getNotCheckedReason());
            ps.setInt(++index, motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());

            if (ps.executeUpdate() > 0) {
                return motorEngineerDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public MotorEngineerDetailsDto insertTemporary(Connection connection, MotorEngineerDetailsDto inspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public MotorEngineerDetailsDto updateTemporary(Connection connection, MotorEngineerDetailsDto inspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public MotorEngineerDetailsDto insertHistory(Connection connection, MotorEngineerDetailsDto inspectionDetailsDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public MotorEngineerDetailsDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_BY_REF_NO);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getInspctionDetalsDto(rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public MotorEngineerDetailsDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<MotorEngineerDetailsDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public DataGridDto getJobDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate) {
        int index = start;
        DataGridDto dataGridDTO = new DataGridDto();
        int count = 0;
        List jobList = new ArrayList(200);

        PreparedStatement ps = null;
        String SQL_SEARCH = formatSQL(parameterList).toString();
        final String SQL_ORDER = formatOrderSQL(start, length, orderType, orderField).toString();

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL_SEARCH = SQL_SEARCH.concat(" AND t3.assign_datetime BETWEEN" + "'" + fromDate + "'" + "AND " + "'" + toDate + "'");
        }

        final String SEL_SQL = SQL_SELECT_ALL_TO_GRID.concat(SQL_SEARCH).concat(SQL_ORDER);
        ;

        final String COUNT_SQL = SQL_SELECT_COUNT_TO_GRID.concat(SQL_SEARCH);
        try {

            ps = conn.prepareStatement(COUNT_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    count = rs.getInt("cnt");
                }
                rs.close();
            }
            ps = conn.prepareStatement(SEL_SQL, ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    InspectionDetailsGridDto gridDto = new InspectionDetailsGridDto();
                    gridDto.setRefNo(rs.getInt("t3.ref_no"));
                    gridDto.setClaimNo(rs.getInt("t1.N_CLIM_NO"));
                    gridDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NO"));
                    gridDto.setJobNo(rs.getString("t3.job_id"));
                    gridDto.setInspectionType(rs.getString("t2.inspection_type_desc"));
                    gridDto.setStatusId(rs.getInt("t3.record_status"));
                    gridDto.setPolRefNo(rs.getInt("t1.N_POL_REF_NO"));
                    gridDto.setAssignDateTime(Utility.getDate(rs.getString("t3.assign_datetime"), AppConstant.DATE_TIME_FORMAT));
                    gridDto.setIntimationType(rs.getInt("t1.N_INTIMATION_TYPE"));
                    gridDto.setJobStatus(rs.getString("t4.v_status_desc"));

                    long[] hours = Utility.getDayHoursMinSecondDifference(gridDto.getAssignDateTime(), Utility.sysDateTime(), AppConstant.DATE_TIME_FORMAT);
                    long diffHours = hours[1];
                    long days = hours[0];
                    diffHours = (days * 24) + diffHours;
                    if (diffHours > 24 && diffHours <= 48) {
                        gridDto.setExcessType(1);
                    } else if (diffHours > 48) {
                        gridDto.setExcessType(2);
                    } else {
                        gridDto.setExcessType(0);
                    }

                    gridDto.setIndex(++index);
                    jobList.add(gridDto);
                }
                rs.close();
            }
            dataGridDTO.setDraw(drawRandomId);
            dataGridDTO.setRecordsTotal(count);
            dataGridDTO.setRecordsFiltered(count);
            dataGridDTO.setData(jobList);

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception ex) {
            }
        }
        return dataGridDTO;

    }

    @Override
    public List<Integer> getClaimListByPolNo(Integer polRefNo, Connection connection) throws Exception {
        PreparedStatement ps = null;
        List<Integer> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_CLAIM_LIST_BY_POL_REF_NO);
            ps.setInt(1, polRefNo);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                list.add(rs.getInt("N_CLIM_NO"));
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<PreviousClaimsDto> getPreviousClaimList(Integer claimNo, Integer jobNo, Connection connection) throws Exception {
        PreparedStatement ps;
        List<PreviousClaimsDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_INSPECTION_LIST_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            ps.setInt(2, jobNo);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                PreviousClaimsDto previousClaimsDto = new PreviousClaimsDto();
                previousClaimsDto.setJobNo(rs.getString("t3.job_id"));
                previousClaimsDto.setDateOfAccident(rs.getString("t2.D_ACCID_DATE"));
                previousClaimsDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NUMBER"));
                previousClaimsDto.setPolicyNo(rs.getString("t1.V_POL_NUMBER"));
                previousClaimsDto.setInspectionType(rs.getString("t5.inspection_type_desc"));
                previousClaimsDto.setPolicyRefNo(rs.getInt("t1.N_POL_REF_NO"));
                list.add(previousClaimsDto);
            }
            ps.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<PreviousClaimsDto> searchAllByClaimNo(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        List<PreviousClaimsDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_INSPECTION_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                PreviousClaimsDto previousClaimsDto = new PreviousClaimsDto();
                previousClaimsDto.setJobRefNo(rs.getInt("t3.ref_no"));
                previousClaimsDto.setJobNo(rs.getString("t3.job_id"));
                previousClaimsDto.setDateOfAccident(rs.getString("t2.D_ACCID_DATE"));
                previousClaimsDto.setVehicleNo(rs.getString("t1.V_VEHICLE_NUMBER"));
                previousClaimsDto.setPolicyNo(rs.getString("t1.V_POL_NUMBER"));
                previousClaimsDto.setInspectionType(rs.getString("t5.inspection_type_desc"));
                previousClaimsDto.setPolicyRefNo(rs.getInt("t1.N_POL_REF_NO"));
                list.add(previousClaimsDto);
            }
            ps.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    private MotorEngineerDetailsDto getInspctionDetalsDto(ResultSet rs) {
        MotorEngineerDetailsDto inspectionDetailsDto = new MotorEngineerDetailsDto();
        try {
            AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
            assessorAllocationDto.setJobId(rs.getString("v_job_no"));
            ClaimsDto claimsDto = new ClaimsDto();
            claimsDto.setClaimNo(rs.getInt("n_claim_no"));

            inspectionDetailsDto.setJobId(rs.getString("v_job_no"));
            inspectionDetailsDto.setClaimNo(rs.getInt("n_claim_no"));
            inspectionDetailsDto.setInspectionId(rs.getInt("n_inspection_id"));
            inspectionDetailsDto.setRefNo(rs.getInt("n_ref_no"));
            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);

            inspectionDetailsDto.setMakeConfirm(rs.getString("v_make_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_make_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_make_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);


            inspectionDetailsDto.setModelConfirm(rs.getString("v_model_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_model_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_model_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);


            inspectionDetailsDto.setEngNoConfirm(rs.getString("v_eng_no_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_eng_no_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_eng_no_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);

            inspectionDetailsDto.setChassisNo(rs.getString("v_chassis_no"));
            inspectionDetailsDto.setYearMakeConfirm(rs.getString("v_year_make_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_year_make_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_year_make_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);
            inspectionDetailsDto.setInspectDateTime(Utility.getCustomDateFormat(rs.getString("d_inspect_datetime"), AppConstant.DATE_TIME_FORMAT, AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
            inspectionDetailsDto.setPav(rs.getBigDecimal("n_pav"));
            inspectionDetailsDto.setDamageDetails(rs.getString("v_damage_details"));
            inspectionDetailsDto.setPad(rs.getString("v_pad"));
            if (rs.getString("v_genun_of_accid").equals("C")) {
                inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Consistent);
            } else if (rs.getString("v_genun_of_accid").equals("D")) {
                inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Doubtful);
            } else {
                inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Non_Consistence);
            }
            inspectionDetailsDto.setFirstStatementReq(rs.getString("v_first_statement_rqed").equals("Y") ? ConditionType.Yes : ConditionType.No);
            inspectionDetailsDto.setFirstStatementReqReason(rs.getString("v_first_statement_req_reason"));
            inspectionDetailsDto.setInvestReq(rs.getString("v_invest_rqed").equals("Y") ? ConditionType.Yes : ConditionType.No);
            inspectionDetailsDto.setAssessorSpecialRemark(rs.getString("v_assessor_remark"));
            InspectionDto inspectionDto = new InspectionDto();
            inspectionDto.setInspectionId(rs.getInt("n_inspection_type"));
            inspectionDetailsDto.setInspectionDto(inspectionDto);
            inspectionDetailsDto.setJobType(rs.getInt("n_job_type"));
            inspectionDetailsDto.setAssignedLocation(rs.getString("v_assigned_location"));
            inspectionDetailsDto.setCurrentLocation(rs.getString("v_current_location"));
            inspectionDetailsDto.setPlaceOfInspection(rs.getString("v_place_of_inspection"));
            inspectionDetailsDto.setMileage(rs.getInt("n_mileage"));
            inspectionDetailsDto.setCostOfCall(rs.getBigDecimal("n_cost_of_call"));
            inspectionDetailsDto.setOtherFee(rs.getBigDecimal("n_other_fee"));
            inspectionDetailsDto.setDeductions(rs.getBigDecimal("n_deductions"));
            inspectionDetailsDto.setReasonOfDeduction(rs.getString("v_reason_of_deduction"));
            inspectionDetailsDto.setTotalAssessorFee(rs.getBigDecimal("n_total_assessor_fee"));
            inspectionDetailsDto.setFeeDesc(rs.getString("v_fee_desc"));
            inspectionDetailsDto.setRecordStatus(rs.getInt("n_record_status"));
            inspectionDetailsDto.setInputUserId(rs.getString("v_inpuser"));
            inspectionDetailsDto.setInputDatetime(Utility.getDate(rs.getString("d_inpdatetime"), AppConstant.DATE_TIME_FORMAT));
            inspectionDetailsDto.setAssessorFeeAuthStatus(rs.getString("v_ass_fee_apr_status"));
            inspectionDetailsDto.setAssessorFeeAuthUserId(rs.getString("v_ass_fee_apr_user"));
            inspectionDetailsDto.setAssessorFeeAuthDatetime(rs.getString("v_ass_fee_apr_datetime"));
            inspectionDetailsDto.setInspectionDetailsAuthStatus(rs.getString("v_ass_esti_apr_status"));
            inspectionDetailsDto.setInspectionDetailsAuthUserId(rs.getString("v_ass_esti_apr_user"));
            inspectionDetailsDto.setInspectionDetailsAuthDatetime(rs.getString("v_ass_esti_apr_datetime"));
            inspectionDetailsDto.setChassisNoConfirm(rs.getString("v_chassis_no_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_chassis_no_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_chassis_no_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);
            inspectionDetailsDto.setNotCheckedReason(rs.getInt("n_not_checked_reason"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return inspectionDetailsDto;
    }

    @Override
    public void updateDesktopInformDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement("UPDATE desktop_inspection_details_me SET\n"
                    + "		inform_to_garage_name=?,\n"
                    + "		inform_to_garage_contact=?,\n"
                    + "		is_agree_garage=?,\n"
                    + "		inform_to_customer_name=?,\n"
                    + "		inform_to_customer_contact=?,\n"
                    + "		is_agree_customer=?,\n"
                    + "		reason_for_disagree=?,\n"
                    + "		desktop_comment=?,\n"
                    + "		is_informed=?,\n"
                    + "		v_informed_user=?,\n"
                    + "		d_informed_datetime=?\n"
                    + "WHERE n_ref_no = ?");
            ps.setString(1, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getInformToGarageName());
            ps.setString(2, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getInformToGarageContact());
            ps.setString(3, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getIsAgreeGarage());
            ps.setString(4, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getInformToCustomerName());
            ps.setString(5, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getInformToCustomerContact());
            ps.setString(6, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getIsAgreeCustomer());
            ps.setString(7, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getReasonForDisagree());
            ps.setString(8, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getDesktopComment());
            ps.setString(9, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getIsInformed());
            ps.setString(10, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getInformedUser());
            ps.setString(11, motorEngineerDetailsDto.getDesktopInspectionDetailsDto().getInformedDateTime());
            ps.setInt(12, motorEngineerDetailsDto.getRefNo());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    @Override
    public List<InspectionFormDto> getInspectionFormDtoList(Connection connection, Integer claimNo) {
        List<InspectionFormDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement("SELECT\n" +
                    "t1.n_ref_no,\n" +
                    "t2.inspection_type_id,\n" +
                    "t2.inspection_type_desc\n" +
                    "FROM\n" +
                    "claim_inspection_info_main AS t1\n" +
                    "INNER JOIN claim_inspection_type AS t2 ON t2.inspection_type_id = t1.n_inspection_type\n" +
                    "WHERE\n" +
                    "t1.n_claim_no = ?\n" +
                    "ORDER BY\n" +
                    "t1.n_ref_no ASC");
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                InspectionFormDto inspectionFormDto = new InspectionFormDto();
                inspectionFormDto.setRefNo(rs.getInt("t1.n_ref_no"));
                inspectionFormDto.setInspectionType(rs.getString("t2.inspection_type_desc"));
                list.add(inspectionFormDto);
            }
            rs.close();
            ps.close();

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<MotorEngineerDetailsDto> searchByClaimNo(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        List<MotorEngineerDetailsDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_BY_CLAIM_NO);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                list.add(getInspctionDetalsDto(rs));
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public MotorEngineerDetailsDto getLatestInspectionMotorEngineer(Connection connection, Integer claimNo) {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(SQL_SELECT_LATEST_ONSITE_INSPECTION_MOTOR_ENG);
            ps.setObject(1, claimNo);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return getInspctionDetalsDto(rs);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public boolean isInspectionExist(Connection connection, Integer claimNo, Integer inspectionType) {
        PreparedStatement ps;
        boolean result = false;
        try {
            ps = connection.prepareStatement(SQL_SELECT_LATEST_ONSITE_INSPECTION_MOTOR_ENG_BY_INSPECTION_TYPE);
            ps.setObject(1, inspectionType);
            ps.setObject(2, claimNo);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                result = true;
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return result;
    }

    @Override
    public String getAssignRte(Connection connection, String jobNo) throws Exception {
        PreparedStatement ps;
        String assignRte = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_ASSIGN_RTE);
            ps.setObject(1, jobNo);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                assignRte = rs.getString("v_assign_rte_user");
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return assignRte;
    }

    @Override
    public ContactDetailDto getContactDetails(Connection connection, String user) {
        PreparedStatement ps;
        ContactDetailDto contactDetailDto = new ContactDetailDto();
        try {
            ps = connection.prepareStatement(SQL_SELECT_MOBILE_NO_BY_ASSIGN_RTE);
            ps.setObject(1, user);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                contactDetailDto.setContactNo(rs.getString("v_mobile"));
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return contactDetailDto;
    }

    @Override
    public Integer getClaimNoByRefNo(Connection connection, Object id) {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(SQL_SELECT_CLAIM_NO_BY_REF_NO);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return (rs.getInt("n_claim_no"));
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public boolean isRteApprovedData(Connection connection, Integer claimNo) {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(SQL_ONE_SELECT_BY_CLAIM_NO);
            ps.setObject(1, claimNo);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public Integer getClaimAssignLossType(Connection connection, int claimNo) {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(SQL_COUSE_OF_LOSS_BY_CLAIM_NO);
            ps.setObject(1, claimNo);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return (rs.getInt("N_CAUSE_OF_LOSS"));
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public void setAssignUserAndLiabilityAssignUserNull(Connection connection, int claimNo) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_ASSIGN_USER_AND_LIABILITY_ASSIGN_USER_BY_CLAIM_NO);
            ps.setObject(1, claimNo);
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    @Override
    public BigDecimal getPreviousPavFromMe(Connection connection, Integer claimNo) {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(SELECT_PAV_FROM_INSPECTION_INFO_MAIN_ME);
            ps.setObject(1, claimNo);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return (rs.getBigDecimal("n_pav"));
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public BigDecimal getPreviousPav(Connection connection, Integer claimNo) {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(SELECT_PAV_FROM_INSPECTION_INFO_MAIN);
            ps.setObject(1, claimNo);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return (rs.getBigDecimal("n_pav"));
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public boolean isInspectionPending(Connection connection, Integer claimNo,Integer inspectionId) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_ONE_FROM_CLAIM_ASSIGN_ASSESSOR_IN_INSPECTION_PENDING);
            ps.setInt(1, claimNo);
            ps.setInt(2, inspectionId);
            rs = ps.executeQuery();
            return rs.next();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public List<String> getPendingInspectionReportingRteList(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_APPROVE_ASSIGN_RTE_USER_BY_CLAIM_NO);
            ps.setObject(1, claimNo);
            rs = ps.executeQuery();

            while (rs.next()) {
                if (null != rs.getString("v_approve_assign_rte_user") && !rs.getString("v_approve_assign_rte_user").isEmpty()) {
                    list.add(rs.getString("v_approve_assign_rte_user"));
                }
            }
            ps.close();
            rs.close();
            if (list.isEmpty()) {
                ps = connection.prepareStatement(SELECT_ASSIGN_RTE_USER_BY_CLAIM_NO);
                ps.setInt(1, claimNo);
                rs = ps.executeQuery();

                while (rs.next()) {
                    if (null != rs.getString("v_assign_rte_user") && !rs.getString("v_assign_rte_user").isEmpty()) {
                        list.add(rs.getString("v_assign_rte_user"));
                    }
                }
            }
            ps.close();
            rs.close();
            ps = connection.prepareStatement(SELECT_ASSIGN_ASSESOR_REPORTING_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();

            while (rs.next()) {
                if (null != rs.getString("t2.V_REPORT_TO") && !rs.getString("t2.V_REPORT_TO").isEmpty()) {
                    if (!list.contains(rs.getString("t2.V_REPORT_TO"))) {
                        list.add(rs.getString("t2.V_REPORT_TO"));
                    }
                }
            }
            ps.close();
            rs.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public boolean isAvailableApprovedInspectionByInspectionType(Connection connection, Integer claimNo, int inspectionType) {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(SELECT_IS_AVAILABLE_APPROVED_INSPECTION_BY_TYPE);
            ps.setObject(1, claimNo);
            ps.setObject(2, inspectionType);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public boolean isAvailablePendingInspection(Connection connection, int claimNo, int inspectionType) {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(SELECT_IS_AVAILABLE_PENDING_INSPECTION);
            ps.setObject(1, claimNo);
            ps.setObject(2, inspectionType);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return true;
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public MotorEngineerDetailsDto updateAssessorFeeInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_ASSESSOR_FEE_INSPECTION_DETAILS);
            ps.setInt(++index, motorEngineerDetailsDto.getJobType());
            ps.setString(++index, motorEngineerDetailsDto.getAssignedLocation());
            ps.setString(++index, motorEngineerDetailsDto.getCurrentLocation());
            ps.setString(++index, motorEngineerDetailsDto.getPlaceOfInspection());
            ps.setInt(++index, motorEngineerDetailsDto.getMileage());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getCostOfCall());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getOtherFee());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getDeductions());
            ps.setString(++index, motorEngineerDetailsDto.getReasonOfDeduction());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getTotalAssessorFee());
            ps.setString(++index, motorEngineerDetailsDto.getFeeDesc());
            ps.setString(++index, motorEngineerDetailsDto.getInputUserId());
            ps.setString(++index, motorEngineerDetailsDto.getInputDatetime());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorFeeAuthStatus());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorFeeAuthUserId());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorFeeAuthDatetime());
            ps.setInt(++index, motorEngineerDetailsDto.getInspectionDetailsDto().getAssessorFeeDetailId());
            ps.setInt(++index, motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());

            if (ps.executeUpdate() > 0) {
                return motorEngineerDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public MotorEngineerDetailsDto updateInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_ONLY_INSPECTION_DEATILS_AND_INSPECTION_REPORT_DETAILS);
            ps.setString(++index, motorEngineerDetailsDto.getAssessorAllocationDto().getJobId());
            ps.setInt(++index, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo());
            ps.setString(++index, motorEngineerDetailsDto.getMakeConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getModelConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getEngNoConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getChassisNo());
            ps.setString(++index, motorEngineerDetailsDto.getYearMakeConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getInspectDateTime());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getPav());
            ps.setString(++index, motorEngineerDetailsDto.getDamageDetails());
            ps.setString(++index, motorEngineerDetailsDto.getPad());
            ps.setString(++index, motorEngineerDetailsDto.getGenuineOfAccident().getAccidentStatus());
            ps.setString(++index, motorEngineerDetailsDto.getFirstStatementReq().getCondtionType());
            ps.setString(++index, motorEngineerDetailsDto.getFirstStatementReqReason());
            ps.setString(++index, motorEngineerDetailsDto.getInvestReq().getCondtionType());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorSpecialRemark());
            ps.setInt(++index, motorEngineerDetailsDto.getInspectionDto().getInspectionId());
            ps.setInt(++index, motorEngineerDetailsDto.getRecordStatus());
            ps.setString(++index, motorEngineerDetailsDto.getInputUserId());
            ps.setString(++index, motorEngineerDetailsDto.getInputDatetime());
            ps.setString(++index, motorEngineerDetailsDto.getInspectionDetailsAuthStatus());
            ps.setString(++index, motorEngineerDetailsDto.getInspectionDetailsAuthUserId());
            ps.setString(++index, motorEngineerDetailsDto.getInspectionDetailsAuthDatetime());
            ps.setString(++index, motorEngineerDetailsDto.getChassisNoConfirm().getSelectionType());
            ps.setInt(++index, motorEngineerDetailsDto.getNotCheckedReason());
            ps.setInt(++index, motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());

            if (ps.executeUpdate() > 0) {
                return motorEngineerDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public MotorEngineerDetailsDto updateInspectionReportDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_ONLY_INSPECTION_REPORT_DEATILS);
            ps.setString(++index, motorEngineerDetailsDto.getAssessorAllocationDto().getJobId());
            ps.setInt(++index, motorEngineerDetailsDto.getAssessorAllocationDto().getClaimsDto().getClaimNo());
            ps.setString(++index, motorEngineerDetailsDto.getMakeConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getModelConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getEngNoConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getChassisNo());
            ps.setString(++index, motorEngineerDetailsDto.getYearMakeConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getInspectDateTime());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getPav());
            ps.setString(++index, motorEngineerDetailsDto.getDamageDetails());
            ps.setString(++index, motorEngineerDetailsDto.getPad());
            ps.setString(++index, motorEngineerDetailsDto.getGenuineOfAccident().getAccidentStatus());
            ps.setString(++index, motorEngineerDetailsDto.getFirstStatementReq().getCondtionType());
            ps.setString(++index, motorEngineerDetailsDto.getFirstStatementReqReason());
            ps.setString(++index, motorEngineerDetailsDto.getInvestReq().getCondtionType());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorSpecialRemark());
            ps.setString(++index, motorEngineerDetailsDto.getInputUserId());
            ps.setString(++index, motorEngineerDetailsDto.getInputDatetime());
            ps.setString(++index, motorEngineerDetailsDto.getChassisNoConfirm().getSelectionType());
            ps.setInt(++index, motorEngineerDetailsDto.getNotCheckedReason());
            ps.setInt(++index, motorEngineerDetailsDto.getAssessorAllocationDto().getRefNo());

            if (ps.executeUpdate() > 0) {
                return motorEngineerDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public MotorEngineerDetailsDto saveInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_INSPECTION_DETAILS);
            ps.setInt(++index, motorEngineerDetailsDto.getRefNo());
            ps.setString(++index, motorEngineerDetailsDto.getJobId());
            ps.setInt(++index, motorEngineerDetailsDto.getClaimNo());
            ps.setString(++index, motorEngineerDetailsDto.getMakeConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getModelConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getEngNoConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getChassisNo());
            ps.setString(++index, motorEngineerDetailsDto.getYearMakeConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getInspectDateTime());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getPav());
            ps.setString(++index, motorEngineerDetailsDto.getDamageDetails());
            ps.setString(++index, motorEngineerDetailsDto.getPad());
            ps.setString(++index, motorEngineerDetailsDto.getGenuineOfAccident().getAccidentStatus());
            ps.setString(++index, motorEngineerDetailsDto.getFirstStatementReq().getCondtionType());
            ps.setString(++index, motorEngineerDetailsDto.getFirstStatementReqReason());
            ps.setString(++index, motorEngineerDetailsDto.getInvestReq().getCondtionType());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorSpecialRemark());
            ps.setInt(++index, motorEngineerDetailsDto.getInspectionDto().getInspectionId());
            ps.setInt(++index, motorEngineerDetailsDto.getRecordStatus());
            ps.setString(++index, motorEngineerDetailsDto.getInputUserId());
            ps.setString(++index, motorEngineerDetailsDto.getInputDatetime());
            ps.setString(++index, motorEngineerDetailsDto.getInspectionDetailsAuthStatus());
            ps.setString(++index, motorEngineerDetailsDto.getInspectionDetailsAuthUserId());
            ps.setString(++index, motorEngineerDetailsDto.getInspectionDetailsAuthDatetime());
            ps.setString(++index, null == motorEngineerDetailsDto.getChassisNoConfirm() || null == motorEngineerDetailsDto.getChassisNoConfirm().getSelectionType() ? AppConstant.STRING_PENDING : motorEngineerDetailsDto.getChassisNoConfirm().getSelectionType());
            ps.setInt(++index, motorEngineerDetailsDto.getNotCheckedReason());


            if (ps.executeUpdate() > 0) {
                return motorEngineerDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public MotorEngineerDetailsDto saveAssessorFeeInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_ASSESSOR_FEE_INSPECTION_DETAILS);
            ps.setInt(++index, motorEngineerDetailsDto.getRefNo());
            ps.setString(++index, motorEngineerDetailsDto.getJobId());
            ps.setInt(++index, motorEngineerDetailsDto.getClaimNo());
            ps.setInt(++index, motorEngineerDetailsDto.getInspectionDto().getInspectionId());
            ps.setInt(++index, motorEngineerDetailsDto.getJobType());
            ps.setString(++index, motorEngineerDetailsDto.getAssignedLocation());
            ps.setString(++index, motorEngineerDetailsDto.getCurrentLocation());
            ps.setString(++index, motorEngineerDetailsDto.getPlaceOfInspection());
            ps.setInt(++index, motorEngineerDetailsDto.getMileage());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getCostOfCall());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getOtherFee());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getDeductions());
            ps.setString(++index, motorEngineerDetailsDto.getReasonOfDeduction());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getTotalAssessorFee());
            ps.setString(++index, motorEngineerDetailsDto.getFeeDesc());
            ps.setInt(++index, motorEngineerDetailsDto.getRecordStatus());
            ps.setString(++index, motorEngineerDetailsDto.getInputUserId());
            ps.setString(++index, motorEngineerDetailsDto.getInputDatetime());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorFeeAuthStatus());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorFeeAuthUserId());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorFeeAuthDatetime());
            ps.setInt(++index, motorEngineerDetailsDto.getInspectionDetailsDto().getAssessorFeeDetailId());

            if (ps.executeUpdate() > 0) {
                return motorEngineerDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public MotorEngineerDetailsDto saveInspectionReportDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SQL_INSERT_INSPECTION_REPORT_DETAILS);
            ps.setInt(++index, motorEngineerDetailsDto.getRefNo());
            ps.setString(++index, motorEngineerDetailsDto.getJobId());
            ps.setInt(++index, motorEngineerDetailsDto.getClaimNo());
            ps.setString(++index, motorEngineerDetailsDto.getMakeConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getModelConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getEngNoConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getChassisNo());
            ps.setString(++index, motorEngineerDetailsDto.getYearMakeConfirm().getSelectionType());
            ps.setString(++index, motorEngineerDetailsDto.getInspectDateTime());
            ps.setBigDecimal(++index, motorEngineerDetailsDto.getPav());
            ps.setString(++index, motorEngineerDetailsDto.getDamageDetails());
            ps.setString(++index, motorEngineerDetailsDto.getPad());
            ps.setString(++index, motorEngineerDetailsDto.getGenuineOfAccident().getAccidentStatus());
            ps.setString(++index, motorEngineerDetailsDto.getFirstStatementReq().getCondtionType());
            ps.setString(++index, motorEngineerDetailsDto.getFirstStatementReqReason());
            ps.setString(++index, motorEngineerDetailsDto.getInvestReq().getCondtionType());
            ps.setString(++index, motorEngineerDetailsDto.getAssessorSpecialRemark());
            ps.setInt(++index, motorEngineerDetailsDto.getInspectionDto().getInspectionId());
            ps.setInt(++index, motorEngineerDetailsDto.getRecordStatus());
            ps.setString(++index, motorEngineerDetailsDto.getInputUserId());
            ps.setString(++index, motorEngineerDetailsDto.getInputDatetime());
            ps.setString(++index, null == motorEngineerDetailsDto.getChassisNoConfirm() || null == motorEngineerDetailsDto.getChassisNoConfirm().getSelectionType() ? AppConstant.STRING_PENDING : motorEngineerDetailsDto.getChassisNoConfirm().getSelectionType());
            ps.setInt(++index, motorEngineerDetailsDto.getNotCheckedReason());

            if (ps.executeUpdate() > 0) {
                return motorEngineerDetailsDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public boolean isApprovedInspectionReportDetails(Connection connection, Object id) {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(SQL_SELECT_V_MAKE_CONFIRM_BY_REF_NO);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return null == rs.getString("v_make_confirm") || rs.getString("v_make_confirm").isEmpty() ? false : true;
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public MotorEngineerDetailsDto getInspectionReportDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, Object id) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_BY_REF_NO);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getInspctionReportDetalsDto(motorEngineerDetailsDto, rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public boolean isApprovedOrPendingInspectionDetails(Connection connection, Object id) {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(SQL_SELECT_V_ASS_ESTI_APRV_STATUS_BY_REF_NO);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return null != rs.getString("v_ass_esti_apr_status") && (AppConstant.APPROVE.equalsIgnoreCase(rs.getString("v_ass_esti_apr_status")) || AppConstant.STRING_PENDING.equalsIgnoreCase(rs.getString("v_ass_esti_apr_status")));
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public MotorEngineerDetailsDto getInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, Object id) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_BY_REF_NO);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getInspctionDetals(motorEngineerDetailsDto, rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public boolean isApprovedAssessorFeeInspectionDetails(Connection connection, Object id) {
        PreparedStatement ps;
        try {
            ps = connection.prepareStatement(SQL_SELECT_V_ASS_FEE_APRV_STATUS_BY_REF_NO);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return null != rs.getString("v_ass_fee_apr_status") && AppConstant.APPROVE.equalsIgnoreCase(rs.getString("v_ass_fee_apr_status"));
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public MotorEngineerDetailsDto getAssessorFeeInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, Object id) {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_SELECT_BY_REF_NO);
            ps.setObject(1, id);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return getAssessorFeeInspctionDetalsDto(motorEngineerDetailsDto, rs);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    private MotorEngineerDetailsDto getAssessorFeeInspctionDetalsDto(MotorEngineerDetailsDto inspectionDetailsDto, ResultSet rs) {
        try {
            AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
            assessorAllocationDto.setJobId(rs.getString("v_job_no"));
            ClaimsDto claimsDto = new ClaimsDto();
            claimsDto.setClaimNo(rs.getInt("n_claim_no"));

            inspectionDetailsDto.setJobId(rs.getString("v_job_no"));
            inspectionDetailsDto.setClaimNo(rs.getInt("n_claim_no"));
            inspectionDetailsDto.setInspectionId(rs.getInt("n_inspection_id"));
            inspectionDetailsDto.setRefNo(rs.getInt("n_ref_no"));
            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);
            InspectionDto inspectionDto = new InspectionDto();
            inspectionDto.setInspectionId(rs.getInt("n_inspection_type"));
            inspectionDetailsDto.setInspectionDto(inspectionDto);
            inspectionDetailsDto.setJobType(rs.getInt("n_job_type"));
            inspectionDetailsDto.setAssignedLocation(rs.getString("v_assigned_location"));
            inspectionDetailsDto.setCurrentLocation(rs.getString("v_current_location"));
            inspectionDetailsDto.setPlaceOfInspection(rs.getString("v_place_of_inspection"));
            inspectionDetailsDto.setMileage(rs.getInt("n_mileage"));
            inspectionDetailsDto.setCostOfCall(rs.getBigDecimal("n_cost_of_call"));
            inspectionDetailsDto.setOtherFee(rs.getBigDecimal("n_other_fee"));
            inspectionDetailsDto.setDeductions(rs.getBigDecimal("n_deductions"));
            inspectionDetailsDto.setReasonOfDeduction(rs.getString("v_reason_of_deduction"));
            inspectionDetailsDto.setTotalAssessorFee(rs.getBigDecimal("n_total_assessor_fee"));
            inspectionDetailsDto.setFeeDesc(rs.getString("v_fee_desc"));
            inspectionDetailsDto.setRecordStatus(rs.getInt("n_record_status"));
            inspectionDetailsDto.setInputUserId(rs.getString("v_inpuser"));
            inspectionDetailsDto.setInputDatetime(Utility.getDate(rs.getString("d_inpdatetime"), AppConstant.DATE_TIME_FORMAT));
            inspectionDetailsDto.setAssessorFeeAuthStatus(rs.getString("v_ass_fee_apr_status"));
            inspectionDetailsDto.setAssessorFeeAuthUserId(rs.getString("v_ass_fee_apr_user"));
            inspectionDetailsDto.setAssessorFeeAuthDatetime(rs.getString("v_ass_fee_apr_datetime"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return inspectionDetailsDto;
    }

    private MotorEngineerDetailsDto getInspctionDetals(MotorEngineerDetailsDto inspectionDetailsDto, ResultSet rs) {
        try {
            AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
            assessorAllocationDto.setJobId(rs.getString("v_job_no"));
            ClaimsDto claimsDto = new ClaimsDto();
            claimsDto.setClaimNo(rs.getInt("n_claim_no"));

            inspectionDetailsDto.setJobId(rs.getString("v_job_no"));
            inspectionDetailsDto.setClaimNo(rs.getInt("n_claim_no"));
            inspectionDetailsDto.setInspectionId(rs.getInt("n_inspection_id"));
            inspectionDetailsDto.setRefNo(rs.getInt("n_ref_no"));
            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);

            inspectionDetailsDto.setMakeConfirm(rs.getString("v_make_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_make_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_make_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);


            inspectionDetailsDto.setModelConfirm(rs.getString("v_model_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_model_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_model_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);


            inspectionDetailsDto.setEngNoConfirm(rs.getString("v_eng_no_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_eng_no_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_eng_no_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);

            inspectionDetailsDto.setChassisNo(rs.getString("v_chassis_no"));
            inspectionDetailsDto.setYearMakeConfirm(rs.getString("v_year_make_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_year_make_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_year_make_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);
            inspectionDetailsDto.setInspectDateTime(Utility.getCustomDateFormat(rs.getString("d_inspect_datetime"), AppConstant.DATE_TIME_FORMAT, AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
            inspectionDetailsDto.setPav(rs.getBigDecimal("n_pav"));
            inspectionDetailsDto.setDamageDetails(rs.getString("v_damage_details"));
            inspectionDetailsDto.setPad(rs.getString("v_pad"));
            if (rs.getString("v_genun_of_accid").equals("C")) {
                inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Consistent);
            } else if (rs.getString("v_genun_of_accid").equals("D")) {
                inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Doubtful);
            } else {
                inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Non_Consistence);
            }
            inspectionDetailsDto.setFirstStatementReq(rs.getString("v_first_statement_rqed").equals("Y") ? ConditionType.Yes : ConditionType.No);
            inspectionDetailsDto.setFirstStatementReqReason(rs.getString("v_first_statement_req_reason"));
            inspectionDetailsDto.setInvestReq(rs.getString("v_invest_rqed").equals("Y") ? ConditionType.Yes : ConditionType.No);
            inspectionDetailsDto.setAssessorSpecialRemark(rs.getString("v_assessor_remark"));
            InspectionDto inspectionDto = new InspectionDto();
            inspectionDto.setInspectionId(rs.getInt("n_inspection_type"));
            inspectionDetailsDto.setInspectionDto(inspectionDto);
            inspectionDetailsDto.setRecordStatus(rs.getInt("n_record_status"));
            inspectionDetailsDto.setInputUserId(rs.getString("v_inpuser"));
            inspectionDetailsDto.setInputDatetime(Utility.getDate(rs.getString("d_inpdatetime"), AppConstant.DATE_TIME_FORMAT));
            inspectionDetailsDto.setInspectionDetailsAuthStatus(rs.getString("v_ass_esti_apr_status"));
            inspectionDetailsDto.setInspectionDetailsAuthUserId(rs.getString("v_ass_esti_apr_user"));
            inspectionDetailsDto.setInspectionDetailsAuthDatetime(rs.getString("v_ass_esti_apr_datetime"));
            inspectionDetailsDto.setChassisNoConfirm(rs.getString("v_chassis_no_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_chassis_no_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_chassis_no_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);
            inspectionDetailsDto.setNotCheckedReason(rs.getInt("n_not_checked_reason"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return inspectionDetailsDto;
    }

    private MotorEngineerDetailsDto getInspctionReportDetalsDto(MotorEngineerDetailsDto inspectionDetailsDto, ResultSet rs) {
        try {
            AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
            assessorAllocationDto.setJobId(rs.getString("v_job_no"));
            ClaimsDto claimsDto = new ClaimsDto();
            claimsDto.setClaimNo(rs.getInt("n_claim_no"));

            inspectionDetailsDto.setJobId(rs.getString("v_job_no"));
            inspectionDetailsDto.setClaimNo(rs.getInt("n_claim_no"));
            inspectionDetailsDto.setRefNo(rs.getInt("n_ref_no"));
            inspectionDetailsDto.setAssessorAllocationDto(assessorAllocationDto);

            inspectionDetailsDto.setMakeConfirm(rs.getString("v_make_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_make_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_make_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);


            inspectionDetailsDto.setModelConfirm(rs.getString("v_model_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_model_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_model_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);


            inspectionDetailsDto.setEngNoConfirm(rs.getString("v_eng_no_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_eng_no_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_eng_no_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);

            inspectionDetailsDto.setChassisNo(rs.getString("v_chassis_no"));
            inspectionDetailsDto.setYearMakeConfirm(rs.getString("v_year_make_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_year_make_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_year_make_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);
            inspectionDetailsDto.setInspectDateTime(Utility.getCustomDateFormat(rs.getString("d_inspect_datetime"), AppConstant.DATE_TIME_FORMAT, AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
            inspectionDetailsDto.setPav(rs.getBigDecimal("n_pav"));
            inspectionDetailsDto.setDamageDetails(rs.getString("v_damage_details"));
            inspectionDetailsDto.setPad(rs.getString("v_pad"));
            if (rs.getString("v_genun_of_accid").equals("C")) {
                inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Consistent);
            } else if (rs.getString("v_genun_of_accid").equals("D")) {
                inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Doubtful);
            } else {
                inspectionDetailsDto.setGenuineOfAccident(AccidentStatus.Non_Consistence);
            }
            inspectionDetailsDto.setFirstStatementReq(rs.getString("v_first_statement_rqed").equals("Y") ? ConditionType.Yes : ConditionType.No);
            inspectionDetailsDto.setFirstStatementReqReason(rs.getString("v_first_statement_req_reason"));
            inspectionDetailsDto.setInvestReq(rs.getString("v_invest_rqed").equals("Y") ? ConditionType.Yes : ConditionType.No);
            inspectionDetailsDto.setAssessorSpecialRemark(rs.getString("v_assessor_remark"));
            InspectionDto inspectionDto = new InspectionDto();
            inspectionDto.setInspectionId(rs.getInt("n_inspection_type"));
            inspectionDetailsDto.setInspectionDto(inspectionDto);
            inspectionDetailsDto.setRecordStatus(rs.getInt("n_record_status"));
            inspectionDetailsDto.setInputUserId(rs.getString("v_inpuser"));
            inspectionDetailsDto.setInputDatetime(Utility.getDate(rs.getString("d_inpdatetime"), AppConstant.DATE_TIME_FORMAT));
            inspectionDetailsDto.setChassisNoConfirm(rs.getString("v_chassis_no_confirm").equals("C") ?
                    SelectionType.Confirm : rs.getString("v_chassis_no_confirm").equals("W") ?
                    SelectionType.Wrong : rs.getString("v_chassis_no_confirm").equals("NC") ?
                    SelectionType.Not_Checked : SelectionType.Pending);
            inspectionDetailsDto.setNotCheckedReason(rs.getInt("n_not_checked_reason"));


        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return inspectionDetailsDto;
    }

    @Override
    public boolean updateRecordStatusByRefNo(Connection connection, int statusId, int refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_INSPECTION_INFO_MAIN_ME_STATUS_BY_REF_NO);
            ps.setInt(1, statusId);
            ps.setInt(2, refNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    @Override
    public List<InspectionDetailsSummaryDto> getInspectionDetailsSummary(Connection connection, Integer claimNo) {
        PreparedStatement ps = null;
        List<InspectionDetailsSummaryDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_CLAIM_SUMMARY_DETAILS);
            ps.setObject(1, claimNo);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                InspectionDetailsSummaryDto dto = new InspectionDetailsSummaryDto();
                dto.setRefNo(rs.getInt("t1.ref_no"));
                dto.setJobRefNo(rs.getString("t1.job_id"));
                dto.setInspectionId(rs.getInt("t1.insepction_id"));
                dto.setInspectionDesc(rs.getString("t3.inspection_type_desc"));
                dto.setStatus(rs.getInt("t1.record_status"));
                dto.setStatusDesc(rs.getString("t2.v_status_desc"));
                list.add(dto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public boolean isAriSalvagePending(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_ARI_SALVAGE_FROM_CLAIM_ASSIGN_ASSESSOR_IN_INSPECTION_PENDING);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            return rs.next();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

    @Override
    public boolean isAriSalvagePending(Connection connection, Integer claimNo, Integer refNo) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_ARI_SALVAGE_FROM_CLAIM_ASSIGN_ASSESSOR_IN_INSPECTION_PENDING_AND_NOTSAME_INSPECTION_ID);
            ps.setInt(1, claimNo);
            ps.setInt(2, refNo);
            ps.setInt(3, refNo);
            rs = ps.executeQuery();
            return rs.next();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return false;
    }

}
