package com.misyn.mcms.claim.dao.impl.motorengineer;

import com.misyn.mcms.claim.dao.motorengineer.ThirdPartyAssessorMeDao;
import com.misyn.mcms.claim.dto.ClaimThirdPartyDetailsGenericDto;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
public class ThirdPartyAssessorMeDaoImpl implements ThirdPartyAssessorMeDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ThirdPartyAssessorMeDaoImpl.class);

    @Override
    public ClaimThirdPartyDetailsGenericDto insertMaster(Connection connection, ClaimThirdPartyDetailsGenericDto thirdPartyDto) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(INSERT_CLAIM_THIRD_PARTY_DETAILS);
            ps.setInt(1, thirdPartyDto.getMappingId());
            ps.setString(2, thirdPartyDto.getMappingType());
            ps.setInt(3, thirdPartyDto.getClaimNo());
            ps.setString(4, thirdPartyDto.getThirdPartyInvolved());
            ps.setInt(5, thirdPartyDto.getLossType());
            ps.setInt(6, thirdPartyDto.getItemType());
            ps.setString(7, thirdPartyDto.getVehicleNo());
            ps.setString(8, thirdPartyDto.getContactNo());
            ps.setString(9, thirdPartyDto.getInsurerDetails());
            ps.setString(10, thirdPartyDto.getIntendClaim());
            ps.setString(11, thirdPartyDto.getRemark());
            ps.setString(12, thirdPartyDto.getInpUserId());
            ps.setString(13, thirdPartyDto.getInpDateTime());
            if (ps.executeUpdate() > 0) {
                return thirdPartyDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public ClaimThirdPartyDetailsGenericDto updateMaster(Connection connection, ClaimThirdPartyDetailsGenericDto thirdPartyDto) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_THIRD_PARTY_DETAILS);
            ps.setInt(1, thirdPartyDto.getMappingId());
            ps.setString(2, thirdPartyDto.getMappingType());
            ps.setInt(3, thirdPartyDto.getClaimNo());
            ps.setString(4, thirdPartyDto.getThirdPartyInvolved());
            ps.setInt(5, thirdPartyDto.getLossType());
            ps.setInt(6, thirdPartyDto.getItemType());
            ps.setString(7, thirdPartyDto.getVehicleNo());
            ps.setString(8, thirdPartyDto.getContactNo());
            ps.setString(9, thirdPartyDto.getInsurerDetails());
            ps.setString(10, thirdPartyDto.getIntendClaim());
            ps.setString(11, thirdPartyDto.getRemark());
            ps.setString(12, thirdPartyDto.getInpUserId());
            ps.setString(13, thirdPartyDto.getInpDateTime());
            ps.setInt(14, thirdPartyDto.getTxnId());
            if (ps.executeUpdate() > 0) {
                return thirdPartyDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public List<ClaimThirdPartyDetailsGenericDto> searchAll(Connection connection, Integer claimNo) throws Exception {
        List<ClaimThirdPartyDetailsGenericDto> thirdPartyDtoList = new LinkedList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_THIRD_PARTY_DETAILS);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimThirdPartyDetailsGenericDto thirdPartyDto = new ClaimThirdPartyDetailsGenericDto();
                thirdPartyDto.setTxnId(rs.getInt("txn_id"));
                thirdPartyDto.setMappingId(rs.getInt("mapping_id"));
                thirdPartyDto.setMappingType(rs.getString("mapping_type"));
                thirdPartyDto.setClaimNo(rs.getInt("claim_no"));
                thirdPartyDto.setThirdPartyInvolved(rs.getString("third_party_involved"));
                thirdPartyDto.setLossType(rs.getInt("loss_type"));
                thirdPartyDto.setItemType(rs.getInt("item_type"));

                thirdPartyDto.setVehicleNo(rs.getString("vehicle_no"));
                thirdPartyDto.setContactNo(rs.getString("contact_no"));
                thirdPartyDto.setInsurerDetails(rs.getString("insurer_details"));
                thirdPartyDto.setIntendClaim(rs.getString("intend_claim"));
                thirdPartyDto.setRemark(rs.getString("remark"));
                thirdPartyDto.setInpUserId(rs.getString("inp_user_id"));
                thirdPartyDto.setInpDateTime(rs.getString("inp_date_time"));

                thirdPartyDtoList.add(thirdPartyDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
        }
        return thirdPartyDtoList;
    }

    @Override
    public Integer insertThirdPartyList(Connection connection, Map<Integer, ClaimThirdPartyDetailsGenericDto> thirdPartyMap, Integer claimNo) throws Exception {
        int result = 0;
        PreparedStatement ps = null;
        try {
            for (Map.Entry<Integer, ClaimThirdPartyDetailsGenericDto> entry : thirdPartyMap.entrySet()) {
                ps = connection.prepareStatement(INSERT_CLAIM_THIRD_PARTY_DETAILS);
                ps.setInt(1, claimNo);
                ps.setString(2, entry.getValue().getThirdPartyInvolved());
                ps.setInt(3, entry.getValue().getLossType());
                ps.setInt(4, entry.getValue().getItemType());
                ps.setString(5, entry.getValue().getVehicleNo());
                ps.setString(6, entry.getValue().getIntendClaim());
                ps.setString(7, entry.getValue().getRemark());
                ps.setString(8, entry.getValue().getInpUserId());
                ps.setString(9, Utility.sysDateTime());
                result = result + ps.executeUpdate();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return result;
    }

    @Override
    public Map<Integer, ClaimThirdPartyDetailsGenericDto> searchAllClaimsThirdParty(Connection connection, Integer claimNo) throws Exception {
        Map<Integer, ClaimThirdPartyDetailsGenericDto> thirdPartyMap = new LinkedHashMap<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_THIRD_PARTY_DETAILS);
            ps.setInt(1, claimNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                ClaimThirdPartyDetailsGenericDto thirdPartyDto = new ClaimThirdPartyDetailsGenericDto();
                thirdPartyDto.setTxnId(rs.getInt("txn_id"));
                thirdPartyDto.setClaimNo(rs.getInt("claim_no"));
                thirdPartyDto.setThirdPartyInvolved(rs.getString("third_party_involved"));
                thirdPartyDto.setLossType(rs.getInt("loss_type"));
                thirdPartyDto.setItemType(rs.getInt("item_type"));

                thirdPartyDto.setVehicleNo(rs.getString("vehicle_no"));
                thirdPartyDto.setIntendClaim(rs.getString("intend_claim"));
                thirdPartyDto.setRemark(rs.getString("remark"));
                thirdPartyDto.setInpUserId(rs.getString("inp_user_id"));
                thirdPartyDto.setInpDateTime(rs.getString("inp_date_time"));

                thirdPartyMap.put(++index, thirdPartyDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
        }
        return thirdPartyMap;
    }

    @Override
    public Integer updateMasterList(Connection connection, Map<Integer, ClaimThirdPartyDetailsGenericDto> thirdPartyMap) throws Exception {
        int result = 0;
        PreparedStatement ps = null;
        try {
            for (Map.Entry<Integer, ClaimThirdPartyDetailsGenericDto> entry : thirdPartyMap.entrySet()) {
                ps = connection.prepareStatement(UPDATE_CLAIM_THIRD_PARTY_DETAILS);
                ps.setString(1, entry.getValue().getThirdPartyInvolved());
                ps.setInt(2, entry.getValue().getLossType());
                ps.setInt(3, entry.getValue().getItemType());
                ps.setString(4, entry.getValue().getVehicleNo());
                ps.setString(5, entry.getValue().getIntendClaim());
                ps.setString(6, entry.getValue().getRemark());
                ps.setString(7, entry.getValue().getInpUserId());
                ps.setString(8, entry.getValue().getInpDateTime());
                ps.setInt(9, entry.getValue().getTxnId());
                result = result + ps.executeUpdate();
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return result;
    }

    @Override
    public void removeClaimThirdPartyByClaimNo(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(DELETE_CLAIM_THIRD_PARTY_MAIN_BY_CLAIM_NO);
            ps.setInt(1, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    @Override
    public void removeClaimThirdParty(Connection connection, Integer claimNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(DELETE_CLAIM_THIRD_PARTY_MAIN);
            ps.setInt(1, claimNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

}
