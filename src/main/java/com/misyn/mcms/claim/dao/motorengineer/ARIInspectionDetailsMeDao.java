package com.misyn.mcms.claim.dao.motorengineer;

import com.misyn.mcms.claim.dao.BaseDao;
import com.misyn.mcms.claim.dto.ARIInspectionDetailsDto;

import java.sql.Connection;

/**
 * Created by a<PERSON><PERSON> on 5/21/18.
 */
public interface ARIInspectionDetailsMeDao extends BaseDao<ARIInspectionDetailsDto> {

    String SQL_INSERT_ARI_INSPECTION_DETAILS = "INSERT INTO ari_inspection_details_me VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    String SQL_SELECT_ARI_DETAILS = "SELECT * FROM ari_inspection_details_me WHERE n_ref_no=?";

    String SQL_SELECT_ARI_ASSESSOR_FEE_DETAILS = "SELECT\n" +
            "	inspection_id,\n" +
            "	n_ref_no,\n" +
            "	professional_fee,\n" +
            "	miles,\n" +
            "	telephone_charge,\n" +
            "	other_charge,\n" +
            "	special_deduction,\n" +
            "	reason,\n" +
            "	total_charge \n" +
            "FROM\n" +
            "	ari_inspection_details_me \n" +
            "WHERE\n" +
            "	n_ref_no =?";

    String SQL_SELECT_ARI_INSPECTION_DETAILS = "SELECT\n" +
            "	inspection_id,\n" +
            "	n_ref_no,\n" +
            "	is_ari,\n" +
            "	ari_order,\n" +
            "   is_salvage,\n" +
            "	salvage_order,\n" +
            "	assessor_remark,\n" +
            "	inspection_remark\n" +
            "FROM\n" +
            "	ari_inspection_details_me \n" +
            "WHERE\n" +
            "	n_ref_no =?";

    String SQL_UPDATE_ARI_INSPECTION = "UPDATE ari_inspection_details_me\n" +
            "SET inspection_id= ?,\n" +
            " ari_order = ?,\n" +
            " salvage_order = ?,\n" +
            " assessor_remark = ?,\n" +
            " inspection_remark = ?,\n" +
            " professional_fee = ?,\n" +
            " miles = ?,\n" +
            " telephone_charge = ?,\n" +
            " other_charge = ?,\n" +
            " special_deduction = ?,\n" +
            " reason= ?,\n" +
            " total_charge= ?\n" +
            "WHERE\n" +
            " n_ref_no = ?\n";

    String SQL_INSERT_ARI_INSPECTION_DETAIL_MASTER = "INSERT INTO `ari_inspection_details_me` (\n" +
            "	`inspection_id`,\n" +
            "	`n_ref_no`,\n" +
            "	`is_ari`,\n" +
            "	`ari_order`,\n" +
            "	`is_salvage`,\n" +
            "	`salvage_order`,\n" +
            "	`assessor_remark`,\n" +
            "	`inspection_remark`\n" +
            ")\n" +
            "VALUES\n" +
            "	(?,?,?,?,?,?,?,?)";

    String SQL_INSERT_ARI_INSPECTION_ASSESSOR_FEE_DETAIL_MASTER = "INSERT INTO `ari_inspection_details_me` (\n" +
            "	`inspection_id`,\n" +
            "	`n_ref_no`,\n" +
            "	`professional_fee`,\n" +
            "	`miles`,\n" +
            "	`telephone_charge`,\n" +
            "	`other_charge`,\n" +
            "	`special_deduction`,\n" +
            "	`reason`,\n" +
            "	`total_charge` \n" +
            ")\n" +
            "VALUES\n" +
            "	(?,?,?,?,?,?,?,?,?);";


    String SQL_UPDATE_ARI_INSPECTION_DETAILS = "UPDATE ari_inspection_details_me \n" +
            "SET inspection_id = ?,\n" +
            "is_ari = ?,\n" +
            "ari_order = ?,\n" +
            "is_salvage = ?,\n" +
            "salvage_order = ?,\n" +
            "assessor_remark = ?,\n" +
            "inspection_remark = ? \n" +
            "WHERE\n" +
            "	`n_ref_no` = ?";

    String SQL_UPDATE_ARI_ASSESSOR_FEE_DETAILS = "UPDATE ari_inspection_details_me \n" +
            "SET professional_fee = ?,\n" +
            "miles = ?,\n" +
            "telephone_charge = ?,\n" +
            "other_charge = ?,\n" +
            "special_deduction = ?,\n" +
            "reason = ?,\n" +
            "total_charge = ? \n" +
            "WHERE\n" +
            "	`n_ref_no` = ?";

    ARIInspectionDetailsDto updateAriInspectionDetailMaster(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto) throws Exception;

    ARIInspectionDetailsDto updateAriAssessorFeeDetailMaster(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto) throws Exception;

    ARIInspectionDetailsDto insertAriInspectionDetailMaster(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto) throws Exception;

    ARIInspectionDetailsDto insertAriAssessorFeeDetailMaster(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto) throws Exception;

    ARIInspectionDetailsDto getARIInspectionDetails(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto, Object id);

    ARIInspectionDetailsDto getARIInspectionAssessorFeeDetails(Connection connection, ARIInspectionDetailsDto ariInspectionDetailsDto, Object id);
}
