package com.misyn.mcms.claim.dao.motorengineer;

import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.motorengineer.CallCenterCommonBasketDto;

import java.sql.Connection;
import java.util.List;

public interface CallCenterCommonBasketDao {

    String INSERT_CALL_CENTER_REVIEW_COMMON_BASKET = """
    INSERT INTO call_center_review_common_basket (
        inspection_id, inspection_type, claim_no,
        rte_user_id, call_center_user_id, assessor_id,
        is_onsite_review_apply, status,
        submitted_datetime, submitted_user, last_updated_user,
        rejected_reason, reject_count
    ) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)
""";

    String SELECT_BY_INSPECTION_ID = "SELECT * FROM call_center_review_common_basket WHERE inspection_id = ?";
    String UPDATE_CALL_CENTER_USER = "UPDATE call_center_review_common_basket " + "SET call_center_user_id = ?, " + "    status = 'A', " + "    last_updated_datetime = NOW(), " + "    last_updated_user = ? " + "WHERE inspection_id = ? " + "  AND claim_no = ?";

    String UPDATE_RTE_USER = "UPDATE call_center_review_common_basket SET rte_user_id = ?, last_updated_datetime = NOW(), last_updated_user = ? WHERE inspection_id = ?";

    String IS_CALL_CENTER_USER_EXIST = "SELECT COUNT(*) " + "FROM call_center_review_common_basket " + "WHERE claim_no = ? " + "AND inspection_id = ? " + "AND call_center_user_id IS NOT NULL " + "AND TRIM(call_center_user_id) <> ''";
    String UPDATE_STATUS = "UPDATE call_center_review_common_basket " + "SET status = ?, last_updated_datetime = NOW(), last_updated_user = ? " + "WHERE inspection_id = ? AND claim_no = ?";
    String IS_ASSIGNED_TO_USER = "SELECT COUNT(*) FROM call_center_review_common_basket " + "WHERE claim_no = ? AND inspection_id = ? AND call_center_user_id = ?";

    String UPDATE_REJECTION_DETAILS = "UPDATE call_center_review_common_basket " +
        "SET rejected_reason = ?, reject_count = reject_count + 1, status = ?, " +
        "last_updated_datetime = NOW(), last_updated_user = ? " +
        "WHERE claim_no = ?";

    CallCenterCommonBasketDto insertCommonBasket(Connection connection, CallCenterCommonBasketDto commonBasketDto) throws Exception;

    CallCenterCommonBasketDto getByInspectionId(Connection connection, String inspectionId) throws Exception;

    boolean updateCallCenterUser(Connection connection, String callCenterUserId, String lastUpdatedUser, Integer inspectionId, Integer claimNo) throws Exception;

    boolean updateRteUser(Connection connection, String rteUserId, String lastUpdatedUser, Integer inspectionId) throws Exception;

    boolean isCallCenterUserExist(Connection connection, Integer claimNo, Integer inspectionId) throws Exception;

    boolean updateStatus(Connection connection, String statusCode, String lastUpdatedUser, Integer inspectionId, Integer claimNo) throws Exception;

    List<CallCenterCommonBasketDto> fetchAll(Connection connection) throws Exception;

    DataGridDto fetchOnsiteReviewList(Connection connection, List<FieldParameterDto> parameterList, int start, int length, String columnOrder, String orderColumnName, String fromDate, String toDate) throws Exception;

    boolean isAssignedUser(Connection connection, Integer claimNo, Integer inspectionId, String userId) throws Exception;

    boolean updateRejectionDetails(Connection connection, String rejectedReason, String statusCode, String lastUpdatedUser, Integer claimNo) throws Exception;

}
