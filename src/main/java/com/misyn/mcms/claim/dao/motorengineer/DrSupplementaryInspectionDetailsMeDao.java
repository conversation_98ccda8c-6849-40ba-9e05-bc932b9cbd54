package com.misyn.mcms.claim.dao.motorengineer;

import com.misyn.mcms.claim.dao.BaseDao;
import com.misyn.mcms.claim.dto.DrSupplementaryInspectionDetailsDto;

import java.math.BigDecimal;
import java.sql.Connection;

/**
 * Created by akila on 5/21/18.
 */
public interface DrSupplementaryInspectionDetailsMeDao extends BaseDao<DrSupplementaryInspectionDetailsDto> {

    String SQL_INSERT_DR_SUPPLEMENTARY = " INSERT INTO dr_inspection_details_me VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    String SQL_SELECT_DR_SUPPLEMENTARY = " SELECT * FROM dr_inspection_details_me WHERE n_ref_no=?";

    String SQL_SELECT_DR_SUPPLEMENTARY_DETAILS = "SELECT\n" +
            "	inspection_id,\n" +
            "	n_ref_no,\n" +
            "	sum_insured,\n" +
            "	pre_accident_value,\n" +
            "	excess,\n" +
            "	acr,\n" +
            "	bold_tyre_penalty_amount,\n" +
            "	under_insurance_penalty_amount,\n" +
            "	payable_amount,\n" +
            "	assessor_remark,\n" +
            "	inspection_remark,\n" +
            "	ari_and_salvage\n" +
            "	\n" +
            "FROM\n" +
            "	dr_inspection_details_me \n" +
            "WHERE\n" +
            "	n_ref_no =?";

    String SQL_SELECT_DR_SUPPLEMENTARY_ASSESSOR_FEE_DETAILS = "SELECT\n" +
            "	professional_fee,\n" +
            "	miles,\n" +
            "	telephone_charge,\n" +
            "	other_charge,\n" +
            "	special_deduction,\n" +
            "	reason,\n" +
            "	total_charge \n" +
            "FROM\n" +
            "	dr_inspection_details_me \n" +
            "WHERE\n" +
            "	n_ref_no =?";

    String SQL_UPDATE_DR_SUPPLEMENTARY = " UPDATE dr_inspection_details_me \n" +
            "SET  inspection_id  = ?,\n" +
            "  sum_insured  = ?,\n" +
            "  pre_accident_value  = ?,\n" +
            "  excess  = ?,\n" +
            "  acr  = ?,\n" +
            "  bold_tyre_penalty_amount  = ?,\n" +
            "  under_insurance_penalty_amount  = ?,\n" +
            "  payable_amount  = ?,\n" +
            "  assessor_remark  = ?,\n" +
            "  inspection_remark  = ?,\n" +
            "  ari_and_salvage  = ?,\n" +
            "  professional_fee  = ?,\n" +
            "  miles  = ?,\n" +
            "  telephone_charge  = ?,\n" +
            "  other_charge  = ?,\n" +
            "  special_deduction  = ?,\n" +
            "  reason  = ?,\n" +
            "  total_charge  = ?\n" +
            "WHERE\n" +
            "  n_ref_no  = ?\n";

    String SQL_INSERT_DR_SUPPLEMENTARY_INSPECTION_DETAIL = "INSERT INTO `dr_inspection_details_me` (\n" +
            "	`inspection_id`,\n" +
            "	`n_ref_no`,\n" +
            "	`sum_insured`,\n" +
            "	`pre_accident_value`,\n" +
            "	`excess`,\n" +
            "	`acr`,\n" +
            "	`previous_acr`,\n" +
            "	`bold_tyre_penalty_amount`,\n" +
            "	`under_insurance_penalty_amount`,\n" +
            "	`payable_amount`,\n" +
            "	`assessor_remark`,\n" +
            "	`inspection_remark`,\n" +
            "	`ari_and_salvage`\n" +
            ")\n" +
            "VALUES\n" +
            "	(?,?,?,?,?,?,?,?,?,?,?,?,?)";

    String SQL_INSERT_DR_SUPPLEMENTARY_ASSESSOR_FEE_DETAIL = "INSERT INTO `dr_inspection_details_me` (\n" +
            "	`inspection_id`,\n" +
            "	`n_ref_no`,\n" +
            "	`professional_fee`,\n" +
            "	`miles`,\n" +
            "	`telephone_charge`,\n" +
            "	`other_charge`,\n" +
            "	`special_deduction`,\n" +
            "	`reason`,\n" +
            "	`total_charge` \n" +
            ")\n" +
            "VALUES\n" +
            "	(?,?,?,?,?,?,?,?,?)";

    String SQL_UPDATE_DR_SUPPLEMENTARY_INSPECTION_DETAIL = "UPDATE dr_inspection_details_me \n" +
            "SET inspection_id = ?,\n" +
            "sum_insured = ?,\n" +
            "pre_accident_value = ?,\n" +
            "excess = ?,\n" +
            "acr = ?,\n" +
            "previous_acr = ?,\n" +
            "bold_tyre_penalty_amount = ?,\n" +
            "under_insurance_penalty_amount = ?,\n" +
            "payable_amount = ?,\n" +
            "assessor_remark = ?,\n" +
            "inspection_remark = ?,\n" +
            "ari_and_salvage = ? \n" +
            "WHERE\n" +
            "	n_ref_no = ?";

    String SQL_UPDATE_DR_SUPPLEMENTARY_ASSESSOR_FEE_DETAIL = "UPDATE dr_inspection_details_me \n" +
            "SET inspection_id = ?,\n" +
            "professional_fee = ?,\n" +
            "miles = ?,\n" +
            "telephone_charge = ?,\n" +
            "other_charge = ?,\n" +
            "special_deduction = ?,\n" +
            "reason = ?,\n" +
            "total_charge = ? \n" +
            "WHERE\n" +
            "	n_ref_no = ?";

    String SQL_SELECT_ACR_BY_REF_NO = "SELECT acr FROM dr_inspection_details_me WHERE n_ref_no = ?";

    DrSupplementaryInspectionDetailsDto updateDrInspectionDetailMaster(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto) throws Exception;

    DrSupplementaryInspectionDetailsDto updateDrAssessorFeeDetailMaster(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto) throws Exception;

    DrSupplementaryInspectionDetailsDto insertDrInspectionDetailMaster(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto) throws Exception;

    DrSupplementaryInspectionDetailsDto insertDrAssessorFeeDetailMaster(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto) throws Exception;

    DrSupplementaryInspectionDetailsDto getDrSupplementaryInspectionDetails(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto, Object id);

    DrSupplementaryInspectionDetailsDto getDrSupplementaryAssessorFeeDetails(Connection connection, DrSupplementaryInspectionDetailsDto drSupplementaryInspectionDetailsDto, Object id);

    BigDecimal getAcr(Connection connection, Integer refNo);
}
