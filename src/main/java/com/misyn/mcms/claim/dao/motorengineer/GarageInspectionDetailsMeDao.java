package com.misyn.mcms.claim.dao.motorengineer;

import com.misyn.mcms.claim.dao.BaseDao;
import com.misyn.mcms.claim.dto.GarageInspectionDetailsDto;

import java.math.BigDecimal;
import java.sql.Connection;

/**
 * Created by akila on 5/21/18.
 */
public interface GarageInspectionDetailsMeDao extends BaseDao<GarageInspectionDetailsDto> {

    String SQL_INSERT_INTO_GRAGE_INSPECTION_DETAILS = "INSERT INTO garage_inspection_details_me VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

    String SQL_SELECT_GARAGE_INSPECTION = "SELECT * FROM garage_inspection_details_me WHERE n_ref_no=? ";

    String SQL_SELECT_GARAGE_INSPECTION_DETAILS = "SELECT\n" +
            "	inspection_id,\n" +
            "	n_ref_no,\n" +
            "	excess,\n" +
            "	acr,\n" +
            "	bold_tyre_penalty_amount,\n" +
            "	under_insurance_penalty_amount,\n" +
            "	payable_amount,\n" +
            "	advanced_amount,\n" +
            "	settlement_method,\n" +
            "	offer_amount,\n" +
            "	inspection_remark,\n" +
            "	police_report_requested,\n" +
            "	special_remark,\n" +
            "	investigaed_claim,\n" +
            "	ari_and_salvage,\n" +
            "	pre_accident_value, \n" +
            "	advance_amount_action \n" +
            "FROM\n" +
            "	garage_inspection_details_me \n" +
            "WHERE\n" +
            "	n_ref_no =?";

    String SQL_SELECT_GARAGE_ASSESSOR_FEE_DETAILS = "SELECT\n" +
            "	inspection_id,\n" +
            "	n_ref_no,\n" +
            "	professional_fee,\n" +
            "	miles,\n" +
            "	telephone_charge,\n" +
            "	other_charge,\n" +
            "	special_deduction,\n" +
            "	reason,\n" +
            "	total_charge \n" +
            "FROM\n" +
            "	garage_inspection_details_me \n" +
            "WHERE\n" +
            "	n_ref_no =?";

    String SQL_UPDATE_GARAGE_INSPECTION = "UPDATE  garage_inspection_details_me \n" +
            "SET  inspection_id  = ?,\n" +
            "  excess  = ?,\n" +
            "  acr  = ?,\n" +
            "  bold_tyre_penalty_amount  = ?,\n" +
            "  under_insurance_penalty_amount  = ?,\n" +
            "  payable_amount  = ?,\n" +
            "  advanced_amount  = ?,\n" +
            "  settlement_method  = ?,\n" +
            "  offer_amount  = ?,\n" +
            "  inspection_remark  = ?,\n" +
            "  police_report_requested  = ?,\n" +
            "  special_remark  = ?,\n" +
            "  investigaed_claim  = ?,\n" +
            "  ari_and_salvage  = ?,\n" +
            "  pre_accident_value  = ?,\n" +
            "  professional_fee  = ?,\n" +
            "  miles  = ?,\n" +
            "  telephone_charge  = ?,\n" +
            "  other_charge  = ?,\n" +
            "  special_deduction  = ?,\n" +
            "  reason  = ?,\n" +
            "  total_charge  = ?\n" +
            "WHERE\n" +
            "  n_ref_no  = ?";

    String SQL_INSERT_GARAGE_INSPECTION_DETAIL_MASTER = "INSERT INTO `garage_inspection_details_me` (\n" +
            "	`inspection_id`,\n" +
            "	`n_ref_no`,\n" +
            "	`excess`,\n" +
            "	`acr`,\n" +
            "    previous_acr,\n" +
            "	`bold_tyre_penalty_amount`,\n" +
            "	`under_insurance_penalty_amount`,\n" +
            "	`payable_amount`,\n" +
            "	`advanced_amount`,\n" +
            "	`settlement_method`,\n" +
            "	`offer_amount`,\n" +
            "	`inspection_remark`,\n" +
            "	`police_report_requested`,\n" +
            "	`special_remark`,\n" +
            "	`investigaed_claim`,\n" +
            "	`ari_and_salvage`,\n" +
            "	`pre_accident_value`,\n" +
            "	`advance_amount_action`\n" +
            ")\n" +
            "VALUES\n" +
            "	(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";

    String SQL_INSERT_GARAGE_ASSESSOR_FEE_DETAIL_MASTER = "INSERT INTO `garage_inspection_details_me` (\n" +
            "	`inspection_id`,\n" +
            "	`n_ref_no`,\n" +
            "	`professional_fee`,\n" +
            "	`miles`,\n" +
            "	`telephone_charge`,\n" +
            "	`other_charge`,\n" +
            "	`special_deduction`,\n" +
            "	`reason`,\n" +
            "	`total_charge` \n" +
            ")\n" +
            "VALUES\n" +
            "	(?,?,?,?,?,?,?,?,?);";

    String SQL_UPDATE_GARAGE_INSPECTION_DETAIL_MASTER = "UPDATE garage_inspection_details_me \n" +
            "SET inspection_id = ?,\n" +
            "excess = ?,\n" +
            "acr = ?,\n" +
            "previous_acr = ?,\n" +
            "bold_tyre_penalty_amount = ?,\n" +
            "under_insurance_penalty_amount = ?,\n" +
            "payable_amount = ?,\n" +
            "advanced_amount = ?,\n" +
            "settlement_method = ?,\n" +
            "offer_amount = ?,\n" +
            "inspection_remark = ?,\n" +
            "police_report_requested = ?,\n" +
            "special_remark = ?,\n" +
            "investigaed_claim = ?,\n" +
            "ari_and_salvage = ?,\n" +
            "pre_accident_value = ?, \n" +
            "advance_amount_action = ? \n" +
            "WHERE\n" +
            "	n_ref_no = ?";

    String SQL_UPDATE_GARAGE_ASSESSOR_FEE_DETAIL_MASTER = "UPDATE garage_inspection_details_me \n" +
            "SET professional_fee = ?,\n" +
            "miles = ?,\n" +
            "telephone_charge = ?,\n" +
            "other_charge = ?,\n" +
            "special_deduction = ?,\n" +
            "reason = ?,\n" +
            "total_charge = ? \n" +
            "WHERE\n" +
            "	n_ref_no = ?";

    String SQL_SELECT_ACR_BY_REF_NO = "SELECT acr FROM garage_inspection_details_me WHERE n_ref_no = ?";

    GarageInspectionDetailsDto updateGarageInspectionDetailMaster(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto) throws Exception;

    GarageInspectionDetailsDto updateGarageAssessorFeeDetailMaster(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto) throws Exception;

    GarageInspectionDetailsDto insertGarageInspectionDetailMaster(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto) throws Exception;

    GarageInspectionDetailsDto insertGarageAssessorFeeDetailMaster(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto) throws Exception;

    GarageInspectionDetailsDto getGarageInspectionDetails(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto, Object id);

    GarageInspectionDetailsDto getGarageAssessorFeeDetails(Connection connection, GarageInspectionDetailsDto garageInspectionDetailsDto, Object id);

    BigDecimal getAcr(Connection connection, Integer refNo);
}
