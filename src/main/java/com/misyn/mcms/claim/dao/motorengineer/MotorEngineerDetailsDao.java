package com.misyn.mcms.claim.dao.motorengineer;

import com.misyn.mcms.claim.dao.BaseDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.motorengineer.MotorEngineerDetailsDto;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;

/**
 * Created by akila on 5/16/18.
 */
public interface MotorEngineerDetailsDao extends BaseDao<MotorEngineerDetailsDto> {

    String SQL_INSERT_CLAIM_INSPECTION_INFO_MAIN = "INSERT INTO claim_inspection_info_main_me\n"
            + "	(n_ref_no, v_job_no, n_claim_no, v_make_confirm, v_model_confirm, v_eng_no_confirm, v_chassis_no, "
            + "v_year_make_confirm, d_inspect_datetime, n_pav, v_damage_details, v_pad, v_genun_of_accid, "
            + "v_first_statement_rqed, v_first_statement_req_reason, v_invest_rqed, v_assessor_remark, n_inspection_type, n_job_type, v_assigned_location, v_current_location, "
            + "v_place_of_inspection, n_mileage, n_cost_of_call, n_other_fee, n_deductions, v_reason_of_deduction, n_total_assessor_fee, v_fee_desc, n_record_status, v_inpuser, d_inpdatetime"
            + ",v_ass_fee_apr_status, v_ass_fee_apr_user, v_ass_fee_apr_datetime, v_ass_esti_apr_status, v_ass_esti_apr_user, v_ass_esti_apr_datetime,v_chassis_no_confirm,n_not_checked_reason)\n"
            + "	VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?)";

    String SQL_INSERT_INSPECTION_REPORT_DETAILS = "INSERT INTO claim_inspection_info_main_me (\n" +
            "	n_ref_no,\n" +
            "	v_job_no,\n" +
            "	n_claim_no,\n" +
            "	v_make_confirm,\n" +
            "	v_model_confirm,\n" +
            "	v_eng_no_confirm,\n" +
            "	v_chassis_no,\n" +
            "	v_year_make_confirm,\n" +
            "	d_inspect_datetime,\n" +
            "	n_pav,\n" +
            "	v_damage_details,\n" +
            "	v_pad,\n" +
            "	v_genun_of_accid,\n" +
            "	v_first_statement_rqed,\n" +
            "	v_first_statement_req_reason,\n" +
            "	v_invest_rqed,\n" +
            "	v_assessor_remark,\n" +
            "	n_inspection_type,\n" +
            "	n_record_status,\n" +
            "	v_inpuser,\n" +
            "	d_inpdatetime,\n" +
            "	v_chassis_no_confirm,\n" +
            "	n_not_checked_reason \n" +
            ")\n" +
            "VALUES\n" +
            " (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    String SQL_INSERT_INSPECTION_DETAILS = "INSERT INTO claim_inspection_info_main_me (\n" +
            "	n_ref_no,\n" +
            "	v_job_no,\n" +
            "	n_claim_no,\n" +
            "	v_make_confirm,\n" +
            "	v_model_confirm,\n" +
            "	v_eng_no_confirm,\n" +
            "	v_chassis_no,\n" +
            "	v_year_make_confirm,\n" +
            "	d_inspect_datetime,\n" +
            "	n_pav,\n" +
            "	v_damage_details,\n" +
            "	v_pad,\n" +
            "	v_genun_of_accid,\n" +
            "	v_first_statement_rqed,\n" +
            "	v_first_statement_req_reason,\n" +
            "	v_invest_rqed,\n" +
            "	v_assessor_remark,\n" +
            "	n_inspection_type,\n" +
            "	n_record_status,\n" +
            "	v_inpuser,\n" +
            "	d_inpdatetime,\n" +
            "	v_ass_esti_apr_status,\n" +
            "	v_ass_esti_apr_user,\n" +
            "	v_ass_esti_apr_datetime,\n" +
            "	v_chassis_no_confirm,\n" +
            "	n_not_checked_reason \n" +
            ")\n" +
            "VALUES\n" +
            " (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    String SQL_INSERT_ASSESSOR_FEE_INSPECTION_DETAILS = "INSERT INTO claim_inspection_info_main_me (\n" +
            "	n_ref_no,\n" +
            "	v_job_no,\n" +
            "	n_claim_no,\n" +
            "	n_inspection_type,\n" +
            "	n_job_type,\n" +
            "	v_assigned_location,\n" +
            "	v_current_location,\n" +
            "	v_place_of_inspection,\n" +
            "	n_mileage,\n" +
            "	n_cost_of_call,\n" +
            "	n_other_fee,\n" +
            "	n_deductions,\n" +
            "	v_reason_of_deduction,\n" +
            "	n_total_assessor_fee,\n" +
            "	v_fee_desc,\n" +
            "	n_record_status,\n" +
            "	v_inpuser,\n" +
            "	d_inpdatetime,\n" +
            "	v_ass_fee_apr_status,\n" +
            "	v_ass_fee_apr_user,\n" +
            "	v_ass_fee_apr_datetime,\n" +
            "	n_assessor_fee_detail_id\n" +
            ")\n" +
            "VALUES\n" +
            " (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    String SQL_SELECT_ALL_TO_GRID = "SELECT\n"
            + "\tt3.ref_no,\n"
            + "\tt1.N_CLIM_NO,\n"
            + "\tt1.V_VEHICLE_NO,\n"
            + "\tt3.job_id,\n"
            + "\tt2.inspection_type_desc,\n"
            + "\tt3.record_status,\n"
            + "\tt3.assign_datetime,\n"
            + "\tt1.N_POL_REF_NO,\n"
            + "\tt1.N_INTIMATION_TYPE,\n"
            + "\tt4.v_status_desc\n"
            + "FROM\n"
            + "\tclaim_claim_info_main AS t1\n"
            + "INNER JOIN claim_assign_assesor AS t3 ON t3.claim_no = t1.N_CLIM_NO\n"
            + "INNER JOIN claim_inspection_type AS t2 ON t2.inspection_type_id = t3.insepction_id "
            + "INNER JOIN claim_status_para AS t4 ON t3.job_status = t4.n_ref_id ";

    String SQL_SELECT_COUNT_TO_GRID = "SELECT\n"
            + "\tCOUNT(*) as cnt\n"
            + "FROM\n"
            + "\tclaim_claim_info_main AS t1\n"
            + "INNER JOIN claim_assign_assesor AS t3 ON t3.claim_no = t1.N_CLIM_NO\n"
            + "INNER JOIN claim_inspection_type AS t2 ON t2.inspection_type_id = t3.insepction_id ";

    String SQL_SELECT_BY_REF_NO = "SELECT * FROM claim_inspection_info_main_me where n_ref_no=?";

    String SQL_UPDATE_INSPECTION_DEATIL_MASTER = "UPDATE  claim_inspection_info_main_me \n"
            + "SET \n"
            + "  v_job_no  = ?,\n"
            + "  n_claim_no  = ?,\n"
            + "  v_make_confirm  = ?,\n"
            + "  v_model_confirm  = ?,\n"
            + "  v_eng_no_confirm  =?,\n"
            + "  v_chassis_no  = ?,\n"
            + "  v_year_make_confirm  = ?,\n"
            + "  d_inspect_datetime  = ?,\n"
            + "  n_pav  = ?,\n"
            + "  v_damage_details  = ?,\n"
            + "  v_pad  = ?,\n"
            + "  v_genun_of_accid  = ?,\n"
            + "  v_first_statement_rqed  = ?,\n"
            + "  v_first_statement_req_reason  = ?,\n"
            + "  v_invest_rqed  = ?,\n"
            + "  v_assessor_remark  = ?,\n"
            + "  n_inspection_type  = ?,\n"
            + "  n_job_type  = ?,\n"
            + "  v_assigned_location  = ?,\n"
            + "  v_current_location  = ?,\n"
            + "  v_place_of_inspection  = ?,\n"
            + "  n_mileage  = ?,\n"
            + "  n_cost_of_call  = ?,\n"
            + "  n_other_fee  = ?,\n"
            + "  n_deductions  = ?,\n"
            + "  v_reason_of_deduction  = ?,\n"
            + "  n_total_assessor_fee  = ?,\n"
            + "  v_fee_desc  = ?,\n"
            + "  n_record_status  = ?,\n"
            + "  v_inpuser  = ?,\n"
            + "  d_inpdatetime  = ?,\n"
            + "  v_ass_fee_apr_status=?,\n"
            + "	 v_ass_fee_apr_user=?,\n"
            + "	 v_ass_fee_apr_datetime=?,\n"
            + "	 v_ass_esti_apr_status=?,\n"
            + "	 v_ass_esti_apr_user=?,\n"
            + "	 v_ass_esti_apr_datetime=?," +
            "v_chassis_no_confirm=?," +
            "n_not_checked_reason=? \n"
            + "WHERE\n"
            + "  n_ref_no  = ?";

    String SQL_UPDATE_ONLY_INSPECTION_REPORT_DEATILS = "UPDATE claim_inspection_info_main_me \n" +
            "SET v_job_no = ?,\n" +
            "n_claim_no = ?,\n" +
            "v_make_confirm = ?,\n" +
            "v_model_confirm = ?,\n" +
            "v_eng_no_confirm =?,\n" +
            "v_chassis_no = ?,\n" +
            "v_year_make_confirm = ?,\n" +
            "d_inspect_datetime = ?,\n" +
            "n_pav = ?,\n" +
            "v_damage_details = ?,\n" +
            "v_pad = ?,\n" +
            "v_genun_of_accid = ?,\n" +
            "v_first_statement_rqed = ?,\n" +
            "v_first_statement_req_reason = ?,\n" +
            "v_invest_rqed = ?,\n" +
            "v_assessor_remark = ?,\n" +
            "v_inpuser = ?,\n" +
            "d_inpdatetime = ?,\n" +
            "v_chassis_no_confirm =?,\n" +
            "n_not_checked_reason =? \n" +
            "WHERE\n" +
            "	n_ref_no = ?";

    String SQL_UPDATE_ONLY_INSPECTION_DEATILS_AND_INSPECTION_REPORT_DETAILS = "UPDATE claim_inspection_info_main_me \n" +
            "SET v_job_no = ?,\n" +
            "n_claim_no = ?,\n" +
            "v_make_confirm = ?,\n" +
            "v_model_confirm = ?,\n" +
            "v_eng_no_confirm =?,\n" +
            "v_chassis_no = ?,\n" +
            "v_year_make_confirm = ?,\n" +
            "d_inspect_datetime = ?,\n" +
            "n_pav = ?,\n" +
            "v_damage_details = ?,\n" +
            "v_pad = ?,\n" +
            "v_genun_of_accid = ?,\n" +
            "v_first_statement_rqed = ?,\n" +
            "v_first_statement_req_reason = ?,\n" +
            "v_invest_rqed = ?,\n" +
            "v_assessor_remark = ?,\n" +
            "n_inspection_type = ?,\n" +
            "n_record_status = ?,\n" +
            "v_inpuser = ?,\n" +
            "d_inpdatetime = ?,\n" +
            "v_ass_esti_apr_status =?,\n" +
            "v_ass_esti_apr_user =?,\n" +
            "v_ass_esti_apr_datetime =?,\n" +
            "v_chassis_no_confirm =?,\n" +
            "n_not_checked_reason =? \n" +
            "WHERE\n" +
            "	n_ref_no = ?";

    String SQL_UPDATE_ASSESSOR_FEE_INSPECTION_DETAILS = "UPDATE claim_inspection_info_main_me \n" +
            "SET n_job_type = ?,\n" +
            "v_assigned_location = ?,\n" +
            "v_current_location = ?,\n" +
            "v_place_of_inspection = ?,\n" +
            "n_mileage = ?,\n" +
            "n_cost_of_call = ?,\n" +
            "n_other_fee = ?,\n" +
            "n_deductions = ?,\n" +
            "v_reason_of_deduction = ?,\n" +
            "n_total_assessor_fee = ?,\n" +
            "v_fee_desc = ?,\n" +
            "v_inpuser = ?,\n" +
            "d_inpdatetime = ?,\n" +
            "v_ass_fee_apr_status =?,\n" +
            "v_ass_fee_apr_user =?,\n" +
            "v_ass_fee_apr_datetime =?, \n" +
            "n_assessor_fee_detail_id =? \n" +
            "WHERE\n" +
            "	n_ref_no = ?";

    String SQL_SELECT_CLAIM_LIST_BY_POL_REF_NO = "SELECT N_CLIM_NO FROM claim_claim_info_main WHERE N_POL_REF_NO=? ";

    String SQL_SELECT_INSPECTION_LIST_BY_CLAIM_NO = "SELECT\n"
            + "t3.job_id,\n"
            + "t1.V_VEHICLE_NUMBER,\n"
            + "t1.V_POL_NUMBER,\n"
            + "t2.D_ACCID_DATE,\n"
            + "t5.inspection_type_desc,\n"
            + "t1.N_POL_REF_NO\n"
            + "FROM\n"
            + "claim_vehicle_info_main AS t1\n"
            + "INNER JOIN claim_claim_info_main AS t2 ON t2.N_POL_REF_NO = t1.N_POL_REF_NO\n"
            + "INNER JOIN claim_assign_assesor AS t3 ON t3.claim_no = t2.N_CLIM_NO\n"
            + "INNER JOIN claim_inspection_info_main ON claim_inspection_info_main.n_ref_no = t3.ref_no\n"
            + "INNER JOIN claim_inspection_type AS t5 ON claim_inspection_info_main.n_inspection_type = t5.inspection_type_id\n"
            + "WHERE\n"
            + "t2.N_CLIM_NO = ? and t3.ref_no NOT IN (?)";

    String SQL_SELECT_INSPECTION_BY_CLAIM_NO = "SELECT\n"
            + "t3.ref_no,\n"
            + "t3.job_id,\n"
            + "t1.V_VEHICLE_NUMBER,\n"
            + "t1.V_POL_NUMBER,\n"
            + "t2.D_ACCID_DATE,\n"
            + "t5.inspection_type_desc,\n"
            + "t1.N_POL_REF_NO\n"
            + "FROM\n"
            + "claim_vehicle_info_main AS t1\n"
            + "INNER JOIN claim_claim_info_main AS t2 ON t2.N_POL_REF_NO = t1.N_POL_REF_NO\n"
            + "INNER JOIN claim_assign_assesor AS t3 ON t3.claim_no = t2.N_CLIM_NO\n"
            + "INNER JOIN claim_inspection_info_main ON claim_inspection_info_main.n_ref_no = t3.ref_no\n"
            + "INNER JOIN claim_inspection_type AS t5 ON claim_inspection_info_main.n_inspection_type = t5.inspection_type_id\n"
            + "WHERE\n"
            + "t2.N_CLIM_NO = ?";

    String SQL_SELECT_BY_CLAIM_NO = "SELECT * FROM claim_inspection_info_main_me where n_claim_no=?";

    String SQL_SELECT_LATEST_ONSITE_INSPECTION_MOTOR_ENG = "SELECT * FROM claim_inspection_info_main_me WHERE n_inspection_type=1 AND n_claim_no=? ORDER BY n_ref_no desc LIMIT 1";
    String SQL_SELECT_LATEST_ONSITE_INSPECTION_MOTOR_ENG_BY_INSPECTION_TYPE = "SELECT * FROM claim_inspection_info_main_me WHERE n_inspection_type=? AND n_claim_no=? ORDER BY n_ref_no desc LIMIT 1";

    String SQL_SELECT_ASSIGN_RTE = "SELECT v_assign_rte_user FROM claim_inspection_info_main WHERE v_job_no=?";

    String SQL_SELECT_MOBILE_NO_BY_ASSIGN_RTE = "SELECT v_mobile FROM usr_mst WHERE v_usrid=?";

    String SQL_SELECT_CLAIM_NO_BY_REF_NO = "SELECT n_claim_no FROM claim_inspection_info_main WHERE n_ref_no=?";

    String SQL_ONE_SELECT_BY_CLAIM_NO = "SELECT 1 FROM claim_inspection_info_main_me WHERE n_claim_no=?";

    String SQL_COUSE_OF_LOSS_BY_CLAIM_NO = "SELECT N_CAUSE_OF_LOSS FROM claim_claim_info_main WHERE N_CLIM_NO = ?";

    String UPDATE_ASSIGN_USER_AND_LIABILITY_ASSIGN_USER_BY_CLAIM_NO = "UPDATE claim_assign_claim_handler\n" +
            "SET V_ASSIGN_USER_ID = NULL,\n" +
            " V_INIT_LIABILITY_ASSIGN_USER_ID = NULL,\n" +
            " V_LIABILITY_APRV_ASSIGN_USER = NULL\n" +
            "WHERE\n" +
            "	N_CLAIM_NO = ?";

    String SELECT_PAV_FROM_INSPECTION_INFO_MAIN_ME = "SELECT\n" +
            "\tciimm.n_pav\n" +
            "FROM\n" +
            "\tclaim_inspection_info_main_me AS ciimm\n" +
            "\tINNER JOIN claim_inspection_info_main AS ciim ON ciimm.n_ref_no = ciim.n_ref_no \n" +
            "WHERE\n" +
            "\tciimm.n_claim_no = ?\n" +
            "\tAND ciimm.n_inspection_type IN ( 1, 2, 4 ) \n" +
            "\tAND ciim.is_vehicle_available NOT IN ( 'N' ) \n" +
            "ORDER BY\n" +
            "\tciimm.d_inpdatetime DESC \n" +
            "\tLIMIT 1";

    String SELECT_PAV_FROM_INSPECTION_INFO_MAIN = "SELECT\n" +
            "	n_pav\n" +
            "FROM\n" +
            "	claim_inspection_info_main\n" +
            "WHERE\n" +
            "	n_claim_no = ?\n" +
            "AND n_inspection_type IN (1,2, 4)\n" +
            "AND is_vehicle_available NOT IN ( 'N' )\n" +
            "ORDER BY\n" +
            "	d_inpdatetime DESC\n" +
            "LIMIT 1";

    String SELECT_ONE_FROM_CLAIM_ASSIGN_ASSESSOR_IN_INSPECTION_PENDING = "SELECT\n" +
            "\tcaa.claim_no \n" +
            "FROM\n" +
            "\tclaim_assign_assesor caa\n" +
            "\tLEFT JOIN claim_inspection_info_main ciim ON caa.ref_no = ciim.n_ref_no\n" +
            "\tLEFT JOIN request_ari_details rad ON caa.ref_no = rad.ref_id \n" +
            "WHERE\n" +
            "\tcaa.claim_no = ?\n" +
            "\tAND ((\n" +
            "\t\tcaa.record_status IN (29,10,80)\n" +
            "\t\tOR (ciim.n_record_status IN (8,29)\n" +
            "\t\tAND ciim.is_vehicle_available = 'Y'\n" +
            "\t\tAND ciim.n_inspection_id <> ?)) \n" +
            "\t\tOR rad.`status` = 'PR' \n" +
            "\t)\n" +
            "\tLIMIT 1";

    String SELECT_ASSIGN_RTE_USER_BY_CLAIM_NO = "SELECT DISTINCT\n" +
            "v_assign_rte_user\n" +
            "FROM claim_inspection_info_main ciim\n" +
            " INNER JOIN request_ari_details rad ON ciim.n_ref_no = rad.ref_id\n" +
            "WHERE\n" +
            "n_claim_no = ?\n" +
            "AND (n_record_status NOT IN ( 4, 9, 23 ) OR rad.`status` = 'PR')";

    String SELECT_APPROVE_ASSIGN_RTE_USER_BY_CLAIM_NO = "SELECT DISTINCT\n" +
            "v_approve_assign_rte_user\n" +
            "FROM claim_inspection_info_main ciim\n" +
            " INNER JOIN request_ari_details rad ON ciim.n_ref_no = rad.ref_id\n" +
            "WHERE\n" +
            "n_claim_no = ?\n" +
            "AND (n_record_status NOT IN ( 4, 9, 23 ) OR rad.`status` = 'PR')";

    String SELECT_ASSIGN_ASSESOR_REPORTING_BY_CLAIM_NO = "SELECT DISTINCT(t2.V_REPORT_TO) FROM claim_assign_assesor AS t1\n" +
            "INNER JOIN usr_mst AS t2 ON t1.assessor_code = t2.v_emp_no\n" +
            "WHERE t1.claim_no = ? AND t1.record_status = 29 AND t1.assessor_code <> ''";

    String FORWARD_TO_RTE = "UPDATE claim_inspection_info_main SET v_approve_assign_rte_user = ?, d_approve_assign_datetime = ? WHERE n_ref_no = ?";

    String SELECT_IS_AVAILABLE_APPROVED_INSPECTION_BY_TYPE = "SELECT\n" +
            "	1 \n" +
            "FROM\n" +
            "	claim_inspection_info_main_me \n" +
            "WHERE\n" +
            "	n_claim_no =? \n" +
            "	AND n_inspection_type = ? \n" +
            "	AND v_ass_esti_apr_status = 'A'";

    String SELECT_IS_AVAILABLE_PENDING_INSPECTION = "SELECT\n" +
            "	1 \n" +
            "FROM\n" +
            "	claim_assign_assesor \n" +
            "WHERE\n" +
            "	claim_no = ? \n" +
            "	AND insepction_id = ? \n" +
            "	AND record_status NOT IN ( 4, 23, 9 )";

    String SQL_SELECT_V_ASS_ESTI_APRV_STATUS_BY_REF_NO = "SELECT v_ass_esti_apr_status FROM claim_inspection_info_main_me where n_ref_no=?";

    String SQL_SELECT_V_ASS_FEE_APRV_STATUS_BY_REF_NO = "SELECT v_ass_fee_apr_status FROM claim_inspection_info_main_me where n_ref_no=?";

    String SQL_SELECT_V_MAKE_CONFIRM_BY_REF_NO = "SELECT v_make_confirm FROM claim_inspection_info_main_me where n_ref_no=?";

    String SQL_UPDATE_INSPECTION_INFO_MAIN_ME_STATUS_BY_REF_NO = "update claim_inspection_info_main_me set n_record_status=? where n_ref_no=?";

    String SQL_SELECT_CLAIM_SUMMARY_DETAILS = "SELECT\n" +
            "	t1.ref_no,\n" +
            "	t1.job_id,\n" +
            "	t1.insepction_id,\n" +
            "	t1.record_status,\n" +
            "	t2.v_status_desc,\n" +
            "	t3.inspection_type_desc \n" +
            "FROM\n" +
            "	claim_assign_assesor AS t1\n" +
            "	INNER JOIN claim_status_para AS t2 ON t2.n_ref_id = t1.record_status\n" +
            "	INNER JOIN claim_inspection_type AS t3 ON t1.insepction_id = t3.inspection_type_id \n" +
            "WHERE\n" +
            "	t1.claim_no = ? \n" +
            "	AND t1.record_status NOT IN ( 4, 3, 23, 29 )";

    String SELECT_ARI_SALVAGE_FROM_CLAIM_ASSIGN_ASSESSOR_IN_INSPECTION_PENDING = "SELECT\n" +
            " caa.claim_no FROM claim_assign_assesor caa\n" +
            " LEFT JOIN claim_inspection_info_main ciim ON caa.ref_no = ciim.n_ref_no\n" +
            " INNER JOIN request_ari_details rad ON caa.ref_no = rad.ref_id\n" +
            " WHERE caa.claim_no = ?\n" +
            " AND (((caa.record_status NOT IN(4, 9, 23) AND caa.insepction_id IN(7,9)) AND ciim.is_vehicle_available ='Y')\n" +
            " OR caa.record_status = 29 OR rad.`status` = 'PR') limit 1";

    String SELECT_ARI_SALVAGE_FROM_CLAIM_ASSIGN_ASSESSOR_IN_INSPECTION_PENDING_AND_NOTSAME_INSPECTION_ID = "SELECT\n" +
            "\tcaa.claim_no \n" +
            "FROM\n" +
            "\tclaim_assign_assesor caa\n" +
            "\tLEFT JOIN claim_inspection_info_main ciim ON caa.ref_no = ciim.n_ref_no\n" +
            "\tINNER JOIN request_ari_details rad ON caa.ref_no = rad.ref_id \n" +
            "WHERE\n" +
            "\tcaa.claim_no = ? \n" +
            "\tAND (((\n" +
            "\t\t\t\tcaa.record_status NOT IN ( 4, 9, 23 ) \n" +
            "\t\t\t\tAND caa.insepction_id IN ( 7, 9 ) \n" +
            "\t\t\t\tAND ciim.n_ref_no <> ?) \n" +
            "\t\t\tAND ciim.is_vehicle_available = 'Y' \n" +
            "\t\t\t) \n" +
            "\t\tOR caa.record_status = 29 \n" +
            "\t\tOR (\n" +
            "\t\t\trad.STATUS = 'PR' \n" +
            "\t\tAND rad.ref_id <> ?) \n" +
            "\t) \n" +
            "\tLIMIT 1";

    DataGridDto getJobDataGridDto(Connection conn, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, String fromDate, String toDate);

    List<Integer> getClaimListByPolNo(Integer polRefNo, Connection connection) throws Exception;

    List<PreviousClaimsDto> getPreviousClaimList(Integer claimNo, Integer jobNo, Connection connection) throws Exception;

    List<PreviousClaimsDto> searchAllByClaimNo(Connection connection, Integer claimNo) throws Exception;

    void updateDesktopInformDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception;

    List<InspectionFormDto> getInspectionFormDtoList(Connection connection, Integer claimNo);

    List<MotorEngineerDetailsDto> searchByClaimNo(Connection connection, Object id) throws Exception;

    MotorEngineerDetailsDto getLatestInspectionMotorEngineer(Connection connection, Integer claimNo);

    boolean isInspectionExist(Connection connection, Integer claimNo, Integer inspectionType);

    String getAssignRte(Connection connection, String jobNo) throws Exception;

    ContactDetailDto getContactDetails(Connection connection, String user);

    Integer getClaimNoByRefNo(Connection connection, Object id);

    boolean isRteApprovedData(Connection connection, Integer claimNo);

    Integer getClaimAssignLossType(Connection connection, int claimNo);

    void setAssignUserAndLiabilityAssignUserNull(Connection connection, int claimNo);

    BigDecimal getPreviousPavFromMe(Connection connection, Integer claimNo);

    BigDecimal getPreviousPav(Connection connection, Integer claimNo);

    boolean isInspectionPending(Connection connection, Integer claimNo, Integer inspectionId);

    List<String> getPendingInspectionReportingRteList(Connection connection, Integer claimNo);

    boolean isAvailableApprovedInspectionByInspectionType(Connection connection, Integer claimNo, int inspectionType);

    boolean isAvailablePendingInspection(Connection connection, int claimNo, int inspectionType);

    MotorEngineerDetailsDto updateAssessorFeeInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception;

    MotorEngineerDetailsDto updateInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception;

    MotorEngineerDetailsDto updateInspectionReportDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception;

    MotorEngineerDetailsDto saveInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception;

    MotorEngineerDetailsDto saveAssessorFeeInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception;

    MotorEngineerDetailsDto saveInspectionReportDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto) throws Exception;

    boolean isApprovedInspectionReportDetails(Connection connection, Object id);

    MotorEngineerDetailsDto getInspectionReportDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, Object id);

    boolean isApprovedOrPendingInspectionDetails(Connection connection, Object id);

    MotorEngineerDetailsDto getInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, Object id);

    boolean isApprovedAssessorFeeInspectionDetails(Connection connection, Object id);

    MotorEngineerDetailsDto getAssessorFeeInspectionDetails(Connection connection, MotorEngineerDetailsDto motorEngineerDetailsDto, Object id);

    boolean updateRecordStatusByRefNo(Connection connection, int statusId, int refNo) throws Exception;

    List<InspectionDetailsSummaryDto> getInspectionDetailsSummary(Connection connection, Integer claimNo);

    boolean isAriSalvagePending(Connection connection, Integer claimNo) throws Exception;

    boolean isAriSalvagePending(Connection connection, Integer claimNo, Integer refNo) throws Exception;
}
