package com.misyn.mcms.claim.dao.motorengineer;

import com.misyn.mcms.claim.dto.ClaimThirdPartyDetailsGenericDto;

import java.sql.Connection;
import java.util.List;
import java.util.Map;

public interface ThirdPartyAssessorMeDao {

    String INSERT_CLAIM_THIRD_PARTY_DETAILS = "INSERT INTO claim_third_party_assessor_details_me\n"
            + "	(mapping_id, mapping_type, claim_no, third_party_involved, loss_type, item_type, vehicle_no, contact_no, "
            + "insurer_details, intend_claim, remark, inp_user_id, inp_date_time) "
            + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    String UPDATE_CLAIM_THIRD_PARTY_DETAILS = "UPDATE claim_third_party_assessor_details_me SET\n"
            + "		mapping_id=?,\n"
            + "		mapping_type=?,\n"
            + "		claim_no=?,\n"
            + "		third_party_involved=?,\n"
            + "		loss_type=?,\n"
            + "		item_type=?,\n"
            + "		vehicle_no=?,\n"
            + "		contact_no=?,\n"
            + "		insurer_details=?,\n"
            + "		intend_claim=?,\n"
            + "		remark=?,\n"
            + "		inp_user_id=?,\n"
            + "		inp_date_time= ?\n"
            + "WHERE txn_id=?";

    String SELECT_CLAIM_THIRD_PARTY_DETAILS = "SELECT * FROM claim_third_party_assessor_details_me WHERE claim_no=?";
    String DELETE_CLAIM_THIRD_PARTY_MAIN_BY_CLAIM_NO = "DELETE FROM claim_third_party_assessor_details_me WHERE claim_no=?";
    String DELETE_CLAIM_THIRD_PARTY_MAIN = "DELETE FROM claim_third_party_assessor_details_me WHERE txn_id=?";

    ClaimThirdPartyDetailsGenericDto insertMaster(Connection connection, ClaimThirdPartyDetailsGenericDto thirdPartyDto) throws Exception;

    ClaimThirdPartyDetailsGenericDto updateMaster(Connection connection, ClaimThirdPartyDetailsGenericDto thirdPartyDto) throws Exception;

    List<ClaimThirdPartyDetailsGenericDto> searchAll(Connection connection, Integer claimNo) throws Exception;

    Integer insertThirdPartyList(Connection connection, Map<Integer, ClaimThirdPartyDetailsGenericDto> thirdPartyList, Integer claimNo) throws Exception;

    public Map<Integer, ClaimThirdPartyDetailsGenericDto> searchAllClaimsThirdParty(Connection connection, Integer claimNo) throws Exception;

    Integer updateMasterList(Connection connection, Map<Integer, ClaimThirdPartyDetailsGenericDto> thirdPartyList) throws Exception;

    void removeClaimThirdPartyByClaimNo(Connection connection, Integer claimNo) throws Exception;

    void removeClaimThirdParty(Connection connection, Integer claimNo) throws Exception;

}
