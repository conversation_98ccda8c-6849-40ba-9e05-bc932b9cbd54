package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.math.BigDecimal;
public class CalSheetTypeDto {
    private Integer typeId;
    private String typeDesc;
    private String isPolicyWise = AppConstant.NO;
    private String code;
    private BigDecimal limit;

    public Integer getTypeId() {
        return typeId;
    }

    public void setTypeId(Integer typeId) {
        this.typeId = typeId;
    }

    public String getTypeDesc() {
        return typeDesc;
    }

    public void setTypeDesc(String typeDesc) {
        this.typeDesc = typeDesc;
    }

    public String getIsPolicyWise() {
        return isPolicyWise;
    }

    public void setIsPolicyWise(String isPolicyWise) {
        this.isPolicyWise = isPolicyWise;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public BigDecimal getLimit() {
        return limit;
    }

    public void setLimit(BigDecimal limit) {
        this.limit = limit;
    }
}
