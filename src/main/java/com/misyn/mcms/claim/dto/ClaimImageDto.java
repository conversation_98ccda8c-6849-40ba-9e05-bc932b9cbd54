package com.misyn.mcms.claim.dto;

import com.misyn.mcms.claim.enums.FileTypeEnum;
import com.misyn.mcms.utility.AppConstant;

import java.io.InputStream;
import java.io.Serializable;
public class ClaimImageDto implements Serializable {
    private Integer refNo = AppConstant.ZERO_INT;
    private Integer jobRefNo = AppConstant.ZERO_INT;
    private Integer claimNo = AppConstant.ZERO_INT;
    private Integer documentTypeId = AppConstant.ZERO_INT;
    private String documentPath = AppConstant.STRING_EMPTY;
    private String documentName = AppConstant.STRING_EMPTY;
    private String documentStatus = AppConstant.STRING_PENDING;
    private String inpStat = "I";
    private String inpUser = AppConstant.STRING_EMPTY;
    private String inpDateTime = AppConstant.DEFAULT_DATE_TIME;
    private InputStream inputStream = null;
    private InputStream thumbInputStream = null;
    private FileTypeEnum fileTypeEnum = FileTypeEnum.PDF;
    private String fileExtension = AppConstant.STRING_EMPTY;
    private String toolTip = AppConstant.STRING_EMPTY;

    public Integer getRefNo() {
        return refNo;
    }

    public void setRefNo(Integer refNo) {
        this.refNo = refNo;
    }

    public Integer getJobRefNo() {
        return jobRefNo;
    }

    public void setJobRefNo(Integer jobRefNo) {
        this.jobRefNo = jobRefNo;
    }

    public Integer getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(Integer claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getDocumentTypeId() {
        return documentTypeId;
    }

    public void setDocumentTypeId(Integer documentTypeId) {
        this.documentTypeId = documentTypeId;
    }

    public String getDocumentPath() {
        return documentPath;
    }

    public void setDocumentPath(String documentPath) {
        this.documentPath = documentPath;
    }

    public String getDocumentName() {
        return documentName;
    }

    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }

    public String getDocumentStatus() {
        return documentStatus;
    }

    public void setDocumentStatus(String documentStatus) {
        this.documentStatus = documentStatus;
    }

    public String getInpStat() {
        return inpStat;
    }

    public void setInpStat(String inpStat) {
        this.inpStat = inpStat;
    }

    public String getInpUser() {
        return inpUser;
    }

    public void setInpUser(String inpUser) {
        this.inpUser = inpUser;
    }

    public String getInpDateTime() {
        return inpDateTime;
    }

    public void setInpDateTime(String inpDateTime) {
        this.inpDateTime = inpDateTime;
    }

    public InputStream getInputStream() {
        return inputStream;
    }

    public void setInputStream(InputStream inputStream) {
        this.inputStream = inputStream;
    }

    public FileTypeEnum getFileTypeEnum() {
        return fileTypeEnum;
    }

    public void setFileTypeEnum(FileTypeEnum fileTypeEnum) {
        this.fileTypeEnum = fileTypeEnum;
    }

    public String getFileExtension() {
        return fileExtension;
    }

    public void setFileExtension(String fileExtension) {
        this.fileExtension = fileExtension;
    }

    public String getToolTip() {
        return toolTip;
    }

    public void setToolTip(String toolTip) {
        this.toolTip = toolTip;
    }

    public InputStream getThumbInputStream() {
        return thumbInputStream;
    }

    public void setThumbInputStream(InputStream thumbInputStream) {
        this.thumbInputStream = thumbInputStream;
    }
}
