package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
public class ClaimPanelDto implements Serializable {
    private String dMaker;
    private String rejectionReason;
    private String panelDecision;
    private String dmComment;
    private boolean isPanelDecision = false;
    private List<PanelDecisionDto>userDtos = new ArrayList<>();

    public String getdMaker() {
        return dMaker;
    }

    public void setdMaker(String dMaker) {
        this.dMaker = dMaker;
    }

    public String getRejectionReason() {
        return rejectionReason;
    }

    public boolean getIsPanelDecision() {
        return isPanelDecision;
    }

    public void setIsPanelDecision(boolean panelDecision) {
        isPanelDecision = panelDecision;
    }

    public void setRejectionReason(String rejectionReason) {
        this.rejectionReason = rejectionReason;
    }

    public String getPanelDecision() {
        return panelDecision;
    }

    public void setPanelDecision(String panelDecision) {
        this.panelDecision = panelDecision;
    }

    public List<PanelDecisionDto> getUserDtos() {
        return userDtos;
    }

    public void setUserDtos(List<PanelDecisionDto> userDtos) {
        this.userDtos = userDtos;
    }

    public String getDmComment() {
        return dmComment;
    }

    public void setDmComment(String dmComment) {
        this.dmComment = dmComment;
    }
}
