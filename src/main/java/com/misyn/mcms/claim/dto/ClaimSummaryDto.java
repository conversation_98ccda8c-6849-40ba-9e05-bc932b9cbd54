package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
public class ClaimSummaryDto implements Serializable {
    private String policyStatus;
    private List<InspectionDetailsSummaryDto> inspectionDetailsSummaryDtoList;
    private String initialLiabilityStatus;
    private String initialLiabilityApprovedUser;
    private String liabilityStatus;
    private String liabilityApprovedUser;
    private BigDecimal totalApprovedAcr;

    public String getPolicyStatus() {
        return policyStatus;
    }

    public void setPolicyStatus(String policyStatus) {
        this.policyStatus = policyStatus;
    }

    public List<InspectionDetailsSummaryDto> getInspectionDetailsSummaryDtoList() {
        return inspectionDetailsSummaryDtoList;
    }

    public void setInspectionDetailsSummaryDtoList(List<InspectionDetailsSummaryDto> inspectionDetailsSummaryDtoList) {
        this.inspectionDetailsSummaryDtoList = inspectionDetailsSummaryDtoList;
    }

    public String getInitialLiabilityStatus() {
        return initialLiabilityStatus;
    }

    public void setInitialLiabilityStatus(String initialLiabilityStatus) {
        this.initialLiabilityStatus = initialLiabilityStatus;
    }

    public String getInitialLiabilityApprovedUser() {
        return initialLiabilityApprovedUser;
    }

    public void setInitialLiabilityApprovedUser(String initialLiabilityApprovedUser) {
        this.initialLiabilityApprovedUser = initialLiabilityApprovedUser;
    }

    public String getLiabilityStatus() {
        return liabilityStatus;
    }

    public void setLiabilityStatus(String liabilityStatus) {
        this.liabilityStatus = liabilityStatus;
    }

    public String getLiabilityApprovedUser() {
        return liabilityApprovedUser;
    }

    public void setLiabilityApprovedUser(String liabilityApprovedUser) {
        this.liabilityApprovedUser = liabilityApprovedUser;
    }

    public BigDecimal getTotalApprovedAcr() {
        return totalApprovedAcr;
    }

    public void setTotalApprovedAcr(BigDecimal totalApprovedAcr) {
        this.totalApprovedAcr = totalApprovedAcr;
    }
}
