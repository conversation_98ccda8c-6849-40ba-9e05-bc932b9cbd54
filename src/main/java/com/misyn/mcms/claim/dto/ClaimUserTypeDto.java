package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class ClaimUserTypeDto implements Serializable {
    private UserDto user;
    private boolean isTwoPanelUser = false;
    private boolean isFourPanelUser = false;
    private boolean isDecisionMaker = false;
    private boolean isInitLiabilityUser = false;
    private boolean isOfferTeamInitLiabilityUser = false;
    private boolean isClaimHandlerUser = false;
    private boolean isOfferTeamClaimHandlerUser = false;
    private boolean isTotalLossClaimHandlerUser = false;
    private boolean isSparePartsCoordinator = false;
    private boolean isScrutinizingTeam = false;
    private boolean isSpecialTeam = false;
    private boolean isOfferTeamSpecialTeam = false;
    private boolean isMofaTeam = false;
    private boolean isOfferTeamMofaTeam = false;
    private boolean isBranchUser = false;
    private boolean isAssessor = false;
    private boolean isRte = false;
    private boolean isTechnicalCoordinator = false;
    private boolean isTechnicalCoordinatorAriOnly = false;


    public boolean isTwoPanelUser() {
        return isTwoPanelUser;
    }

    public void setTwoPanelUser(boolean twoPanelUser) {
        isTwoPanelUser = twoPanelUser;
    }

    public boolean isFourPanelUser() {
        return isFourPanelUser;
    }

    public void setFourPanelUser(boolean fourPanelUser) {
        isFourPanelUser = fourPanelUser;
    }

    public boolean isDecisionMaker() {
        return isDecisionMaker;
    }

    public void setDecisionMaker(boolean decisionMaker) {
        isDecisionMaker = decisionMaker;
    }

    public boolean isInitLiabilityUser() {
        return isInitLiabilityUser;
    }

    public void setInitLiabilityUser(boolean initLiabilityUser) {
        isInitLiabilityUser = initLiabilityUser;
    }

    public boolean isClaimHandlerUser() {
        return isClaimHandlerUser;
    }

    public void setClaimHandlerUser(boolean claimHandlerUser) {
        isClaimHandlerUser = claimHandlerUser;
    }

    public UserDto getUser() {
        return user;
    }

    public void setUser(UserDto user) {
        this.user = user;
    }

    public boolean isSparePartsCoordinator() {
        return isSparePartsCoordinator;
    }

    public void setSparePartsCoordinator(boolean sparePartsCoordinator) {
        isSparePartsCoordinator = sparePartsCoordinator;
    }

    public boolean isScrutinizingTeam() {
        return isScrutinizingTeam;
    }

    public void setScrutinizingTeam(boolean scrutinizingTeam) {
        isScrutinizingTeam = scrutinizingTeam;
    }

    public boolean isSpecialTeam() {
        return isSpecialTeam;
    }

    public void setSpecialTeam(boolean specialTeam) {
        isSpecialTeam = specialTeam;
    }

    public boolean isMofaTeam() {
        return isMofaTeam;
    }

    public void setMofaTeam(boolean mofaTeam) {
        isMofaTeam = mofaTeam;
    }

    public boolean isBranchUser() {
        return isBranchUser;
    }

    public void setBranchUser(boolean branchUser) {
        isBranchUser = branchUser;
    }

    public boolean isAssessor() {
        return isAssessor;
    }

    public void setAssessor(boolean assessor) {
        isAssessor = assessor;
    }

    public boolean isRte() {
        return isRte;
    }

    public void setRte(boolean rte) {
        isRte = rte;
    }

    public boolean isTotalLossClaimHandlerUser() {
        return isTotalLossClaimHandlerUser;
    }

    public void setTotalLossClaimHandlerUser(boolean totalLossClaimHandlerUser) {
        isTotalLossClaimHandlerUser = totalLossClaimHandlerUser;
    }

    public boolean isTechnicalCoordinator() {
        return isTechnicalCoordinator;
    }

    public void setTechnicalCoordinator(boolean technicalCoordinator) {
        isTechnicalCoordinator = technicalCoordinator;
    }

    public boolean isOfferTeamInitLiabilityUser() {
        return isOfferTeamInitLiabilityUser;
    }

    public void setOfferTeamInitLiabilityUser(boolean offerTeamInitLiabilityUser) {
        isOfferTeamInitLiabilityUser = offerTeamInitLiabilityUser;
    }

    public boolean isOfferTeamClaimHandlerUser() {
        return isOfferTeamClaimHandlerUser;
    }

    public void setOfferTeamClaimHandlerUser(boolean offerTeamClaimHandlerUser) {
        isOfferTeamClaimHandlerUser = offerTeamClaimHandlerUser;
    }

    public boolean isOfferTeamSpecialTeam() {
        return isOfferTeamSpecialTeam;
    }

    public void setOfferTeamSpecialTeam(boolean offerTeamSpecialTeam) {
        isOfferTeamSpecialTeam = offerTeamSpecialTeam;
    }

    public boolean isOfferTeamMofaTeam() {
        return isOfferTeamMofaTeam;
    }

    public void setOfferTeamMofaTeam(boolean offerTeamMofaTeam) {
        isOfferTeamMofaTeam = offerTeamMofaTeam;
    }

    public boolean isTechnicalCoordinatorAriOnly() {
        return isTechnicalCoordinatorAriOnly;
    }

    public void setTechnicalCoordinatorAriOnly(boolean technicalCoordinatorAriOnly) {
        isTechnicalCoordinatorAriOnly = technicalCoordinatorAriOnly;
    }
}
