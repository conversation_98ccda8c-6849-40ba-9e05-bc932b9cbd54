package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.util.Date;
public class ErrorMessageDto implements Serializable {
    private Date timestamp = new Date();
    private Integer errorCode = 200;
    private String message = "NO ERRORS";
    private String dtoFieldName = "";
    private String formFieldName = "";

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }


    public String getDtoFieldName() {
        return dtoFieldName;
    }

    public void setDtoFieldName(String dtoFieldName) {
        this.dtoFieldName = dtoFieldName;
    }

    public String getFormFieldName() {
        return formFieldName;
    }

    public void setFormFieldName(String formFieldName) {
        this.formFieldName = formFieldName;
    }
}
