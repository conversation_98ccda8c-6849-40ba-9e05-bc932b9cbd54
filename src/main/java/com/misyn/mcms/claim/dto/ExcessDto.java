package com.misyn.mcms.claim.dto;

import java.io.Serializable;
import java.math.BigDecimal;
public class ExcessDto implements Serializable {
    private String policyNo;
    private String excessDesc;
    private BigDecimal excessAmount;
    private BigDecimal excessRate;
    private BigDecimal premium;

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getExcessDesc() {
        return excessDesc;
    }

    public void setExcessDesc(String excessDesc) {
        this.excessDesc = excessDesc;
    }

    public BigDecimal getExcessAmount() {
        return excessAmount;
    }

    public void setExcessAmount(BigDecimal excessAmount) {
        this.excessAmount = excessAmount;
    }

    public BigDecimal getExcessRate() {
        return excessRate;
    }

    public void setExcessRate(BigDecimal excessRate) {
        this.excessRate = excessRate;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }
}
