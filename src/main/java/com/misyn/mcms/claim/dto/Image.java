/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class Image implements Serializable {

    private int n_ref_id = 0;
    private int n_clim_no = 0;
    private int n_img_id = 0;
    private String v_status = "P";
    private String v_doc_path = "";
    private String v_doc_name = "";

    private String v_inpstat = "I";
    private String v_inpuser = "";
    private String d_inptime = "1900-01-01 12:00:00";
    private String v_auth1stat = "";
    private String v_auth1user = "";
    private String d_auth1time = "1900-01-01 12:00:00";
    private String v_auth2stat = "";
    private String v_auth2user = "";
    private String d_auth2time = "1900-01-01 12:00:00";

    public String getD_auth1time() {
        return d_auth1time;
    }

    public void setD_auth1time(String d_auth1time) {
        this.d_auth1time = d_auth1time;
    }

    public String getD_auth2time() {
        return d_auth2time;
    }

    public void setD_auth2time(String d_auth2time) {
        this.d_auth2time = d_auth2time;
    }

    public String getD_inptime() {
        return d_inptime;
    }

    public void setD_inptime(String d_inptime) {
        this.d_inptime = d_inptime;
    }

    public int getN_clim_no() {
        return n_clim_no;
    }

    public void setN_clim_no(int n_clim_no) {
        this.n_clim_no = n_clim_no;
    }

    public int getN_img_id() {
        return n_img_id;
    }

    public void setN_img_id(int n_img_id) {
        this.n_img_id = n_img_id;
    }

    public String getV_auth1stat() {
        return v_auth1stat;
    }

    public void setV_auth1stat(String v_auth1stat) {
        this.v_auth1stat = v_auth1stat;
    }

    public String getV_auth1user() {
        return v_auth1user;
    }

    public void setV_auth1user(String v_auth1user) {
        this.v_auth1user = v_auth1user;
    }

    public String getV_auth2stat() {
        return v_auth2stat;
    }

    public void setV_auth2stat(String v_auth2stat) {
        this.v_auth2stat = v_auth2stat;
    }

    public String getV_auth2user() {
        return v_auth2user;
    }

    public void setV_auth2user(String v_auth2user) {
        this.v_auth2user = v_auth2user;
    }

    public String getV_doc_name() {
        return v_doc_name;
    }

    public void setV_doc_name(String v_doc_name) {
        this.v_doc_name = v_doc_name;
    }

    public String getV_doc_path() {
        return v_doc_path;
    }

    public void setV_doc_path(String v_doc_path) {
        this.v_doc_path = v_doc_path;
    }

    public String getV_inpstat() {
        return v_inpstat;
    }

    public void setV_inpstat(String v_inpstat) {
        this.v_inpstat = v_inpstat;
    }

    public String getV_inpuser() {
        return v_inpuser;
    }

    public void setV_inpuser(String v_inpuser) {
        this.v_inpuser = v_inpuser;
    }

    public String getV_status() {
        return v_status;
    }

    public void setV_status(String v_status) {
        this.v_status = v_status;
    }

    public int getN_ref_id() {
        return n_ref_id;
    }

    public void setN_ref_id(int n_ref_id) {
        this.n_ref_id = n_ref_id;
    }


}
