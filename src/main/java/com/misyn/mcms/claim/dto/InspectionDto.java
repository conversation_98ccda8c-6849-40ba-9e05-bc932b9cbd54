package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class InspectionDto implements Serializable {

    private int inspectionId = AppConstant.ZERO_INT;
    private String inspectionValue = AppConstant.STRING_EMPTY;
    private String inspectionCode = AppConstant.STRING_EMPTY;
    private int recordStatus = AppConstant.ZERO_INT;
    private boolean isInformedIfDesktop = false;
    private String offerType = AppConstant.STRING_EMPTY;

    public int getInspectionId() {
        return inspectionId;
    }

    public void setInspectionId(int inspectionId) {
        this.inspectionId = inspectionId;
    }

    public String getInspectionValue() {
        return inspectionValue;
    }

    public void setInspectionValue(String inspectionValue) {
        this.inspectionValue = inspectionValue;
    }

    public String getInspectionCode() {
        return inspectionCode;
    }

    public void setInspectionCode(String inspectionCode) {
        this.inspectionCode = inspectionCode;
    }

    public int getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(int recordStatus) {
        this.recordStatus = recordStatus;
    }

    public boolean getIsInformedIfDesktop() {
        return isInformedIfDesktop;
    }

    public void setInformedIfDesktop(boolean informedIfDesktop) {
        isInformedIfDesktop = informedIfDesktop;
    }

    public String getOfferType() {return offerType;}

    public void setOfferType(String offerType) {this.offerType = offerType;}

}
