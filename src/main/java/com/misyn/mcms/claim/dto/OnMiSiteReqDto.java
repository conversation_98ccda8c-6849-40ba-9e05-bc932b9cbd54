package com.misyn.mcms.claim.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

public class OnMiSiteReqDto implements Serializable {
    private String claimNumber;
    private String inspectionJobNumber;
    private String assignee;
    private String callingMobileNo;
    private int inspectionTypeId;
    private String policyNo;
    private String vehicleNo;
    private String vehicleMake;
    private String vehicleModel;
    private String insuredName;
    private String insuredAddress1;
    private String insuredAddress2;
    private String insuredAddress3;
    private String insuredMobileNo;
    private String insuredEmail;

    public OnMiSiteReqDto() {
    }

    public OnMiSiteReqDto(String claimNumber, String inspectionJobNumber, String assignee, String callingMobileNo, int inspectionTypeId, String policyNo, String vehicleNo, String vehicleMake, String vehicleModel, String insuredName, String insuredAddress1, String insuredAddress2, String insuredAddress3, String insuredMobileNo, String insuredEmail) {
        this.claimNumber = claimNumber;
        this.inspectionJobNumber = inspectionJobNumber;
        this.assignee = assignee;
        this.callingMobileNo = callingMobileNo;
        this.inspectionTypeId = inspectionTypeId;
        this.policyNo = policyNo;
        this.vehicleNo = vehicleNo;
        this.vehicleMake = vehicleMake;
        this.vehicleModel = vehicleModel;
        this.insuredName = insuredName;
        this.insuredAddress1 = insuredAddress1;
        this.insuredAddress2 = insuredAddress2;
        this.insuredAddress3 = insuredAddress3;
        this.insuredMobileNo = insuredMobileNo;
        this.insuredEmail = insuredEmail;
    }

    public String getClaimNumber() {
        return claimNumber;
    }

    public void setClaimNumber(String claimNumber) {
        this.claimNumber = claimNumber;
    }

    public String getInspectionJobNumber() {
        return inspectionJobNumber;
    }

    public void setInspectionJobNumber(String inspectionJobNumber) {
        this.inspectionJobNumber = inspectionJobNumber;
    }

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    public String getCallingMobileNo() {
        return callingMobileNo;
    }

    public void setCallingMobileNo(String callingMobileNo) {
        this.callingMobileNo = callingMobileNo;
    }

    public int getInspectionTypeId() {
        return inspectionTypeId;
    }

    public void setInspectionTypeId(int inspectionTypeId) {
        this.inspectionTypeId = inspectionTypeId;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public String getVehicleMake() {
        return vehicleMake;
    }

    public void setVehicleMake(String vehicleMake) {
        this.vehicleMake = vehicleMake;
    }

    public String getVehicleModel() {
        return vehicleModel;
    }

    public void setVehicleModel(String vehicleModel) {
        this.vehicleModel = vehicleModel;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public String getInsuredAddress1() {
        return insuredAddress1;
    }

    public void setInsuredAddress1(String insuredAddress1) {
        this.insuredAddress1 = insuredAddress1;
    }

    public String getInsuredAddress2() {
        return insuredAddress2;
    }

    public void setInsuredAddress2(String insuredAddress2) {
        this.insuredAddress2 = insuredAddress2;
    }

    public String getInsuredAddress3() {
        return insuredAddress3;
    }

    public void setInsuredAddress3(String insuredAddress3) {
        this.insuredAddress3 = insuredAddress3;
    }

    public String getInsuredMobileNo() {
        return insuredMobileNo;
    }

    public void setInsuredMobileNo(String insuredMobileNo) {
        this.insuredMobileNo = insuredMobileNo;
    }

    public String getInsuredEmail() {
        return insuredEmail;
    }

    public void setInsuredEmail(String insuredEmail) {
        this.insuredEmail = insuredEmail;
    }
}
