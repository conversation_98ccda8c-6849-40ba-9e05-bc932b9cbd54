package com.misyn.mcms.claim.dto;

import com.misyn.mcms.admin.admin.dto.ProductDetailListDto;
import com.misyn.mcms.claim.enums.PolicyStatusEnum;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
public class PolicyDto implements Serializable {

    private Integer index = AppConstant.ZERO_INT;
    private Integer policyRefNo = AppConstant.ZERO_INT;
    private String policyBranch = AppConstant.STRING_EMPTY;
    private String policyType = AppConstant.STRING_EMPTY;
    private String policyNumber = AppConstant.STRING_EMPTY;
    private Integer renCount = AppConstant.ZERO_INT;
    private Integer endCount = AppConstant.ZERO_INT;
    private String vehicleNumber = AppConstant.STRING_EMPTY;
    private String expireDate = AppConstant.DEFAULT_DATE;
    private String inspecDate = AppConstant.DEFAULT_DATE;
    private String orgInspecDate = AppConstant.DEFAULT_DATE;
    private String polStatus = AppConstant.STRING_EMPTY;
    private String polStatusDesc = AppConstant.STRING_EMPTY;
    private String custName = AppConstant.STRING_EMPTY;
    private String custNic = AppConstant.STRING_EMPTY;
    private String custAddressLine1 = AppConstant.STRING_EMPTY;
    private String custAddressLine2 = AppConstant.STRING_EMPTY;
    private String custAddressLine3 = AppConstant.STRING_EMPTY;
    private String custMobileNo = AppConstant.STRING_EMPTY;
    private String custLandNo = AppConstant.STRING_EMPTY;
    private BigDecimal annualPremium = BigDecimal.ZERO;
    private BigDecimal sumInsured = BigDecimal.ZERO;
    private String latestClmIntimDate = AppConstant.DEFAULT_DATE;
    private String latestClmLossDate = AppConstant.DEFAULT_DATE;
    private String agentBroker = AppConstant.STRING_EMPTY;
    private BigDecimal totPremOutstand = BigDecimal.ZERO;
    private Integer noDayPremOutstand = AppConstant.ZERO_INT;
    private String engineNo = AppConstant.STRING_EMPTY;
    private String chassisNo = AppConstant.STRING_EMPTY;
    private String vehicleMake = AppConstant.STRING_EMPTY;
    private String vehicleModel = AppConstant.STRING_EMPTY;
    private Integer manufactYear = AppConstant.ZERO_INT;
    private BigDecimal excess = BigDecimal.ZERO;
    private Integer ncbRate = AppConstant.ZERO_INT;
    private BigDecimal ncbAmount = BigDecimal.ZERO;
    private String coverNoteNo = AppConstant.STRING_EMPTY;
    private String coverType = AppConstant.STRING_EMPTY;
    private String channel = AppConstant.STRING_EMPTY;
    private String updateFlag = AppConstant.STRING_EMPTY;
    private String insertFlag = AppConstant.STRING_EMPTY;
    private String createUser = AppConstant.STRING_EMPTY;
    private String createDate = AppConstant.DEFAULT_DATE;
    private String createTime = AppConstant.DEFAULT_TIME;
    private String cancelReason = AppConstant.STRING_EMPTY;
    private String lapsedDate = AppConstant.DEFAULT_DATE;
    private String registDate = AppConstant.DEFAULT_DATE;
    private String polCancelDate = AppConstant.DEFAULT_DATE;
    private String location = AppConstant.STRING_EMPTY;
    private String risk = AppConstant.STRING_EMPTY;
    private String bodyType = AppConstant.STRING_EMPTY;
    private String clientId = AppConstant.STRING_EMPTY;
    private String isThirdParty = AppConstant.STRING_EMPTY;
    private String financeCompany = AppConstant.STRING_EMPTY;
    private String vehicleUsage = AppConstant.STRING_EMPTY;
    private String agentCode = AppConstant.STRING_EMPTY;
    private String fuelType = AppConstant.STRING_EMPTY;
    private Integer noOfSeat = AppConstant.ZERO_INT;
    private Integer vehicleAge = AppConstant.ZERO_INT;
    private String polSuspend = AppConstant.STRING_EMPTY;
    private String engCapacity = AppConstant.STRING_EMPTY;
    private String branchCode = AppConstant.STRING_EMPTY;
    private String product = AppConstant.STRING_EMPTY;
    private String lastModifyUser = AppConstant.STRING_EMPTY;
    private String lastModifyDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String currentPolStatus = AppConstant.STRING_EMPTY;
    private PolicyStatusEnum validePolicyStatus;
    private String policyStatus = AppConstant.STRING_EMPTY;
    private String vehicleColor = AppConstant.STRING_EMPTY;
    private String tradePlateNo = AppConstant.STRING_EMPTY;
    private String lastModifyDate = AppConstant.DEFAULT_DATE;
    private String indComFlag = AppConstant.STRING_EMPTY;
    private String companyBranch = AppConstant.STRING_EMPTY;
    private String companyCode = AppConstant.STRING_EMPTY;
    private String cmsUpdateDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String isfClaimNo = AppConstant.EMPTY_STRING;

    private String deduction = AppConstant.STRING_EMPTY;
    private BigDecimal paidTotalAmount = BigDecimal.ZERO;

    private List<CoverDto> coverDtoList = new ArrayList<>();
    private List<ExcessDto> excessDtoList = new ArrayList<>();
    private List<PaidDetailsDto> paidDetailsDtoList = new ArrayList<>();
    private List<PolicyMemoDto> policyMemoDtoList = new ArrayList<>();
    private List<SellingAgentDetailsDto> sellingAgentDetailsDtoList = new ArrayList<>();
    private List<EndorsementHistoryDto> endorsementHistoryDtoList = new ArrayList<>();
    private List<BillingInfoDto> billingInfoDtoList = new ArrayList<>();
    private List<LearnerDriverDetailsDto> learnerDriverDetailsDtoList = new ArrayList<>();
    private List<CweDetailDto> cweDetailDtoList = new ArrayList<>();
    private List<TradePlateDetailDto> tradePlateDetailDtoList = new ArrayList<>();
    private List<IntroducerDto> introducerDetailDtoList = new ArrayList<>();
    private TrailerDetailDto trailerDetailDto = new TrailerDetailDto();
    private PremiumBreakupFormDto premiumBreakupFormDto = new PremiumBreakupFormDto();
    private Integer vehicleClassId = AppConstant.ZERO_INT;
    private String curDate = Utility.sysDate();

    private PolicySellingAgentDetailsDto policySellingAgentDetailsDto = new PolicySellingAgentDetailsDto();
    private String finCompanyCode = AppConstant.STRING_EMPTY;
    private String finCompanyBranch = AppConstant.STRING_EMPTY;
    private String loanAccNo = AppConstant.STRING_EMPTY;
    private String bankRefNo = AppConstant.STRING_EMPTY;
    private String idenCode = AppConstant.STRING_EMPTY;
    private String policyChannelType;

    private Integer ncbPercentange = AppConstant.ZERO_INT;
    private Integer ncbYear = AppConstant.ZERO_INT;
    private List<NcbHistoryDetailsDto> ncbHistoryDetailsSummary = new ArrayList<>();
    private boolean callAndGoDescription = false;
    private String bizType = AppConstant.STRING_EMPTY;

    private String vehicleNoLastDigit = AppConstant.STRING_EMPTY;
    private String policyNumberLastDigit = AppConstant.STRING_EMPTY;

    private String introducer = AppConstant.STRING_EMPTY;
    private String workflow = AppConstant.EMPTY_STRING;
    private String categoryDescription = AppConstant.EMPTY_STRING;

    private IntroducerDto introducerDto = new IntroducerDto();

    private ProductDetailListDto productDetailListDto = new ProductDetailListDto();

    public ProductDetailListDto getProductDetailListDto() {
        return productDetailListDto;
    }

    public void setProductDetailListDto(ProductDetailListDto productDetailListDto) {
        this.productDetailListDto = productDetailListDto;
    }

    public List<CweDetailDto> getCweDetailDtoList() {
        return cweDetailDtoList;
    }

    public void setCweDetailDtoList(List<CweDetailDto> cweDetailDtoList) {
        this.cweDetailDtoList = cweDetailDtoList;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getPolStatusDesc() {
        return polStatusDesc;
    }

    public void setPolStatusDesc(String polStatusDesc) {
        this.polStatusDesc = polStatusDesc;
    }

    public Integer getPolicyRefNo() {
        return policyRefNo;
    }

    public void setPolicyRefNo(Integer policyRefNo) {
        this.policyRefNo = policyRefNo;
    }

    public String getPolicyBranch() {
        return policyBranch;
    }

    public void setPolicyBranch(String policyBranch) {
        this.policyBranch = policyBranch;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getPolicyNumber() {
        return policyNumber;
    }

    public void setPolicyNumber(String policyNumber) {
        this.policyNumber = policyNumber;
    }

    public Integer getRenCount() {
        return renCount;
    }

    public void setRenCount(Integer renCount) {
        this.renCount = renCount;
    }

    public Integer getEndCount() {
        return endCount;
    }

    public void setEndCount(Integer endCount) {
        this.endCount = endCount;
    }

    public String getVehicleNumber() {
        return vehicleNumber;
    }

    public void setVehicleNumber(String vehicleNumber) {
        this.vehicleNumber = vehicleNumber;
    }

    public String getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(String expireDate) {
        this.expireDate = expireDate;
    }

    public String getInspecDate() {
        return inspecDate;
    }

    public void setInspecDate(String inspecDate) {
        this.inspecDate = inspecDate;
    }

    public String getOrgInspecDate() {
        return orgInspecDate;
    }

    public void setOrgInspecDate(String orgInspecDate) {
        this.orgInspecDate = orgInspecDate;
    }

    public String getPolStatus() {
        return polStatus;
    }

    public void setPolStatus(String polStatus) {
        this.polStatus = polStatus;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getCustNic() {
        return custNic;
    }

    public void setCustNic(String custNic) {
        this.custNic = custNic;
    }

    public String getCustAddressLine1() {
        return custAddressLine1;
    }

    public void setCustAddressLine1(String custAddressLine1) {
        this.custAddressLine1 = custAddressLine1;
    }

    public String getCustAddressLine2() {
        return custAddressLine2;
    }

    public void setCustAddressLine2(String custAddressLine2) {
        this.custAddressLine2 = custAddressLine2;
    }

    public String getCustAddressLine3() {
        return custAddressLine3;
    }

    public void setCustAddressLine3(String custAddressLine3) {
        this.custAddressLine3 = custAddressLine3;
    }

    public String getCustMobileNo() {
        return custMobileNo;
    }

    public void setCustMobileNo(String custMobileNo) {
        this.custMobileNo = custMobileNo;
    }

    public String getCustLandNo() {
        return custLandNo;
    }

    public void setCustLandNo(String custLandNo) {
        this.custLandNo = custLandNo;
    }

    public BigDecimal getAnnualPremium() {
        return annualPremium;
    }

    public void setAnnualPremium(BigDecimal annualPremium) {
        this.annualPremium = annualPremium;
    }

    public BigDecimal getSumInsured() {
        return sumInsured;
    }

    public void setSumInsured(BigDecimal sumInsured) {
        this.sumInsured = sumInsured;
    }

    public String getLatestClmIntimDate() {
        return latestClmIntimDate;
    }

    public void setLatestClmIntimDate(String latestClmIntimDate) {
        this.latestClmIntimDate = latestClmIntimDate;
    }

    public String getLatestClmLossDate() {
        return latestClmLossDate;
    }

    public void setLatestClmLossDate(String latestClmLossDate) {
        this.latestClmLossDate = latestClmLossDate;
    }

    public String getAgentBroker() {
        return agentBroker;
    }

    public void setAgentBroker(String agentBroker) {
        this.agentBroker = agentBroker;
    }

    public BigDecimal getTotPremOutstand() {
        return totPremOutstand;
    }

    public void setTotPremOutstand(BigDecimal totPremOutstand) {
        this.totPremOutstand = totPremOutstand;
    }

    public Integer getNoDayPremOutstand() {
        return noDayPremOutstand;
    }

    public void setNoDayPremOutstand(Integer noDayPremOutstand) {
        this.noDayPremOutstand = noDayPremOutstand;
    }

    public String getEngineNo() {
        return engineNo;
    }

    public void setEngineNo(String engineNo) {
        this.engineNo = engineNo;
    }

    public String getChassisNo() {
        return chassisNo;
    }

    public void setChassisNo(String chassisNo) {
        this.chassisNo = chassisNo;
    }

    public String getVehicleMake() {
        return vehicleMake;
    }

    public void setVehicleMake(String vehicleMake) {
        this.vehicleMake = vehicleMake;
    }

    public String getVehicleModel() {
        return vehicleModel;
    }

    public void setVehicleModel(String vehicleModel) {
        this.vehicleModel = vehicleModel;
    }

    public Integer getManufactYear() {
        return manufactYear;
    }

    public void setManufactYear(Integer manufactYear) {
        this.manufactYear = manufactYear;
    }

    public BigDecimal getExcess() {
        return excess;
    }

    public void setExcess(BigDecimal excess) {
        this.excess = excess;
    }

    public Integer getNcbRate() {
        return ncbRate;
    }

    public void setNcbRate(Integer ncbRate) {
        this.ncbRate = ncbRate;
    }

    public BigDecimal getNcbAmount() {
        return ncbAmount;
    }

    public void setNcbAmount(BigDecimal ncbAmount) {
        this.ncbAmount = ncbAmount;
    }

    public String getCoverNoteNo() {
        return coverNoteNo;
    }

    public void setCoverNoteNo(String coverNoteNo) {
        this.coverNoteNo = coverNoteNo;
    }

    public String getCoverType() {
        return coverType;
    }

    public void setCoverType(String coverType) {
        this.coverType = coverType;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getUpdateFlag() {
        return updateFlag;
    }

    public void setUpdateFlag(String updateFlag) {
        this.updateFlag = updateFlag;
    }

    public String getInsertFlag() {
        return insertFlag;
    }

    public void setInsertFlag(String insertFlag) {
        this.insertFlag = insertFlag;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public String getLapsedDate() {
        return lapsedDate;
    }

    public void setLapsedDate(String lapsedDate) {
        this.lapsedDate = lapsedDate;
    }

    public String getRegistDate() {
        return registDate;
    }

    public void setRegistDate(String registDate) {
        this.registDate = registDate;
    }

    public String getPolCancelDate() {
        return polCancelDate;
    }

    public void setPolCancelDate(String polCancelDate) {
        this.polCancelDate = polCancelDate;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getRisk() {
        return risk;
    }

    public void setRisk(String risk) {
        this.risk = risk;
    }

    public String getBodyType() {
        return bodyType;
    }

    public void setBodyType(String bodyType) {
        this.bodyType = bodyType;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getIsThirdParty() {
        return isThirdParty;
    }

    public void setIsThirdParty(String isThirdParty) {
        this.isThirdParty = isThirdParty;
    }

    public String getFinanceCompany() {
        return financeCompany;
    }

    public void setFinanceCompany(String financeCompany) {
        this.financeCompany = financeCompany;
    }

    public String getVehicleUsage() {
        return vehicleUsage;
    }

    public void setVehicleUsage(String vehicleUsage) {
        this.vehicleUsage = vehicleUsage;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getFuelType() {
        return fuelType;
    }

    public void setFuelType(String fuelType) {
        this.fuelType = fuelType;
    }

    public Integer getNoOfSeat() {
        return noOfSeat;
    }

    public void setNoOfSeat(Integer noOfSeat) {
        this.noOfSeat = noOfSeat;
    }

    public Integer getVehicleAge() {
        return vehicleAge;
    }

    public void setVehicleAge(Integer vehicleAge) {
        this.vehicleAge = vehicleAge;
    }

    public String getPolSuspend() {
        return polSuspend;
    }

    public void setPolSuspend(String polSuspend) {
        this.polSuspend = polSuspend;
    }

    public String getEngCapacity() {
        return engCapacity;
    }

    public void setEngCapacity(String engCapacity) {
        this.engCapacity = engCapacity;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getLastModifyUser() {
        return lastModifyUser;
    }

    public void setLastModifyUser(String lastModifyUser) {
        this.lastModifyUser = lastModifyUser;
    }

    public String getLastModifyDateTime() {
        return lastModifyDateTime;
    }

    public void setLastModifyDateTime(String lastModifyDateTime) {
        this.lastModifyDateTime = lastModifyDateTime;
    }

    public String getCurrentPolStatus() {
        return currentPolStatus;
    }

    public void setCurrentPolStatus(String currentPolStatus) {
        this.currentPolStatus = currentPolStatus;
    }

    public PolicyStatusEnum getValidePolicyStatus() {
        return validePolicyStatus;
    }

    public void setValidePolicyStatus(PolicyStatusEnum validePolicyStatus) {
        this.validePolicyStatus = validePolicyStatus;
    }

    public String getVehicleColor() {
        return vehicleColor;
    }

    public void setVehicleColor(String vehicleColor) {
        this.vehicleColor = vehicleColor;
    }

    public String getDeduction() {
        return deduction;
    }

    public void setDeduction(String deduction) {
        this.deduction = deduction;
    }

    public List<CoverDto> getCoverDtoList() {
        return coverDtoList;
    }

    public void setCoverDtoList(List<CoverDto> coverDtoList) {
        this.coverDtoList = coverDtoList;
    }

    public List<ExcessDto> getExcessDtoList() {
        return excessDtoList;
    }

    public void setExcessDtoList(List<ExcessDto> excessDtoList) {
        this.excessDtoList = excessDtoList;
    }


    public List<PaidDetailsDto> getPaidDetailsDtoList() {
        return paidDetailsDtoList;
    }

    public void setPaidDetailsDtoList(List<PaidDetailsDto> paidDetailsDtoList) {
        this.paidDetailsDtoList = paidDetailsDtoList;
    }

    public BigDecimal getPaidTotalAmount() {
        return paidTotalAmount;
    }

    public void setPaidTotalAmount(BigDecimal paidTotalAmount) {
        this.paidTotalAmount = paidTotalAmount;
    }

    public List<PolicyMemoDto> getPolicyMemoDtoList() {
        return policyMemoDtoList;
    }

    public void setPolicyMemoDtoList(List<PolicyMemoDto> policyMemoDtoList) {
        this.policyMemoDtoList = policyMemoDtoList;
    }

    public List<SellingAgentDetailsDto> getSellingAgentDetailsDtoList() {
        return sellingAgentDetailsDtoList;
    }

    public void setSellingAgentDetailsDtoList(List<SellingAgentDetailsDto> sellingAgentDetailsDtoList) {
        this.sellingAgentDetailsDtoList = sellingAgentDetailsDtoList;
    }

    public List<EndorsementHistoryDto> getEndorsementHistoryDtoList() {
        return endorsementHistoryDtoList;
    }

    public void setEndorsementHistoryDtoList(List<EndorsementHistoryDto> endorsementHistoryDtoList) {
        this.endorsementHistoryDtoList = endorsementHistoryDtoList;
    }

    public List<BillingInfoDto> getBillingInfoDtoList() {
        return billingInfoDtoList;
    }

    public void setBillingInfoDtoList(List<BillingInfoDto> billingInfoDtoList) {
        this.billingInfoDtoList = billingInfoDtoList;
    }

    public List<LearnerDriverDetailsDto> getLearnerDriverDetailsDtoList() {
        return learnerDriverDetailsDtoList;
    }

    public void setLearnerDriverDetailsDtoList(List<LearnerDriverDetailsDto> learnerDriverDetailsDtoList) {
        this.learnerDriverDetailsDtoList = learnerDriverDetailsDtoList;
    }

    public Integer getVehicleClassId() {
        return vehicleClassId;
    }

    public void setVehicleClassId(Integer vehicleClassId) {
        this.vehicleClassId = vehicleClassId;
    }

    public String getTradePlateNo() {
        return tradePlateNo;
    }

    public void setTradePlateNo(String tradePlateNo) {
        this.tradePlateNo = tradePlateNo;
    }

    public String getPolicyStatus() {
        return policyStatus;
    }

    public void setPolicyStatus(String policyStatus) {
        this.policyStatus = policyStatus;
    }

    public String getLastModifyDate() {
        return lastModifyDate;
    }

    public void setLastModifyDate(String lastModifyDate) {
        this.lastModifyDate = lastModifyDate;
    }

    public String getCmsUpdateDateTime() {
        return cmsUpdateDateTime;
    }

    public void setCmsUpdateDateTime(String cmsUpdateDateTime) {
        this.cmsUpdateDateTime = cmsUpdateDateTime;
    }

    public String getCurDate() {
        return curDate;
    }

    public void setCurDate(String curDate) {
        this.curDate = curDate;
    }

    public PremiumBreakupFormDto getPremiumBreakupFormDto() {
        return premiumBreakupFormDto;
    }

    public void setPremiumBreakupFormDto(PremiumBreakupFormDto premiumBreakupFormDto) {
        this.premiumBreakupFormDto = premiumBreakupFormDto;
    }

    public String getIndComFlag() {
        return indComFlag;
    }

    public void setIndComFlag(String indComFlag) {
        this.indComFlag = indComFlag;
    }

    public String getCompanyBranch() {
        return companyBranch;
    }

    public void setCompanyBranch(String companyBranch) {
        this.companyBranch = companyBranch;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getIdenCode() {
        return idenCode;
    }

    public void setIdenCode(String idenCode) {
        this.idenCode = idenCode;
    }


    public PolicySellingAgentDetailsDto getPolicySellingAgentDetailsDto() {
        return policySellingAgentDetailsDto;
    }

    public void setPolicySellingAgentDetailsDto(PolicySellingAgentDetailsDto policySellingAgentDetailsDto) {
        this.policySellingAgentDetailsDto = policySellingAgentDetailsDto;
    }

    public String getFinCompanyCode() {
        return finCompanyCode;
    }

    public void setFinCompanyCode(String finCompanyCode) {
        this.finCompanyCode = finCompanyCode;
    }

    public String getFinCompanyBranch() {
        return finCompanyBranch;
    }

    public void setFinCompanyBranch(String finCompanyBranch) {
        this.finCompanyBranch = finCompanyBranch;
    }

    public String getLoanAccNo() {
        return loanAccNo;
    }

    public void setLoanAccNo(String loanAccNo) {
        this.loanAccNo = loanAccNo;
    }

    public String getBankRefNo() {
        return bankRefNo;
    }

    public void setBankRefNo(String bankRefNo) {
        this.bankRefNo = bankRefNo;
    }

    public String getVehicleNoLastDigit() {
        return vehicleNoLastDigit;
    }

    public void setVehicleNoLastDigit(String vehicleNoLastDigit) {
        this.vehicleNoLastDigit = vehicleNoLastDigit;
    }

    public String getPolicyNumberLastDigit() {
        return policyNumberLastDigit;
    }

    public void setPolicyNumberLastDigit(String policyNumberLastDigit) {
        this.policyNumberLastDigit = policyNumberLastDigit;
    }

    public String getPolicyChannelType() {
        return policyChannelType;
    }

    public void setPolicyChannelType(String policyChannelType) {
        this.policyChannelType = policyChannelType;
    }

    public Integer getNcbPercentange() {
        return ncbPercentange;
    }

    public void setNcbPercentange(Integer ncbPercentange) {
        this.ncbPercentange = ncbPercentange;
    }

    public Integer getNcbYear() {
        return ncbYear;
    }

    public void setNcbYear(Integer ncbYear) {
        this.ncbYear = ncbYear;
    }

    public List<NcbHistoryDetailsDto> getNcbHistoryDetailsSummary() {
        return ncbHistoryDetailsSummary;
    }

    public void setNcbHistoryDetailsSummary(List<NcbHistoryDetailsDto> ncbHistoryDetailsSummary) {
        this.ncbHistoryDetailsSummary = ncbHistoryDetailsSummary;
    }

    public boolean getCallAndGoDescription() {
        return callAndGoDescription;
    }

    public void setCallAndGoDescription(boolean callAndGoDescription) {
        this.callAndGoDescription = callAndGoDescription;
    }

    public IntroducerDto getIntroducerDto() {
        return introducerDto;
    }

    public void setIntroducerDto(IntroducerDto introducerDto) {
        this.introducerDto = introducerDto;
    }

    public String getIntroducer() {
        return introducer;
    }

    public void setIntroducer(String introducer) {
        this.introducer = introducer;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public List<TradePlateDetailDto> getTradePlateDetailDtoList() {
        return tradePlateDetailDtoList;
    }

    public void setTradePlateDetailDtoList(List<TradePlateDetailDto> tradePlateDetailDtoList) {
        this.tradePlateDetailDtoList = tradePlateDetailDtoList;
    }

    public TrailerDetailDto getTrailerDetailDto() {
        return trailerDetailDto;
    }

    public void setTrailerDetailDto(TrailerDetailDto trailerDetailDto) {
        this.trailerDetailDto = trailerDetailDto;
    }

    public String getWorkflow() {
        return workflow;
    }

    public void setWorkflow(String workflow) {
        this.workflow = workflow;
    }

    public String getCategoryDescription() {
        return categoryDescription;
    }

    public void setCategoryDescription(String categoryDescription) {
        this.categoryDescription = categoryDescription;
    }

    public List<IntroducerDto> getIntroducerDetailDtoList() {
        return introducerDetailDtoList;
    }

    public void setIntroducerDetailDtoList(List<IntroducerDto> introducerDetailDtoList) {
        this.introducerDetailDtoList = introducerDetailDtoList;
    }

    public String getIsfClaimNo() {
        return isfClaimNo;
    }

    public void setIsfClaimNo(String isfClaimNo) {
        this.isfClaimNo = isfClaimNo;
    }

}
