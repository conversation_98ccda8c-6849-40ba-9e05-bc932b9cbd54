package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class SmsDto implements Serializable {
    private String destination = AppConstant.STRING_EMPTY;
    private String message = AppConstant.STRING_EMPTY;
    private int messagePriority = AppConstant.ZERO_INT;
    private String messageType = AppConstant.STRING_EMPTY;
    private int status = AppConstant.ZERO_INT;
    private String insertDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String sendDateTime = AppConstant.DEFAULT_DATE_TIME;
    private String smsStatus = AppConstant.STRING_EMPTY;
    private int retryAttempts = AppConstant.ZERO_INT;
    private String policyChannelType = AppConstant.STRING_EMPTY;

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getMessagePriority() {
        return messagePriority;
    }

    public void setMessagePriority(int messagePriority) {
        this.messagePriority = messagePriority;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getInsertDateTime() {
        return insertDateTime;
    }

    public void setInsertDateTime(String insertDateTime) {
        this.insertDateTime = insertDateTime;
    }

    public String getSendDateTime() {
        return sendDateTime;
    }

    public void setSendDateTime(String sendDateTime) {
        this.sendDateTime = sendDateTime;
    }

    public String getSmsStatus() {
        return smsStatus;
    }

    public void setSmsStatus(String smsStatus) {
        this.smsStatus = smsStatus;
    }

    public int getRetryAttempts() {
        return retryAttempts;
    }

    public void setRetryAttempts(int retryAttempts) {
        this.retryAttempts = retryAttempts;
    }

    public String getPolicyChannelType() {
        return policyChannelType;
    }

    public void setPolicyChannelType(String policyChannelType) {
        this.policyChannelType = policyChannelType;
    }
}
