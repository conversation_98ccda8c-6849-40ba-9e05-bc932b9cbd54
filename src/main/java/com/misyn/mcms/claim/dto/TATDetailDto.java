package com.misyn.mcms.claim.dto;

import java.io.Serializable;
public class TATDetailDto implements Serializable {

    private Integer tatId;

    private String taskName;
    private String minTime;
    private String maxTime;
    private String visibility;
    private String deleteReason;
    private String status;


    public TATDetailDto() {
    }

    public TATDetailDto(Integer tatId, String taskName, String minTime, String maxTime, String visibility, String deleteReason, String status) {
        this.tatId = tatId;
        this.taskName = taskName;
        this.minTime = minTime;
        this.maxTime = maxTime;
        this.visibility = visibility;
        this.deleteReason = deleteReason;
        this.status = status;
    }

    public Integer getTatId() {
        return tatId;
    }

    public void setTatId(Integer tatId) {
        this.tatId = tatId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getMinTime() {
        return minTime;
    }

    public void setMinTime(String minTime) {
        this.minTime = minTime;
    }

    public String getMaxTime() {
        return maxTime;
    }

    public void setMaxTime(String maxTime) {
        this.maxTime = maxTime;
    }

    public String getVisibility() {
        return visibility;
    }

    public void setVisibility(String visibility) {
        this.visibility = visibility;
    }

    public String getDeleteReason() {
        return deleteReason;
    }

    public void setDeleteReason(String deleteReason) {
        this.deleteReason = deleteReason;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "TATDetailDto{" +
                "tatId=" + tatId +
                ", taskName='" + taskName + '\'' +
                ", minTime='" + minTime + '\'' +
                ", maxTime='" + maxTime + '\'' +
                ", visibility='" + visibility + '\'' +
                ", deleteReason='" + deleteReason + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
