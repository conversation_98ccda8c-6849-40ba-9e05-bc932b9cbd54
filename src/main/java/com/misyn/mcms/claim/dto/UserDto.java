package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

import java.io.Serializable;
public class UserDto implements Serializable {
    private Integer userCode;
    private String userId;
    private String fullName;
    private String firstName;
    private String lastName;
    private String address1;
    private String address2;
    private String nic;
    private String title;
    private String districtCode;
    private String mobile;
    private String landPhone;
    private int accessUserType;
    private String email;
    private String userStatus;

    private double paymentAuthLimit = 0.0;
    private double paymentLimit = 0.0;
    private double liabilityLimit = 0.0;
    private double reserveLimit = 0.0;
    private Integer teamId ;
    private String reportingTo = AppConstant.STRING_EMPTY;
    private String reportingToName = AppConstant.STRING_EMPTY;
    private String assessorType = AppConstant.STRING_EMPTY;
    private String branchCode = AppConstant.STRING_EMPTY;
    private String needToSendEmail = AppConstant.STRING_EMPTY;
    private Integer rteReserveLimitLevel = 0;

    private String rteLevel2 = AppConstant.STRING_EMPTY;
    private String rteLevel3 = AppConstant.STRING_EMPTY;
    private String rteLevel4 = AppConstant.STRING_EMPTY;
    private String employeeNumber = AppConstant.STRING_EMPTY;
    private String ipaddress = AppConstant.STRING_EMPTY;
    private String sessionId = AppConstant.STRING_EMPTY;

    public Integer getUserCode() {
        return userCode;
    }

    public void setUserCode(Integer userCode) {
        this.userCode = userCode;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getLandPhone() {
        return landPhone;
    }

    public void setLandPhone(String landPhone) {
        this.landPhone = landPhone;
    }

    public int getAccessUserType() {
        return accessUserType;
    }

    public void setAccessUserType(int accessUserType) {
        this.accessUserType = accessUserType;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public double getPaymentAuthLimit() {
        return paymentAuthLimit;
    }

    public void setPaymentAuthLimit(double paymentAuthLimit) {
        this.paymentAuthLimit = paymentAuthLimit;
    }

    public String getReportingTo() {
        return reportingTo;
    }

    public void setReportingTo(String reportingTo) {
        this.reportingTo = reportingTo;
    }

    public String getReportingToName() {
        return reportingToName;
    }

    public void setReportingToName(String reportingToName) {
        this.reportingToName = reportingToName;
    }

    public String getAssessorType() {
        return assessorType;
    }

    public void setAssessorType(String assessorType) {
        this.assessorType = assessorType;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getNeedToSendEmail() {
        return needToSendEmail;
    }

    public void setNeedToSendEmail(String needToSendEmail) {
        this.needToSendEmail = needToSendEmail;
    }

    public Integer getRteReserveLimitLevel() {
        return rteReserveLimitLevel;
    }

    public void setRteReserveLimitLevel(Integer rteReserveLimitLevel) {
        this.rteReserveLimitLevel = rteReserveLimitLevel;
    }

    public String getRteLevel2() {
        return rteLevel2;
    }

    public void setRteLevel2(String rteLevel2) {
        this.rteLevel2 = rteLevel2;
    }

    public String getRteLevel3() {
        return rteLevel3;
    }

    public void setRteLevel3(String rteLevel3) {
        this.rteLevel3 = rteLevel3;
    }

    public String getRteLevel4() {
        return rteLevel4;
    }

    public void setRteLevel4(String rteLevel4) {
        this.rteLevel4 = rteLevel4;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getEmployeeNumber() {
        return employeeNumber;
    }

    public void setEmployeeNumber(String employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getNic() {
        return nic;
    }

    public void setNic(String nic) {
        this.nic = nic;
    }

    public String getUserStatus() {
        return userStatus;
    }

    public void setUserStatus(String userStatus) {
        this.userStatus = userStatus;
    }

    public Integer getTeamId() {
        return teamId;
    }

    public void setTeamId(Integer teamId) {
        this.teamId = teamId;
    }

    public double getPaymentLimit() {
        return paymentLimit;
    }

    public void setPaymentLimit(double paymentLimit) {
        this.paymentLimit = paymentLimit;
    }

    public String getAddress1() {
        return address1;
    }

    public void setAddress1(String address1) {
        this.address1 = address1;
    }

    public String getAddress2() {
        return address2;
    }

    public void setAddress2(String address2) {
        this.address2 = address2;
    }

    public double getLiabilityLimit() {
        return liabilityLimit;
    }

    public void setLiabilityLimit(double liabilityLimit) {
        this.liabilityLimit = liabilityLimit;
    }

    public double getReserveLimit() {
        return reserveLimit;
    }

    public void setReserveLimit(double reserveLimit) {
        this.reserveLimit = reserveLimit;
    }

    public String getIpaddress() {
        return ipaddress;
    }

    public void setIpaddress(String ipAddress) {
        this.ipaddress = ipAddress;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
}
