package com.misyn.mcms.claim.dto.keycloak;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AttributesDto {
    private List<String> parentMenuOrderIndex = new ArrayList<>();
    private List<String> childMenuOrderIndex = new ArrayList<>();
    private List<String> parentMenuTitle = new ArrayList<>();
    private List<String> menuIcon = new ArrayList<>();
    private List<String> parentMenuIcon = new ArrayList<>();
    private List<String> appUrl = new ArrayList<>();

    public List<String> getParentMenuOrderIndex() {
        return parentMenuOrderIndex;
    }

    public void setParentMenuOrderIndex(List<String> parentMenuOrderIndex) {
        this.parentMenuOrderIndex = parentMenuOrderIndex;
    }

    public List<String> getChildMenuOrderIndex() {
        return childMenuOrderIndex;
    }

    public void setChildMenuOrderIndex(List<String> childMenuOrderIndex) {
        this.childMenuOrderIndex = childMenuOrderIndex;
    }

    public List<String> getParentMenuTitle() {
        return parentMenuTitle;
    }

    public void setParentMenuTitle(List<String> parentMenuTitle) {
        this.parentMenuTitle = parentMenuTitle;
    }

    public List<String> getMenuIcon() {
        return menuIcon;
    }

    public void setMenuIcon(List<String> menuIcon) {
        this.menuIcon = menuIcon;
    }

    public List<String> getParentMenuIcon() {
        return parentMenuIcon;
    }

    public void setParentMenuIcon(List<String> parentMenuIcon) {
        this.parentMenuIcon = parentMenuIcon;
    }

    public List<String> getAppUrl() {
        return appUrl;
    }

    public void setAppUrl(List<String> appUrl) {
        this.appUrl = appUrl;
    }
}
