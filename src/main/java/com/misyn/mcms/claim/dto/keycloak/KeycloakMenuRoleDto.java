package com.misyn.mcms.claim.dto.keycloak;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)

public class KeycloakMenuRoleDto {
    private String id;
    private String name;
    private String description;
    private AttributesDto attributes;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public AttributesDto getAttributes() {
        return attributes;
    }

    public void setAttributes(AttributesDto attributes) {
        this.attributes = attributes;
    }
}
