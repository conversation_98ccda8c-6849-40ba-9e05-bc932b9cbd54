package com.misyn.mcms.claim.dto.keycloak;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
public class UserGroupDto {
    private String id;
    private String name;
    private String path;

    @JsonProperty("subGroups")
    private List<UserGroupDto> subGroups; // Recursive structure

    // Getters and setters (or use Lombok @Data)
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public List<UserGroupDto> getSubGroups() {
        return subGroups;
    }

    public void setSubGroups(List<UserGroupDto> subGroups) {
        this.subGroups = subGroups;
    }

    @Override
    public String toString() {
        return "UserGroup{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", path='" + path + '\'' +
                ", subGroups=" + subGroups +
                '}';
    }
}
