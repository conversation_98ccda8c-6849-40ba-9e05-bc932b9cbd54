package com.misyn.mcms.claim.dto.list;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.misyn.mcms.claim.dto.ChargesBreakupDto;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)

public class ChargesBreakupDtoList {

    List<ChargesBreakupDto> results;

    public List<ChargesBreakupDto> getResults() {
        return results;
    }

    public void setResults(List<ChargesBreakupDto> results) {
        this.results = results;
    }
}
