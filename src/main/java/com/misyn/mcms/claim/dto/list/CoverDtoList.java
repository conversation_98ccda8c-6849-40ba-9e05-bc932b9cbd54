package com.misyn.mcms.claim.dto.list;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.misyn.mcms.claim.dto.CoverDto;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CoverDtoList {

    List<CoverDto> results;

    public List<CoverDto> getResults() {
        return results;
    }

    public void setResults(List<CoverDto> results) {
        this.results = results;
    }
}
