package com.misyn.mcms.claim.dto.list;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.misyn.mcms.claim.dto.MvwCweCategoryDto;

import java.util.ArrayList;
import java.util.List;


@JsonIgnoreProperties(ignoreUnknown = true)
public class CweCategoryDtoList {

    List<MvwCweCategoryDto> results=new ArrayList<>();

    public List<MvwCweCategoryDto> getResults() {
        return results;
    }

    public void setResults(List<MvwCweCategoryDto> results) {
        this.results = results;
    }
}
