package com.misyn.mcms.claim.dto.list;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.misyn.mcms.claim.dto.LearnerDriverDetailsDto;

@JsonIgnoreProperties(ignoreUnknown = true)
public class LearnerDriverDetailsDtoList {

    java.util.List<LearnerDriverDetailsDto> results;

    public java.util.List<LearnerDriverDetailsDto> getResults() {
        return results;
    }

    public void setResults(java.util.List<LearnerDriverDetailsDto> results) {
        this.results = results;
    }
}
