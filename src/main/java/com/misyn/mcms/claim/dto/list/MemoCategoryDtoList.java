package com.misyn.mcms.claim.dto.list;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.misyn.mcms.claim.dto.MvwMemoCategoryDto;

import java.util.ArrayList;
import java.util.List;


@JsonIgnoreProperties(ignoreUnknown = true)
public class MemoCategoryDtoList {

    List<MvwMemoCategoryDto> results=new ArrayList<>();

    public List<MvwMemoCategoryDto> getResults() {
        return results;
    }

    public void setResults(List<MvwMemoCategoryDto> results) {
        this.results = results;
    }
}
