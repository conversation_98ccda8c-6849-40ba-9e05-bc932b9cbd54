package com.misyn.mcms.claim.dto.list;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.misyn.mcms.claim.dto.PaidDetailsDto;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PaidDetailsDtoList {

    List<PaidDetailsDto> results;

    public List<PaidDetailsDto> getResults() {
        return results;
    }

    public void setResults(List<PaidDetailsDto> results) {
        this.results = results;
    }
}
