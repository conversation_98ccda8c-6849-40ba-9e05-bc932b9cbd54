package com.misyn.mcms.claim.dto.list;

import com.misyn.mcms.claim.dto.PolicyBaseCategoryDataDto;
import com.misyn.mcms.claim.dto.PolicyExcessCategoryDataDto;
import com.misyn.mcms.claim.dto.PolicyMemoCategoryDataDto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
public class PolicyCategoryDataList implements Serializable {
    private List<PolicyBaseCategoryDataDto> policyServiceDataList =new ArrayList<>();
    private List<PolicyBaseCategoryDataDto> policyCoverDataList=new ArrayList<>();
    private List<PolicyBaseCategoryDataDto> policyBenefitDataList =new ArrayList<>();
    private List<PolicyBaseCategoryDataDto> policyConditionDataList=new ArrayList<>();
    private List<PolicyBaseCategoryDataDto> policySpecialDataList=new ArrayList<>();
    private List<PolicyBaseCategoryDataDto> policyOtherDataList=new ArrayList<>();
    private List<PolicyMemoCategoryDataDto> policyMemoDataList=new ArrayList<>();
    private List<PolicyExcessCategoryDataDto> policyExcessDataList=new ArrayList<>();

    public List<PolicyExcessCategoryDataDto> getPolicyExcessDataList() {
        return policyExcessDataList;
    }

    public void setPolicyExcessDataList(List<PolicyExcessCategoryDataDto> policyExcessDataList) {
        this.policyExcessDataList = policyExcessDataList;
    }

    public PolicyCategoryDataList() {
    }

    public PolicyCategoryDataList(List<PolicyBaseCategoryDataDto> policyServiceDataList, List<PolicyBaseCategoryDataDto> policyCoverDataList, List<PolicyBaseCategoryDataDto> policyBenefitDataList, List<PolicyBaseCategoryDataDto> policyConditionDataList, List<PolicyBaseCategoryDataDto> policySpecialDataList, List<PolicyBaseCategoryDataDto> policyOtherDataList, List<PolicyMemoCategoryDataDto> policyMemoDataList, List<PolicyExcessCategoryDataDto> policyExcessDataList) {
        this.policyServiceDataList = policyServiceDataList;
        this.policyCoverDataList = policyCoverDataList;
        this.policyBenefitDataList = policyBenefitDataList;
        this.policyConditionDataList = policyConditionDataList;
        this.policySpecialDataList = policySpecialDataList;
        this.policyOtherDataList = policyOtherDataList;
        this.policyMemoDataList = policyMemoDataList;
        this.policyExcessDataList = policyExcessDataList;
    }

    public List<PolicyBaseCategoryDataDto> getPolicyServiceDataList() {
        return policyServiceDataList;
    }

    public void setPolicyServiceDataList(List<PolicyBaseCategoryDataDto> policyServiceDataList) {
        this.policyServiceDataList = policyServiceDataList;
    }

    public List<PolicyBaseCategoryDataDto> getPolicyCoverDataList() {
        return policyCoverDataList;
    }

    public void setPolicyCoverDataList(List<PolicyBaseCategoryDataDto> policyCoverDataList) {
        this.policyCoverDataList = policyCoverDataList;
    }

    public List<PolicyBaseCategoryDataDto> getPolicyBenefitDataList() {
        return policyBenefitDataList;
    }

    public void setPolicyBenefitDataList(List<PolicyBaseCategoryDataDto> policyBenefitDataList) {
        this.policyBenefitDataList = policyBenefitDataList;
    }

    public List<PolicyBaseCategoryDataDto> getPolicyConditionDataList() {
        return policyConditionDataList;
    }

    public void setPolicyConditionDataList(List<PolicyBaseCategoryDataDto> policyConditionDataList) {
        this.policyConditionDataList = policyConditionDataList;
    }

    public List<PolicyBaseCategoryDataDto> getPolicySpecialDataList() {
        return policySpecialDataList;
    }

    public void setPolicySpecialDataList(List<PolicyBaseCategoryDataDto> policySpecialDataList) {
        this.policySpecialDataList = policySpecialDataList;
    }

    public List<PolicyBaseCategoryDataDto> getPolicyOtherDataList() {
        return policyOtherDataList;
    }

    public void setPolicyOtherDataList(List<PolicyBaseCategoryDataDto> policyOtherDataList) {
        this.policyOtherDataList = policyOtherDataList;
    }

    public List<PolicyMemoCategoryDataDto> getPolicyMemoDataList() {
        return policyMemoDataList;
    }

    public void setPolicyMemoDataList(List<PolicyMemoCategoryDataDto> policyMemoDataList) {
        this.policyMemoDataList = policyMemoDataList;
    }
}
