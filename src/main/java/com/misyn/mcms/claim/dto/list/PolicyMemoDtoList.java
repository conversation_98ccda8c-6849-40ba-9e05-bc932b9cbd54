package com.misyn.mcms.claim.dto.list;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.misyn.mcms.claim.dto.PolicyMemoDto;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PolicyMemoDtoList {

    List<PolicyMemoDto> results=new ArrayList<>();

    public List<PolicyMemoDto> getResults() {
        return results;
    }

    public void setResults(List<PolicyMemoDto> results) {
        this.results = results;
    }
}
