package com.misyn.mcms.claim.dto.list;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.misyn.mcms.claim.dto.PolicyOthChargesDetailsDto;

import java.util.ArrayList;
import java.util.List;


@JsonIgnoreProperties(ignoreUnknown = true)
public class PolicyOthChargesDetailsDtoList {

    List<PolicyOthChargesDetailsDto> results=new ArrayList<>();

    public List<PolicyOthChargesDetailsDto> getResults() {
        return results;
    }

    public void setResults(List<PolicyOthChargesDetailsDto> results) {
        this.results = results;
    }
}
