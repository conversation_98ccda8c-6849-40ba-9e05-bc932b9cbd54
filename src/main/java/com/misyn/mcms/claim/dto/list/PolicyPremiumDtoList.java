package com.misyn.mcms.claim.dto.list;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.misyn.mcms.claim.dto.PolicyPremiumDto;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PolicyPremiumDtoList {

    List<PolicyPremiumDto> results;

    public List<PolicyPremiumDto> getResults() {
        return results;
    }

    public void setResults(List<PolicyPremiumDto> results) {
        this.results = results;
    }
}
