package com.misyn.mcms.claim.dto.list;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.misyn.mcms.claim.dto.PolicySrcctcDetailsDto;

import java.util.ArrayList;
import java.util.List;


@JsonIgnoreProperties(ignoreUnknown = true)
public class PolicySrcctcDetailsDtoList {

    List<PolicySrcctcDetailsDto> results=new ArrayList<>();

    public List<PolicySrcctcDetailsDto> getResults() {
        return results;
    }

    public void setResults(List<PolicySrcctcDetailsDto> results) {
        this.results = results;
    }
}
