package com.misyn.mcms.claim.dto.list;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.misyn.mcms.claim.dto.TradePlateDetailDto;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TradePlateDetailDtoList {
    java.util.List<TradePlateDetailDto> results;

    public List<TradePlateDetailDto> getResults() {
        return results;
    }

    public void setResults(java.util.List<TradePlateDetailDto> results) {
        this.results = results;
    }
}
