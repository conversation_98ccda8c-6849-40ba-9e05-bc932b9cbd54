package com.misyn.mcms.claim.service.keycloak;
import com.misyn.mcms.claim.dto.keycloak.KeyCloakGroupDto;
import com.misyn.mcms.claim.dto.keycloak.KeyCloakUserDto;
import com.misyn.mcms.claim.exception.KeycloakCustomException;

import java.util.List;

/**
 * Servlet-compatible Keycloak client using existing KeycloakApiClient for token.
 */
public interface KeycloakUserService {

    void saveUser(KeyCloakUserDto user, String userName) throws KeycloakCustomException;

    void updateUser(String keycloakUserID, KeyCloakUserDto user, String userName) throws KeycloakCustomException;

    List<KeyCloakUserDto> getUser(String userName, Boolean exact) throws KeycloakCustomException;

    List<KeyCloakGroupDto> getAllGroups(String userName)throws KeycloakCustomException;

}
