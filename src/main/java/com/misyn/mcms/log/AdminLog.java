package com.misyn.mcms.log;

import com.misyn.mcms.dbconfig.ConnectionPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
public class AdminLog {
    private static final Logger LOGGER = LoggerFactory.getLogger(AdminLog.class);
    private static AdminLog adminLog = null;
    private ConnectionPool cp = null;

    private AdminLog() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }


    public static synchronized AdminLog getInstance() {
        if (adminLog == null) {
            adminLog = new AdminLog();
        }
        return adminLog;
    }


    public synchronized boolean logRecord(String userID, String ipAddress, String description) {
        Connection conn = null;
        PreparedStatement ps = null;
        boolean status = false;

        try {
            conn = getJDBCConnection();

            String sql = "insert into admin_log (txnid,txndate,txntime,userid,ip_address,description) "
                    + "select (max(txnid)+1),current_date,current_time,?,?,? from admin_log";

            ps = conn.prepareStatement(sql);
            ps.setString(1, userID);
            ps.setString(2, ipAddress);
            ps.setString(3, description);

            if (ps.executeUpdate() > 0) {
                status = true;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                    ps = null;
                }
            } catch (Exception ex) {
            }
            releaseJDBCConnection(conn);
        }
        return status;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

}
