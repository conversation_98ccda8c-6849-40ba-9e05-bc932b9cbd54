<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.Date" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <title>Sample JSP Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .table th, .table td {
            vertical-align: middle;
        }
    </style>
    <link href="/css/common/tableStyle.css" rel="stylesheet" type="text/css"/>
</head>
<body>
<div class="container-fluid">
    <div class="row header-bg">
        <div class="col-sm-12 py-2 bg-dark">
            <h5 class="float-left text-dark">System User Groups</h5>
        </div>
    </div>

    <div class="row">
    <div class="col-sm-12 py-2 text-right">
        <button class="btn btn-success btn-sm" data-toggle="modal" data-target="#addRoleModal">
            + User Group
        </button>
    </div>
    </div>
    <!-- Data Table -->
    <table id="user-privileges-table" class="table table-sm table-hover" cellspacing="0" style="cursor:pointer" width="100%">
        <thead class="blueheader">
        <tr>
            <th>Group Id</th>
            <th>Group Name</th>
            <th>Status</th>
            <th style="width: 10%">Action</th>
        </tr>
        </thead>
        <tbody id="roles-table-body">
        <c:forEach var="group" items="${groupList}">
            <tr>
                <td>${group.groupId}</td>
                <td>${group.groupName}</td>
                <td>${group.status}</td>
                <td>
                    <div style="display: flex;gap: 5px">
                        <button class='btn-primary btn btn-sm' onclick="loadRole('${group.groupId}')"> <i class="fa fa-edit"></i></button>
                    </div>
                </td>
            </tr>
        </c:forEach>

        </tbody>
    </table>
</div>

<!-- Modal -->
<div class="modal fade" id="addRoleModal" tabindex="-1" role="dialog" aria-labelledby="addRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRoleModalLabel">Add New User Group</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="roleForm">
                    <div class="form-group">
                        <label for="roleName">Group Name</label>
                        <input type="text" class="form-control" id="groupName" placeholder="Enter group name" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">Close</button>
                <button type="button" id="saveUserGroupBtn" class="btn btn-primary btn-sm">Save</button>
            </div>
        </div>
    </div>
</div>
</body>
<script>
    <%--// Save role--%>
    <%--document.getElementById('saveRoleBtn').addEventListener('click', function () {--%>
    <%--    const roleName = document.getElementById('roleName').value;--%>
    <%--    fetch('/roles', {--%>
    <%--        method: 'POST',--%>
    <%--        headers: {'Content-Type': 'application/json'},--%>
    <%--        body: JSON.stringify(roleName)--%>
    <%--    })--%>
    <%--        .then(response => {--%>
    <%--            if (response.ok) {--%>
    <%--                $('#addRoleModal').modal('hide'); // Close modal--%>
    <%--            } else {--%>
    <%--                alert('Error creating role');--%>
    <%--            }--%>
    <%--        });--%>
    <%--});--%>

    <%--// Delete role--%>
    <%--function deleteRole(roleName) {--%>
    <%--    if (!confirm('Are you sure?')) return;--%>
    <%--    fetch(`/roles/${roleName}`, { method: 'DELETE' })--%>
    <%--        .then(response => {--%>
    <%--            if (response.ok) loadRoles();--%>
    <%--            else alert('Failed to delete role');--%>
    <%--        });--%>
    <%--}--%>

    //save user group
    $('#saveUserGroupBtn').click(function() {
        const groupName = $('#groupName').val();
        if (groupName.trim() === "") {
            alert("Please enter a group name.");
            return;
        }
        $.ajax({
            url: contextPath + "/rolesController/saveUserGroup",
            type: 'POST',
            contentType: 'application/json; charset=utf-8',
            data: JSON.stringify({
                groupName: groupName
            }),
            success: function (result) {
                //ToDO : Alert Message not working properly
                var messageType = JSON.parse(result);
                var message = "";
                if (messageType.status == "success") {
                    message = "User Group Saved Successfully!";
                    notify(message, "success");
                    $('#addRoleModal').modal('hide');
                    $('#groupName').val('');
                } else {
                    message = "Failed to Save User Group";
                    notify(message, "danger");
                }
            }
        });
    });

    function loadRole(groupName) {
        window.location = contextPath + "/rolesController/loadRoleById?groupId =" + groupId;
    }

    hideLoader()
</script>
</html>
