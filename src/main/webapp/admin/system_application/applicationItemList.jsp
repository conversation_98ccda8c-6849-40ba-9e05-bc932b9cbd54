<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.Date" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <title>Sample JSP Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .table th, .table td {
            vertical-align: middle;
        }
    </style>
    <link href="/css/common/tableStyle.css" rel="stylesheet" type="text/css"/>

    <style>
        .section-box {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .form-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row input[type=text] {
            flex: 1;
            padding: 6px 8px;
            border: 1px solid #ccc;
            border-radius: 6px;
        }

        .form-row button:hover {
            background: #0056b3;
        }

        select[multiple] {
            width: 100%;
            height: 120px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 6px;
        }

        .transfer-container {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .transfer-list {
            width: 45%;
            height: 400px;
            border: 1px solid #ccc;
            border-radius: 8px;
            overflow-y: auto;
            padding: 10px;
            background: #f9f9f9;
        }

        .transfer-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .transfer-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .transfer-buttons button {
            padding: 8px 12px;
            border: none;
            background: #007bff;
            color: white;
            cursor: pointer;
            border-radius: 5px;
            transition: 0.2s;
        }

        .transfer-buttons button:hover {
            background: #0056b3;
        }
    </style>


</head>
<body>

<%
    // Java code block (scriptlet)
    Date now = new Date();
    int hour = now.getHours();
    String greeting;

    if (hour < 12) {
        greeting = "Good morning!";
    } else if (hour < 18) {
        greeting = "Good afternoon!";
    } else {
        greeting = "Good evening!";
    }
%>
<div class="container-fluid">
    <div class="row header-bg">
        <div class="col-sm-12 py-2 bg-dark">
            <h5 class="float-left text-dark">System User Roles</h5>
        </div>
    </div>

    <div class="row header-bg mt-2" style="justify-content: center">
        <div class="col-sm-6">
            <div class="section-box">
                <p>Group Name</p>
                <div class="form-row">
                    <input type="text" id="roleName" value="${groupName}" placeholder="Enter role name" readonly/>
                    <button type="button" class="btn btn-danger" onclick="deleteGroup()">Delete Group</button>
                    <button type="button" class="btn btn-dark" onclick="disableGroup()">Disable Group</button>
                    <button type="button" class="btn btn-primary" onclick="updateGroup()">Update Group</button>
                </div>
            </div>

        </div>
    </div>
    <div class="row header-bg mt-2" style="justify-content: center">
        <div class="col-sm-6">
            <div class="transfer-container">
                <!-- Left List -->
                <div class="transfer-list" id="available">
                    <p>Available Roles</p>
                    <div class="transfer-item"><input type="checkbox" value="Admin"> Sample Available</div>
                </div>

                <!-- Buttons -->
                <div class="transfer-buttons">
                    <button onclick="moveSelected('available','assigned')"> &gt; </button>
                    <button onclick="moveSelected('assigned','available')"> &lt; </button>
                </div>

                <!-- Right List -->
                <div class="transfer-list" id="assigned">
                    <p>Assigned Roles</p>
                    <div class="transfer-item"><input type="checkbox" value="Admin"> Sample Assigned</div>
                </div>
            </div>
        </div>
    </div>

</div>

<script>
    // Clear role input
    function deleteGroup() {
        document.getElementById("roleName").value = "";
    }

    // Update role (example alert, replace with AJAX/save later)
    function updateGroup() {
        const role = document.getElementById("roleName").value;
        if (role.trim() === "") {
            alert("Please enter a role name.");
            return;
        }
        alert("Role updated: " + role);
    }


    // Transfer roles
    function moveSelected(fromId, toId) {
        const from = document.getElementById(fromId);
        const to = document.getElementById(toId);

        const checkedBoxes = from.querySelectorAll("input[type=checkbox]:checked");
        checkedBoxes.forEach(cb => {
            cb.checked = false;
            const parent = cb.parentElement;
            to.appendChild(parent);
        });
    }

    // Hide loader (your existing function call)
    hideLoader();
</script>

</body>
<script>
    hideLoader()
</script>




</html>
